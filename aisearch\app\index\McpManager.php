<?php

require_once 'McpConfig.php';

/**
 * MCP (Model Context Protocol) 管理器 v2.0
 * 优化版本，增强稳定性和错误处理能力
 */
class McpManager
{
    private AiTools $aiTools;
    private indexAction $indexAction;
    private array $logger = [];

    // MCP类型常量
    const MCP_TEXT_CORRECTION = 'text_correction';
    const MCP_TEXT_KEYWORDS = 'text_keywords';
    const MCP_CHECK_TEXT_REFERENCE = 'text_reference';

    // 支持的MCP列表
    private array $supportedMcps = [
        self::MCP_TEXT_CORRECTION => [
            'name' => '文本纠错',
            'description' => '检测并纠正文本中的语法错误、拼写错误等',
            'method' => 'textCorrection'
        ],
            // 预留其他MCP扩展
        self::MCP_TEXT_KEYWORDS => [
            'name' => '关键词提取',
            'description' => '从文本中提取关键信息和主题词',
            'method' => 'getTextKeywords'
        ],
        self::MCP_CHECK_TEXT_REFERENCE => [
            'name' => '检测文本引用',
            'description' => '检测文本是否引用了外部的资讯',
            'method' => 'checkTextReference'
        ]
    ];

    public function __construct(AiTools $aiTools, indexAction $indexAction)
    {
        $this->aiTools = $aiTools;
        $this->indexAction = $indexAction;
        $this->initializeLogger();
    }

    /**
     * 初始化日志记录器
     */
    private function initializeLogger(): void
    {
        $this->logger = [
            'start_time' => microtime(true),
            'requests' => [],
            'errors' => []
        ];
    }

    /**
     * 记录日志
     * @param string $level 日志级别 (info, warning, error)
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    private function log(string $level, string $message, array $context = []): void
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'memory_usage' => memory_get_usage(true)
        ];

        if ($level === 'error') {
            $this->logger['errors'][] = $logEntry;
        } else {
            $this->logger['requests'][] = $logEntry;
        }

        // 写入文件日志
        $this->writeLogToFile($logEntry);
    }

    /**
     * 写入文件日志
     * @param array $logEntry
     */
    private function writeLogToFile(array $logEntry): void
    {
        $logDir = './logs/mcp/' . date('Y-m-d') . '/';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }

        $logFile = $logDir . 'mcp-' . date('H') . '.log';
        $logLine = sprintf(
            "[%s] %s: %s %s\n",
            $logEntry['timestamp'],
            strtoupper($logEntry['level']),
            $logEntry['message'],
            !empty($logEntry['context']) ? json_encode($logEntry['context'], JSON_UNESCAPED_UNICODE) : ''
        );

        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }

    /**
     * 获取支持的MCP列表
     * @return array
     */
    public function getSupportedMcps(): array
    {
        return $this->supportedMcps;
    }

    /**
     * 执行MCP调用
     * @param string $mcpType MCP类型
     * @param array $params 参数
     * @return array 统一的返回格式
     */
    public function executeMcp(string $mcpType, array $params): array
    {

        $params['text'] = $params['content'];
        $params['from'] ??= 'www-web';

        // 检查MCP类型是否支持
        if (!isset($this->supportedMcps[$mcpType])) {
            return $this->formatResponse(0, "不支持的MCP类型: {$mcpType}");
        }

        $mcpConfig = $this->supportedMcps[$mcpType];
        $method = $mcpConfig['method'];

        // 检查方法是否存在
        if (!method_exists($this, $method)) {
            return $this->formatResponse(0, "MCP方法未实现: {$method}");
        }

        try {
            // 执行对应的MCP方法
            return $this->$method($params);
        } catch (Exception $e) {
            return $this->formatResponse(0, "MCP执行失败: " . $e->getMessage());
        }
    }

    /**
     * 执行文本纠错MCP v2.0 - 优化版本
     * @param array $params 参数包含: text(必需), from(可选), model(可选)
     * @return array
     */
    private function textCorrection(array $params): array
    {
        $startTime = microtime(true);
        // $this->log('info', 'MCP文本纠错开始', ['params' => $params]);

        // 参数验证
        $validation = $this->validateTextCorrectionParams($params);
        if (!$validation['valid']) {
            // $this->log('error', '参数验证失败', ['error' => $validation['error']]);
            return $this->formatResponse(0, $validation['error'], [], McpConfig::getErrorCode('INVALID_FORMAT'));
        }

        $text = $params['text'];
        $from = $params['from'] ?? 'www-web';

        // 使用配置文件中的模型设置
        $serviceConfig = McpConfig::getServiceConfig(self::MCP_TEXT_CORRECTION);
        $model = $params['model'] ?? $serviceConfig['default_model'];

        // 构造AI调用参数，使用新的优化提示词
        $aiParams = [
            'stream' => 0,  // 关闭流式输出
            'model' => $model,
            'prompt_type' => $serviceConfig['prompt_type'], // 使用新的prompt_type=10
            'temperature' => $serviceConfig['temperature'],
            'max_tokens' => $serviceConfig['max_tokens']
        ];

        $data = [
            'content' => $text,
            'session_id' => '',  // MCP调用不需要session
            'from' => $from,
            'isSearch' => 0
        ];

        try {
            // 调用AI进行文本纠错
            // $this->log('info', 'AI调用开始', ['model' => $model, 'text_length' => mb_strlen($text)]);

            [$content, $reasoning_content, $tool_calls] = $this->indexAction->chat_by_oneapi(
                $aiParams,
                $data,
                ['is_save_chat' => false]
            );
            // $this->log('info', 'AI调用完成', ['response_length' => mb_strlen($content)]);

            // 解析AI返回的结果 - 使用新的解析逻辑
            $result = $this->parseTextCorrectionResult($content, $text);
            // $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            /*$this->log('info', 'MCP文本纠错完成', [
                'execution_time_ms' => $executionTime,
                'has_errors' => $result['has_errors'] ?? false,
                'corrections_count' => count($result['corrections'] ?? [])
            ]);*/
            // dd($result);

            $response_json_model = <<<JSON
            {
                "has_errors": false,
                "highlighted_text": "纠错后的完整文本，错误部分用<span style='color:red'>标红</span>",
                "corrections": [
                    {
                        "ori": "原始错误文本",
                        "mod": "纠正后的文本", 
                    }
                ],
                "summary": "纠错摘要信息"
            }
            JSON;
            $success = 1;
            $msg = '文本纠错完成';
            $response_arr = [];
            if (!is_array($result)) {
                $success = 0;
                $msg = '文本纠错异常';
                $response_arr = json_decode(($response_json_model), true);
                $response_arr["corrected_text"] = $result;
                $response_arr["highlighted_text"] = $result;
                $response_arr["corrections"] = [
                    "ori" => $text,
                    "mod" => $result,
                ];
                $response_arr["summary"] = $result;
            } else {
                // $ori_text = $text;
                $has_errors = false;
                foreach ($result as $key => $val) {
                    if ($val['ori'] == $val['mod']) {
                        unset($result[$key]);
                        continue;
                    }
                    $text = str_replace($val['ori'], "<span style='color:red;' class='custom_color'>{$val['ori']}</span>", $text);
                    $has_errors = true;
                }
                $result = array_values($result);
                $response_arr = [
                    'has_errors' => $has_errors,
                    'highlighted_text' => $text,
                    'corrections' => $result,
                ];
            }

            return $this->formatResponse($success, $msg, $response_arr);


        } catch (Exception $e) {
            /*$this->log('error', 'MCP文本纠错失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);*/
            return $this->formatResponse(0, "文本纠错失败: " . $e->getMessage(), [], McpConfig::getErrorCode('AI_RESPONSE_ERROR'));
        }
    }

    /**
     * 验证文本纠错参数
     * @param array $params
     * @return array
     */
    private function validateTextCorrectionParams(array $params): array
    {
        $text = $params['text'];
        // 检查文本是否为纯空白
        if (trim($text) === '') {
            return ['valid' => false, 'error' => '文本内容不能为空白字符'];
        }

        return ['valid' => true, 'error' => ''];
    }

    /**
     * 解析文本纠错结果 v2.0 - 优化版本，支持JSON格式解析
     * @param string $aiResponse AI返回的内容
     * @param string $originalText 原始文本
     * @return array
     */
    private function parseTextCorrectionResult(string $aiResponse, string $originalText): array|null|string
    {
        // $this->log('info', '开始解析AI响应', ['response_length' => mb_strlen($aiResponse)]);
        // dd($aiResponse);
        // 首先尝试解析JSON格式响应
        $jsonResult = $this->parseJsonResponse($aiResponse);
        if ($jsonResult !== null) {
            // 验证JSON响应格式
            [$isValid, $errorMessage, $errorCode] = McpConfig::validateResponse($jsonResult);
            if ($isValid) {
                // $this->log('info', 'JSON格式解析成功', ['has_errors' => $jsonResult['has_errors']]);
                return $jsonResult;
            } else {
                // $this->log('warning', 'JSON格式验证失败', ['error' => $errorMessage]);
                // 继续尝试传统格式解析
                return $jsonResult;
            }
        } else {
            return $aiResponse;
        }

    }

    /**
     * 解析关键词提取结果
     * @param string $aiResponse AI返回的内容
     * @param string $originalText 原始文本
     * @return array|null
     */
    private function parseTextKeywordsResult(string $aiResponse, string $originalText): ?array
    {
        // $this->log('info', '开始解析关键词提取AI响应', ['response_length' => mb_strlen($aiResponse)]);

        // 首先尝试解析JSON格式响应
        $jsonResult = $this->parseJsonResponse($aiResponse);
        if ($jsonResult !== null) {
            // 验证关键词提取JSON响应格式
            // if (isset($jsonResult['keywords']) && is_array($jsonResult['keywords'])) {
            // $this->log('info', '关键词提取JSON格式解析成功', ['keywords_count' => count($jsonResult['keywords'])]);
            return $jsonResult;
            // }
        }

        // 如果JSON解析失败，尝试从文本中提取关键词（以逗号分隔）
        $keywords = [];
        // $summary = '';

        // 尝试提取关键词列表（用逗号分隔）
        if (preg_match('/^(.*?)(?:\n|$)/', $aiResponse, $matches)) {
            $firstLine = trim($matches[1]);
            if (strpos($firstLine, ',') !== false) {
                $keywords = array_map('trim', explode(',', $firstLine));
            } else {
                $keywords = [trim($firstLine)];
            }
        }

        // 尝试提取摘要（在关键词之后的部分）
        // if (preg_match('/\n(.*)/s', $aiResponse, $matches)) {
        //     $summary = trim($matches[1]);
        // }

        return [
            'keywords' => $keywords,
            // 'summary' => $summary ?: $aiResponse
        ];
    }

    /**
     * 解析文本引用检测结果
     * @param string $aiResponse AI返回的内容
     * @param string $originalText 原始文本
     * @return array|null
     */
    private function parseTextReferenceResult(string $aiResponse, string $originalText): ?array
    {
        // $this->log('info', '开始解析文本引用检测AI响应', ['response_length' => mb_strlen($aiResponse)]);

        $white_list = [
            '钢之家',
            '国内讯',
            '北京讯',
            '上海讯',
            '广州讯',
            '唐山讯',
            '河南讯',
            '河北讯',
            '沈阳讯',
            '南宁讯',
            '西安讯',
            '山西讯',
            '山东讯',
            '成都讯',
            '重庆讯',
            '乐从讯',
            '兰州讯',
            '长沙讯',
            '福州讯',
            '贵州讯',
            '贵阳讯',
            '昆明讯',
            '济南讯',
            '陕西讯',
            '银川讯',
            '安徽讯',
            '晋南讯',
            '港口讯',
            '武汉讯',
            '新疆讯',
            '无锡讯',
            '佛山讯',
            '四川讯',
            '郑州讯',
            '黑龙江讯',
            '内蒙古讯',
            '乌鲁木齐讯',
            '雅加达讯',
            '钢厂快讯',
            '市场快讯',
            '东北讯',
            '华北讯',
            '华东讯',
            '中南讯',
            '西北讯',
            '中西讯',
            '西南讯',
        ];

        // 首先尝试解析JSON格式响应
        $jsonResult = $this->parseJsonResponse($aiResponse);
        $jsonResult = null; // 禁用AI结果
        if ($jsonResult !== null) {
            // 验证文本引用检测JSON响应格式
            // if (isset($jsonResult['has_external_reference'])) { // 尽量降低 AI 的计算复杂度，以提升正确率，故因此不返回多余字段，只返回 text 一个字段
            // $this->log('info', '文本引用检测JSON格式解析成功', ['has_external_reference' => $jsonResult['has_external_reference']]);

            $returned_array = [];
            foreach ($jsonResult as $item) {
                $found = false;
                foreach ($white_list as $a_word) {
                    if ($item && (strpos($item['text'], $a_word) !== false)) {
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    $returned_array[] = $item;
                }
            }

            return [
                'result_type' => 'ai',
                'results' => $returned_array
            ];
            // }
        }

        // 如果JSON解析失败，使用正则表达式直接检测文本中的引用
        $references = [];
        $hasExternalReference = false;

        // 查找"xxxx讯[，|。|：]"格式的引用，但排除"钢之家讯"
        $pattern = '/([^\s]+?讯)([，。：])/';
        if (preg_match_all($pattern, $originalText, $matches, PREG_OFFSET_CAPTURE)) {
            foreach ($matches[1] as $index => $match) {
                $source = $match[0];
                $add_this = true;
                if ($source == '<p>快讯') {
                    $add_this = false;
                }
                if ($add_this) {
                foreach ($white_list as $a_word) {
                    if (strpos($source, $a_word) !== false) {
                        $add_this = false;
                        break;
                    }
                }
            }
                // 排除白名单
                if ($add_this) {
                    $hasExternalReference = true;
                    $startPos = $match[1];
                    $endPos = $startPos + strlen($source) + mb_strlen($matches[2][$index][0]);

                    // 获取完整的引用文本（从引用标识到下一个标点符号）
                    $fullText = '';
                    if (preg_match('/(' . preg_quote($source) . '[，。：].*?)[，。]/u', substr($originalText, $startPos), $textMatches)) {
                        $fullText = $textMatches[1];
                    }

                    $references[] = [
                        'source' => $source,
                        'position' => [
                            'start' => $startPos,
                            'end' => $endPos
                        ],
                        'text' => $fullText ?: $source
                    ];
                }
            }
        }

        $summary = $hasExternalReference ?
            '检测到' . count($references) . '个外部引用' :
            '未检测到外部引用';

        return [
            'result_type' => 'manual',
            'has_external_reference' => $hasExternalReference,
            'references' => $references,
            'summary' => $summary
        ];
    }

    /**
     * 解析JSON格式的AI响应
     * @param string $aiResponse
     * @return array|null
     */
    private function parseJsonResponse(string $aiResponse): ?array
    {
        // 清理响应内容，移除可能的markdown代码块标记
        $cleanResponse = preg_replace('/```json\s*|\s*```/', '', $aiResponse);
        $cleanResponse = trim($cleanResponse);

        // 尝试解析JSON
        $decoded = json_decode($cleanResponse, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }

        // 尝试提取JSON部分（可能被其他文本包围）
        if (preg_match('/\{.*\}/s', $cleanResponse, $matches)) {
            $decoded = json_decode($matches[0], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return $decoded;
            }
        }

        return null;
    }

    /**
     * 根据正文提取关键词
     * @param array $params
     * @return array
     */
    private function getTextKeywords(array $params): array
    {
        $startTime = microtime(true);
        // $this->log('info', 'MCP关键词提取开始', ['params' => $params]);

        $text = $params['text'];
        $from = $params['from'] ?? 'www-web';

        // 使用配置文件中的模型设置
        $serviceConfig = McpConfig::getServiceConfig(self::MCP_TEXT_KEYWORDS);
        $model = $params['model'] ?? $serviceConfig['default_model'];

        // 构造AI调用参数
        $aiParams = [
            'stream' => 0,  // 关闭流式输出
            'model' => $model,
            'prompt_type' => $serviceConfig['prompt_type'],
            'temperature' => $serviceConfig['temperature'],
            'max_tokens' => $serviceConfig['max_tokens']
        ];

        $data = [
            'content' => $text,
            'session_id' => '',  // MCP调用不需要session
            'from' => $from,
            'isSearch' => 0
        ];

        try {
            // 调用AI进行关键词提取
            // $this->log('info', 'AI调用开始', ['model' => $model, 'text_length' => mb_strlen($text)]);

            [$content, $reasoning_content, $tool_calls] = $this->indexAction->chat_by_oneapi(
                $aiParams,
                $data,
                ['is_save_chat' => false]
            );
            // $this->log('info', 'AI调用完成', ['response_length' => mb_strlen($content)]);

            // 解析AI返回的结果
            $result = $this->parseTextKeywordsResult($content, $text);
            // $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            /*$this->log('info', 'MCP关键词提取完成', [
                'execution_time_ms' => $executionTime,
                'keywords_count' => count($result['keywords'] ?? [])
            ]);*/

            $success = 1;
            $msg = '关键词提取完成';
            $response_arr = [];

            if (!is_array($result)) {
                $success = 0;
                $msg = '关键词提取异常';
                $response_arr = [
                    "keywords" => [],
                    "summary" => $content
                ];
            } else {
                $response_arr = $result;
            }

            return $this->formatResponse($success, $msg, $response_arr);

        } catch (Exception $e) {
            /*$this->log('error', 'MCP关键词提取失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);*/
            return $this->formatResponse(0, "关键词提取失败: " . $e->getMessage(), [], McpConfig::getErrorCode('AI_RESPONSE_ERROR'));
        }
    }

    /**
     * 检测文本引用MCP
     * @param array $params
     * @return array
     */
    private function checkTextReference(array $params): array
    {
        $startTime = microtime(true);
        // $this->log('info', 'MCP检测文本引用开始', ['params' => $params]);

        $text = $params['text'];
        $from = $params['from'] ?? 'www-web';
        // 获取额外参数
        $entryId = $params['entry_id'] ?? '';
        $checkerId = $params['checker_id'] ?? '';
        $title = $params['ntitle'] ?? '';

        // 使用配置文件中的模型设置
        $serviceConfig = McpConfig::getServiceConfig('text_reference');
        $model = $params['model'] ?? $serviceConfig['default_model'];

        // 构造AI调用参数
        $aiParams = [
            'stream' => 0,  // 关闭流式输出
            'model' => $model,
            'prompt_type' => $serviceConfig['prompt_type'],
            'temperature' => $serviceConfig['temperature'],
            'max_tokens' => $serviceConfig['max_tokens']
        ];

        $data = [
            'content' => $text,
            'session_id' => '',  // MCP调用不需要session
            'from' => $from,
            'isSearch' => 0
        ];

        try {
            // 调用AI进行文本引用检测
            // $this->log('info', 'AI调用开始', ['model' => $model, 'text_length' => mb_strlen($text)]);

            [$content, $reasoning_content, $tool_calls] = $this->indexAction->chat_by_oneapi(
                $aiParams,
                $data,
                ['is_save_chat' => false]
            );
            // $this->log('info', 'AI调用完成', ['response_length' => mb_strlen($content)]);

            // 解析AI返回的结果
            $result = $this->parseTextReferenceResult($content, $text);
            // $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            /*$this->log('info', 'MCP检测文本引用完成', [
                'execution_time_ms' => $executionTime,
                'has_external_reference' => $result['has_external_reference'] ?? false
            ]);*/

            $success = 1;
            $msg = '文本引用检测完成';
            $response_arr = [];

            if (!is_array($result)) {
                $success = 0;
                $msg = '文本引用检测异常';
                $response_arr = [
                    "has_external_reference" => false,
                    "references" => [],
                    "summary" => $content,
                    "entry_id" => $entryId,
                    "checker_id" => $checkerId,
                    "ntitle" => $title
                ];
            } else {
                // 添加额外参数到响应中
                $response_arr = $result;
                $response_arr['entry_id'] = $entryId;
                $response_arr['checker_id'] = $checkerId;
                $response_arr['title'] = $title;
            }

            return $this->formatResponse($success, $msg, $response_arr);

        } catch (Exception $e) {
            /*$this->log('error', 'MCP检测文本引用失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);*/
            return $this->formatResponse(0, "文本引用检测失败: " . $e->getMessage(), [
                'entry_id' => $entryId,
                'checker_id' => $checkerId,
                'title' => $title
            ], McpConfig::getErrorCode('AI_RESPONSE_ERROR'));
        }
    }

    /**
     * 格式化返回结果 v2.0 - 优化版本
     * @param bool $success 是否成功
     * @param string $message 消息
     * @param array $data 数据
     * @param int $errorCode 错误代码
     * @return array
     */
    private function formatResponse(bool $success, string $message, array $data = [], int $errorCode = 0): array
    {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'execution_time' => round((microtime(true) - $this->logger['start_time']) * 1000, 2)
        ];

        if (!$success && $errorCode > 0) {
            $response['error_code'] = $errorCode;
        }

        // 添加调试信息（仅在开发环境）
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            $response['debug'] = [
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'log_entries' => count($this->logger['requests']),
                'error_count' => count($this->logger['errors'])
            ];
        }

        return $response;
    }

    /**
     * 计算文本中错误的精确位置
     * @param string $originalText 原始文本
     * @param string $errorText 错误文本
     * @param int $occurrence 第几次出现（默认第1次）
     * @return array [start, end] 位置信息
     */
    private function calculateTextPosition(string $originalText, string $errorText, int $occurrence = 1): array
    {
        $pos = 0;
        $currentOccurrence = 0;

        while (($pos = mb_strpos($originalText, $errorText, $pos)) !== false) {
            $currentOccurrence++;
            if ($currentOccurrence === $occurrence) {
                return [
                    'start' => $pos,
                    'end' => $pos + mb_strlen($errorText)
                ];
            }
            $pos++;
        }

        // 如果找不到，返回默认位置
        return ['start' => 0, 'end' => 0];
    }

    /**
     * 生成带高亮的文本
     * @param string $text 原始文本
     * @param array $corrections 纠错信息
     * @return string 带高亮的HTML文本
     */
    private function generateHighlightedText(string $text, array $corrections): string
    {
        if (empty($corrections)) {
            return $text;
        }

        // 按位置排序，从后往前处理避免位置偏移
        usort($corrections, function ($a, $b) {
            return ($b['position']['start'] ?? 0) - ($a['position']['start'] ?? 0);
        });

        $highlightedText = $text;

        foreach ($corrections as $correction) {
            if (!isset($correction['position']['start']) || !isset($correction['position']['end'])) {
                continue;
            }

            $start = $correction['position']['start'];
            $end = $correction['position']['end'];
            $correctedPart = $correction['corrected'] ?? $correction['original'];

            // 确保位置有效
            if ($start >= 0 && $end > $start && $end <= mb_strlen($highlightedText)) {
                $before = mb_substr($highlightedText, 0, $start);
                $after = mb_substr($highlightedText, $end);
                $highlighted = "<span style='color:red'>" . htmlspecialchars($correctedPart) . "</span>";

                $highlightedText = $before . $highlighted . $after;
            }
        }

        return $highlightedText;
    }

    /**
     * 获取系统日志
     * @return array
     */
    public function getSystemLogs(): array
    {
        return $this->logger;
    }

    /**
     * 清理系统日志
     */
    public function clearLogs(): void
    {
        $this->initializeLogger();
    }

    /**
     * 获取系统状态
     * @return array
     */
    public function getSystemStatus(): array
    {
        return [
            'status' => 'running',
            'uptime' => round(microtime(true) - $this->logger['start_time'], 2),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'supported_services' => array_keys($this->supportedMcps),
            'total_requests' => count($this->logger['requests']),
            'total_errors' => count($this->logger['errors']),
            'error_rate' => count($this->logger['requests']) > 0 ?
                round(count($this->logger['errors']) / count($this->logger['requests']) * 100, 2) : 0
        ];
    }

    /**
     * 健康检查
     * @return array
     */
    public function healthCheck(): array
    {
        $status = $this->getSystemStatus();
        $isHealthy = $status['error_rate'] < 10; // 错误率小于10%认为健康

        return [
            'healthy' => $isHealthy,
            'status' => $isHealthy ? 'healthy' : 'warning',
            'checks' => [
                'memory_usage' => $status['memory_usage'] < 100 * 1024 * 1024, // 小于100MB
                'error_rate' => $status['error_rate'] < 10,
                'services_available' => !empty($status['supported_services'])
            ],
            'details' => $status
        ];
    }
}
