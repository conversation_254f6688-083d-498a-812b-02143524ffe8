<?php
    require_once("/etc/steelconf/env/www_env.php");
    $env = EDITION;
    require_once ("/etc/steelconf/config/isholiday.php");
    // 正式版
    $keys = array(
        "mj" => WEBHOOK15, // 煤焦
        "mj2" => WEBHOOK15, // 煤焦部
        "hjfl" => WEBHOOK16, //合金辅料部
        "ll" => WEBHOOK17, //炉料部炉料行情
    );
    
    $Date = date("Y-m-d");
    if ( _isholiday($Date) ) { 
        exit;
    }
    $type = $_GET['type'];
    
    $host = $key = "";
    if($env == 'test' || $env == 'dev'){
        $key = WEBHOOK15;
    } else if($env == 'production') {
        $key = $keys[$type];
    }

    $webhook = $key;
    $Date = date("Y-m-d");
    $data=file_get_contents(APP_URL_WWW."/_v2app/marketrecordcheck.php?action=mjhqtx&date=".$Date."&type=".$type);
    
    $data=json_decode($data, true);
    
    if(empty($data)){
        if($type == 'mj2') {
            $cccc = "今日未发布行情：\n无";
            $template = '{"msgtype": "text",
                "text": {
                    "content": "'.$cccc.'"
                }}';
            request_post ( $webhook, $template );
        } else if($type == 'mj') {
            $hm = date("H:i");
            $cccc = "行情提醒：规定（".$hm."）上传行情未发布：\n（无）";
            if($hm == "10:50") {
                $cccc = "行情提醒：规定(9:00、9:30)上传行情已全部发布";
            }
            $template = '{"msgtype": "text",
                "text": {
                    "content": "'.$cccc.'"
                }}';

            if($hm == "09:30" || $hm == "09:00" || $hm == "10:50" || $hm == "11:00" || $hm == "11:30")
            request_post ( $webhook, $template );
        }
        echo "no data 1";
        exit;
    }
    print_r($data);
    $content1 = "行情录入提醒：\n";
    $content2 = "还未录入行情提醒：\n";
    if($type == 'mj') {
        $hm = date("H:i");
        $content2 = "行情提醒：规定（".$hm."）上传行情未发布：\n";
        if($hm == "10:50") {
            $content2 = "行情提醒：规定(9:00、9:30)上传行情未发布：\n";
        }
    }
    $newdata = $data;
    $mentioned_mobile_list = [];
    $flag2 = $flag1 = 0;
    foreach($newdata as $mobile => $val) {
        $str = explode("_",$mobile);
        $mobile = $str[0];
        $mentioned_mobile_list[] = $mobile;
        if($str[1]=='no'){
            foreach($val as $xx) {
                $content2 .= $xx."\n";
                $flag2 = 1;
            }
        } else {
            foreach($val as $xx) {
                $content1 .= $xx."\n";
                $flag1 = 1;
            }
        }
    }
    if($flag1 == 0) $content1 = "";
    if($flag2 == 0) $content2 = "";
    if($content2!="" && $content1!="") {
        $content1 .= "\n";
    }
    $content = $content1.$content2;
    if ( $content ){
        $template = '{
            "msgtype": "text",
            "text": {
                "content": "'.$content.'",
                "mentioned_list":[],
                "mentioned_mobile_list":["'.implode('","',$mentioned_mobile_list).'"]
            }
        }';
        // echo $template;exit;
        $dataRes1 =  request_post ( $webhook, $template );
        $dataRes=json_decode($dataRes1);
        print_r("\n".$content."\n");
    } else {
        echo "no data 2";
    }
    exit;

 
    function request_post($url = '', $param = '')  {
        if (empty($url) || empty($param)){
            return false;
        }
        $curl = curl_init(); // 启动一个CURL会话
        curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
        curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POSTFIELDS, $param); // Post提交的数据包
        curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
        curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
        $data = curl_exec($curl); // 执行操作
        if (curl_errno($curl)) {
            echo 'Errno'.curl_error($curl);//捕抓异常
        }
        curl_close($curl); // 关闭CURL会话
        return $data; // 返回数据，json格式
    }
