<?php
//ini_set( "display_errors", true);
//error_reporting( E_ALL );
// ini_set("display_errors",True);
// error_reporting(0);
//error_reporting(E_ALL);
//ini_set('display_errors', '1');
// require("./include/adodb/adodb.inc.php");
require '/usr/local/www/libs/vendor/autoload.php';
// 上传到外网使用这个路径
require_once('/usr/local/www/libs/jpgraph/src/jpgraph.php');
require_once('/usr/local/www/libs/jpgraph/src/jpgraph_line.php');
require_once('/usr/local/www/libs/jpgraph/src/jpgraph_scatter.php');
include('/etc/steelconf/config/config.php');
require_once("/etc/steelconf/env/www_env.php");
$hostname_steelhomeen = $hostname_steelhome;
$database_steelhomeen = $database_steelhome;
$username_steelhomeen = $username_steelhome;
$password_steelhomeen = $password_steelhome;
$conn = ADONewConnection('mysqli');  # create a connection
$conn->PConnect("$hostname_steelhomeen", "$username_steelhomeen", "$password_steelhomeen", "$database_steelhomeen");

$conn2 = ADONewConnection('mysqli');  # create a connection
$conn2->PConnect(HOST_NAME_T1R . ":" . HOST_PORT_T1R, USER_NAME_T1R, USER_PASSWORD_T1R, DATABASE_NAME_T1R);

$conn_src = ADONewConnection('mysqli');  # create a connection
//$conn_src->PConnect("*************:3306","dbread","sth@50581010","steelhome_drc");
$conn_src->PConnect(HOST_STDRC_SERVER . ":" . HOST_STDRC_PORT, HOST_STDRC_USERNAME, HOST_STDRC_PASSWORD, DBSE_STDRC_NAME);

//added by hezpeng started 2021/12/02
function checkpower()
{
    /*sthomecs = *"ca:".$_SESSION['gcpower']."|ctg:".$_SESSION['tgpower']."|cbx:".$_SESSION['bxgpower']."|cthj:".$_SESSION['thjpower']."|cb:".$_SESSION['llpower']."|cc:".$_SESSION['yspower']."|chg:".$_SESSION['hgpower']."|cmhg:".$_SESSION['mhgpower']."|cd:".$_SESSION['isadmin']."|ce:".$_SESSION['memberid']."|cf:".$_SESSION['id']."|ch:".$_SESSION['maxnum']."|csn:".$_SESSION['snpower']."|csj:".$_SESSION['sjpower']."|cpsshpi:".$_SESSION['psshpipower'];
    */
    $sthomecs = $_COOKIE['sthomecs'];
    $sthomecs = explode("|", $sthomecs);

    $pindao_power = array();
    foreach ($sthomecs as &$tmp) {
        $tmp = explode(":", $tmp);
        $pindao_power[$tmp[0]] = $tmp[1];
    }
    $pw["gcpower"] = $pindao_power['ca'];
    $pw["tgpower"] = $pindao_power['ctg'];
    $pw["llpower"] = $pindao_power['cb'];
    $pw["thjpower"] = $pindao_power['cthj'];
    $pw["bxgpower"] = $pindao_power['cbx'];
    $pw["yspower"] = $pindao_power['cc'];
    $pw["mhgpower"] = $pindao_power['cmhg'];
    $pw["hgpower"] = $pindao_power['chg'];
    $pw["snpower"] = $pindao_power['csn'];

    $_SESSION['maxpower'] = max($pw['gcpower'], $pw['tgpower'], $pw['llpower'], $pw['thjpower'], $pw['bxgpower'], $pw['yspower'], $pw['mhgpower'], $pw['hgpower'], $pw['snpower']);
}

if (!isset($_SESSION['maxpower']) && $_COOKIE["ce"] != "") {
    checkpower();
}
//added by hezpeng ended 2021/12/02

$power = max($_SESSION['tgpower'], $_SESSION["gcpower"], $_SESSION["llpower"], $_SESSION["hgpower"], $_SESSION["yspower"], $_SESSION["mhgpower"], $_SESSION["thjpower"], $_SESSION["bxgpower"]);
if ($power < 4 && $_REQUEST['no_power'] != 1) {
    if (strtotime($_REQUEST['STime']) > strtotime($_REQUEST['ETime'])) {
        $t = $_REQUEST['STime'];
        $_REQUEST['STime'] = $_REQUEST['ETime'];
        $_REQUEST['ETime'] = $t;
    }
    $ag_time = strtotime('-3 month', strtotime($_REQUEST['ETime']));
    if (strtotime($_REQUEST['STime']) < $ag_time) {
        // echo("<SCRIPT>window.location.href='MemberLogin.php?urlstr=".urlencode($urlstr)."';</SCRIPT>");exit;
        $_REQUEST['STime'] = date("Y-m-d", $ag_time);
    }
}

// $conn_src->PConnect("***********:3306","root2","123456","steelhome_drc");
// $conn->PConnect("**********:4307","dbread","sth@50581010","steelhome");

/**
 * @param $priceArray
 * @return array
 * user:shizg
 * time:2022/3/23 11:59
 * TODO  处理补缺失数据的走势图数据
 */
function handleMissingData($priceArray)
{
    $returnArray = array();
    if ($priceArray) {
        foreach ($priceArray as $key => $priceDetail) {
            ksort($priceDetail);
            foreach ($priceDetail as $dkey => $pricevalue) {
                if (empty($pricevalue)) {
                    $dkeylast = date("Y-m-d", strtotime($dkey . "-1 day"));
                    $priceDetail[$dkey] = $priceDetail[$dkeylast];
                }
            }
            $returnArray[$key] = $priceDetail;
        }
    }

    return $returnArray;
}

//add by zfy started 2017/6/27
$avg_5 = $_REQUEST['avg_5'];
$avg_10 = $_REQUEST['avg_10'];
$avg_20 = $_REQUEST['avg_20'];
$avg_40 = $_REQUEST['avg_40'];
$avg_60 = $_REQUEST['avg_60'];
$avg_200 = $_REQUEST['avg_200'];
$count_priceid = $_REQUEST['count_var'];
//xiangbin add 20190202 start
$AppMode = ($_REQUEST ['AppMode'] == '') ? '1' : $_REQUEST ['AppMode'];
//xiangbin add 201980202 end
//add by zfy ended 2017/6/27
//add changhong 2018-06-12
$is_dot_img = $_REQUEST['is_dot_img'] == "" ? 0 : $_REQUEST['is_dot_img'];
if ($avg_5 != "" || $avg_10 != "" || $avg_20 != "" || $avg_40 != "" || $avg_60 != "" || $avg_200 != "") {
    $is_jj = 1;//判断当前是不是均线
}
$dot_size = 1;
$dot_type = MARK_SQUARE;
if ($_REQUEST['priceid'] != "" && $_REQUEST['jc_type'] != "") {
    require_once('./showgraph_new_jc.php');
    exit;
}
if ($_REQUEST['shpi_priceid'] != "" && $_REQUEST['shpi_type'] != "") {
    require_once('./accbarex_shpi.php');
    exit;
}
//end update changhong 2018-06-12
if ($_REQUEST['gsqd'] == 1) {
    //$STime = $_REQUEST['STime'];
    //$ETime = $_REQUEST['ETime'];
    $STime = $_REQUEST['STime']=="" ? date("Y-m-d",strtotime("-1 year")) : $_REQUEST['STime'];
    $ETime = $_REQUEST['ETime']=="" ? date("Y-m-d") : $_REQUEST['ETime'];
    $sql = "select * from shpi_gsqd where datetime>='" . $STime . "' and datetime<='" . $ETime . "' order by datetime asc";
    $query = $conn->getArray($sql);
    // echo "<pre>";print_r($query);
    $zhishu = $datetime = [];
    $lastdate = '';
    foreach ($query as $key => $value) {
        $zhishu[$key] = round($value['value']);
        $datetime[$key] = $value['datetime'];
        $lastdate = $value['datetime'];
    }
    if (empty($zhishu)) {
        $zhishu[0] = 0;
        $datetime[0] = $ETime;
    }
    $maxdate = $datetime;
    $price['1'] = $zhishu;
    $max = max($zhishu);
    $min = min($zhishu);
    $mid_max = abs($max - 50);
    $mid_min = abs($min - 50);
    if ($mid_max > $mid_min) {
        $middle = $mid_max;
    } elseif ($mid_max < $mid_min) {
        $middle = $mid_min;
    } else {
        $middle = $mid_max;
    }
    if ($middle % 2 == 0) {
        $middle = $middle + 2;
    } else {
        $middle = $middle + 1;
    }
    $max_new = 50 + $middle;
    $min_new = 50 - $middle;
    $middle = $middle / 10;
    // echo $middle;
    if ($middle >= 1) {
        $middle1 = 1;
        $middle2 = $middle * 2;
    } elseif ($middle < 1) {
        $middle2 = $middle;
        $middle1 = $middle * 2;
    }
    //zk add 20210702 start
    $fenmu = count($datetime);
    if (count($datetime) >= 5)
        $fenmu = 5;
    if ($fenmu == 0 || $fenmu == 1)
        $maxdate_5 = 0;
    else
        $maxdate_5 = (int)((count($datetime)+$fenmu-1) / $fenmu);
    //$maxdate_5 = (int)count($datetime)/5;
    //zk add 20210702 end
    $num_topic = 1;

    $bt_title = "钢市强度指数走势图(" . $STime . "至" . $ETime . ")";

    $jj = round(array_sum($price['1']) / count($price['1']), 2);
    if (isset($_SESSION['mid'])) {
        $mid = $_SESSION['mid'];
    } else {
        $mid = '';
    }
    // power由7改成6，甲级会员可以显示均价
    if ($mid == 1 || $_SESSION['maxpower'] >= 6) {
        $avgflag = "(平均：" . $jj . ")";
    } else {
        $avgflag = '';
    }

    $lend_title[1] = "钢市强度指数" . $avgflag;

    $nodataimg = APP_URL_WWW . "/images/gglg01.gif";
    if ($_REQUEST['wubj'] == '1') {
        $nodataimg = APP_URL_WWW . "/images/gglg01_nolog.gif";
    }
    if ($maxdate_5 == 0) {
        $image = imagecreatefromgif($nodataimg);
        imagepng($image);
        exit;
    }
    switch ($num_topic) {
        case 1:
            $graph = new Graph(600, 310);
            $graph->img->SetMargin(50, 30, 50, 60);
            break;
        case 2:
            $graph = new Graph(600, 330);
            $graph->img->SetMargin(50, 30, 50, 80);
            break;
        case 3:
            $graph = new Graph(600, 350);
            $graph->img->SetMargin(50, 30, 50, 100);
            break;
        case 4:
            $graph = new Graph(600, 370);
            $graph->img->SetMargin(50, 30, 50, 120);
            break;
        case 5:
            $graph = new Graph(600, 390);
            $graph->img->SetMargin(50, 30, 50, 140);
            break;
    }
    $graph->SetScale("textlin", $min_new, $max_new);
    $graph->yaxis->scale->SetGrace(20, 10);
    $graph->title->Set($bt_title);
    $graph->title->SetMargin(10);
    $graph->title->SetFont(FF_SIMSUN, FS_NORMAL, 11);
    $graph->title->SetColor('black');
    $graph->yscale->ticks->Set($middle1, $middle2);
    $graph->yaxis->HideZeroLabel();
    $graph->yaxis->HideLine(false);
    $graph->yaxis->HideTicks(true, false);

    $graph->ygrid->Show();

    function year_callback($lastdate)
    {

        return $lastdate;
    }

    $graph->xaxis->SetLabelFormatCallback('year_callback');
    $graph->xgrid->SetLineStyle("solid");
    $graph->xgrid->SetColor('#E3E3E3');
    $graph->xaxis->SetTickLabels($maxdate);
    $graph->xaxis->SetTextTickInterval($maxdate_5, 0);
    $graph->xaxis->SetPosAbsDelta(5);
    $arr_color = array("#fb0404", "#8a04fc", "#0309fe", "#03fbf8", "#f9f003");
    for ($i = 1; $i <= $num_topic; $i++) {
        if ($i == 1) {
            //update changhong 2018-06-12
            if ($is_dot_img == 1) {
                $splot = new ScatterPlot($price[$i]);
                $splot->mark->SetType($dot_type);
                $splot->mark->SetWidth($dot_size);
                $splot->mark->SetFillColor($arr_color[$i - 1]);
                $splot->mark->SetColor($arr_color[$i - 1]);
                $p[$i] = $splot;
            } else {
                $p[$i] = new LinePlot($price[$i]);//$_SESSION['datay_1'] Y轴数据
            }
            //end update changhong 2018-06-12
        } else {
            if ($is_jj == 1 || $is_dot_img == 0) {
                $p[$i] = new LinePlot($price[$i]);
            } else {
                $splot = new ScatterPlot($price[$i]);
                $splot->mark->SetType($dot_type);
                $splot->mark->SetWidth($dot_size);
                $splot->mark->SetFillColor($arr_color[$i - 1]);
                $splot->mark->SetColor($arr_color[$i - 1]);
                $p[$i] = $splot;
            }
        }
        $graph->Add($p[$i]);
        $p[$i]->SetColor($arr_color[$i - 1]);
        $p[$i]->SetLegend($lend_title[$i]);
        $p[$i]->SetWeight(2);
    }
    if ($_REQUEST['wubj'] != '1') {
        if ($_REQUEST['tu'] != '1' && $_REQUEST['tu'] != '2') {
            $graph->SetBackgroundImage("./images/gglg.png", BGIMG_COPY); //设置背景
        } elseif ($_REQUEST['tu'] == '2') {
            $graph->SetBackgroundImage("./images/gglg_x.png", BGIMG_COPY); //设置背景
        }
    } else {
        $graph->SetBackgroundImage("./images/gglg_wbj.png", BGIMG_COPY); //设置背景
    }
    $graph->SetBackgroundImageMix(15);
    $graph->SetMarginColor("white");
    $graph->SetFrame(false);
    $graph->legend->SetFillColor('#ffffff');
    $graph->legend->SetFrameWeight(0);
    $graph->legend->SetShadow(false);
    $graph->legend->SetFont(FF_SIMSUN, FS_NORMAL);
    $graph->legend->Pos(0.5, 0.97, "center", "bottom");
    $graph->Stroke();

} else {
    if (isset($_REQUEST['variety'])) {
        $topic = explode("||", $_REQUEST['variety']);
    } elseif (isset($_REQUEST['topicture'])) {
        $topic = explode("||", $_REQUEST['topicture']);
    }
    if (isset($_REQUEST['STime']) && isset($_REQUEST['ETime'])) {
        $STime = date("Y-m-d", strtotime($_REQUEST['STime']));
        $ETime = date("Y-m-d", strtotime($_REQUEST['ETime']));
    } else {
        $STime = date("Y-m-d", strtotime($_REQUEST['Stime']));
        $ETime = date("Y-m-d", strtotime($_REQUEST['ETime']));
    }

    $avgflag = 0;
    if (isset($_SESSION['mid'])) {
        $mid = $_SESSION['mid'];
    } else {
        if ($_COOKIE["ce"] != "") {
            $mid = $_COOKIE["ce"];
        } else {
            $mid = '';
        }
    }

    // power由7改成6, 使甲级会员能显示均价
    if ($mid == 1 || $_SESSION['maxpower'] >= 6) {
        $avgflag = 1;
    }
    $tsxx = array();
    $_articlename = array();
    $_articlename[0] = "";
    // dd($topic);
    foreach ($topic as $news => $names) {
        $tsxx1 = array();
        //added by hezp started 2017/06/14====================
        if ($names == 'lw_qh_spj' || $names == 'rz_qh_spj' || $names == 'tks_qh_spj' || $names == 'jm_qh_spj' || $names == 'jt_qh_spj' || $names == 'dlm_qh_spj' || $names == 'gt_qh_spj' || $names == 'gm_qh_spj' || $names == 'lw_qh_jsj' || $names == 'rz_qh_jsj' || $names == 'tks_qh_jsj' || $names == 'jm_qh_jsj' || $names == 'jt_qh_jsj' || $names == 'dlm_qh_jsj' || $names == 'gt_qh_jsj' || $names == 'gm_qh_jsj') {
            if ($names == 'lw_qh_spj' || $names == 'rz_qh_spj' || $names == 'tks_qh_spj' || $names == 'jm_qh_spj' || $names == 'jt_qh_spj' || $names == 'dlm_qh_spj' || $names == 'gt_qh_spj' || $names == 'gm_qh_spj') {
                $qh_type = "1";
            } else if ($names == 'lw_qh_jsj' || $names == 'rz_qh_jsj' || $names == 'tks_qh_jsj' || $names == 'jm_qh_jsj' || $names == 'jt_qh_jsj' || $names == 'dlm_qh_jsj' || $names == 'gt_qh_jsj' || $names == 'gm_qh_jsj') {
                $qh_type = "2";
            }
        } else {
            $qh_type = "0";
        }
        //added by hezp ended 2017/06/14====================

        if ($news != '0' && $names != 'lw_qh' && $names != 'rz_qh' && $names != 'tjf_qh' && $names != 'jt_qh' && $names != 'jm_qh' && $names != 'dlm_qh' && $names != 'gt_qh' && $names != 'gm_qh' && $names != 'lw_qh_jc' && $names != 'rz_qh_jc' && $names != 'tjf_qh_jc' && $names != 'jt_qh_jc' && $names != 'jm_qh_jc' && $names != 'dlm_qh_jc' && $names != 'gt_qh_jc' && $names != 'gm_qh_jc' && $names != 'tks_62' && $names != 'tks_58' && $names != 'tks_61' && $names != 'tks_63' && $names != 'tks_65' && $qh_type == '0') {
            if(mb_strlen($names) == 20) {
                $sql = "select m.topicture,m.mastertopid,m.id,mpc.price_code from marketconditions_price_code mpc left join marketconditions m on mpc.marketconditions_id=m.id where mpc.price_code='{$names}' and mpc.mconmanagedate between '{$_REQUEST['STime']} 00:00:00' and '{$_REQUEST['ETime']} 23:59:59'";
			    $temparr1 = $conn->getArray($sql);
                $newids = [];
                $marketconditions_ids = [];
                $prids = [];
                // dd($temparr1);
                foreach($temparr1 as $val) {
                    $newids[] = $val['price_code'];
                    $marketconditions_ids[] = $val['id'];
                    if($val['mastertopid']!="") {
                        $prids[] = $val['mastertopid'];
                    } else if($val['topicture']!="") {
                        $prids[] = $val['topicture'];
                    }
                }
                if(count($prids)>0) {
                    $names = array_unique($prids)[0];
                }
            }

            if (strlen($names) == 6) {
                //update by shizg for 普氏指数 started 2017/06/29
                //$sql_psshpi = "select marketrecordid from marketconditions where topicture ='".$names."'";
                //$psshpi_marid=$conn->getOne($sql_psshpi);
                //if($psshpi_marid!='-1'){
                //update by zfy started 2017/6/27
                $sql = "select topicture, `oldprice` , `price` , articlename, specification, material, marketrecord.id,managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
				  topicture='" . $names . "' and  mconmanagedate > '" . $STime . " 00:00'
					and mconmanagedate < '" . $ETime . " 23:59' order by mconmanagedate";
                // echo $sql."<br>";

                //}else{
                //$sql="select topicture, `oldprice` , `price` , articlename, specification, material,id,mconmanagedate as managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60 from marketconditions where topicture='".$names."' and  mconmanagedate > '".$STime." 00:00' and mconmanagedate < '".$ETime." 23:59' order by mconmanagedate";
                //}
                $sql2 = "select id ,topicture ,mconmanagedate ,price,average_price_5 from marketconditions where topicture ='" . $names . "'";
                $_sql = "select articlename from marketconditions where topicture ='" . $names . "' order by id DESC limit 1";
                //update by zfy started 2017/6/27
                //update by shizg for 普氏指数 ended 2017/06/29
            }
            if (strlen($names) == 7) {
                //update by shizg for 普氏指数 started 2017/06/29
                //$sql_psshpi = "select marketrecordid from marketconditions where mastertopid ='".$names."'";
                //$psshpi_marid=$conn->getOne($sql_psshpi);
                //if($psshpi_marid!='-1'){
                //update by zfy started 2017/6/27
                $sql = "select average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 ,mastertopid as  topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
				  mastertopid='" . $names . "' and  mconmanagedate > '" . $STime . " 00:00'
					and mconmanagedate < '" . $ETime . " 23:59' order by mconmanagedate";

                //}else{
                //$sql="select mastertopid as topicture, `oldprice` , `price` , articlename, specification, material,factoryarea,id,mconmanagedate as managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60 from marketconditions where mastertopid='".$names."' and  mconmanagedate > '".$STime." 00:00' and mconmanagedate < '".$ETime." 23:59' order by mconmanagedate";
                //}
                $sql2 = "select id ,mastertopid ,mconmanagedate ,price,average_price_5 from marketconditions where mastertopid ='" . $names . "'";
                $_sql = "select articlename from marketconditions where mastertopid ='" . $names . "' order by id DESC limit 1";
                //update by zfy started 2017/6/27
                //update by shizg for 普氏指数 ended 2017/06/29
            }
            if (strlen($names) == 20) {
                $ids = implode("','", $marketconditions_ids);
                $sql = "select average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 ,mastertopid as  topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
				  marketconditions.id in ('" . $ids . "') and  mconmanagedate > '" . $STime . " 00:00'
					and mconmanagedate < '" . $ETime . " 23:59' order by mconmanagedate";
                $sql2 = "select id ,mastertopid ,mconmanagedate ,price,average_price_5 from marketconditions where marketconditions.id in ('" . $ids . "')";
                $_sql = "select articlename from marketconditions where marketconditions.id in ('" . $ids . "') order by id DESC limit 1";
            }
            $arrays = $conn->getArray($sql);
            //add by std 2020/04/03   走势图拼接
            if (!empty($arrays)) {
                $temp = array();
                foreach ($arrays as $key => $val) {
                    $temp[] = $val['managedate']; // 用一个空数组来承接字段
                }
                $time = min($temp);
            } else {
                $time = $ETime;
            }

            $_articlename_arr = $conn->getArray($_sql);
            $_articlename[] = $_articlename_arr[0]['articlename'];

            if ($_GET['debug'] == 1 || $_REQUEST['debug'] == 1) {
                echo "111";
                print_r($_articlename_arr);
                print_r($_articlename);

            }


            $starttime = $STime;
            $starttime1 = date('Y-m-d', strtotime($time));
            //echo $starttime;
            //echo $starttime1;
            if ($starttime != $starttime1) {

                if (strlen($names) == 6) {
                    $topictures = substr($names, 2, 4);
                    $sql = "SELECT topicture1 FROM  dc_code_topicture2  WHERE topicture2='$topictures' AND  type=0";
                    $names1 = $conn2->getOne($sql);

                    if ($names1 != '') {
                        $head = substr($names, 0, 2);
                        $topictures1 = $head . $names1;

                        $sql = "select topicture, `oldprice` , `price` , articlename, specification, material, marketrecord.id,managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures1 . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate < '" . $starttime1 . " 00:00' order by mconmanagedate";
                        $arrays1 = $conn->getArray($sql);

                        if (!empty($arrays1)) {
                            $temp1 = array();
                            foreach ($arrays1 as $key => $val) {
                                $temp1[] = $val['managedate']; // 用一个空数组来承接字段
                            }
                            $time1 = min($temp1);
                            $startdates1 = date('Y-m-d', strtotime($time1));

                            $sql1 = "select topicture,specification, material from marketconditions,marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures1 . "' order by mconmanagedate desc limit 1";
                            $list = $conn->getArray($sql1);
                            $tsxx1['materials'] = $list[0]['material'];
                            $specification = preg_replace('/^( |\s)*|( |\s)*$/', '', $list[0]['specification']);
                            $tsxx1['specification'] =  $specification;
                            $tsxx1['date2'] = $starttime1;

                            if (!empty($arrays)) {
                                $arrays = array_merge($arrays1, $arrays);
                            } else {
                                $arrays = $arrays1;
                            }

                        }
                    } else {
                        $startdates1 = $starttime1;
                    }

                    if ($starttime != $startdates1) {
                        $sql = "SELECT topicture1 FROM  dc_code_topicture2  WHERE topicture2='$names' AND  type=1";
                        $topictures2 = $conn2->getOne($sql);
                        if ($topictures2 != '') {
                            $sql = "select marketrecord.cityid,topicture, `oldprice` , `price` , articlename, specification, material, marketrecord.id,managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures2 . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate < '" . $startdates1 . " 00:00' order by mconmanagedate";
                            $arrays2 = $conn->getArray($sql);
                            if (!empty($arrays2)) {
                                $markets = $arrays2[0]['cityid'];
                                $markets = explode(',', $markets);
                                $market = $conn->getOne("SELECT cityname FROM city WHERE cityid='$markets[1]' ");
                                $tsxx1['market'] =  $market;
                                $tsxx1['date3'] = $startdates1;

                                if (!empty($arrays)) {
                                    $arrays = array_merge($arrays2, $arrays);
                                } else {
                                    $arrays = $arrays2;
                                }
                            }
                        }
                    }
                }

                if (strlen($names) == 7) {
                    $topictures = substr($names, 0, 6);

                    $sql = "select topicture, `oldprice` , `price` , articlename, specification, material, marketrecord.id,managedate,factoryarea,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate < '" . $starttime1 . " 00:00' order by mconmanagedate";
                    $arrays1 = $conn->getArray($sql);
                    if (!empty($arrays1)) {
                        $temp1 = array();
                        foreach ($arrays1 as $key => $val) {
                            $temp1[] = $val['managedate']; // 用一个空数组来承接字段
                        }
                        $time1 = min($temp1);
                        $startdate1 = date('Y-m-d', strtotime($time1));
                        $tsxx1['date1'] = $starttime1;

                        $tsxx1['gcarea'] =  $arrays1[0]['factoryarea'];
                        if (!empty($arrays)) {
                            $arrays = array_merge($arrays1, $arrays);
                        } else {
                            $arrays = $arrays1;
                        }
                    } else {
                        $startdate1 = $starttime1;
                    }


                    if ($starttime != $startdate1) {
                        $topicturess = substr($topictures, 2, 4);
                        $sql = "SELECT topicture1 FROM  dc_code_topicture2  WHERE topicture2='$topicturess' AND  type=0";
                        $names1 = $conn2->getOne($sql);

                        if ($names1 != '') {
                            $head = substr($names, 0, 2);
                            $topictures1 = $head . $names1;
                            $sql = "select topicture, `oldprice` , `price` , articlename, specification, material, marketrecord.id,managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures1 . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate < '" . $startdate1 . " 00:00' order by mconmanagedate";
                            $arrays2 = $conn->getArray($sql);
                            if (!empty($arrays2)) {
                                $temp2 = array();
                                foreach ($arrays2 as $key => $val) {
                                    $temp2[] = $val['managedate']; // 用一个空数组来承接字段
                                }
                                $time2 = min($temp2);
                                $startdates1 = date('Y-m-d', strtotime($time2));
                                $tsxx1['date2'] = $startdate1;

                                $sql1 = "select topicture,specification, material from marketconditions,marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures1 . "' order by mconmanagedate desc limit 1";
                                $list = $conn->getArray($sql1);
                                $tsxx1['materials'] = $list[0]['material'];
                                $specification = preg_replace('/^( |\s)*|( |\s)*$/', '', $list[0]['specification']);
                                $tsxx1['specification'] =  $specification;
                                if (!empty($arrays)) {
                                    $arrays = array_merge($arrays2, $arrays);
                                } else {
                                    $arrays = $arrays2;
                                }
                            }
                        } else {
                            $startdates1 = $startdate1;
                        }

                        if ($starttime != $startdates1) {
                            $sql = "SELECT topicture1 FROM  dc_code_topicture2  WHERE topicture2='$topictures' AND  type=1";
                            $topictures2 = $conn2->getOne($sql);
                            if ($topictures2 != '') {
                                $sql = "select  marketrecord.cityid,topicture, `oldprice` , `price` , articlename, specification, material, marketrecord.id,managedate,average_price_5,average_price_10,average_price_20,average_price_40,average_price_60,average_price_200 from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $topictures2 . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate < '" . $startdates1 . " 00:00' order by mconmanagedate";
                                $arrays3 = $conn->getArray($sql);
                                if (!empty($arrays3)) {
                                    $markets = $arrays3[0]['cityid'];
                                    $markets = explode(',', $markets);
                                    $market = $conn->getOne("SELECT cityname FROM city WHERE cityid='$markets[1]' ");
                                    $tsxx1['market'] =  $market;
                                    $tsxx1['date3'] = $startdates1;

                                    if (!empty($arrays)) {
                                        $arrays = array_merge($arrays3, $arrays);
                                    } else {
                                        $arrays = $arrays3;
                                    }
                                }
                            }
                        }
                    }
                }

            }

            $sqlrs[$news] = $arrays;
            $tsxx[] = $tsxx1;

            //end by std 2020/04/08
            $varid = substr($names, 2, 2);
            if ($names != 'L1821a' && $varid == "82") { //废钢
                if (strlen($names) == 7) {
                    $city_name[$news] = $conn->getOne("SELECT factoryarea FROM marketconditions WHERE mastertopid='$names' order by id desc");
                } else {
                    $city_name[$news] = $conn->getOne("SELECT factoryarea FROM marketconditions WHERE topicture='$names' order by id desc");
                }
            } else { // 非废钢
                //$cityid ='00'.substr($names,0,2);
                //$city_name[$news] = $conn->getOne( "SELECT cityname FROM city WHERE cityid='$cityid' " );
                if (strlen($names) == 7) {
                    $type = $conn->getOne("SELECT type FROM market_jiageid_map WHERE mastertopid='$names' order by id desc ");
                } else if (strlen($names) == 6) {
                    $type = $conn->getOne("SELECT type FROM market_jiageid_map WHERE topicture='$names' order by id desc ");
                } else {
                    $type=-1;
                }
                // dd($type);

                if ($type == '0' || $type == '') {
                    $cityid = '00' . substr($names, 0, 2);
                    $city_name1 = $conn->getOne("SELECT cityname FROM city WHERE cityid='$cityid' ");
                } else if ($type == '1') {
                    if (strlen($names) == 7) {
                        $sql = "select  marketrecord.cityid from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and mastertopid='" . $names . "'  order by managedate desc limit 1";
                    } else if (strlen($names) == 6) {
                        $sql = "select  marketrecord.cityid from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='" . $names . "'  order by managedate desc limit 1";
                    }
                    $info = $conn->getArray($sql);
                    $info = explode(",", $info[0]['cityid']);
                    $cityid = $info[1];
                    $city_name1 = $conn->getOne("SELECT cityname FROM city WHERE cityid='$cityid' ");
                } else if ($type == '2') {
                    if (strlen($names) == 7) {
                        $city_name1 = $conn->getOne("SELECT factoryarea FROM marketconditions WHERE mastertopid='$names' order by id desc");
                    } else {
                        $city_name1 = $conn->getOne("SELECT factoryarea FROM marketconditions WHERE topicture='$names' order by id desc");
                    }
                } else if ($type == '3') {
                    if (strlen($names) == 7) {
                        $city_name1 = $conn->getOne("SELECT cityname FROM market_jiageid_map WHERE mastertopid='$names' order by id desc");
                    } else {
                        $city_name1 = $conn->getOne("SELECT cityname FROM market_jiageid_map WHERE topicture='$names' order by id desc");
                    }
                } else if ($type == -1) { 
                    $cityid = substr($names, 1, 4);
                    $city_name1 = $conn->getOne("SELECT name_short FROM `sth_city` WHERE `code` = '$cityid' ");
                }

                $city_name[$news] = html_entity_decode($city_name1);
                //end by std 2020/4/13   市场
            }
            //}
            //update by shizg for 普氏指数 ended 2017/06/29
            //print_r($city_name);
            //echo "888";
            //update by zfy started 2017/6/27
            if ($count_priceid == 1) {
                $sql_price_arr = $conn->getArray($sql2);
                //print_r($sql_price_arr);
                $count_sql_price_arr = count($sql_price_arr);
                $avg_now_5 = average_price($sql_price_arr, $count_sql_price_arr - 1, "5");
                $avg_now_10 = average_price($sql_price_arr, $count_sql_price_arr - 1, "10");
                $avg_now_20 = average_price($sql_price_arr, $count_sql_price_arr - 1, "20");
                $avg_now_40 = average_price($sql_price_arr, $count_sql_price_arr - 1, "40");
                $avg_now_60 = average_price($sql_price_arr, $count_sql_price_arr - 1, "60");
                $avg_now_200 = average_price($sql_price_arr, $count_sql_price_arr - 1, "200");
            }
            //echo "avg_now_5=$avg_now_5,avg_now_10=$avg_now_5,avg_now_20=$avg_now_20,avg_now_40=$avg_now_40,avg_now_60=$avg_now_60";
            //update by zfy ended 2017/6/27
        } else {
            if ($names == 'lw_qh' || $names == 'rz_qh' || $names == 'tjf_qh' || $names == 'jt_qh' || $names == 'jm_qh' || $names == 'dlm_qh' || $names == 'gt_qh' || $names == 'gm_qh' || $qh_type > '0') {
                if ($names == 'lw_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_4' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                } elseif ($names == 'rz_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_99' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                } elseif ($names == 'tjf_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_20' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                } elseif ($names == 'jt_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_9' and dta_6>0 and (dta_1!='小计' and dta_1!='焦炭小计' and dta_1!='总计') GROUP BY `dta_ym` order by `dta_ym` asc");
                } elseif ($names == 'jm_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  CAST( dta_11 AS SIGNED INTEGER )   AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_19' and dta_6>0 and `dta_maxValStatus`=1  order by `dta_ym` asc");
                } elseif ($names == 'dlm_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_21' and dta_6>0 and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                } elseif ($names == 'gt_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_22' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                } elseif ($names == 'gm_qh') {
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and `dta_type`='SHQHDAY_23' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                    //added by hezp started 2017/06/14====================
                } elseif ($names == 'lw_qh_spj' || $names == 'lw_qh_jsj') { //螺纹钢
                    $qh_arr = $conn_src->getArray("SELECT   `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_4' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'rz_qh_spj' || $names == 'rz_qh_jsj') { //热卷
                    $qh_arr = $conn_src->getArray("SELECT    `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_99' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'tks_qh_spj' || $names == 'tks_qh_jsj') { //铁矿石
                    $qh_arr = $conn_src->getArray("SELECT    `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_20' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'jm_qh_spj' || $names == 'jm_qh_jsj') { //焦煤
                    $qh_arr = $conn_src->getArray("SELECT    `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_19' and dta_1!='小计' and dta_1!='焦煤小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'jt_qh_spj' || $names == 'jt_qh_jsj') { //焦炭
                    $qh_arr = $conn_src->getArray("SELECT    `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_9' and dta_1!='焦炭小计' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'dlm_qh_spj' || $names == 'dlm_qh_jsj') { //动力煤
                    $qh_arr = $conn_src->getArray("SELECT   `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_21' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'gt_qh_spj' || $names == 'gt_qh_jsj') { //硅铁
                    $qh_arr = $conn_src->getArray("SELECT    `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_22' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");

                } elseif ($names == 'gm_qh_spj' || $names == 'gm_qh_jsj') { //锰硅
                    $qh_arr = $conn_src->getArray("SELECT    `dta_6` ,`dta_7` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <='" . $ETime . "' and dta_maxValStatus=1 and `dta_type`='SHQHDAY_23' and dta_1!='小计' and dta_1!='总计' GROUP BY `dta_ym` order by `dta_ym` asc");
                    //added by hezp ended 2017/06/14====================
                }
                // echo "<pre>";print_r("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='".$STime."' and `dta_ym` <='".$ETime."' and `dta_type`='SHQHDAY_4' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");exit;
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    if ($names == 'lw_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_4' and dta_ym='$dta_ym' and `dta_11`='$dta_11'";
                    } elseif ($names == 'rz_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_99' and dta_ym='$dta_ym' and `dta_11`='$dta_11' limit 1";
                    } elseif ($names == 'tjf_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_20' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    } elseif ($names == 'jt_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_9' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    } elseif ($names == 'jm_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_19' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    } elseif ($names == 'dlm_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_21' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    } elseif ($names == 'gt_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_22' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    } elseif ($names == 'gm_qh') {
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_23' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";

                        //added by hezp started 2017/06/14====================
                    } elseif ($names == 'lw_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'rz_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'tks_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'jm_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'jt_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'dlm_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'gt_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];
                    } elseif ($names == 'gm_qh_spj') {
                        $arr = $qh_arr[$i]["dta_6"];

                    } elseif ($names == 'lw_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'rz_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'tks_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'jm_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'jt_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'dlm_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'gt_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                    } elseif ($names == 'gm_qh_jsj') {
                        $arr = $qh_arr[$i]["dta_7"];
                        //added by hezp ended 2017/06/14====================

                    }
                    //	 echo $sql."<br />";
                    if ($qh_type == "0") {
                        $arr = $conn_src->getArray($sql);

                        $arr_temp[$i] = $arr;
                        if (empty($arr) || $arr['0']['0'] == '') {
                            $arr_temp[$i] = $arr_temp[$i - 1];
                        }
                        $sqlrs['1'][$i]['price'] = $arr_temp[$i]['0']['0'];
                    } else {
                        $arr_temp[$i] = $arr;
                        if (!$arr || $arr == '') {
                            $arr_temp[$i] = $arr_temp[$i - 1];
                        }
                        $sqlrs['1'][$i]['price'] = $arr_temp[$i];
                    }
                    if ($names == 'lw_qh') {
                        $sqlrs['1'][$i]['articlename'] = "螺纹钢";
                    } elseif ($names == 'rz_qh') {
                        $sqlrs['1'][$i]['articlename'] = "热轧板卷";
                    } elseif ($names == 'tjf_qh') {
                        $sqlrs['1'][$i]['articlename'] = "铁精粉";
                    } elseif ($names == 'jt_qh') {
                        $sqlrs['1'][$i]['articlename'] = "一级冶金焦";
                    } elseif ($names == 'jm_qh') {
                        $sqlrs['1'][$i]['articlename'] = "焦煤";
                    } elseif ($names == 'dlm_qh') {
                        $sqlrs['1'][$i]['articlename'] = "动力煤";
                    } elseif ($names == 'gt_qh') {
                        $sqlrs['1'][$i]['articlename'] = "硅铁";
                    } elseif ($names == 'gm_qh') {
                        $sqlrs['1'][$i]['articlename'] = "硅锰";

                        //added by hezp started 2017/06/14====================热卷，铁矿石，焦煤，焦炭，动力煤，硅铁，锰硅
                    } elseif ($names == 'lw_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "螺纹钢期货主力合约收盘价";
                    } elseif ($names == 'rz_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "热卷期货主力合约收盘价";
                    } elseif ($names == 'tks_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "铁矿石期货主力合约收盘价";
                    } elseif ($names == 'jm_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "焦煤期货主力合约收盘价";
                    } elseif ($names == 'jt_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "焦炭期货主力合约收盘价";
                    } elseif ($names == 'dlm_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "动力煤期货主力合约收盘价";
                    } elseif ($names == 'gt_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "硅铁期货主力合约收盘价";
                    } elseif ($names == 'gm_qh_spj') {
                        $sqlrs['1'][$i]['articlename'] = "硅锰期货主力合约收盘价";

                    } elseif ($names == 'lw_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "螺纹钢期货主力合约结算价";
                    } elseif ($names == 'rz_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "热卷期货主力合约结算价";
                    } elseif ($names == 'tks_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "铁矿石期货主力合约结算价";
                    } elseif ($names == 'jm_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "焦煤期货主力合约结算价";
                    } elseif ($names == 'jt_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "焦炭期货主力合约结算价";
                    } elseif ($names == 'dlm_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "动力煤期货主力合约结算价";
                    } elseif ($names == 'gt_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "硅铁期货主力合约结算价";
                    } elseif ($names == 'gm_qh_jsj') {
                        $sqlrs['1'][$i]['articlename'] = "硅锰期货主力合约结算价";
                        //added by hezp ended 2017/06/14====================

                    }
                    $sqlrs['1'][$i]['specification'] = "主力合约";
                    // $sqlrs['1'][$i]['material'] = "HRB400";
                    // $sqlrs['1'][$i]['factoryarea'] = "上海";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "期货";
                }


            } elseif ($names == 'lw_qh_jc') {

                if ($_REQUEST['lwg'] == "3") {
                    // 研究中心基差资讯走势图
                    $num_topic = 3;
                    $lwgcityid = array('072023', '402023', '282023');

                    $sqlrs = sqlrs_data($lwgcityid, 'SHQHDAY_4', $STime, $ETime, array('上海基差', '天津基差', '广州基差'));
                    // echo '<pre>';print_r($sqlrs);
                } elseif ($_REQUEST['jt'] == "3") {
                    // 焦炭基差资讯走势图
                    $num_topic = 3;
                    $jtcityid = array('668311', '408311', '678311');

                    $sqlrs = sqlrs_data($jtcityid, 'SHQHDAY_9', $STime, $ETime, array('山西临汾基差', '天津港基差', '河北唐山基差'));
                } elseif ($_REQUEST['jm'] == "3") {
                    // 焦煤基差资讯走势图
                    $num_topic = 3;
                    $jmcityid = array('S26310', '446310', 'R26310');

                    $sqlrs = sqlrs_data($jmcityid, 'SHQHDAY_19', $STime, $ETime, array('山西介休基差', '河北邯郸基差', '京唐港澳洲基差'));
                    //echo '<pre>';print_r($sqlrs);
                } elseif ($_REQUEST['tks'] == "3") {
                    // 铁矿石基差资讯走势图
                    $num_topic = 3;
                    $tkscityid = array('1886103', '4086103', '448610');

                    $sqlrs = sqlrs_data($tkscityid, 'SHQHDAY_20', $STime, $ETime, array('青岛港基差', '天津港基差', '河北唐山基差'));

                } elseif ($_REQUEST['dlm'] == "3") {
                    // 动力煤基差资讯走势图
                    $num_topic = 3;
                    $dlmcityid = array('996320', '776320', '166325');

                    $sqlrs = sqlrs_data($dlmcityid, 'SHQHDAY_21', $STime, $ETime, array('秦皇岛基差', '大同基差', '徐州基差'));
                } elseif ($_REQUEST['rj'] == "3") {
                    // 热卷基差资讯走势图
                    $num_topic = 3;
                    $dlmcityid = array('073112', '403112', '283112');

                    $sqlrs = sqlrs_data($dlmcityid, 'SHQHDAY_99', $STime, $ETime, array('上海基差', '天津基差', '乐从基差'));
                } //add by zfy started 2017/9/20
                elseif ($_REQUEST['g_t'] == "3") {
                    // 硅铁基差资讯走势图
                    $num_topic = 2;
                    $dlmcityid = array('598710', '418710');

                    $sqlrs = sqlrs_data($dlmcityid, 'SHQHDAY_22', $STime, $ETime, array('宁夏银川FeSi75-B基差', '内蒙古包头FeSi75-B基差'));
                } elseif ($_REQUEST['gm'] == "3") {
                    // 硅锰基差资讯走势图
                    $num_topic = 3;
                    $dlmcityid = array('418910', '3489101', '548910');

                    $sqlrs = sqlrs_data($dlmcityid, 'SHQHDAY_23', $STime, $ETime, array('内蒙古FeMn68Sil8（P≤0.25%）基差', '广西FeMn68Sil8（P≤0.25%）基差', '贵州FeMn68Sil8（P≤0.25%）基差'));
                } //add by zfy ended 2017/9/20


                else {

                    $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='072023' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                    $sqlr_jc = $conn->getArray($sql_jc);
                    // echo "<pre>";print_r($sqlr_jc);exit;
                    $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_4' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                    $arr_temp = array();
                    for ($i = 0; $i < count($qh_arr); $i++) {
                        $dta_11 = $qh_arr[$i]["dta_11"];
                        $dta_ym = $qh_arr[$i]["dta_ym"];
                        $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_4' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                        // echo $sql."<br />";
                        $arr = $conn_src->getArray($sql);
                        $arr_temp[$i] = $arr;
                        if (empty($arr) || $arr['0']['0'] == '') {
                            $arr_temp[$i] = $arr_temp[$i - 1];
                        }
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                        $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                        // $sqlrs['1'][$i]['specification'] = "主力合约";
                        // $sqlrs['1'][$i]['material'] = "HRB400";
                        // $sqlrs['1'][$i]['factoryarea'] = "上海";
                        $sqlrs['1'][$i]['managedate'] = $dta_ym;
                        $city_name['1'] = "螺纹钢";
                    }
                }
            } elseif ($names == 'rz_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('073112');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_9',$STime,$ETime,array('热轧板卷期货基差'));
				$city_name['1'] = "热轧板卷";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='073112' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                //$sql_jc="select mastertopid as  topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
                //mastertopid='073112' and  managedate > '".$STime." 00:00' and managedate < '".$ETime." 23:59' group by managedate order by managedate asc ";
                $sqlr_jc = $conn->getArray($sql_jc);
                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_99' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_99' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "热轧板卷";
                }
            } elseif ($names == 'tjf_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('1886103');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_20',$STime,$ETime,array('铁矿石期货基差'));
				$city_name['1'] = "铁精粉";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					mastertopid='1886103' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                $sqlr_jc = $conn->getArray($sql_jc);
                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_20' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_20' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "铁精粉";
                }
            } elseif ($names == 'jt_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('408311');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_9',$STime,$ETime,array('焦炭期货基差'));
				$city_name['1'] = "焦炭";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='408311' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                $sqlr_jc = $conn->getArray($sql_jc);
                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_9' and dta_1 not like '%计%' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_9' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "焦炭";
                }
            } elseif ($names == 'jm_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('S26310');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_19',$STime,$ETime,array('焦煤期货基差'));
				$city_name['1'] = "焦煤";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='S26310' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                $sqlr_jc = $conn->getArray($sql_jc);
                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_19' and dta_1 not like '%计%' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_19' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "焦煤";
                }
            } elseif ($names == 'dlm_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('996320');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_21',$STime,$ETime,array('动力煤期货基差'));
				$city_name['1'] = "动力煤";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='996320' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                $sqlr_jc = $conn->getArray($sql_jc);
                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_21' and dta_1 not like '%计%' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_21' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "动力煤";
                }
            } elseif ($names == 'gt_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('578710');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_22',$STime,$ETime,array('硅铁期货基差'));
				$city_name['1'] = "硅铁";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='578710' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                $sqlr_jc = $conn->getArray($sql_jc);
                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_22' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();
                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_22' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "硅铁";
                }
            } elseif ($names == 'gm_qh_jc') {
                /*$num_topic =1;
				$jtcityid= array('548910');

				$sqlrs = sqlrs_data($jtcityid,'SHQHDAY_23',$STime,$ETime,array('硅锰期货基差'));
				$city_name['1'] = "硅锰";*/
                $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
					topicture='548910' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' order by mconmanagedate";
                $sqlr_jc = $conn->getArray($sql_jc);

                $qh_arr = $conn_src->getArray("SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='SHQHDAY_23' and dta_1!='小计' GROUP BY `dta_ym` order by `dta_ym` asc");
                $arr_temp = array();

                for ($i = 0; $i < count($qh_arr); $i++) {
                    $dta_11 = $qh_arr[$i]["dta_11"];
                    $dta_ym = $qh_arr[$i]["dta_ym"];
                    $sql = "SELECT `dta_6` FROM `data_table` WHERE  `dta_type`='SHQHDAY_23' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";
                    $arr = $conn_src->getArray($sql);
                    $arr_temp[$i] = $arr;
                    if (empty($arr) || $arr['0']['0'] == '' || $arr['0']['0'] == 0) {
                        $arr_temp[$i] = $arr_temp[$i - 1];
                    }
                    if ($sqlr_jc[$i]['price'] != 0 && $arr_temp[$i]['0']['0'] != 0) {
                        $sqlrs['1'][$i]['price'] = $sqlr_jc[$i]['price'] - $arr_temp[$i]['0']['0'];
                    } else {
                        $sqlrs['1'][$i]['price'] = null;
                    }
                    $sqlrs['1'][$i]['articlename'] = "期货主力合约基差";
                    $sqlrs['1'][$i]['managedate'] = $dta_ym;
                    $city_name['1'] = "硅锰";
                }

            } elseif ($names == 'tks_62') {
                $sql_tks62 = "select * from shpi_material_pzp where dateday >= '" . $STime . "' and dateday <= '" . $ETime . "' and vid='3'  order by dateday";

                $que_tks62 = $conn->getArray($sql_tks62);

                for ($i = 0; $i < count($que_tks62); $i++) {
                    $sqlrs[$news][$i]['price'] = $que_tks62[$i]['weipriceusb'];
                    //add by zfy started 2017/7/26
                    $sqlrs[$news][$i]['average_price_5'] = $que_tks62[$i]['weipriceusb_avg_5'];
                    $sqlrs[$news][$i]['average_price_10'] = $que_tks62[$i]['weipriceusb_avg_10'];
                    $sqlrs[$news][$i]['average_price_20'] = $que_tks62[$i]['weipriceusb_avg_20'];
                    $sqlrs[$news][$i]['average_price_40'] = $que_tks62[$i]['weipriceusb_avg_40'];
                    $sqlrs[$news][$i]['average_price_60'] = $que_tks62[$i]['weipriceusb_avg_60'];
                    $sqlrs[$news][$i]['average_price_200'] = $que_tks62[$i]['weipriceusb_avg_200'];
                    //add by zfy ended 2017/7/26
                    $sqlrs[$news][$i]['articlename'] = "Fe:62%价格指数";
                    // $sqlrs[$news][$i]['specification'] = "主力合约";
                    $sqlrs[$news][$i]['material'] = "Fe:62%";
                    $sqlrs[$news][$i]['factoryarea'] = "进口";
                    $sqlrs[$news][$i]['managedate'] = $que_tks62[$i]['dateday'];
                    // $city_name[$news] = "进口铁矿石价格指数";
                }
            } elseif ($names == 'tks_58') {
                $sql_tks58 = "select * from shpi_pp where bc_id='20' and dateday>='" . $STime . "' and dateday<='" . $ETime . "'";

                $que_tks58 = $conn->getArray($sql_tks58);

                for ($i = 0; $i < count($que_tks58); $i++) {
                    $sqlrs[$news][$i]['price'] = $que_tks58[$i]['weiprice'];
                    //add by zfy started 2017/7/26
                    $sqlrs[$news][$i]['average_price_5'] = $que_tks58[$i]['weiprice_avg_5'];
                    $sqlrs[$news][$i]['average_price_10'] = $que_tks58[$i]['weiprice_avg_10'];
                    $sqlrs[$news][$i]['average_price_20'] = $que_tks58[$i]['weiprice_avg_20'];
                    $sqlrs[$news][$i]['average_price_40'] = $que_tks58[$i]['weiprice_avg_40'];
                    $sqlrs[$news][$i]['average_price_60'] = $que_tks58[$i]['weiprice_avg_60'];
                    $sqlrs[$news][$i]['average_price_200'] = $que_tks58[$i]['weiprice_avg_200'];
                    //add by zfy ended 2017/7/26
                    $sqlrs[$news][$i]['articlename'] = "钢之家铁矿石价格指数";
                    $sqlrs[$news][$i]['material'] = "Fe:58%";
                    $sqlrs[$news][$i]['factoryarea'] = "进口";
                    $sqlrs[$news][$i]['managedate'] = $que_tks58[$i]['dateday'];
                }
            } elseif ($names == 'tks_61') {
                $sql_tks61 = "select * from shpi_pp where bc_id='18' and dateday>='" . $STime . "' and dateday<='" . $ETime . "'";

                $que_tks61 = $conn->getArray($sql_tks61);

                for ($i = 0; $i < count($que_tks61); $i++) {
                    $sqlrs[$news][$i]['price'] = $que_tks61[$i]['weiprice'];
                    //add by zfy started 2017/7/26
                    $sqlrs[$news][$i]['average_price_5'] = $que_tks61[$i]['weiprice_avg_5'];
                    $sqlrs[$news][$i]['average_price_10'] = $que_tks61[$i]['weiprice_avg_10'];
                    $sqlrs[$news][$i]['average_price_20'] = $que_tks61[$i]['weiprice_avg_20'];
                    $sqlrs[$news][$i]['average_price_40'] = $que_tks61[$i]['weiprice_avg_40'];
                    $sqlrs[$news][$i]['average_price_60'] = $que_tks61[$i]['weiprice_avg_60'];
                    $sqlrs[$news][$i]['average_price_200'] = $que_tks61[$i]['weiprice_avg_200'];
                    //add by zfy ended 2017/7/26
                    $sqlrs[$news][$i]['articlename'] = "钢之家铁矿石价格指数";
                    $sqlrs[$news][$i]['material'] = "Fe:61%";
                    $sqlrs[$news][$i]['factoryarea'] = "进口";
                    $sqlrs[$news][$i]['managedate'] = $que_tks61[$i]['dateday'];
                }
            } elseif ($names == 'tks_63') {
                $sql_tks63 = "select * from shpi_pp where bc_id='16' and dateday>='" . $STime . "' and dateday<='" . $ETime . "'";

                $que_tks63 = $conn->getArray($sql_tks63);

                for ($i = 0; $i < count($que_tks63); $i++) {
                    $sqlrs[$news][$i]['price'] = $que_tks63[$i]['weiprice'];
                    //add by zfy started 2017/7/26
                    $sqlrs[$news][$i]['average_price_5'] = $que_tks63[$i]['weiprice_avg_5'];
                    $sqlrs[$news][$i]['average_price_10'] = $que_tks63[$i]['weiprice_avg_10'];
                    $sqlrs[$news][$i]['average_price_20'] = $que_tks63[$i]['weiprice_avg_20'];
                    $sqlrs[$news][$i]['average_price_40'] = $que_tks63[$i]['weiprice_avg_40'];
                    $sqlrs[$news][$i]['average_price_60'] = $que_tks63[$i]['weiprice_avg_60'];
                    $sqlrs[$news][$i]['average_price_200'] = $que_tks63[$i]['weiprice_avg_200'];
                    //add by zfy ended 2017/7/26
                    $sqlrs[$news][$i]['articlename'] = "钢之家铁矿石价格指数";
                    $sqlrs[$news][$i]['material'] = "Fe:63%";
                    $sqlrs[$news][$i]['factoryarea'] = "进口";
                    $sqlrs[$news][$i]['managedate'] = $que_tks63[$i]['dateday'];
                }
            } elseif ($names == 'tks_65') {
                $sql_tks65 = "select * from shpi_pp where bc_id='9' and dateday>='" . $STime . "' and dateday<='" . $ETime . "'";

                $que_tks65 = $conn->getArray($sql_tks65);

                for ($i = 0; $i < count($que_tks65); $i++) {
                    $sqlrs[$news][$i]['price'] = $que_tks65[$i]['weiprice'];
                    //add by zfy started 2017/7/26
                    $sqlrs[$news][$i]['average_price_5'] = $que_tks65[$i]['weiprice_avg_5'];
                    $sqlrs[$news][$i]['average_price_10'] = $que_tks65[$i]['weiprice_avg_10'];
                    $sqlrs[$news][$i]['average_price_20'] = $que_tks65[$i]['weiprice_avg_20'];
                    $sqlrs[$news][$i]['average_price_40'] = $que_tks65[$i]['weiprice_avg_40'];
                    $sqlrs[$news][$i]['average_price_60'] = $que_tks65[$i]['weiprice_avg_60'];
                    $sqlrs[$news][$i]['average_price_200'] = $que_tks65[$i]['weiprice_avg_200'];
                    //add by zfy ended 2017/7/26
                    $sqlrs[$news][$i]['articlename'] = "钢之家铁矿石价格指数";
                    $sqlrs[$news][$i]['material'] = "Fe:65%";
                    $sqlrs[$news][$i]['factoryarea'] = "进口";
                    $sqlrs[$news][$i]['managedate'] = $que_tks65[$i]['dateday'];
                }
            }


        }
    }

    if ($_REQUEST['isbz'] == 1) {
        echo json_encode($tsxx);
        //$str2 = json_decode($str);
        exit;
    }

    foreach ($topic as $topk => $topv) {
        //add by zhudahua start 20160929
        $NotIntArr = array(
            "C18620",
            "A98630",
            "C18690",
            "B98650",
            "B98670",
            "tks_65",
            "tks_63",
            "tks_62",
            "tks_61",
            "tks_58",
            "I98640",
            "I98670",
            "H98640",
            "H98670",
            "H98620",
            "H98600",
            '010108',
            '010109',
            '010110',
        );
        if (in_array($topv, $NotIntArr)) {
            $no_int = 1;
        }
        //add by zhudahua end 20160929
    }

    if ($_REQUEST['lwg'] == "3" || $_REQUEST['jt'] == "3" || $_REQUEST['jm'] == "3" || $_REQUEST['tks'] == "3" || $_REQUEST['dlm'] == "3" || $_REQUEST['rj'] == "3" || $_REQUEST['gm'] == "3") {
        $num_topic = 3;
    } elseif ($_REQUEST['g_t'] == "3") {
        $num_topic = 2;
    } else {
        $num_topic = count($topic) - 1;
    }
    //add by zfy started 2017/6/27
    $nowdate = date("Y-m-d");
    //add by zfy ended 2017/6/27
    $arr_max = array();
    $arr_min = array();
    $articlename = array();

    foreach ($sqlrs as $key => $value) {
        foreach ($value as $key2 => $value2) {
            // libing start
            if ($value2['topicture'] == "2830212") {
                $value2['material'] = "Q345B";
            }
            // libing end
            $value2['managedate'] = date("Y-m-d", strtotime($value2['managedate']));
            $vprice = trim($value2['price']);
            if(strpos($vprice,"-")){
                list($vminprice,$vmaxprice) = explode("-",$vprice);
                $vprice = intval( ((float)$vminprice + (float)$vmaxprice)/2);
            }else{
                $vprice = (float)$vprice;
            }
            $price[$key][$value2['managedate']] = $vprice;
            //add by zfy started 2017/6/27
            if ($value2['average_price_5'] != "0" && $value2['average_price_5'] != "") {
                $price_5[$key][$value2['managedate']] = (int)(trim($value2['average_price_5']));
                $price_floor_5[$key][$value2['managedate']] = (trim($value2['average_price_5']));
            }
            if ($value2['average_price_10'] != "0" && $value2['average_price_10'] != "") {
                $price_10[$key][$value2['managedate']] = (int)(trim($value2['average_price_10']));
                $price_floor_10[$key][$value2['managedate']] = (trim($value2['average_price_10']));
            }
            if ($value2['average_price_20'] != "0" && $value2['average_price_20'] != "") {
                $price_20[$key][$value2['managedate']] = (int)(trim($value2['average_price_20']));
                $price_floor_20[$key][$value2['managedate']] = (trim($value2['average_price_20']));
            }
            if ($value2['average_price_40'] != "0" && $value2['average_price_40'] != "") {
                $price_40[$key][$value2['managedate']] = (int)(trim($value2['average_price_40']));
                $price_floor_40[$key][$value2['managedate']] = (trim($value2['average_price_40']));
            }
            if ($value2['average_price_60'] != "0" && $value2['average_price_60'] != "") {
                $price_60[$key][$value2['managedate']] = (int)(trim($value2['average_price_60']));
                $price_floor_60[$key][$value2['managedate']] = (trim($value2['average_price_60']));
            }
            if ($value2['average_price_200'] != "0" && $value2['average_price_200'] != "") {
                $price_200[$key][$value2['managedate']] = (int)(trim($value2['average_price_200']));
                $price_floor_200[$key][$value2['managedate']] = (trim($value2['average_price_200']));
            }
            if ($value2['managedate'] == $nowdate) {
                if ($avg_now_5 != 0) {
                    $price_5[$key][$value2['managedate']] = (int)(trim($avg_now_5));
                }
                if ($avg_now_10 != 0) {
                    $price_10[$key][$value2['managedate']] = (int)(trim($avg_now_10));
                }
                if ($avg_now_20 != 0) {
                    $price_20[$key][$value2['managedate']] = (int)(trim($avg_now_20));
                }
                if ($avg_now_40 != 0) {
                    $price_40[$key][$value2['managedate']] = (int)(trim($avg_now_40));
                }
                if ($avg_now_60 != 0) {
                    $price_60[$key][$value2['managedate']] = (int)(trim($avg_now_60));
                }
                if ($avg_now_200 != 0) {
                    $price_200[$key][$value2['managedate']] = (int)(trim($avg_now_200));
                }
                if ($avg_now_5 != 0) {
                    $price_floor_5[$key][$value2['managedate']] = (trim($avg_now_5));
                }
                if ($avg_now_10 != 0) {
                    $price_floor_10[$key][$value2['managedate']] = (trim($avg_now_10));
                }
                if ($avg_now_20 != 0) {
                    $price_floor_20[$key][$value2['managedate']] = (trim($avg_now_20));
                }
                if ($avg_now_40 != 0) {
                    $price_floor_40[$key][$value2['managedate']] = (trim($avg_now_40));
                }
                if ($avg_now_60 != 0) {
                    $price_floor_60[$key][$value2['managedate']] = (trim($avg_now_60));
                }
                if ($avg_now_200 != 0) {
                    $price_floor_200[$key][$value2['managedate']] = (trim($avg_now_200));
                }
            }

            $price_floor[$key][$value2['managedate']] = trim($value2['price']);


            $articlename[$key] = $value2['articlename'];

            if ($_articlename[$key] != $value2['articlename'] && $_articlename[$key] != "") {
                $articlename[$key] = $_articlename[$key];
            }
            $material[$key] = $value2['material'] . " " . $value2['specification'];
            $material[$key] = str_replace("\t", "  ", $material[$key]);
            $factoryarea[$key] = $value2['factoryarea'];
            if ($_REQUEST['tu'] == '1') {
                $managedate[$key][$value2['managedate']] = date("m-d", strtotime($value2['managedate']));
            } else {
                $managedate[$key][$value2['managedate']] = date("Y-m-d", strtotime($value2['managedate']));
            }
        }

        if ($value2['topicture'] == '010108' || $value2['topicture'] == '010109') {
            $articlename[$key] = "铁矿石海运费";
            if ($value2['topicture'] == '010108')
                $city_name[$key] = "巴西-中国";
            if ($value2['topicture'] == '010109')
                $city_name[$key] = "西澳-中国";
        }
        //add by zhudahua start 20160923
        if ($value2['topicture'] == '010203' || $value2['topicture'] == '010204' || $value2['topicture'] == '010205' || $value2['topicture'] == '010206') {
            $city_name[$key] = "";
        }
        if ($value2['topicture'] == '408311' || $value2['topicture'] == 'R28310') {
            $city_name[$key] = "天津港";
        }

        //add by zhudahua end 20160923
        if ($value2['topicture'] == '010208') {
            $articlename[$key] = "新交所次月铁矿石掉期";
            $city_name[$key] = "";
        }

        if ($value2['topicture'] == '458610' || $value2['topicture'] == '1886102' || $value2['topicture'] == '448610') {
            if ($city_name[$key] == "邯郸") {
                $city_name[$key] = "河北";
            }
            if ($city_name[$key] == "沈阳") {
                $city_name[$key] = "辽宁";
            }
        }

        if ($value2['topicture'] == 'B99540' || $value2['topicture'] == 'B99530' || $value2['topicture'] == 'N99510' || $value2['topicture'] == 'A99520') {
            $city_name[$key] = "进口";
        }

        if ($articlename[$key] == "钢之家铁矿石价格指数" || $articlename[$key] == "Fe:62%价格指数" || $articlename[$key] == "普氏铁矿石价格指数" || $articlename[$key] == "MB铁矿石价格指数") {
            $_REQUEST['danwei_usa'] = 'usa';
        }
        if ($articlename[$key] == "Fe:62%价格指数") {
            $articlename[$key] = "钢之家铁矿石价格指数";
        }
        if ($articlename[$key] == "普氏铁矿石价格指数") {
            $articlename[$key] = "普氏Platts指数";
        }
        if ($articlename[$key] == "MB铁矿石价格指数") {
            $articlename[$key] = "MB指数";
        }
        //added by quanjw for dpk
        if ($articlename[$key] == "单品块矿溢价") {
            $articlename[$key] = "单品块矿溢价指数";
        }
        //added by quanjw for dpk

        if (isset($_REQUEST['luliaoteshu']) && $_REQUEST['luliaoteshu'] == 'luliaoteshu') {
            $areaTitle = array('', '巴西-中国', '西澳-中国', '印度-中国');
            $articlename[$key] = $areaTitle[$key] . "铁矿石海运费";
        }
        if (isset($_REQUEST['luliaoteshu2']) && $_REQUEST['luliaoteshu2'] == 'luliaoteshu2') {
            $city_name[$key] = "波罗的海海运指数";
        }

        // TSI铁矿石单位特殊处理
        /*if ($value2['topicture'] == 'I98641' || $value2['topicture'] == 'I98671') {
            $city_name[$key] = '外盘';
        }*/

        // $danwei //价格单位
        if ($city_name[$key] == '外盘' || (isset($_REQUEST['danwei_usa']) && $_REQUEST['danwei_usa'] == 'usa') || (isset($_REQUEST['luliaoteshu']) && $_REQUEST['luliaoteshu'] == 'luliaoteshu' || $value2['topicture'] == 'D98220' || $value2['topicture'] == 'G98530' || $value2['topicture'] == 'E98230')) {
            $danwei = "美元";
        } elseif (isset($_REQUEST['luliaoteshu2']) && $_REQUEST['luliaoteshu2'] == 'luliaoteshu2') {
            $danwei = "点";
        } else {
            $danwei = "元";
        }

        if ($value2['topicture'] == 'I98641' || $value2['topicture'] == 'I98671' || $value2['topicture'] == '010108' || $value2['topicture'] == '010109' || $value2['topicture'] == '010208') {
            $danwei = "美元";
        }

        //add by zhudahua start 20160918
        if ($value2['topicture'] == 'C18620' || $value2['topicture'] == 'A98630' || $value2['topicture'] == 'C18690' || $value2['topicture'] == 'B98650' || $value2['topicture'] == 'B98670') {
            $danwei = "美元";
        }
        //add by zhudahua end 20160918

        if ($_REQUEST['lwg'] == "3" || $_REQUEST['jt'] == "3" || $_REQUEST['jm'] == "3" || $_REQUEST['tks'] == "3" || $_REQUEST['dlm'] == "3" || $_REQUEST['rj'] == "3" || $_REQUEST['g_t'] == "3" || $_REQUEST['gm'] == "3") {
            $danwei = "";
        }
        //added by quanjw for dpk
        if ($value2['topicture'] == 'H98600') {
            // update by guozw 2018/02/26 start
            //$danwei="元/吨度";
            $danwei = "美元/吨度";
            // update by guozw 2018/02/26 end
        }
        //added by quanjw fow dpk
        // add by guozw start 2018/02/26
        if ($value2['topicture'] == 'H98692') {
            $danwei = "美元";
        }
        // add by guozw end 2018/02/26
        //added by fxn start 2019/09/04
        if ($value2['topicture'] == '259911') {
            $danwei = '美元';
        }
        //added by fxn ended 2019/09/04
        if ($avgflag == 1) {
            if ($no_int == '1') {
                if ($value2['topicture'] == "H98600") {
                    if (is_array($price_floor[$key])) {
                        $average_price[$key] = "(期间均价：" . round(array_sum($price_floor[$key]) / count($price_floor[$key]), 3) . $danwei . ")";
                    }
                } else {
                    if (is_array($price_floor[$key])) {
                        $average_price[$key] = "(期间均价：" . round(array_sum($price_floor[$key]) / count($price_floor[$key]), 2) . $danwei . ")";
                    }
                }
            } else {
                if (is_array($price[$key])) {
                    $average_price[$key] = "(期间均价：" . round(array_sum($price[$key]) / count($price[$key])) . $danwei . ")";
                }
            }

        } else {
            $average_price[$key] = '';
        }
        if ($_REQUEST['wbt'] == '1') {
            $city_name[$key] = str_replace("青岛", "青岛港", $city_name[$key]);
        }
        if ($articlename[$key] == "钢之家铁矿石价格指数" || $articlename[$key] == "普氏Platts指数" || $articlename[$key] == "MB指数") {
            $lend_title[$key] = $articlename[$key] . "--" . $material[$key] . "-" . $factoryarea[$key] . "  " . $average_price[$key];
        } elseif (isset($_REQUEST['luliaoteshu']) && $_REQUEST['luliaoteshu'] == 'luliaoteshu') {
            $lend_title[$key] = $articlename[$key] . "--" . $material[$key] . "-" . $factoryarea[$key] . "  " . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "螺纹钢") {
            $lend_title[$key] = "期货：螺纹钢" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "热轧板卷") {
            $lend_title[$key] = "期货：热轧板卷" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "铁精粉") {
            $lend_title[$key] = "期货：铁精粉" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "一级冶金焦") {
            $lend_title[$key] = "期货：一级冶金焦" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "焦煤") {
            $lend_title[$key] = "期货：焦煤" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "动力煤") {
            $lend_title[$key] = "期货：动力煤" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "硅铁") {
            $lend_title[$key] = "期货：硅铁" . $average_price[$key];
        } elseif ($city_name[$key] == "期货" && $articlename[$key] == "硅锰") {
            $lend_title[$key] = "期货：硅锰" . $average_price[$key];
        } elseif ($city_name[$key] == "螺纹钢") {
            $lend_title[$key] = "螺纹钢：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "热轧板卷") {
            $lend_title[$key] = "热轧板卷：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "铁精粉") {
            $lend_title[$key] = "铁精粉：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "一级冶金焦") {
            $lend_title[$key] = "一级冶金焦：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "焦煤") {
            $lend_title[$key] = "焦煤：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "动力煤") {
            $lend_title[$key] = "动力煤：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "硅铁") {
            $lend_title[$key] = "硅铁：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "硅锰") {
            $lend_title[$key] = "硅锰：期货主力合约基差" . $average_price[$key];
        } elseif ($city_name[$key] == "") {
            $lend_title[$key] = $articlename[$key] . "--" . $material[$key] . " " . $factoryarea[$key] . "  " . $average_price[$key];
            $othertitle .= $city_name[$key] . $articlename[$key] . '、';

            //added by hezp started 2017/06/14====================热卷，铁矿石，焦煤，焦炭，动力煤，硅铁，锰硅
        } elseif ($city_name[$key] == "期货" && ($articlename[$key] == '螺纹钢期货主力合约收盘价' || $articlename[$key] == '热卷期货主力合约收盘价' || $articlename[$key] == '铁矿石期货主力合约收盘价' || $articlename[$key] == '焦煤期货主力合约收盘价' || $articlename[$key] == '焦炭期货主力合约收盘价' || $articlename[$key] == '动力煤期货主力合约收盘价' || $articlename[$key] == '硅铁期货主力合约收盘价' || $articlename[$key] == '硅锰期货主力合约收盘价')) {
            $lend_title[$key] = $city_name[$key] . "：" . $articlename[$key];

        } elseif ($city_name[$key] == "期货" && ($articlename[$key] == '螺纹钢期货主力合约结算价' || $articlename[$key] == '热卷期货主力合约结算价' || $articlename[$key] == '铁矿石期货主力合约结算价' || $articlename[$key] == '焦煤期货主力合约结算价' || $articlename[$key] == '焦炭期货主力合约结算价' || $articlename[$key] == '动力煤期货主力合约结算价' || $articlename[$key] == '硅铁期货主力合约结算价' || $articlename[$key] == '硅锰期货主力合约结算价')) {
            $lend_title[$key] = $city_name[$key] . "：" . $articlename[$key];
            //added by hezp ended 2017/06/14====================

        }//elseif($psshpi_marid =='-1'&&$city_name[$key] == "上海"){
        //added by shizg for 普氏指数 started 2017/6/29
        //$lend_title[$key] = $articlename[$key]."--".$material[$key]." ".$factoryarea[$key]."  ".$average_price[$key];
        //$othertitle .= $articlename[$key].'、';
        //added by shizg for 普氏指数 ended 2017/6/29
        //}
        else {
            $lend_title[$key] = $city_name[$key] . "：" . $articlename[$key] . "--" . $material[$key] . " " . $factoryarea[$key] . "  " . $average_price[$key];
            $othertitle .= $city_name[$key] . $articlename[$key] . '、';
        }

        if (!empty($price[$key])) {
            $arr_max[$key] = max($price[$key]);
            $arr_min[$key] = min($price[$key]);
        }
        if (empty($arr_max)) {
            $arr_max[] = 0;
            $arr_min[] = 0;
        }
        if (is_array($managedate[$key])) {
            $arr_date[$key] = count($managedate[$key]);
        }
        if ($arr_date[$key] > $arr_date[$key - 1]) {
            $max_date_key = $key;
        }
        $arr_max_5 = $arr_max_10 = $arr_max_20 = $arr_max_40 = $arr_max_60 = $arr_max_200 = [];
        $arr_min_5 = $arr_min_10 = $arr_min_20 = $arr_min_40 = $arr_min_60 = $arr_min_200 = [];
        //add by zfy started 2017/6/27
        if ($no_int != 1) {
            if (!empty($price_5[$key])) {
                $arr_max_5[$key] = max($price_5[$key]);
                $arr_min_5[$key] = min($price_5[$key]);
            }
            if (!empty($price_10[$key])) {
                $arr_max_10[$key] = max($price_10[$key]);
                $arr_min_10[$key] = min($price_10[$key]);
            }
            if (!empty($price_20[$key])) {
                $arr_max_20[$key] = max($price_20[$key]);
                $arr_min_20[$key] = min($price_20[$key]);
            }
            if (!empty($price_40[$key])) {
                $arr_max_40[$key] = max($price_40[$key]);
                $arr_min_40[$key] = min($price_40[$key]);
            }
            if (!empty($price_60[$key])) {
                $arr_max_60[$key] = max($price_60[$key]);
                $arr_min_60[$key] = min($price_60[$key]);
            }
            if (!empty($price_200[$key])) {
                $arr_max_200[$key] = max($price_200[$key]);
                $arr_min_200[$key] = min($price_200[$key]);
            }

        } else {

            if (!empty($price_floor_5[$key])) {
                $price_floor_5[$key] = array_filter($price_floor_5[$key]);
                $arr_max_5[$key] = max($price_floor_5[$key]);
                $arr_min_5[$key] = min($price_floor_5[$key]);
            }
            if (!empty($price_floor_10[$key])) {
                $price_floor_10[$key] = array_filter($price_floor_10[$key]);
                $arr_max_10[$key] = max($price_floor_10[$key]);
                $arr_min_10[$key] = min($price_floor_10[$key]);
            }
            if (!empty($price_floor_20[$key])) {
                $price_floor_20[$key] = array_filter($price_floor_20[$key]);
                $arr_max_20[$key] = max($price_floor_20[$key]);
                $arr_min_20[$key] = min($price_floor_20[$key]);
            }
            if (!empty($price_floor_40[$key])) {
                $price_floor_40[$key] = array_filter($price_floor_40[$key]);
                $arr_max_40[$key] = max($price_floor_40[$key]);
                $arr_min_40[$key] = min($price_floor_40[$key]);
            }
            if (!empty($price_floor_60[$key])) {
                $price_floor_60[$key] = array_filter($price_floor_60[$key]);
                $arr_max_60[$key] = max($price_floor_60[$key]);
                $arr_min_60[$key] = min($price_floor_60[$key]);
            }
            if (!empty($price_floor_200[$key])) {
                $price_floor_200[$key] = array_filter($price_floor_200[$key]);
                $arr_max_200[$key] = max($price_floor_200[$key]);
                $arr_min_200[$key] = min($price_floor_200[$key]);
            }
        }
        //add by zfy ended 2017/6/27
    }
    $max = max($arr_max);
	//$arr_min=array_filter($arr_min);
    $min = min($arr_min);

    if($_REQUEST['debug']=="1"){
        echo "max=".$max;
        echo "min=".$min;
        exit;
    }

//add by zfy started 2017/6/27

    $max_5 = $arr_max_5 ? max($arr_max_5) :0;
    $max_10 = $arr_max_10 ? max($arr_max_10) :0;
    $max_20 = $arr_max_20 ? max($arr_max_20) :0;
    $max_40 = $arr_max_40 ? max($arr_max_40) :0;
    $max_60 = $arr_max_60 ? max($arr_max_60) :0;
    $max_200 = $arr_max_200 ? max($arr_max_200) :0;
    $min_5 = $arr_min_5 ? max($arr_min_5) :0;
    $min_10 = $arr_min_10 ? max($arr_min_10) :0;
    $min_20 = $arr_min_20 ? max($arr_min_20) :0;
    $min_40 = $arr_min_40 ? max($arr_min_40) :0;
    $min_60 = $arr_min_60 ? max($arr_min_60) :0;
    $min_200 = $arr_min_200 ? max($arr_min_200) :0;

//add by zfy ended 2017/6/27
    $unique_cname = array();

    if (is_array($city_name)) {
        $unique_cname = array_unique($city_name);
    }
    if (is_array($articlename)) {
        $unique_articlename = array_unique($articlename);
    }
    $cname = '';
    $aname = '';
// echo "<pre>";print_r($unique_articlename);
    // if($city_name['1'] == "期货"){
    // $cname .= implode("、",array_reverse($unique_cname));
    // }else{
    // $cname .= implode("、",$unique_cname)."市场";
    // $aname .= implode("、",$unique_articlename);
    // }
    $cname .= implode("、", $unique_cname) . "市场";
    $aname .= implode("、", $unique_articlename);

    if ($value2['topicture'] == '408311' || $value2['topicture'] == 'R28310') {
        $cname = '天津港';
    }

    // TSI铁矿石名称特殊处理
    if ($value2['topicture'] == 'I98641' || $value2['topicture'] == 'I98671' || $value2['topicture'] == '010108' || $value2['topicture'] == '010109' || $value2['topicture'] == '010208' || $value2['topicture'] == 'E98230' || $value2['topicture'] == 'D98220') {
        $cname = '';
    }
    //BDI BCI BPI BSI 名称去掉市场
    if ($value2['topicture'] == '010203' || $value2['topicture'] == '010204' || $value2['topicture'] == '010205' || $value2['topicture'] == '010206') {
        $cname = '';
    }
    //

    // added by quanjw for dpk
    if ($value2['topicture'] == 'H98600') {
        $cname = '';
    }
    // added by quanjw for dpk
    if ($articlename[$key] == "钢之家铁矿石价格指数" || $articlename[$key] == "普氏Platts指数" || $articlename[$key] == "MB指数") {
        $bt_title = $aname . "价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif (isset($_REQUEST['luliaoteshu']) && $_REQUEST['luliaoteshu'] == 'luliaoteshu') {
        $bt_title = "铁矿石海运费价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "螺纹钢") {
        if (count($unique_cname) > 1) {
            $bt_title = "螺纹钢" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "螺纹钢期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "热轧板卷") {
        if (count($unique_cname) > 1) {
            $bt_title = "热轧板卷" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "热轧板卷期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "铁精粉") {
        if (count($unique_cname) > 1) {
            $bt_title = "铁精粉" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "铁精粉期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "一级冶金焦") {
        if (count($unique_cname) > 1) {
            $bt_title = "一级冶金焦" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "一级冶金焦期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "焦煤") {
        if (count($unique_cname) > 1) {
            $bt_title = "焦煤" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "焦煤期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "动力煤") {
        if (count($unique_cname) > 1) {
            $bt_title = "动力煤" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "动力煤期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "硅铁") {
        if (count($unique_cname) > 1) {
            $bt_title = "硅铁" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "硅铁期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "期货" && $articlename[$key] == "硅锰") {
        if (count($unique_cname) > 1) {
            $bt_title = "硅锰" . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
        } else {
            $bt_title = "硅锰期货价格走势图(" . $STime . "至" . $ETime . ")";
        }
    } elseif ($city_name['1'] == "螺纹钢") {
        $bt_title = "螺纹钢期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "热轧板卷") {
        $bt_title = "热轧板卷期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "铁精粉") {
        $bt_title = "铁精粉期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "一级冶金焦") {
        $bt_title = "一级冶金焦期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "焦煤") {
        $bt_title = "焦煤期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "动力煤") {
        $bt_title = "动力煤期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "硅铁") {
        $bt_title = "硅铁期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";
    } elseif ($city_name['1'] == "硅锰") {
        $bt_title = "硅锰期货主力合约基差价格走势图(" . $STime . "至" . $ETime . ")";

//added by hezp started 2017/06/14====================热卷，铁矿石，焦煤，焦炭，动力煤，硅铁，锰硅
    } elseif ($city_name[$key] == "期货" && ($articlename[$key] == '螺纹钢期货主力合约收盘价' || $articlename[$key] == '热卷期货主力合约收盘价' || $articlename[$key] == '铁矿石期货主力合约收盘价' || $articlename[$key] == '焦煤期货主力合约收盘价' || $articlename[$key] == '焦炭期货主力合约收盘价' || $articlename[$key] == '动力煤期货主力合约收盘价' || $articlename[$key] == '硅铁期货主力合约收盘价' || $articlename[$key] == '硅锰期货主力合约收盘价')) {
        $bt_title = $articlename[$key] . "走势图(" . $STime . "至" . $ETime . ")";

    } elseif ($city_name[$key] == "期货" && ($articlename[$key] == '螺纹钢期货主力合约结算价' || $articlename[$key] == '热卷期货主力合约结算价' || $articlename[$key] == '铁矿石期货主力合约结算价' || $articlename[$key] == '焦煤期货主力合约结算价' || $articlename[$key] == '焦炭期货主力合约结算价' || $articlename[$key] == '动力煤期货主力合约结算价' || $articlename[$key] == '硅铁期货主力合约结算价' || $articlename[$key] == '硅锰期货主力合约结算价')) {
        $bt_title = $articlename[$key] . "走势图(" . $STime . "至" . $ETime . ")";
//added by hezp ended 2017/06/14====================

    } else {
        if (count($unique_cname) > 1) {
            $othertitle = substr($othertitle, 0, -3);
            if (strpos($othertitle, '铁精粉') || strpos($othertitle, '粉矿')) {
                $bt_title = "$othertitle 价格走势图(" . $STime . "至" . $ETime . ")";
            } else {
                $bt_title = $aname . $cname . "价格走势图(" . $STime . "至" . $ETime . ")";
            }
            //$bt_title = $aname.$cname."价格走势图(".$STime."至".$ETime.")";
        }
        //added by shizg for 普氏指数 started 2017/6/29
        /*elseif($psshpi_marid =='-1'){
		$bt_title = $aname."价格指数走势图(".$STime."至".$ETime.")";
	}*/
        //added by shizg for 普氏指数 ended 2017/6/29
        else {
            $bt_title = $cname . $aname . "价格走势图(" . $STime . "至" . $ETime . ")";
        }
// $bt_title = $cname."：".$aname."价格走势图(".$STime."至".$ETime.")";
    }

    if ($_REQUEST['wbt'] == '1') {
        $bt_title = '';
    }
// 铁矿石走势图片修改
    if ($_REQUEST['wbt'] == '2') {
        $STime = date("Y", strtotime($_REQUEST['STime']));
        $ETime = date("Y", strtotime($_REQUEST['ETime']));
        $DTime = date("m", strtotime($_REQUEST['ETime']));
        $bt_title = $STime . '-' . $ETime . '年' . $DTime . '月国产铁矿石和进口铁矿石价格走势图';
    }

// 研究中心基差走势图标题
    if ($_REQUEST['lwg'] == 3) {
        $bt_title = "上海期货交易所螺纹钢期货基差走势图";
    } elseif ($_REQUEST['jt'] == 3) {
        $bt_title = "大连商品交易所焦炭期货基差走势图";
    } elseif ($_REQUEST['jm'] == 3) {
        $bt_title = "大连商品交易所焦煤期货基差走势图";
    } elseif ($_REQUEST['tks'] == 3) {
        $bt_title = "大连商品交易所铁矿石期货基差走势图";
    } elseif ($_REQUEST['dlm'] == 3) {
        $bt_title = "郑州商品交易所动力煤期货基差走势图";
    } elseif ($_REQUEST['rj'] == 3) {
        $bt_title = "上海期货交易所热卷期货基差走势图";
    } elseif ($_REQUEST['g_t'] == 3) {
        $bt_title = "郑州商品交易所硅铁期货基差走势图";
    } elseif ($_REQUEST['gm'] == 3) {
        $bt_title = "郑州商品交易所硅锰期货基差走势图";
    }

    $maxdate = array();
    $maxdate = $managedate[$max_date_key];
// echo "<pre>";print_r($price);

    if ($no_int == '1') {
        foreach ($price_floor as $ppkey => $ppvalue) {
            foreach ($maxdate as $lkey => $lvaue) {
                $lastdate = $lvaue;
                if ($ppvalue[$lkey] == "") {
                    // echo $ppvalue[$lkey-1]."<br>";
                    // $price_floor[$ppkey][$lkey] = $price_floor[$ppkey][$lkey-1];//无数据时，取上条数据
                    $price_floor[$ppkey][$lkey] = "";//无数据时，为空
                }
            }
        }
        // echo "<pre>";print_r($maxdate);
        //zk add 20210702 start
        $fenmu = count($maxdate);
        if (count($maxdate) >= 5)
            $fenmu = 5;
        if ($fenmu == 0 || $fenmu == 1)
            $maxdate_5 = 0;
        else
            $maxdate_5 = (int)((count($maxdate)+$fenmu-1) / $fenmu);
        //zk add 20210702 end
        // echo $bt_title;

        foreach ($managedate as $mankey => $manvalue) {
            if ($managedate[$mankey]['0'] > $maxdate['0']) {
                $xxxx = array_slice($price_floor[$mankey], 0, count($manvalue));
                $yyyy = count($maxdate) - count($manvalue);
                $zzzz = array_slice($price_floor[$mankey], count($manvalue));
                // echo "<pre>";print_r($zzzz);
                foreach ($zzzz as $zzkey => $zzvalue) {
                    $zzzz[$zzkey] = $price_floor[$mankey]['0'];
                }
                unset($price_floor[$mankey]);
                $price_floor[$mankey] = array_merge($zzzz, $xxxx);
                // $price[$mankey] = $zzzz;
            }
        }
    } else {
        foreach ($price as $ppkey => $ppvalue) {
            foreach ($maxdate as $lkey => $lvaue) {
                $lastdate = $lvaue;
                if ($ppvalue[$lkey] == "") {
                    // echo $ppvalue[$lkey-1]."<br>";
                    // $price[$ppkey][$lkey] = $price[$ppkey][$lkey-1];//无数据时，取上条数据
                    $price[$ppkey][$lkey] = "";//无数据时，为空
                }
            }
        }
        //echo "<pre>";print_r($maxdate);

        //zk add 20210702 start
        if (!is_array($maxdate)) {
            $maxdate = array();
        }
        $fenmu = count($maxdate);
        if (count($maxdate) >= 5)
            $fenmu = 5;
        if ($fenmu == 0 || $fenmu == 1)
            $maxdate_5 = 0;
        else
            $maxdate_5 = (int)((count($maxdate)+$fenmu-1) / $fenmu);
        //zk add 20210702 end
        // echo $bt_title;
        /*
	foreach($managedate as $mankey=>$manvalue){
		if($managedate[$mankey]['0']>$maxdate['0']){
			$xxxx = array_slice($price[$mankey],0,count($manvalue));
			$yyyy = count($maxdate)-count($manvalue);
			$zzzz = array_slice($price[$mankey],count($manvalue));
			// echo "<pre>";print_r($zzzz);
			foreach($zzzz as $zzkey=>$zzvalue){
				// $zzzz[$zzkey] = $price[$mankey]['0'];//补数据
				$zzzz[$zzkey] = "";//不补数据
			}
			unset($price[$mankey]);
			$price[$mankey] = array_merge($zzzz,$xxxx);
			// $price[$mankey] = $zzzz;
		}
	}
*/
    }
//echo "<pre>";print_r($lend_title);
//echo "<pre>";print_r($city_name);exit;
    //added by shizg started
    $price = handleMissingData($price);
    $price_floor = handleMissingData($price_floor);
    //added by shizg ended

//add by zfy started 2017/6/27
    if ($count_priceid == 1 && $qh_type == 0 && ($avg_5 != 0 || $avg_10 != 0 || $avg_20 != 0 || $avg_40 != 0 || $avg_60 != 0 || $avg_200 != 0)) {
        if ($city_name[1] != "") {
            $city_name[1] .= "：";
        } else {
            $city_name[1] = '';
        }

        if ($avg_5 == 1 && $price_5 != '') {
            $price = array_merge_recursive($price, $price_5);
            $price_floor = array_merge_recursive($price_floor, $price_floor_5);
            $num_topic++;
            $lend_title[$num_topic] = $city_name[1] . "" . $articlename[1] . "--" . $material[1] . " " . $factoryarea[1] . "  5日均线";
            $max = max($max, $max_5);
            $min = min($min, $min_5);
        }
        if ($avg_10 == 1 && $price_10 != '') {
            $price = array_merge_recursive($price, $price_10);
            $price_floor = array_merge_recursive($price_floor, $price_floor_10);
            $num_topic++;
            $lend_title[$num_topic] = $city_name[1] . "" . $articlename[1] . "--" . $material[1] . " " . $factoryarea[1] . "  10日均线";
            $max = max($max, $max_10);
            $min = min($min, $min_10);
        }
        if ($avg_20 == 1 && $price_20 != '') {
            $price = array_merge_recursive($price, $price_20);
            $price_floor = array_merge_recursive($price_floor, $price_floor_20);
            $num_topic++;
            $lend_title[$num_topic] = $city_name[1] . "" . $articlename[1] . "--" . $material[1] . " " . $factoryarea[1] . "  20日均线";
            $max = max($max, $max_20);
            $min = min($min, $min_20);
        }
        if ($avg_40 == 1 && $price_40 != '') {
            $price = array_merge_recursive($price, $price_40);
            $price_floor = array_merge_recursive($price_floor, $price_floor_40);
            $num_topic++;
            $lend_title[$num_topic] = $city_name[1] . "" . $articlename[1] . "--" . $material[1] . " " . $factoryarea[1] . "  40日均线";
            $max = max($max, $max_40);
            $min = min($min, $min_40);
        }
        if ($avg_60 == 1 && $price_60 != '') {
            $price = array_merge_recursive($price, $price_60);
            $price_floor = array_merge_recursive($price_floor, $price_floor_60);
            $num_topic++;
            $lend_title[$num_topic] = $city_name[1] . "" . $articlename[1] . "--" . $material[1] . " " . $factoryarea[1] . "  60日均线";
            $max = max($max, $max_60);
            $min = min($min, $min_60);
        }
        if ($avg_200 == 1 && $price_200 != '') {
            $price = array_merge_recursive($price, $price_200);
            $price_floor = array_merge_recursive($price_floor, $price_floor_200);
            $num_topic++;
            $lend_title[$num_topic] = $city_name[1] . "" . $articlename[1] . "--" . $material[1] . " " . $factoryarea[1] . "  200日均线";
            $max = max($max, $max_200);
            $min = min($min, $min_200);
        }
        foreach ($price as $keyp => $valuep) {
            foreach ($maxdate as $keyp2 => $valuep2) {
                if (count($price) == 1) {
                    $price_new[$keyp][] = $valuep[$keyp2];
                } else {
                    $price_new[$keyp + 1][] = $valuep[$keyp2];
                }
            }
        }

        foreach ($price_floor as $keyp => $valuep) {
            foreach ($maxdate as $keyp2 => $valuep2) {
                if (count($price) == 1) {
                    $price_floor_new[$keyp][] = $valuep[$keyp2];
                } else {
                    $price_floor_new[$keyp + 1][] = $valuep[$keyp2];
                }
            }
        }

    } else {
        foreach ($price as $keyp => $valuep) {
            foreach ($maxdate as $keyp2 => $valuep2) {
                $price_new[$keyp][] = $valuep[$keyp2];
            }
        }

        foreach ($price_floor as $keypf => $valuepf) {
            foreach ($maxdate as $keypf2 => $valuepf2) {
                $price_floor_new[$keypf][] = $valuepf[$keypf2];
            }
        }
    }
//add by zfy ended 2017/6/27
    foreach ($maxdate as $keym => $valuem) {
        $maxdate_new[] = $valuem;
    }
    if ($maxdate_5 == 0) {
        if ($AppMode != 4) {
            $image = imagecreatefromgif(APP_URL_WWW . "/images/gglg01.gif");
            if ($_REQUEST['wubj'] == '1') {


                $image = imagecreatefromgif(APP_URL_WWW . "/images/gglg01_nolog.gif");
            }
        } else {
            $image = imagecreatefrompng(APP_URL_WWW . "/images/gglgen.png");
            if ($_REQUEST['wubj'] == '1') {
                $image = imagecreatefrompng(APP_URL_WWW . "/images/gglg01_nolog_en.gif");
            }
        }
        imagepng($image);
        exit;
    } else {
        for ($x = 1; $x <= $num_topic; $x++) {
            if (!empty($price_new[$x])) {
                $xabc = '1';
            } else {
                $yabc[$x] = 1;
            }
        }
        if ($xabc != '1') {
            if ($AppMode != 4) {
                $image = imagecreatefromgif(APP_URL_WWW . "/images/gglg01.gif");
            } else {
                $image = imagecreatefrompng(APP_URL_WWW . "/images/gglgen.png");
            }
            imagepng($image);
            exit;
        }
    }
    if ($AppMode == 4) {
        $eaenglish = eaenglish();
        $bt_title = str_replace($eaenglish[0], $eaenglish[1], $bt_title);
        //$bt_title=ucwords($bt_title);
        $bt_title = str_replace("至", "--", $bt_title);
        $bt_title = str_replace("走势图", "", $bt_title);

    }


// echo "<pre>";print_r($num_topic);
    if ($_REQUEST['tu'] == '1') {
        $graph = new Graph(202, 140);
        $graph->img->SetMargin(32, 15, 10, 40);
        $maxdate_5 = (int)(count($maxdate_new) / 2) - 1;

    } elseif ($_REQUEST['tu'] == '2') {
        $graph = new Graph(400, 200);
        $graph->img->SetMargin(50, 30, 10, 25);
        $maxdate_5 = (int)(count($maxdate_new) / 2) - 1;
    } else {
        switch ($num_topic) {
            case 1:
                $graph = new Graph(600, 310);
                $graph->img->SetMargin(50, 30, 50, 60);
                break;
            case 2:
                $graph = new Graph(600, 330);
                $graph->img->SetMargin(50, 30, 50, 80);
                break;
            case 3:
                $graph = new Graph(600, 350);
                $graph->img->SetMargin(50, 30, 50, 100);
                break;
            case 4:
                $graph = new Graph(600, 370);
                $graph->img->SetMargin(50, 30, 50, 130);
                break;
            case 5:
                $graph = new Graph(600, 400);
                $graph->img->SetMargin(50, 30, 50, 150);
                break;
            //add by zfy started 2017/6/27
            case 6:
                $graph = new Graph(600, 410);
                $graph->img->SetMargin(50, 30, 50, 170);
                break;
            case 7:
                $graph = new Graph(600, 430);
                $graph->img->SetMargin(50, 30, 50, 190);
                break;
            //add by zfy ended 2017/6/27
        }
    }

// echo $min_new = $min-(ceil(($max-$min)*0.02));
// echo $man_new = $max+(ceil(($max-$min)*0.02));
// echo floor(($min-(ceil(($max-$min)*0.02)))/100);exit;
    if ($_REQUEST['gs'] == '1') {
        if (ceil(($max + (ceil(($max - $min) * 0.05))) / 100) * 100 + floor(($min - (ceil(($max - $min) * 0.02))) / 100) * 100 > 0) {
            $graph->SetScale("textlin", -ceil(($max + (ceil(($max - $min) * 0.05))) / 100) * 100, ceil(($max + (ceil(($max - $min) * 0.05))) / 100) * 100);
            $x_jc = ceil(($max + (ceil(($max - $min) * 0.05))) / 100) * 100 / 2;
        } else {
            $graph->SetScale("textlin", floor(($min - (ceil(($max - $min) * 0.02))) / 100) * 100, -floor(($min - (ceil(($max - $min) * 0.02))) / 100) * 100);
            $x_jc = -floor(($min - (ceil(($max - $min) * 0.02))) / 100) * 100 / 2;
        }

    } elseif ($_REQUEST['tu'] == '1' || $_REQUEST['tu'] == '2') {
        $graph->SetScale("textlin", ceil(($max + (ceil(($max - $min) * 0.05))) / 10) * 10, ceil(($max + (ceil(($max - $min) * 0.05))) / 10) * 10);
    } else {
        $graph->SetScale("textlin", $min - (ceil(($max - $min) * 0.02)), $max + (ceil(($max - $min) * 0.05)));
    }

    if (($max - $min) < 1000) {
        $graph->yaxis->scale->SetGrace(20, 10);
    } else {
        $graph->yaxis->scale->SetGrace(20, 1);
    }
    if ($_REQUEST['gs'] == '1') {
        $graph->yscale->ticks->Set($x_jc, 2);
    } else {
        $graph->yaxis->HideZeroLabel();
    }

    $graph->xaxis->SetPos("min");
// $graph->yscale->ticks->Set(100,10);
// $graph->SetScale("linlin");
// $graph->yscale->ticks->Set(0.5);

// $_SESSION['datay_1']['0']='70';
// $_SESSION['datay_1']['1']='70';
// $_SESSION['data_date']['0']='05/12/20';
// $_SESSION['data_date']['1']='05/12/21';

// echo "<pre>";var_dump($_SESSION['datay_1']);
// echo "<pre>";print_r($_SESSION['data_date']);
// $aAxisType = 'intlin'; //第一个int是X轴类型第2个lin是Y轴类型
// $yScaleMin = 0; //Y轴最小值, 如果不需要设置，可以为空，或者NULL，下同
// $yScaleMax = 5000; //Y轴最大值
// $xScaleMin = 0; //X轴最大值
// $xScaleMax = $_SESSION['num_count']-1; //X轴最大值

// $graph->SetScale($aAxisType, $yScaleMin, $yScaleMax, $xScaleMin, $xScaleMax);


    if ($_REQUEST['tu'] != '1' && $_REQUEST['tu'] != '2') {
        $graph->title->Set($bt_title);//$_SESSION['bt_title'] 走势图标题
        $graph->title->SetFont(FF_SIMSUN, FS_NORMAL, 11);
        $graph->title->SetColor('black');
    } else {
        $graph->xaxis->SetColor('#888888');
        $graph->yaxis->SetColor('#888888');
    }
    $graph->title->SetMargin(10);


    $graph->yaxis->HideLine(false);
    $graph->yaxis->HideTicks(true, false);
// $graph->yaxis->SetFont(FF_SIMSUN,FS_NORMAL,8);
// $graph->yaxis->scale->SetGrace(10);
// $graph->ygrid->SetColor('yellow@0.5');

    $graph->ygrid->Show();

    function year_callback($lastdate)
    {

        return $lastdate;//$_SESSION['last_date'] 最后一天的日期
    }

//在X坐标处调用
    $graph->xaxis->SetLabelFormatCallback('year_callback');

    $graph->xgrid->SetLineStyle("solid");
    $graph->xgrid->SetColor('#E3E3E3');
// $graph->xaxis->SetTextLabelInterval($_SESSION['sum_date']);
    $graph->xaxis->SetTickLabels($maxdate_new);//$_SESSION['data_date'] 所有日期
    $graph->xaxis->SetTextTickInterval($maxdate_5, 0);//$maxdate_5 所有日期除以5所得数
    $graph->xaxis->SetPosAbsDelta(5);
// $graph->xaxis->SetLabelAngle(-1);                //设置标签角度
// $graph->xaxis->SetFont(FF_SIMSUN);

// $graph->xaxis->SetLabelFormatCallback('year_callback');
// $graph->xaxis->SetTickLabels($gDateLocale->GetShortMonth());

    $graph->ygrid->SetFill(false);
    $graph->yaxis->HideLine(false);
    $graph->xaxis->HideLine(false);
    $graph->xaxis->HideTicks(false, false);

// Create the first line

// $_SESSION['case']=1;
//update by zfy started 2017/6/27
    $arr_color = array("#fb0404", "#8a04fc", "#0309fe", "#03fbf8", "#f9f003", "#B3EE3A", "#68838B");
//update by zfy ended 2017/6/27
// echo "<pre>";print_r($arr_Margin);
    for ($i = 1; $i <= $num_topic; $i++) {

        if ($AppMode == 4) {

            $lend_title[$i] = str_replace($eaenglish[0], $eaenglish[1], $lend_title[$i]);
            $lend_title = str_replace("：", ":", $lend_title);
            $lend_title = str_replace("期间均价", "avg", $lend_title);
            $lend_title = str_replace("元", "RMB", $lend_title);

        }
        if ($yabc[$i] != '1') {

            if ($_REQUEST['mm'] == 1) {
                echo "<pre>";
                print_r($price[$i]);
            }

            if ($no_int == '1') {
                //$p[$i] = new LinePlot($price_floor_new[$i]);//$_SESSION['datay_1'] Y轴数据
                //update changhong 2018-06-12
                if ($i == 1) {
                    if ($is_dot_img == 1) {
                        $splot = new ScatterPlot($price_floor_new[$i]);
                        $splot->mark->SetType($dot_type);
                        $splot->mark->SetWidth($dot_size);
                        $splot->mark->SetFillColor($arr_color[$i - 1]);
                        $splot->mark->SetColor($arr_color[$i - 1]);
                        $p[$i] = $splot;
                    } else {
                        $p[$i] = new LinePlot($price_floor_new[$i]);//$_SESSION['datay_1'] Y轴数据
                    }
                } else {
                    if ($is_jj == 1 || $is_dot_img == 0) {
                        $p[$i] = new LinePlot($price_floor_new[$i]);//$_SESSION['datay_1'] Y轴数据
                    } else {
                        $splot = new ScatterPlot($price_floor_new[$i]);
                        $splot->mark->SetType($dot_type);
                        $splot->mark->SetWidth($dot_size);
                        $splot->mark->SetFillColor($arr_color[$i - 1]);
                        $splot->mark->SetColor($arr_color[$i - 1]);
                        $p[$i] = $splot;
                    }
                }
                //end update changhong 2018-06-12
            } else {
                //$p[$i] = new LinePlot($price_new[$i]);//$_SESSION['datay_1'] Y轴数据

                //update changhong 2018-06-12
                if ($i == 1) {
                    if ($is_dot_img == 1) {
                        $splot = new ScatterPlot($price_new[$i]);
                        $splot->mark->SetType($dot_type);
                        $splot->mark->SetWidth($dot_size);
                        $splot->mark->SetFillColor($arr_color[$i - 1]);
                        $splot->mark->SetColor($arr_color[$i - 1]);
                        $p[$i] = $splot;
                    } else {

                        $p[$i] = new LinePlot($price_new[$i]);//$_SESSION['datay_1'] Y轴数据
                    }
                } else {
                    if ($is_jj == 1 || $is_dot_img == 0) {
                        $p[$i] = new LinePlot($price_new[$i]);//$_SESSION['datay_1'] Y轴数据
                    } else {
                        $splot = new ScatterPlot($price_new[$i]);
                        $splot->mark->SetType($dot_type);
                        $splot->mark->SetWidth($dot_size);
                        $splot->mark->SetFillColor($arr_color[$i - 1]);
                        $splot->mark->SetColor($arr_color[$i - 1]);
                        $p[$i] = $splot;
                    }
                }
                //end update changhong 2018-06-12

            }
            $graph->Add($p[$i]);
            $p[$i]->SetColor($arr_color[$i - 1]);
            if ($_REQUEST['tu'] != '1' && $_REQUEST['tu'] != '2') {
                $p[$i]->SetLegend($lend_title[$i]);
                $p[$i]->SetWeight(2);
            } else {
                $p[$i]->SetWeight(1);
            }
            //add by zfy started 2017/7/12
            if ($count_priceid == 1) {
                if ($i == 1) {
                    $p[$i]->SetWeight(3);
                } else {
                    $p[$i]->SetWeight(1);
                }
            }
            //add by zfy ended 2017/7/12


        }
    }
    if ($_REQUEST['wubj'] != '1') {
        if ($AppMode != 4) {
            if ($_REQUEST['tu'] != '1' && $_REQUEST['tu'] != '2') {
                $graph->SetBackgroundImage("./images/gglg.png", BGIMG_COPY); //设置背景
            } elseif ($_REQUEST['tu'] == '2') {
                $graph->SetBackgroundImage("./images/gglg_x.png", BGIMG_COPY); //设置背景
            }
        } else {

            if ($_REQUEST['tu'] != '1' && $_REQUEST['tu'] != '2') {
                $graph->SetBackgroundImage("./images/gglg01en.png", BGIMG_COPY); //设置背景
            } elseif ($_REQUEST['tu'] == '2') {
                $graph->SetBackgroundImage("./images/gglg01en.png", BGIMG_COPY); //设置背景
            }
        }
    } else {
        if ($AppMode != 4) {
            $graph->SetBackgroundImage("./images/gglg_wbj.png", BGIMG_COPY); //设置背景
        } else {
            $graph->SetBackgroundImage("./images/gglg_wbj_en.png", BGIMG_COPY); //设置背景
        }
    }

    $graph->SetBackgroundImageMix(40);
    $graph->SetMarginColor("white");
    $graph->SetFrame(false);//是否显示边框
    $graph->legend->SetFillColor('#ffffff');
    $graph->legend->SetFrameWeight(0);//图例外框
    $graph->legend->SetShadow(false);
    $graph->legend->SetFont(FF_SIMSUN, FS_NORMAL);
    $graph->legend->SetColumns(1);
    $graph->legend->Pos(0.5, 0.97, "center", "bottom");
// echo"<pre>";print_r($graph->legend);exit;
// $graph->legend->SetLayout(LEGEND_HOR);
// Output line
    ob_clean();
    $graph->Stroke();

}


// 查询基差走势图数据
function sqlrs_data($arrid, $dta_type, $STime, $ETime, $title)
{

    global $conn, $conn_src;

    $qh_arr = $conn_src->getArray("SELECT  dta_1,MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` >='" . $STime . "' and `dta_ym` <= '" . $ETime . "' and `dta_type`='" . $dta_type . "' AND dta_1 not like '%小计%' AND dta_1 not like '%总计%' GROUP BY `dta_ym` order by `dta_ym` asc ");

    $sb = 0;
    foreach ($arrid as $citykey => $cityval) {
        if (strlen($cityval) == 6) {
            $sql_jc = "select topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
			topicture='" . $cityval . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' group by managedate order by managedate desc limit " . count($qh_arr);

        }
        if (strlen($cityval) == 7) {
            $sql_jc = "select mastertopid as  topicture, `oldprice` , `price` , articlename, specification, material, factoryarea, marketrecord.id,managedate from marketconditions, marketrecord where marketconditions.`marketrecordid` = marketrecord.id and
			mastertopid='" . $cityval . "' and  mconmanagedate >= '" . $STime . " 00:00' and mconmanagedate <= '" . $ETime . " 23:59' group by managedate order by managedate desc limit " . count($qh_arr);
        }

        $sqlr_jc[$sb] = $conn->getArray($sql_jc);
        $sb++;
    }

    usort($sqlr_jc[0], 'my_sort');
    usort($sqlr_jc[1], 'my_sort');
    usort($sqlr_jc[2], 'my_sort');

    // 取得列的列表
    $arr_temp = array();
    for ($i = 0; $i < count($qh_arr); $i++) {
        $dta_11 = $qh_arr[$i]["dta_11"];
        $dta_ym = $qh_arr[$i]["dta_ym"];
        $sql = "SELECT `dta_7`,dta_ym FROM `data_table` WHERE  `dta_type`='" . $dta_type . "' and `dta_ym`='$dta_ym' and `dta_11`='$dta_11'";

        $arr = $conn_src->getArray($sql);
        $arr_temp[$i] = $arr;
        if (empty($arr) || $arr['0']['0'] == '') {
            $arr_temp[$i] = $arr_temp[$i - 1];
        }


        if ($sqlr_jc[0][$i]['topicture'] == '072023') {
            //上海螺纹钢基差特殊处理
            $sqlrs['1'][$i]['price'] = ($sqlr_jc[0][$i]['price'] / 0.965) - $arr_temp[$i]['0']['0'];
            $sqlrs['1'][$i]['articlename'] = $title[0];
            $sqlrs['1'][$i]['managedate'] = $dta_ym;
        } elseif ($sqlr_jc[0][$i]['topicture'] == '1886103') {
            //青岛港铁矿石基差特殊处理
            $sqlrs['1'][$i]['price'] = ($sqlr_jc[0][$i]['price'] / 0.94) - $arr_temp[$i]['0']['0'];
            $sqlrs['1'][$i]['articlename'] = $title[0];
            $sqlrs['1'][$i]['managedate'] = $dta_ym;
        } else {
            $sqlrs['1'][$i]['price'] = ($sqlr_jc[0][$i]['price']) - $arr_temp[$i]['0']['0'];
            $sqlrs['1'][$i]['articlename'] = $title[0];
            $sqlrs['1'][$i]['managedate'] = $dta_ym;
        }
        if ($sqlr_jc[1][$i]['topicture'] == '402023') {
            $sqlrs['2'][$i]['price'] = ($sqlr_jc[1][$i]['price'] / 0.965) - $arr_temp[$i]['0']['0'];
            $sqlrs['2'][$i]['articlename'] = $title[1];
            $sqlrs['2'][$i]['managedate'] = $dta_ym;
        } elseif ($sqlr_jc[1][$i]['topicture'] == '4086103') {
            // 天津港铁矿石基差特殊处理
            $sqlrs['2'][$i]['price'] = ($sqlr_jc[1][$i]['price'] / 0.94) - $arr_temp[$i]['0']['0'];
            $sqlrs['2'][$i]['articlename'] = $title[1];
            $sqlrs['2'][$i]['managedate'] = $dta_ym;
        } else {
            $sqlrs['2'][$i]['price'] = ($sqlr_jc[1][$i]['price']) - $arr_temp[$i]['0']['0'];
            $sqlrs['2'][$i]['articlename'] = $title[1];
            $sqlrs['2'][$i]['managedate'] = $dta_ym;
        }

        $sqlrs['3'][$i]['price'] = ($sqlr_jc[2][$i]['price']) - $arr_temp[$i]['0']['0'];
        $sqlrs['3'][$i]['articlename'] = $title[2];
        $sqlrs['3'][$i]['managedate'] = $dta_ym;
    }

    return $sqlrs;
}

// 排序
function my_sort($a, $b)
{
    if ($a[8] == $b[8]) return 0;
    return $a[8] > $b[8] ? 1 : -1;
}

function average_price($array, $count, $num)
{
    $price = 0;
    $i = 1;
    for ($j = 0; $j < $num; $j++) {
        if ($array[$count - $j]['price'] == '') {
            $i = 0;
            break;
        }
        $price += $array[$count - $j]['price'];
    }
    if ($i == 0) {
        return 0;
    } else {
        return ($price / $num);
    }
}

//xiangbin add 20190202 start

function eaenglish()
{

    global $conn;
    $result = $conn->getArray("select * from translate order by LENGTH(chinatxt)  desc  ");
    $english = array();
    $english1 = array();
    $english2 = array();
    foreach ($result as $tmp) {
        // $english[$tmp["chinatxt"]] = $tmp["englishtxt"];
        $english1[] = $tmp["chinatxt"];
        $english2[] = is_numeric($tmp["englishtxt"]) ? $tmp["englishtxt"] : (ucwords($tmp["englishtxt"]) . " ");
    }


    /*for($i=0;$i<count($english1);$i++)
	 {
         for($j=$i+1;$j<count($english1);$j++)
		 {
           if (strlen($english1[$i]) < strlen($english1[$j])) {
            $tem = $english1[$i]; // 这里临时变量，存贮$i的值
            $english1[$i] = $english1[$j]; // 第一次更换位置
            $english1[$j] = $tem; // 完成位置互换

			$tem1 = $english2[$i]; // 这里临时变量，存贮$i的值
            $english2[$i] = $english2[$j]; // 第一次更换位置
            $english2[$j] = $tem1; // 完成位置互换
           }
		 }
	 }*/


    $english = array($english1, $english2);
    //print_r($english1);
    return $english;

}


//xiangbin add 20190202 end


?>
