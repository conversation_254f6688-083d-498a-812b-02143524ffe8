<?php

 /**
 * @Date: 2025-08-19 10:15:54
 * @parms: 
 * @return: SYMBOL_TYPE 
 * @Description: 借用李兵写的导历史数据的程序，每天更新一遍所有数据，避免数据漏
*/
ini_set('max_execution_time', 0);
require_once("/etc/steelconf/env/www_env.php");
require_once("/usr/local/www/libs/vendor/autoload.php");

$HOST_91W_SERVER ='**************' ;
$HOST_91W_PORT =4306;
$HOST_91W_USERNAME =USER_NAME_91W;
$HOST_91W_PASSWORD =USER_PASSWORD_91W;
$DBSE91W_NAME =DATABASE_NAME_91W;
$ADODB_FETCH_MODE = ADODB_FETCH_ASSOC;//返回的记录集形式,关联形式

$db= ADONewConnection('mysqli');
$db->Connect($HOST_91W_SERVER.":".$HOST_91W_PORT,$HOST_91W_USERNAME,$HOST_91W_PASSWORD,$DBSE91W_NAME);

// $url = "http://**************:8888/search/add_information/m";
// $logfile="/tmp/es_touch".date("Y-m-d").".log";

// $date = date("Y-m-d");
// $starttime = microtime();
// file_put_contents($logfile, "Start at $starttime \n",FILE_APPEND);
// $sql = " select newsid from news_mrhq where ndate>='$date 00:00:00' and ndate<'$date 23:59:59'";
// $datas = $db->getCol($sql);
// foreach($datas as $newsid) {
//     $turl = $url.$newsid;
//     echo $turl.":";
//     echo file_get_contents($turl);
//     echo "\n";
//     file_put_contents($logfile, "$turl \n",FILE_APPEND);
// }
// $endtime = microtime();
// file_put_contents($logfile, " execute time: $starttime -- $endtime \n",FILE_APPEND);
// echo " news_mrhq done";

/**
 * 以下是资讯的
 */
$url = "http://**************:8888/search/add_information/n";
$logfile="/tmp/es_touch_news".date("Y-m-d").".log";

$starttime = microtime();
file_put_contents($logfile, "Start at $starttime \n",FILE_APPEND);
$sql = " select nid from news where ndate>='$date 00:00:00' and ndate<'$date 23:59:59' and isshow=1";
$datas = $db->getCol($sql);
foreach($datas as $newsid) {
    $turl = $url.$newsid;
    echo $turl.":";
    echo file_get_contents($turl);
    echo "\n";
    file_put_contents($logfile, "$turl \n",FILE_APPEND);
}
$endtime = microtime();
file_put_contents($logfile, "$start_date to $end_date. execute time: $starttime -- $endtime \n",FILE_APPEND);