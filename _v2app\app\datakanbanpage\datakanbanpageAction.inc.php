<?php
include_once APP_DIR . "/master/masterAction.inc.php";
class datakanbanpageAction extends masterAction
{
	public $dept_array = array(
		"2,3,2" => "华东区",
		"2,3,11" => "北方区",
		"2,3,4" => "中西部",
		"2,3,6" => "特钢部",
		"2,7,0" => "钢铁原料部",
		"2,7,2" => "煤焦部",
		"2,7,3" => "合金辅料部",
		"2,10,0" => "国际部",
		"17,0,0" => "商务咨询部",
		"9,0,0" => "研究院"
	);
	public $news_channel_array = array(
		"01" => "综合资讯",
		"02" => "钢材频道",
		"2" => "钢材频道",
		"3" => "炉料频道",
		"04" => "炉料频道",
		"05" => "英文频道",
		"08" => "有色金属",
		"11" => "煤焦频道",
		"12" => "特钢频道",
		"15" => "铁合金频道",
		"10" => "不锈钢频道",
		"17" => "化工频道",
		"19" => "水泥频道",
		"25" => "新能源频道",
		"26" => "铁矿石频道",
		"27" => "废钢频道"
	);
	public $market_channel_array = array(
		"01" => "综合资讯",
		"02" => "钢材频道",
		"2" => "钢材频道",
		"3" => "炉料频道",
		"04" => "炉料频道",
		"05" => "英文频道",
		"08" => "有色金属",
		"11" => "煤焦频道",
		"12" => "特钢频道",
		"15" => "铁合金频道",
		"10" => "不锈钢频道",
		"17" => "化工频道",
		"19" => "水泥频道",
		"25" => "新能源频道",
		"26" => "炉料频道", // 铁矿石
		"27" => "炉料频道" // 废钢
	);
	public function __construct()
	{
		parent::__construct();
	}
	function cmp($a, $b)
	{
		$order = ["华东区", "北方区＆商务咨询部", "中西部", "特钢部", "煤焦部", "钢铁原料部", "合金辅料部", "研究院", "国际部", "其他", "合计"];
		$posA = array_search($a, $order);
		$posB = array_search($b, $order);
		if ($posA === false)
			return 1;
		if ($posB === false)
			return -1;
		return $posA - $posB;
	}
	function cmpChannel($a, $b)
	{
		$order = ["综合资讯", "钢材频道", "炉料频道", "英文频道", "有色金属", "煤焦频道", "特钢频道", "铁合金频道", "不锈钢频道", "化工频道", "水泥频道", "新能源频道", "其他", "合计"];
		$posA = array_search($a, $order);
		$posB = array_search($b, $order);
		if ($posA === false)
			return 1;
		if ($posB === false)
			return -1;
		return $posA - $posB;
	}
	function getAndHandleData(&$that, $date, &$dept_arr, &$dept_data, $force)
	{
		if (!(($force && $force == '1') || $date == date('Y-m-d'))) {
			$sql = "SELECT * FROM `data_kanban` WHERE `calculation_date` = ? AND `dimension` = 'day' LIMIT 1";
			$result = $that->_dao->query($sql, [$date]);
			if (count($result) > 0) {
				$dept_data['资讯上传量'] = json_decode($result[0]['news_publish'], true);
				$dept_data['价格上传条数'] = json_decode($result[0]['price_publish'], true);
				$dept_data['资讯访问量'] = json_decode($result[0]['news_access'], true);
				$dept_data['行情上传条数'] = json_decode($result[0]['market_publish'], true);
				$dept_data['行情访问量'] = json_decode($result[0]['market_access'], true);
				return;
			}
		}
		$that->calcMarketPushNum($dept_arr, $date, $date, $dept_data);
		$that->calcNewsPushNum($dept_arr, $date, $date, $dept_data);

		$sql = "SELECT * FROM `data_kanban` WHERE `calculation_date` = ? AND `dimension` = 'day' LIMIT 1";
		$result = $that->_dao->query($sql, [$date]);
		if (count($result) == 0) {
			$sql = "INSERT INTO `data_kanban` (
				`calculation_date`,
				`news_publish`,
				`price_publish`,
				`market_publish`,
				`news_access`,
				`market_access`,
				`dimension`
			) VALUES (?,?,?,?,?,?,?)";
			$that->_dao->execute($sql, [
				$date,
				json_encode($dept_data['资讯上传量']),
				json_encode($dept_data['价格上传条数']),
				json_encode($dept_data['行情上传条数']),
				json_encode($dept_data['资讯访问量']),
				json_encode($dept_data['行情访问量']),
				'day'
			]);
		} else {
			$sql = "UPDATE `data_kanban` SET `news_publish` = ?, `price_publish` = ?, `market_publish` = ?, `news_access` = ?, `market_access` = ? WHERE `calculation_date` = ? AND `dimension` = 'day'";
			$that->_dao->execute($sql, [
				json_encode($dept_data['资讯上传量']),
				json_encode($dept_data['价格上传条数']),
				json_encode($dept_data['行情上传条数']),
				json_encode($dept_data['资讯访问量']),
				json_encode($dept_data['行情访问量']),
				$date
			]);
		}
	}
	function getAndHandleChannelData(&$that, $date, &$dept_data, $force)
	{
		if (!(($force && $force == '1') || $date == date('Y-m-d'))) {
			$sql = "SELECT * FROM `data_kanban_channel` WHERE `calculation_date` = ? AND `dimension` = 'day' LIMIT 1";
			$result = $that->_dao->query($sql, [$date]);
			if (count($result) > 0) {
				$dept_data['资讯上传量'] = json_decode($result[0]['news_publish'], true);
				$dept_data['价格上传条数'] = json_decode($result[0]['price_publish'], true);
				$dept_data['资讯访问量'] = json_decode($result[0]['news_access'], true);
				$dept_data['行情上传条数'] = json_decode($result[0]['market_publish'], true);
				$dept_data['行情访问量'] = json_decode($result[0]['market_access'], true);
				return;
			}
		}
		$that->calcChannelMarketPushNum($date, $date, $dept_data);
		$that->calcChannelNewsPushNum($date, $date, $dept_data);

		$sql = "SELECT * FROM `data_kanban_channel` WHERE `calculation_date` = ? AND `dimension` = 'day' LIMIT 1";
		$result = $that->_dao->query($sql, [$date]);
		if (count($result) == 0) {
			$sql = "INSERT INTO `data_kanban_channel` (
				`calculation_date`,
				`news_publish`,
				`price_publish`,
				`market_publish`,
				`news_access`,
				`market_access`,
				`dimension`
			) VALUES (?,?,?,?,?,?,?)";
			$that->_dao->execute($sql, [
				$date,
				json_encode($dept_data['资讯上传量']),
				json_encode($dept_data['价格上传条数']),
				json_encode($dept_data['行情上传条数']),
				json_encode($dept_data['资讯访问量']),
				json_encode($dept_data['行情访问量']),
				'day'
			]);
		} else {
			$sql = "UPDATE `data_kanban_channel` SET `news_publish` = ?, `price_publish` = ?, `market_publish` = ?, `news_access` = ?, `market_access` = ? WHERE `calculation_date` = ? AND `dimension` = 'day'";
			$that->_dao->execute($sql, [
				json_encode($dept_data['资讯上传量']),
				json_encode($dept_data['价格上传条数']),
				json_encode($dept_data['行情上传条数']),
				json_encode($dept_data['资讯访问量']),
				json_encode($dept_data['行情访问量']),
				$date
			]);
		}
	}
	public function updateMonthlyData(&$that, $current_month, &$monthly_data)
	{
		$sql = "SELECT * FROM `data_kanban` WHERE `calculation_date` = ? AND `dimension` = 'month' LIMIT 1";
		$result = $that->_dao->query($sql, [$current_month]);
		if (count($result) == 0) {
			$sql = "INSERT INTO `data_kanban` (
						`calculation_date`,
						`news_publish`,
						`price_publish`,
						`market_publish`,
						`news_access`,
						`market_access`,
						`dimension`
					) VALUES (?,?,?,?,?,?,?)";
			$that->_dao->execute($sql, [
				$current_month,
				json_encode($monthly_data['资讯上传量']),
				json_encode($monthly_data['价格上传条数']),
				json_encode($monthly_data['行情上传条数']),
				json_encode($monthly_data['资讯访问量']),
				json_encode($monthly_data['行情访问量']),
				'month'
			]);
		} else {
			$sql = "UPDATE `data_kanban` SET `news_publish` = ?, `price_publish` = ?, `market_publish` = ?, `news_access` = ?, `market_access` = ? WHERE `calculation_date` = ? AND `dimension` = 'month'";
			$that->_dao->execute($sql, [
				json_encode($monthly_data['资讯上传量']),
				json_encode($monthly_data['价格上传条数']),
				json_encode($monthly_data['行情上传条数']),
				json_encode($monthly_data['资讯访问量']),
				json_encode($monthly_data['行情访问量']),
				$current_month
			]);
		}
	}
	public function updateMonthlyChannelData(&$that, $current_month, &$monthly_data)
	{
		$sql = "SELECT * FROM `data_kanban_channel` WHERE `calculation_date` = ? AND `dimension` = 'month' LIMIT 1";
		$result = $that->_dao->query($sql, [$current_month]);
		if (count($result) == 0) {
			$sql = "INSERT INTO `data_kanban_channel` (
						`calculation_date`,
						`news_publish`,
						`price_publish`,
						`market_publish`,
						`news_access`,
						`market_access`,
						`dimension`
					) VALUES (?,?,?,?,?,?,?)";
			$that->_dao->execute($sql, [
				$current_month,
				json_encode($monthly_data['资讯上传量']),
				json_encode($monthly_data['价格上传条数']),
				json_encode($monthly_data['行情上传条数']),
				json_encode($monthly_data['资讯访问量']),
				json_encode($monthly_data['行情访问量']),
				'month'
			]);
		} else {
			$sql = "UPDATE `data_kanban_channel` SET `news_publish` = ?, `price_publish` = ?, `market_publish` = ?, `news_access` = ?, `market_access` = ? WHERE `calculation_date` = ? AND `dimension` = 'month'";
			$that->_dao->execute($sql, [
				json_encode($monthly_data['资讯上传量']),
				json_encode($monthly_data['价格上传条数']),
				json_encode($monthly_data['行情上传条数']),
				json_encode($monthly_data['资讯访问量']),
				json_encode($monthly_data['行情访问量']),
				$current_month
			]);
		}
	}
	// 
	public function calcData($params)
	{
		if (empty($params['date'])) {
			$this->responseError("必须传入开始和结束日期！");
			return;
		}
		if (empty($params['dimension'])) {
			$this->responseError("必须传入统计的时间维度！");
			return;
		}
		$all_dept_arr = $this->_dao->query("SELECT * FROM `dept`");
		$dept_arr = array();
		$dept_array = $this->dept_array;
		foreach ($all_dept_arr as $dept_item) {
			$bm_id = $dept_item['did'] . "," . $dept_item['dzid'] . "," . $dept_item['dzzid'];
			foreach ($dept_array as $dk => $dv) {
				if ($bm_id == $dk) {
					$dept_arr[] = $dept_item;
				}
			}
		}
		$dept_data = [];
		if ($params['dimension'] === 'day') {
			$this->getAndHandleData($this, $params['date'], $dept_arr, $dept_data, $params['force']);
			foreach ($dept_data as $childKey => &$childData) {
				uksort($childData, [$this, 'cmp']);
				if (empty($childData['其他'])) {
					$childData['其他'] = 0;
				}
			}
		} else if ($params['dimension'] === 'month') {
			$year = (int) explode("-", $params['date'])[0];
			$month = (int) explode("-", $params['date'])[1];
			$days_in_month = date('t', mktime(0, 0, 0, $month, 1, $year));
			$monthly_data = [];
			$current_month = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
			for ($day = 1; $day <= $days_in_month; $day++) {
				$current_date = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-' . str_pad($day, 2, '0', STR_PAD_LEFT);
				$current_data = [];
				$this->getAndHandleData($this, $current_date, $dept_arr, $current_data, $params['force']);
				foreach ($current_data as $childKey => &$childData) {
					uksort($childData, [$this, 'cmp']);
					if (empty($childData['其他'])) {
						$childData['其他'] = 0;
					}
				}
				$dept_data[$current_date] = $current_data;
				foreach ($current_data as $type => $type_value) {
					foreach ($type_value as $dept => $value) {
						if ($monthly_data[$type][$dept]) {
							$monthly_data[$type][$dept] += $value;
						} else {
							$monthly_data[$type][$dept] = $value;
						}
					}
				}
			}
			$this->updateMonthlyData($this, $current_month, $monthly_data);
		} else if ($params['dimension'] === 'year') {
			$year = (int) explode("-", $params['date'])[0];
			for ($month = 1; $month <= 12; $month++) {
				$days_in_month = date('t', mktime(0, 0, 0, $month, 1, $year));
				$current_month = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
				$monthly_data = [];

				// 尝试获取月度数据
				$exist_monthly_data = false;
				if (!($params['force'] && $params['force'] == '1')) {
					$sql = "SELECT * FROM `data_kanban` WHERE `calculation_date` = ? AND `dimension` = 'month' LIMIT 1";
					$result = $this->_dao->query($sql, [$current_month]);
					if (count($result) > 0) {
						$monthly_data['资讯上传量'] = json_decode($result[0]['news_publish'], true);
						$monthly_data['价格上传条数'] = json_decode($result[0]['price_publish'], true);
						$monthly_data['资讯访问量'] = json_decode($result[0]['news_access'], true);
						$monthly_data['行情上传条数'] = json_decode($result[0]['market_publish'], true);
						$monthly_data['行情访问量'] = json_decode($result[0]['market_access'], true);
						foreach ($monthly_data as $childKey => &$childData) {
							uksort($childData, [$this, 'cmp']);
							if (empty($childData['其他'])) {
								$childData['其他'] = 0;
							}
						}
						$exist_monthly_data = true;
					}
				}
				if (!$exist_monthly_data) {
					for ($day = 1; $day <= $days_in_month; $day++) {
						$current_date = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-' . str_pad($day, 2, '0', STR_PAD_LEFT);
						$current_data = [];
						$this->getAndHandleData($this, $current_date, $dept_arr, $current_data, $params['force']);
						foreach ($current_data as $childKey => &$childData) {
							uksort($childData, [$this, 'cmp']);
							if (empty($childData['其他'])) {
								$childData['其他'] = 0;
							}
						}
						foreach ($current_data as $type => $type_value) {
							foreach ($type_value as $dept => $value) {
								if ($monthly_data[$type][$dept]) {
									$monthly_data[$type][$dept] += $value;
								} else {
									$monthly_data[$type][$dept] = $value;
								}
							}
						}
					}

					// 更新月度数据
					$this->updateMonthlyData($this, $current_month, $monthly_data);
				}

				$dept_data[$current_month] = $monthly_data;
			}
		}
		$this->responseSuccess($dept_data, $params['dimension']);
	}
	public function calcChannelData($params)
	{
		if (empty($params['date'])) {
			$this->responseError("必须传入开始和结束日期！");
			return;
		}
		if (empty($params['dimension'])) {
			$this->responseError("必须传入统计的时间维度！");
			return;
		}
		$dept_data = [];
		if ($params['dimension'] === 'day') {
			$this->getAndHandleChannelData($this, $params['date'], $dept_data, $params['force']);
			foreach ($dept_data as $childKey => &$childData) {
				uksort($childData, [$this, 'cmpChannel']);
				if (empty($childData['其他'])) {
					$childData['其他'] = 0;
				}
			}
		} else if ($params['dimension'] === 'month') {
			$year = (int) explode("-", $params['date'])[0];
			$month = (int) explode("-", $params['date'])[1];
			$days_in_month = date('t', mktime(0, 0, 0, $month, 1, $year));
			$monthly_data = [];
			$current_month = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
			for ($day = 1; $day <= $days_in_month; $day++) {
				$current_date = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-' . str_pad($day, 2, '0', STR_PAD_LEFT);
				$current_data = [];
				$this->getAndHandleChannelData($this, $current_date, $current_data, $params['force']);
				foreach ($current_data as $childKey => &$childData) {
					uksort($childData, [$this, 'cmpChannel']);
					if (empty($childData['其他'])) {
						$childData['其他'] = 0;
					}
				}
				$dept_data[$current_date] = $current_data;
				foreach ($current_data as $type => $type_value) {
					foreach ($type_value as $dept => $value) {
						if ($monthly_data[$type][$dept]) {
							$monthly_data[$type][$dept] += $value;
						} else {
							$monthly_data[$type][$dept] = $value;
						}
					}
				}
			}
			$this->updateMonthlyChannelData($this, $current_month, $monthly_data);
		} else if ($params['dimension'] === 'year') {
			$year = (int) explode("-", $params['date'])[0];
			for ($month = 1; $month <= 12; $month++) {
				$days_in_month = date('t', mktime(0, 0, 0, $month, 1, $year));
				$current_month = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01';
				$monthly_data = [];

				// 尝试获取月度数据
				$exist_monthly_data = false;
				if (!($params['force'] && $params['force'] == '1')) {
					$sql = "SELECT * FROM `data_kanban_channel` WHERE `calculation_date` = ? AND `dimension` = 'month' LIMIT 1";
					$result = $this->_dao->query($sql, [$current_month]);
					if (count($result) > 0) {
						$monthly_data['资讯上传量'] = json_decode($result[0]['news_publish'], true);
						$monthly_data['价格上传条数'] = json_decode($result[0]['price_publish'], true);
						$monthly_data['资讯访问量'] = json_decode($result[0]['news_access'], true);
						$monthly_data['行情上传条数'] = json_decode($result[0]['market_publish'], true);
						$monthly_data['行情访问量'] = json_decode($result[0]['market_access'], true);
						foreach ($monthly_data as $childKey => &$childData) {
							uksort($childData, [$this, 'cmpChannel']);
							if (empty($childData['其他'])) {
								$childData['其他'] = 0;
							}
						}
						$exist_monthly_data = true;
					}
				}
				if (!$exist_monthly_data) {
					for ($day = 1; $day <= $days_in_month; $day++) {
						$current_date = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-' . str_pad($day, 2, '0', STR_PAD_LEFT);
						$current_data = [];
						$this->getAndHandleChannelData($this, $current_date, $current_data, $params['force']);
						foreach ($current_data as $childKey => &$childData) {
							uksort($childData, [$this, 'cmpChannel']);
							if (empty($childData['其他'])) {
								$childData['其他'] = 0;
							}
						}
						foreach ($current_data as $type => $type_value) {
							foreach ($type_value as $dept => $value) {
								if ($monthly_data[$type][$dept]) {
									$monthly_data[$type][$dept] += $value;
								} else {
									$monthly_data[$type][$dept] = $value;
								}
							}
						}
					}

					// 更新月度数据
					$this->updateMonthlyChannelData($this, $current_month, $monthly_data);
				}

				$dept_data[$current_month] = $monthly_data;
			}
		}
		$this->responseSuccess($dept_data, $params['dimension']);
	}
	//行情
	public function calcMarketPushNum(&$depts, $start_datetime, $end_datetime, &$dept_market_push_num)
	{
		// 行情的上传量
		$dept_array = $this->dept_array;
		// 行情条数上传量
		$sql = "SELECT `id`,`manageid` FROM `marketrecord` where managedate >= '" . $start_datetime . " 00:00:00' and managedate <= '" . $end_datetime . " 23:59:59' AND `isnews` = 1";
		$market_publish_total = $this->_dao->query($sql);
		$sql = "SELECT `marketrecordid`, count(*) as `count` FROM marketconditions where mconmanagedate >= '" . $start_datetime . " 00:00:00' and mconmanagedate <= '" . $end_datetime . " 23:59:59' and `isview` = 0 GROUP BY `marketrecordid`";
		$conditions_total = $this->_dao->query($sql);
		foreach ($market_publish_total as $market_item) {
			$dept_name = null;
			foreach ($depts as $dept_admin) {
				if ($market_item["manageid"] == $dept_admin["userid"]) {
					$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
					if ($dept_market_push_num['行情上传条数'][$dept_name]) {
						$dept_market_push_num['行情上传条数'][$dept_name]++;
						if ($dept_market_push_num['行情上传条数']["合计"]) {
							$dept_market_push_num['行情上传条数']["合计"]++;
						} else {
							$dept_market_push_num['行情上传条数']["合计"] = 1;
						}
					} else {
						$dept_market_push_num['行情上传条数'][$dept_name] = 1;
						if ($dept_market_push_num['行情上传条数']["合计"]) {
							$dept_market_push_num['行情上传条数']["合计"]++;
						} else {
							$dept_market_push_num['行情上传条数']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$dept_name) {
				$dept_name = '其他';
				if ($dept_market_push_num['行情上传条数']["其他"]) {
					$dept_market_push_num['行情上传条数']["其他"]++;
				} else {
					$dept_market_push_num['行情上传条数']["其他"] = 1;
				}
				if ($dept_market_push_num['行情上传条数']["合计"]) {
					$dept_market_push_num['行情上传条数']["合计"]++;
				} else {
					$dept_market_push_num['行情上传条数']["合计"] = 1;
				}
			}
			foreach ($conditions_total as $condition) {
				if (($market_item["id"] == $condition["marketrecordid"]) && $dept_name) {
					if ($dept_market_push_num['价格上传条数'][$dept_name]) {
						$dept_market_push_num['价格上传条数'][$dept_name] += $condition['count'];
						if ($dept_market_push_num['价格上传条数']["合计"]) {
							$dept_market_push_num['价格上传条数']["合计"] += $condition['count'];
						} else {
							$dept_market_push_num['价格上传条数']["合计"] = $condition['count'];
						}
					} else {
						$dept_market_push_num['价格上传条数'][$dept_name] = $condition['count'];
						if ($dept_market_push_num['价格上传条数']["合计"]) {
							$dept_market_push_num['价格上传条数']["合计"] += $condition['count'];
						} else {
							$dept_market_push_num['价格上传条数']["合计"] = $condition['count'];
						}
					}
					break;
				}
			}
		}

		// // 行情访问量
		$sql = 'SELECT `memberip`, `newsid`, `obj`, `nmanageid` FROM steelhome.';

		if ($start_datetime >= '2022-01-01') {
			$sql .= 'memberaction';
		} else if ($start_datetime >= '2021-01-01') {
			$sql .= 'memberaction_2021';
		} else if ($start_datetime >= '2020-01-01') {
			$sql .= 'memberaction_2020';
		} else if ($start_datetime >= '2019-01-01') {
			$sql .= 'memberaction_2019';
		} else if ($start_datetime >= '2018-01-01') {
			$sql .= 'memberaction_2018';
		} else if ($start_datetime >= '2017-01-01') {
			$sql .= 'memberaction_2017';
		} else {
			$sql .= 'memberaction_h';
		}

		$sql .= ' where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "行情" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$market_access_total = $this->_dao->query($sql);
		foreach ($market_access_total as $market_item) {
			$dept_name = null;
			foreach ($depts as $dept_admin) {
				if ($market_item["nmanageid"] == $dept_admin["userid"]) {
					$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
					if ($dept_market_push_num['行情访问量'][$dept_name]) {
						$dept_market_push_num['行情访问量'][$dept_name] += 1;
						if ($dept_market_push_num['行情访问量']["合计"]) {
							$dept_market_push_num['行情访问量']["合计"] += 1;
						} else {
							$dept_market_push_num['行情访问量']["合计"] = 1;
						}
					} else {
						$dept_market_push_num['行情访问量'][$dept_name] = 1;
						if ($dept_market_push_num['行情访问量']["合计"]) {
							$dept_market_push_num['行情访问量']["合计"] += 1;
						} else {
							$dept_market_push_num['行情访问量']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$dept_name) {
				if ($dept_market_push_num['行情访问量']["其他"]) {
					$dept_market_push_num['行情访问量']["其他"]++;
				} else {
					$dept_market_push_num['行情访问量']["其他"] = 1;
				}
				if ($dept_market_push_num['行情访问量']["合计"]) {
					$dept_market_push_num['行情访问量']["合计"] += 1;
				} else {
					$dept_market_push_num['行情访问量']["合计"] = 1;
				}
			}
		}
		// 英文
		$sql = "SELECT `nid` FROM steelhomeen.news where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59' and ncolumnid = '002,021'";
		$markets_published = $this->_dao->query($sql);
		$sql = "SELECT count(*) as `count` FROM steelhomeen.news_variety where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59'";
		$conditions_total = $this->_dao->getOne($sql);
		if ($dept_market_push_num['行情上传条数']["合计"]) {
			$dept_market_push_num['行情上传条数']["合计"] += count($markets_published);
		} else {
			$dept_market_push_num['行情上传条数']["合计"] = count($markets_published);
		}
		if ($dept_market_push_num['行情上传条数']["国际部"]) {
			$dept_market_push_num['行情上传条数']["国际部"] += count($markets_published);
		} else {
			$dept_market_push_num['行情上传条数']["国际部"] = count($markets_published);
		}
		if ($dept_market_push_num['价格上传条数']["合计"]) {
			$dept_market_push_num['价格上传条数']["合计"] += $conditions_total;
		} else {
			$dept_market_push_num['价格上传条数']["合计"] = $conditions_total;
		}
		if ($dept_market_push_num['价格上传条数']["国际部"]) {
			$dept_market_push_num['价格上传条数']["国际部"] += $conditions_total;
		} else {
			$dept_market_push_num['价格上传条数']["国际部"] = $conditions_total;
		}
		$dept_market_push_num['行情上传条数']["北方区＆商务咨询部"] = $dept_market_push_num['行情上传条数']["北方区"] + $dept_market_push_num['行情上传条数']["商务咨询部"];
		$dept_market_push_num['价格上传条数']["北方区＆商务咨询部"] = $dept_market_push_num['价格上传条数']["北方区"] + $dept_market_push_num['价格上传条数']["商务咨询部"];
		unset($dept_market_push_num['行情上传条数']["北方区"]);
		unset($dept_market_push_num['行情上传条数']["商务咨询部"]);
		unset($dept_market_push_num['价格上传条数']["北方区"]);
		unset($dept_market_push_num['价格上传条数']["商务咨询部"]);
		// 英文访问量只能去查 memberaction
		$sql = 'SELECT `memberip`,`newsid`,`obj`,`nmanageid` FROM steelhomeen.memberaction where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and title like "%Market Price%" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$news_fwl = count($this->_dao->query($sql));
		if ($dept_market_push_num['行情访问量']["合计"]) {
			$dept_market_push_num['行情访问量']["合计"] += $news_fwl;
		} else {
			$dept_market_push_num['行情访问量']["合计"] = $news_fwl;
		}
		if ($dept_market_push_num['行情访问量']["国际部"]) {
			$dept_market_push_num['行情访问量']["国际部"] += $news_fwl;
		} else {
			$dept_market_push_num['行情访问量']["国际部"] = $news_fwl;
		}
		$dept_market_push_num['行情访问量']["北方区＆商务咨询部"] = $dept_market_push_num['行情访问量']["北方区"] + $dept_market_push_num['行情访问量']["商务咨询部"];
		unset($dept_market_push_num['行情访问量']["北方区"]);
		unset($dept_market_push_num['行情访问量']["商务咨询部"]);
	}
	public function calcChannelMarketPushNum($start_datetime, $end_datetime, &$channel_market_push_num)
	{
		// 行情的上传量
		$channel_array = $this->market_channel_array;
		// 行情条数上传量
		$sql = "SELECT `id`,`channelid` FROM `marketrecord` where managedate >= '" . $start_datetime . " 00:00:00' and managedate <= '" . $end_datetime . " 23:59:59' AND `isnews` = 1";
		$market_publish_total = $this->_dao->query($sql);
		$sql = "SELECT `marketrecordid`, count(*) as `count` FROM marketconditions where mconmanagedate >= '" . $start_datetime . " 00:00:00' and mconmanagedate <= '" . $end_datetime . " 23:59:59' and `isview` = 0 GROUP BY `marketrecordid`";
		$conditions_total = $this->_dao->query($sql);
		foreach ($market_publish_total as $market_item) {
			$channel_name = null;
			foreach ($channel_array as $channel_id => $channel_label) {
				if ($market_item["channelid"] == $channel_id) {
					$channel_name = $channel_label;
					if ($channel_market_push_num['行情上传条数'][$channel_name]) {
						$channel_market_push_num['行情上传条数'][$channel_name]++;
						if ($channel_market_push_num['行情上传条数']["合计"]) {
							$channel_market_push_num['行情上传条数']["合计"]++;
						} else {
							$channel_market_push_num['行情上传条数']["合计"] = 1;
						}
					} else {
						$channel_market_push_num['行情上传条数'][$channel_name] = 1;
						if ($channel_market_push_num['行情上传条数']["合计"]) {
							$channel_market_push_num['行情上传条数']["合计"]++;
						} else {
							$channel_market_push_num['行情上传条数']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$channel_name) {
				$channel_name = '其他';
				if ($channel_market_push_num['行情上传条数']["其他"]) {
					$channel_market_push_num['行情上传条数']["其他"]++;
				} else {
					$channel_market_push_num['行情上传条数']["其他"] = 1;
				}
				if ($channel_market_push_num['行情上传条数']["合计"]) {
					$channel_market_push_num['行情上传条数']["合计"]++;
				} else {
					$channel_market_push_num['行情上传条数']["合计"] = 1;
				}
			}
			foreach ($conditions_total as $condition) {
				if (($market_item["id"] == $condition["marketrecordid"]) && $channel_name) {
					if ($channel_market_push_num['价格上传条数'][$channel_name]) {
						$channel_market_push_num['价格上传条数'][$channel_name] += $condition['count'];
						if ($channel_market_push_num['价格上传条数']["合计"]) {
							$channel_market_push_num['价格上传条数']["合计"] += $condition['count'];
						} else {
							$channel_market_push_num['价格上传条数']["合计"] = $condition['count'];
						}
					} else {
						$channel_market_push_num['价格上传条数'][$channel_name] = $condition['count'];
						if ($channel_market_push_num['价格上传条数']["合计"]) {
							$channel_market_push_num['价格上传条数']["合计"] += $condition['count'];
						} else {
							$channel_market_push_num['价格上传条数']["合计"] = $condition['count'];
						}
					}
					break;
				}
			}
		}

		// // 行情访问量
		$sql = 'SELECT `memberip`, `newsid`, `obj`, `channelid` FROM steelhome.';

		if ($start_datetime >= '2022-01-01') {
			$sql .= 'memberaction';
		} else if ($start_datetime >= '2021-01-01') {
			$sql .= 'memberaction_2021';
		} else if ($start_datetime >= '2020-01-01') {
			$sql .= 'memberaction_2020';
		} else if ($start_datetime >= '2019-01-01') {
			$sql .= 'memberaction_2019';
		} else if ($start_datetime >= '2018-01-01') {
			$sql .= 'memberaction_2018';
		} else if ($start_datetime >= '2017-01-01') {
			$sql .= 'memberaction_2017';
		} else {
			$sql .= 'memberaction_h';
		}

		$sql .= ' where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "行情" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$market_access_total = $this->_dao->query($sql);
		foreach ($market_access_total as $market_item) {
			$channel_name = null;
			foreach ($channel_array as $channel_id => $channel_label) {
				if ($market_item["channelid"] == $channel_id) {
					$channel_name = $channel_label;
					if ($channel_market_push_num['行情访问量'][$channel_name]) {
						$channel_market_push_num['行情访问量'][$channel_name] += 1;
						if ($channel_market_push_num['行情访问量']["合计"]) {
							$channel_market_push_num['行情访问量']["合计"] += 1;
						} else {
							$channel_market_push_num['行情访问量']["合计"] = 1;
						}
					} else {
						$channel_market_push_num['行情访问量'][$channel_name] = 1;
						if ($channel_market_push_num['行情访问量']["合计"]) {
							$channel_market_push_num['行情访问量']["合计"] += 1;
						} else {
							$channel_market_push_num['行情访问量']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$channel_name) {
				if ($channel_market_push_num['行情访问量']["其他"]) {
					$channel_market_push_num['行情访问量']["其他"]++;
				} else {
					$channel_market_push_num['行情访问量']["其他"] = 1;
				}
				if ($channel_market_push_num['行情访问量']["合计"]) {
					$channel_market_push_num['行情访问量']["合计"] += 1;
				} else {
					$channel_market_push_num['行情访问量']["合计"] = 1;
				}
			}
		}
		// 英文
		$sql = "SELECT `nid` FROM steelhomeen.news where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59' and ncolumnid = '002,021'";
		$markets_published = $this->_dao->query($sql);
		$sql = "SELECT count(*) as `count` FROM steelhomeen.news_variety where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59'";
		$conditions_total = $this->_dao->getOne($sql);
		if ($channel_market_push_num['行情上传条数']["合计"]) {
			$channel_market_push_num['行情上传条数']["合计"] += count($markets_published);
		} else {
			$channel_market_push_num['行情上传条数']["合计"] = count($markets_published);
		}
		if ($channel_market_push_num['行情上传条数']["英文频道"]) {
			$channel_market_push_num['行情上传条数']["英文频道"] += count($markets_published);
		} else {
			$channel_market_push_num['行情上传条数']["英文频道"] = count($markets_published);
		}
		if ($channel_market_push_num['价格上传条数']["合计"]) {
			$channel_market_push_num['价格上传条数']["合计"] += $conditions_total;
		} else {
			$channel_market_push_num['价格上传条数']["合计"] = $conditions_total;
		}
		if ($channel_market_push_num['价格上传条数']["英文频道"]) {
			$channel_market_push_num['价格上传条数']["英文频道"] += $conditions_total;
		} else {
			$channel_market_push_num['价格上传条数']["英文频道"] = $conditions_total;
		}

		// 英文访问量只能去查 memberaction
		$sql = 'SELECT `memberip`,`newsid`,`obj`,`nmanageid` FROM steelhomeen.memberaction where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and title like "%Market Price%" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$news_fwl = count($this->_dao->query($sql));
		if ($channel_market_push_num['行情访问量']["合计"]) {
			$channel_market_push_num['行情访问量']["合计"] += $news_fwl;
		} else {
			$channel_market_push_num['行情访问量']["合计"] = $news_fwl;
		}
		if ($channel_market_push_num['行情访问量']["英文频道"]) {
			$channel_market_push_num['行情访问量']["英文频道"] += $news_fwl;
		} else {
			$channel_market_push_num['行情访问量']["英文频道"] = $news_fwl;
		}
	}
	//资讯
	public function calcNewsPushNum(&$depts, $start_datetime, $end_datetime, &$dept_market_push_num)
	{
		$dept_array = $this->dept_array;
		$sql = "SELECT `nmanageid` FROM `news` where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59'";
		$news_publish_total = $this->_dao->query($sql);
		foreach ($news_publish_total as $news_item) {
			$dept_name = null;
			foreach ($depts as $dept_admin) {
				if ($news_item["nmanageid"] == $dept_admin["userid"]) {
					$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
					if ($dept_market_push_num['资讯上传量'][$dept_name]) {
						$dept_market_push_num['资讯上传量'][$dept_name]++;
						if ($dept_market_push_num['资讯上传量']["合计"]) {
							$dept_market_push_num['资讯上传量']["合计"]++;
						} else {
							$dept_market_push_num['资讯上传量']["合计"] = 1;
						}
					} else {
						$dept_market_push_num['资讯上传量'][$dept_name] = 1;
						if ($dept_market_push_num['资讯上传量']["合计"]) {
							$dept_market_push_num['资讯上传量']["合计"]++;
						} else {
							$dept_market_push_num['资讯上传量']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$dept_name) {
				if ($dept_market_push_num['资讯上传量']["其他"]) {
					$dept_market_push_num['资讯上传量']["其他"]++;
				} else {
					$dept_market_push_num['资讯上传量']["其他"] = 1;
				}
				if ($dept_market_push_num['资讯上传量']["合计"]) {
					$dept_market_push_num['资讯上传量']["合计"]++;
				} else {
					$dept_market_push_num['资讯上传量']["合计"] = 1;
				}
			}
		}

		// 资讯访问量
		$sql = 'SELECT `memberip`, `newsid`, `obj`, `nmanageid` FROM steelhome.';

		if ($start_datetime >= '2022-01-01') {
			$sql .= 'memberaction';
		} else if ($start_datetime >= '2021-01-01') {
			$sql .= 'memberaction_2021';
		} else if ($start_datetime >= '2020-01-01') {
			$sql .= 'memberaction_2020';
		} else if ($start_datetime >= '2019-01-01') {
			$sql .= 'memberaction_2019';
		} else if ($start_datetime >= '2018-01-01') {
			$sql .= 'memberaction_2018';
		} else if ($start_datetime >= '2017-01-01') {
			$sql .= 'memberaction_2017';
		} else {
			$sql .= 'memberaction_h';
		}

		$sql .= ' where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$news_access_total = $this->_dao->query($sql);
		foreach ($news_access_total as $news_item) {
			$dept_name = null;
			foreach ($depts as $dept_admin) {
				if ($news_item["nmanageid"] == $dept_admin["userid"]) {
					$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
					if ($dept_market_push_num['资讯访问量'][$dept_name]) {
						$dept_market_push_num['资讯访问量'][$dept_name] += 1;
						if ($dept_market_push_num['资讯访问量']["合计"]) {
							$dept_market_push_num['资讯访问量']["合计"] += 1;
						} else {
							$dept_market_push_num['资讯访问量']["合计"] = 1;
						}
					} else {
						$dept_market_push_num['资讯访问量'][$dept_name] = 1;
						if ($dept_market_push_num['资讯访问量']["合计"]) {
							$dept_market_push_num['资讯访问量']["合计"] += 1;
						} else {
							$dept_market_push_num['资讯访问量']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$dept_name) {
				if ($dept_market_push_num['资讯访问量']["其他"]) {
					$dept_market_push_num['资讯访问量']["其他"]++;
				} else {
					$dept_market_push_num['资讯访问量']["其他"] = 1;
				}
				if ($dept_market_push_num['资讯访问量']["合计"]) {
					$dept_market_push_num['资讯访问量']["合计"] += 1;
				} else {
					$dept_market_push_num['资讯访问量']["合计"] = 1;
				}
			}
		}

		$sql = "SELECT count(*) as `count` FROM steelhomeen.news where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59' and ncolumnid != '002,021'";
		$news_publish_total = $this->_dao->getOne($sql);
		if ($dept_market_push_num['资讯上传量']["合计"]) {
			$dept_market_push_num['资讯上传量']["合计"] += $news_publish_total;
		} else {
			$dept_market_push_num['资讯上传量']["合计"] = $news_publish_total;
		}
		if ($dept_market_push_num['资讯上传量']["国际部"]) {
			$dept_market_push_num['资讯上传量']["国际部"] += $news_publish_total;
		} else {
			$dept_market_push_num['资讯上传量']["国际部"] = $news_publish_total;
		}
		$dept_market_push_num['资讯上传量']["北方区＆商务咨询部"] = $dept_market_push_num['资讯上传量']["北方区"] + $dept_market_push_num['资讯上传量']["商务咨询部"];
		unset($dept_market_push_num['资讯上传量']["北方区"]);
		unset($dept_market_push_num['资讯上传量']["商务咨询部"]);
		$sql = 'SELECT `memberip`,`newsid`,`obj`,`nmanageid` FROM steelhomeen.memberaction where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and title not like "%Market Price%" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$news_fwl = count($this->_dao->query($sql));
		if ($dept_market_push_num['资讯访问量']["合计"]) {
			$dept_market_push_num['资讯访问量']["合计"] += $news_fwl;
		} else {
			$dept_market_push_num['资讯访问量']["合计"] = $news_fwl;
		}
		if ($dept_market_push_num['资讯访问量']["国际部"]) {
			$dept_market_push_num['资讯访问量']["国际部"] += $news_fwl;
		} else {
			$dept_market_push_num['资讯访问量']["国际部"] = $news_fwl;
		}
		$dept_market_push_num['资讯访问量']["北方区＆商务咨询部"] = $dept_market_push_num['资讯访问量']["北方区"] + $dept_market_push_num['资讯访问量']["商务咨询部"];
		unset($dept_market_push_num['资讯访问量']["北方区"]);
		unset($dept_market_push_num['资讯访问量']["商务咨询部"]);
	}
	public function calcChannelNewsPushNum($start_datetime, $end_datetime, &$channel_market_push_num)
	{
		$channel_array = $this->news_channel_array;
		$sql = "SELECT `nchannelid` as `channelid` FROM `news` where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59'";
		$news_publish_total = $this->_dao->query($sql);
		foreach ($news_publish_total as $news_item) {
			$channel_name = null;
			foreach ($channel_array as $channel_id => $channel_label) {
				if ($news_item["channelid"] == $channel_id) {
					$channel_name = $channel_label;
					if ($channel_market_push_num['资讯上传量'][$channel_name]) {
						$channel_market_push_num['资讯上传量'][$channel_name]++;
						if ($channel_market_push_num['资讯上传量']["合计"]) {
							$channel_market_push_num['资讯上传量']["合计"]++;
						} else {
							$channel_market_push_num['资讯上传量']["合计"] = 1;
						}
					} else {
						$channel_market_push_num['资讯上传量'][$channel_name] = 1;
						if ($channel_market_push_num['资讯上传量']["合计"]) {
							$channel_market_push_num['资讯上传量']["合计"]++;
						} else {
							$channel_market_push_num['资讯上传量']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$channel_name) {
				if ($channel_market_push_num['资讯上传量']["其他"]) {
					$channel_market_push_num['资讯上传量']["其他"]++;
				} else {
					$channel_market_push_num['资讯上传量']["其他"] = 1;
				}
				if ($channel_market_push_num['资讯上传量']["合计"]) {
					$channel_market_push_num['资讯上传量']["合计"]++;
				} else {
					$channel_market_push_num['资讯上传量']["合计"] = 1;
				}
			}
		}

		// 资讯访问量
		$sql = 'SELECT `memberip`, `newsid`, `obj`, `channelid` FROM steelhome.';

		if ($start_datetime >= '2022-01-01') {
			$sql .= 'memberaction';
		} else if ($start_datetime >= '2021-01-01') {
			$sql .= 'memberaction_2021';
		} else if ($start_datetime >= '2020-01-01') {
			$sql .= 'memberaction_2020';
		} else if ($start_datetime >= '2019-01-01') {
			$sql .= 'memberaction_2019';
		} else if ($start_datetime >= '2018-01-01') {
			$sql .= 'memberaction_2018';
		} else if ($start_datetime >= '2017-01-01') {
			$sql .= 'memberaction_2017';
		} else {
			$sql .= 'memberaction_h';
		}

		$sql .= ' where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$news_access_total = $this->_dao->query($sql);
		foreach ($news_access_total as $news_item) {
			$channel_name = null;
			foreach ($channel_array as $channel_id => $channel_label) {
				if ($news_item["channelid"] == $channel_id) {
					$channel_name = $channel_label;
					if ($channel_market_push_num['资讯访问量'][$channel_name]) {
						$channel_market_push_num['资讯访问量'][$channel_name] += 1;
						if ($channel_market_push_num['资讯访问量']["合计"]) {
							$channel_market_push_num['资讯访问量']["合计"] += 1;
						} else {
							$channel_market_push_num['资讯访问量']["合计"] = 1;
						}
					} else {
						$channel_market_push_num['资讯访问量'][$channel_name] = 1;
						if ($channel_market_push_num['资讯访问量']["合计"]) {
							$channel_market_push_num['资讯访问量']["合计"] += 1;
						} else {
							$channel_market_push_num['资讯访问量']["合计"] = 1;
						}
					}
					break;
				}
			}
			if (!$channel_name) {
				if ($channel_market_push_num['资讯访问量']["其他"]) {
					$channel_market_push_num['资讯访问量']["其他"]++;
				} else {
					$channel_market_push_num['资讯访问量']["其他"] = 1;
				}
				if ($channel_market_push_num['资讯访问量']["合计"]) {
					$channel_market_push_num['资讯访问量']["合计"] += 1;
				} else {
					$channel_market_push_num['资讯访问量']["合计"] = 1;
				}
			}
		}

		$sql = "SELECT count(*) as `count` FROM steelhomeen.news where ndate >= '" . $start_datetime . " 00:00:00' and ndate <= '" . $end_datetime . " 23:59:59' and ncolumnid != '002,021'";
		$news_publish_total = $this->_dao->getOne($sql);
		if ($channel_market_push_num['资讯上传量']["合计"]) {
			$channel_market_push_num['资讯上传量']["合计"] += $news_publish_total;
		} else {
			$channel_market_push_num['资讯上传量']["合计"] = $news_publish_total;
		}
		if ($channel_market_push_num['资讯上传量']["英文频道"]) {
			$channel_market_push_num['资讯上传量']["英文频道"] += $news_publish_total;
		} else {
			$channel_market_push_num['资讯上传量']["英文频道"] = $news_publish_total;
		}
		$sql = 'SELECT `memberip`,`newsid`,`obj`,`nmanageid` FROM steelhomeen.memberaction where acttime >= "' . $start_datetime . ' 00:00:00" and acttime <= "' . $end_datetime . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and title not like "%Market Price%" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
		$news_fwl = count($this->_dao->query($sql));
		if ($channel_market_push_num['资讯访问量']["合计"]) {
			$channel_market_push_num['资讯访问量']["合计"] += $news_fwl;
		} else {
			$channel_market_push_num['资讯访问量']["合计"] = $news_fwl;
		}
		if ($channel_market_push_num['资讯访问量']["英文频道"]) {
			$channel_market_push_num['资讯访问量']["英文频道"] += $news_fwl;
		} else {
			$channel_market_push_num['资讯访问量']["英文频道"] = $news_fwl;
		}
	}
	public function calcDailyStaticData($params)
	{
		// 先把数据拿到再说
		$data_type = $params['data_type'];
		$statistic_date = $params['statistic_date'];
		$statistic_type = $params['statistic_type'];
		$statistic_value = $params['statistic_value'];

		if ($data_type === 'markets_pushlished') {
			// 行情的上传量
			$sql = "SELECT `id`, `channelid`, `manageid` FROM `marketrecord` where managedate >= '" . $statistic_date . " 00:00:00' and managedate <= '" . $statistic_date . " 23:59:59' AND `isnews` = 1";
			$market_publish_total = $this->_dao->query($sql);
			$sql = "SELECT `nid` FROM steelhomeen.news where ndate >= '" . $statistic_date . " 00:00:00' and ndate <= '" . $statistic_date . " 23:59:59' and ncolumnid = '002,021'";
			$market_publish_total_english = $this->_dao->query($sql);
			$dept_array = $this->dept_array;
			$channel_array = $this->market_channel_array;
			$all_dept_arr = $this->_dao->query("SELECT * FROM `dept`");
			$dept_arr = [];
			foreach ($all_dept_arr as $dept_item) {
				$bm_id = $dept_item['did'] . "," . $dept_item['dzid'] . "," . $dept_item['dzzid'];
				foreach ($dept_array as $dk => $dv) {
					if ($bm_id == $dk) {
						$dept_arr[] = $dept_item;
					}
				}
			}
			if ($statistic_type === 'dept') {
				$returned_data = [
					$dept_array[$statistic_value] => [],
				];
				// 对于每个发布的行情，先匹配发布人的部门，看看是不是需要返回的数据
				foreach ($market_publish_total as $market_publish_item) {
					$dept_name = null;
					$channel_name = $channel_array[$market_publish_item['channelid']];
					if (!$channel_name) {
						$channel_name = '其他';
					}
					foreach ($dept_arr as $dept_admin) {
						if ($market_publish_item["manageid"] == $dept_admin["userid"]) {
							// 能匹配上就说明 dept 表里能查到这人的部门
							$dept_name = $dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid'];
							if ($dept_name == $statistic_value) {
								// 匹配了部门，现在进行频道分类
								if ($returned_data[$dept_array[$statistic_value]][$channel_name]) {
									$returned_data[$dept_array[$statistic_value]][$channel_name] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]][$channel_name] = 1;
								}
								if ($returned_data[$dept_array[$statistic_value]]['合计']) {
									$returned_data[$dept_array[$statistic_value]]['合计'] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]]['合计'] = 1;
								}
							}
							break;
						}
					}
					// 如果是其他
					if (!$dept_name && $statistic_value === 'other') {
						// 匹配了部门，现在进行频道分类
						if ($returned_data['其他'][$channel_name]) {
							$returned_data['其他'][$channel_name] += 1;
						} else {
							$returned_data['其他'][$channel_name] = 1;
						}
						if ($returned_data['其他']['合计']) {
							$returned_data['其他']['合计'] += 1;
						} else {
							$returned_data['其他']['合计'] = 1;
						}
					}
				}
				// 英文的处理
				if ($dept_array[$statistic_value] === '国际部') {
					if ($returned_data['国际部']['英文频道']) {
						$returned_data['国际部']['英文频道'] += count($market_publish_total_english);
					} else {
						$returned_data['国际部']['英文频道'] = count($market_publish_total_english);
					}
					if ($returned_data['国际部']['合计']) {
						$returned_data['国际部']['合计'] += count($market_publish_total_english);
					} else {
						$returned_data['国际部']['合计'] = count($market_publish_total_english);
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			} else if ($statistic_type === 'channel') {
				$channel_name = $channel_array[$statistic_value];
				if (!$channel_name) {
					$channel_name = '其他';
				}
				$returned_data = [
					$channel_name => [],
				];
				// 对于每个发布的行情，先匹配行情的频道，看看是不是需要返回的数据
				foreach ($market_publish_total as $market_publish_item) {
					if ($market_publish_item["channelid"] === $statistic_value || ($channel_name === '其他' && !($channel_array[$market_publish_item["channelid"]]))) {
						// 匹配了频道，现在进行部门分类
						$dept_name = null;
						foreach ($dept_arr as $dept_admin) {
							if ($market_publish_item["manageid"] == $dept_admin["userid"]) {
								$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
								if ($returned_data[$channel_name][$dept_name]) {
									$returned_data[$channel_name][$dept_name] += 1;
								} else {
									$returned_data[$channel_name][$dept_name] = 1;
								}
								if ($returned_data[$channel_name]['合计']) {
									$returned_data[$channel_name]['合计'] += 1;
								} else {
									$returned_data[$channel_name]['合计'] = 1;
								}
								break;
							}
						}
						if (!$dept_name) {
							if ($returned_data[$channel_name]['其他']) {
								$returned_data[$channel_name]['其他'] += 1;
							} else {
								$returned_data[$channel_name]['其他'] = 1;
							}
							if ($returned_data[$channel_name]['合计']) {
								$returned_data[$channel_name]['合计'] += 1;
							} else {
								$returned_data[$channel_name]['合计'] = 1;
							}
						}
					}
				}
				// 英文的处理
				if ($channel_name === '英文频道') {
					if ($returned_data['英文频道']['国际部']) {
						$returned_data['英文频道']['国际部'] += count($market_publish_total_english);
					} else {
						$returned_data['英文频道']['国际部'] = count($market_publish_total_english);
					}
					if ($returned_data['英文频道']['合计']) {
						$returned_data['英文频道']['合计'] += count($market_publish_total_english);
					} else {
						$returned_data['英文频道']['合计'] = count($market_publish_total_english);
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			}
		} else if ($data_type === 'prices_pushlished') {
			// 价格条数上传量
			$sql = "SELECT m.id,m.channelid,m.manageid,c.marketrecordid,COUNT(*) as count FROM marketrecord m INNER JOIN marketconditions c ON m.id = c.marketrecordid WHERE m.managedate >= '" . $statistic_date . " 00:00:00' AND m.managedate <= '" . $statistic_date . " 23:59:59' AND m.isnews = 1 AND c.mconmanagedate >= '" . $statistic_date . " 00:00:00' AND c.mconmanagedate <= '" . $statistic_date . " 23:59:59' AND c.isview = 0 GROUP BY m.id, m.channelid, m.manageid, c.marketrecordid";
			$conditions_total = $this->_dao->query($sql);
			$sql = "SELECT count(*) as `count` FROM steelhomeen.news_variety where ndate >= '" . $statistic_date . " 00:00:00' and ndate <= '" . $statistic_date . " 23:59:59'";
			$conditions_total_english = $this->_dao->getOne($sql);
			$dept_array = $this->dept_array;
			$channel_array = $this->market_channel_array;
			$all_dept_arr = $this->_dao->query("SELECT * FROM `dept`");
			$dept_arr = [];
			foreach ($all_dept_arr as $dept_item) {
				$bm_id = $dept_item['did'] . "," . $dept_item['dzid'] . "," . $dept_item['dzzid'];
				foreach ($dept_array as $dk => $dv) {
					if ($bm_id == $dk) {
						$dept_arr[] = $dept_item;
					}
				}
			}
			if ($statistic_type === 'dept') {
				$returned_data = [
					$dept_array[$statistic_value] => [],
				];
				// 对于每个发布的行情，先匹配发布人的部门，看看是不是需要返回的数据
				foreach ($conditions_total as $market_condition_item) {
					$dept_name = null;
					$channel_name = $channel_array[$market_condition_item['channelid']];
					if (!$channel_name) {
						$channel_name = '其他';
					}
					foreach ($dept_arr as $dept_admin) {
						if ($market_condition_item["manageid"] == $dept_admin["userid"]) {
							// 能匹配上就说明 dept 表里能查到这人的部门
							$dept_name = $dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid'];
							if ($dept_name == $statistic_value) {
								// 匹配了部门，现在进行频道分类
								if ($returned_data[$dept_array[$statistic_value]][$channel_name]) {
									$returned_data[$dept_array[$statistic_value]][$channel_name] += (int) ($market_condition_item['count']);
								} else {
									$returned_data[$dept_array[$statistic_value]][$channel_name] = (int) ($market_condition_item['count']);
								}
								if ($returned_data[$dept_array[$statistic_value]]['合计']) {
									$returned_data[$dept_array[$statistic_value]]['合计'] += (int) ($market_condition_item['count']);
								} else {
									$returned_data[$dept_array[$statistic_value]]['合计'] = (int) ($market_condition_item['count']);
								}
							}
							break;
						}
					}
					// 如果是其他
					if (!$dept_name && $statistic_value === 'other') {
						// 匹配了部门，现在进行频道分类
						if ($returned_data['其他'][$channel_name]) {
							$returned_data['其他'][$channel_name] += (int) ($market_condition_item['count']);
						} else {
							$returned_data['其他'][$channel_name] = (int) ($market_condition_item['count']);
						}
						if ($returned_data['其他']['合计']) {
							$returned_data['其他']['合计'] += (int) ($market_condition_item['count']);
						} else {
							$returned_data['其他']['合计'] = (int) ($market_condition_item['count']);
						}
					}
				}
				// 英文的处理
				if ($dept_array[$statistic_value] === '国际部') {
					if ($returned_data['国际部']['英文频道']) {
						$returned_data['国际部']['英文频道'] += count($conditions_total_english);
					} else {
						$returned_data['国际部']['英文频道'] = count($conditions_total_english);
					}
					if ($returned_data['国际部']['合计']) {
						$returned_data['国际部']['合计'] += count($conditions_total_english);
					} else {
						$returned_data['国际部']['合计'] = count($conditions_total_english);
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			} else if ($statistic_type === 'channel') {
				$channel_name = $channel_array[$statistic_value];
				if (!$channel_name) {
					$channel_name = '其他';
				}
				$returned_data = [
					$channel_name => [],
				];
				// 对于每个发布的价格，先匹配价格对应行情的频道，看看是不是需要返回的数据
				foreach ($conditions_total as $market_condition_item) {
					if ($market_condition_item["channelid"] === $statistic_value || ($channel_name === '其他' && !($channel_array[$market_condition_item["channelid"]]))) {
						// 匹配了频道，现在进行部门分类
						$dept_name = null;
						foreach ($dept_arr as $dept_admin) {
							if ($market_condition_item["manageid"] == $dept_admin["userid"]) {
								$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
								if ($returned_data[$channel_name][$dept_name]) {
									$returned_data[$channel_name][$dept_name] += (int) ($market_condition_item['count']);
								} else {
									$returned_data[$channel_name][$dept_name] = (int) ($market_condition_item['count']);
								}
								if ($returned_data[$channel_name]['合计']) {
									$returned_data[$channel_name]['合计'] += (int) ($market_condition_item['count']);
								} else {
									$returned_data[$channel_name]['合计'] = (int) ($market_condition_item['count']);
								}
								break;
							}
						}
						if (!$dept_name) {
							if ($returned_data[$channel_name]['其他']) {
								$returned_data[$channel_name]['其他'] += (int) ($market_condition_item['count']);
							} else {
								$returned_data[$channel_name]['其他'] = (int) ($market_condition_item['count']);
							}
							if ($returned_data[$channel_name]['合计']) {
								$returned_data[$channel_name]['合计'] += (int) ($market_condition_item['count']);
							} else {
								$returned_data[$channel_name]['合计'] = (int) ($market_condition_item['count']);
							}
						}
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			}
		} else if ($data_type === 'markets_accessed') {
			// 行情访问量
			$sql = 'SELECT `memberip`, `newsid`, `obj`, `nmanageid`, `channelid` FROM steelhome.';
			if ($statistic_date >= '2022-01-01') {
				$sql .= 'memberaction';
			} else if ($statistic_date >= '2021-01-01') {
				$sql .= 'memberaction_2021';
			} else if ($statistic_date >= '2020-01-01') {
				$sql .= 'memberaction_2020';
			} else if ($statistic_date >= '2019-01-01') {
				$sql .= 'memberaction_2019';
			} else if ($statistic_date >= '2018-01-01') {
				$sql .= 'memberaction_2018';
			} else if ($statistic_date >= '2017-01-01') {
				$sql .= 'memberaction_2017';
			} else {
				$sql .= 'memberaction_h';
			}
			$sql .= ' where acttime >= "' . $statistic_date . ' 00:00:00" and acttime <= "' . $statistic_date . ' 23:59:59" and membaction = "浏览" and obj = "行情" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
			$market_access_total = $this->_dao->query($sql);
			$sql = 'SELECT `memberip`,`newsid`,`obj`,`nmanageid` FROM steelhomeen.memberaction where acttime >= "' . $statistic_date . ' 00:00:00" and acttime <= "' . $statistic_date . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and title like "%Market Price%" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
			$market_access_total_english = count($this->_dao->query($sql));
			$dept_array = $this->dept_array;
			$channel_array = $this->market_channel_array;
			$all_dept_arr = $this->_dao->query("SELECT * FROM `dept`");
			$dept_arr = [];
			foreach ($all_dept_arr as $dept_item) {
				$bm_id = $dept_item['did'] . "," . $dept_item['dzid'] . "," . $dept_item['dzzid'];
				foreach ($dept_array as $dk => $dv) {
					if ($bm_id == $dk) {
						$dept_arr[] = $dept_item;
					}
				}
			}
			if ($statistic_type === 'dept') {
				$returned_data = [
					$dept_array[$statistic_value] => [],
				];
				// 对于每个行情访问，先匹配发布人的部门，看看是不是需要返回的数据
				foreach ($market_access_total as $market_access_item) {
					$dept_name = null;
					$channel_name = $channel_array[$market_access_item['channelid']];
					if (!$channel_name) {
						$channel_name = '其他';
					}
					foreach ($dept_arr as $dept_admin) {
						if ($market_access_item["nmanageid"] == $dept_admin["userid"]) {
							// 能匹配上就说明 dept 表里能查到这人的部门
							$dept_name = $dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid'];
							if ($dept_name == $statistic_value) {
								// 匹配了部门，现在进行频道分类
								if ($returned_data[$dept_array[$statistic_value]][$channel_name]) {
									$returned_data[$dept_array[$statistic_value]][$channel_name] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]][$channel_name] = 1;
								}
								if ($returned_data[$dept_array[$statistic_value]]['合计']) {
									$returned_data[$dept_array[$statistic_value]]['合计'] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]]['合计'] = 1;
								}
							}
							break;
						}
					}
					// 如果是其他
					if (!$dept_name && $statistic_value === 'other') {
						// 匹配了部门，现在进行频道分类
						if ($returned_data['其他'][$channel_name]) {
							$returned_data['其他'][$channel_name] += 1;
						} else {
							$returned_data['其他'][$channel_name] = 1;
						}
						if ($returned_data['其他']['合计']) {
							$returned_data['其他']['合计'] += 1;
						} else {
							$returned_data['其他']['合计'] = 1;
						}
					}
				}
				// 英文的处理
				if ($dept_array[$statistic_value] === '国际部') {
					if ($returned_data['国际部']['英文频道']) {
						$returned_data['国际部']['英文频道'] += $market_access_total_english;
					} else {
						$returned_data['国际部']['英文频道'] = $market_access_total_english;
					}
					if ($returned_data['国际部']['合计']) {
						$returned_data['国际部']['合计'] += $market_access_total_english;
					} else {
						$returned_data['国际部']['合计'] = $market_access_total_english;
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			} else if ($statistic_type === 'channel') {
				$channel_name = $channel_array[$statistic_value];
				if (!$channel_name) {
					$channel_name = '其他';
				}
				$returned_data = [
					$channel_name => [],
				];
				// 对于每个行情访问，先匹配对应行情的频道，看看是不是需要返回的数据
				foreach ($market_access_total as $market_access_item) {
					if ($market_access_item["channelid"] === $statistic_value || ($channel_name === '其他' && !($channel_array[$market_access_item["channelid"]]))) {
						// 匹配了频道，现在进行部门分类
						$dept_name = null;
						foreach ($dept_arr as $dept_admin) {
							if ($market_access_item["nmanageid"] == $dept_admin["userid"]) {
								$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
								if ($returned_data[$channel_name][$dept_name]) {
									$returned_data[$channel_name][$dept_name] += 1;
								} else {
									$returned_data[$channel_name][$dept_name] = 1;
								}
								if ($returned_data[$channel_name]['合计']) {
									$returned_data[$channel_name]['合计'] += 1;
								} else {
									$returned_data[$channel_name]['合计'] = 1;
								}
								break;
							}
						}
						if (!$dept_name) {
							if ($returned_data[$channel_name]['其他']) {
								$returned_data[$channel_name]['其他'] += 1;
							} else {
								$returned_data[$channel_name]['其他'] = 1;
							}
							if ($returned_data[$channel_name]['合计']) {
								$returned_data[$channel_name]['合计'] += 1;
							} else {
								$returned_data[$channel_name]['合计'] = 1;
							}
						}
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			}
		} else if ($data_type === 'news_published') {
			// 资讯的上传量
			$sql = "SELECT `nmanageid`, `nchannelid` FROM `news` where ndate >= '" . $statistic_date . " 00:00:00' and ndate <= '" . $statistic_date . " 23:59:59'";
			$news_publish_total = $this->_dao->query($sql);
			$sql = "SELECT count(*) as `count` FROM steelhomeen.news where ndate >= '" . $statistic_date . " 00:00:00' and ndate <= '" . $statistic_date . " 23:59:59' and ncolumnid != '002,021'";
			$news_publish_total_english = $this->_dao->getOne($sql);
			$dept_array = $this->dept_array;
			$channel_array = $this->news_channel_array;
			$all_dept_arr = $this->_dao->query("SELECT * FROM `dept`");
			$dept_arr = [];
			foreach ($all_dept_arr as $dept_item) {
				$bm_id = $dept_item['did'] . "," . $dept_item['dzid'] . "," . $dept_item['dzzid'];
				foreach ($dept_array as $dk => $dv) {
					if ($bm_id == $dk) {
						$dept_arr[] = $dept_item;
					}
				}
			}
			if ($statistic_type === 'dept') {
				$returned_data = [
					$dept_array[$statistic_value] => [],
				];
				// 对于每个上传的资讯，先匹配发布人的部门，看看是不是需要返回的数据
				foreach ($news_publish_total as $news_publish_item) {
					$dept_name = null;
					$channel_name = $channel_array[$news_publish_item['nchannelid']];
					if (!$channel_name) {
						$channel_name = '其他';
					}
					foreach ($dept_arr as $dept_admin) {
						if ($news_publish_item["nmanageid"] == $dept_admin["userid"]) {
							// 能匹配上就说明 dept 表里能查到这人的部门
							$dept_name = $dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid'];
							if ($dept_name == $statistic_value) {
								// 匹配了部门，现在进行频道分类
								if ($returned_data[$dept_array[$statistic_value]][$channel_name]) {
									$returned_data[$dept_array[$statistic_value]][$channel_name] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]][$channel_name] = 1;
								}
								if ($returned_data[$dept_array[$statistic_value]]['合计']) {
									$returned_data[$dept_array[$statistic_value]]['合计'] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]]['合计'] = 1;
								}
							}
							break;
						}
					}
					// 如果是其他
					if (!$dept_name && $statistic_value === 'other') {
						// 匹配了部门，现在进行频道分类
						if ($returned_data['其他'][$channel_name]) {
							$returned_data['其他'][$channel_name] += 1;
						} else {
							$returned_data['其他'][$channel_name] = 1;
						}
						if ($returned_data['其他']['合计']) {
							$returned_data['其他']['合计'] += 1;
						} else {
							$returned_data['其他']['合计'] = 1;
						}
					}
				}
				// 英文的处理
				if ($dept_array[$statistic_value] === '国际部') {
					if ($returned_data['国际部']['英文频道']) {
						$returned_data['国际部']['英文频道'] += $news_publish_total_english;
					} else {
						$returned_data['国际部']['英文频道'] = $news_publish_total_english;
					}
					if ($returned_data['国际部']['合计']) {
						$returned_data['国际部']['合计'] += $news_publish_total_english;
					} else {
						$returned_data['国际部']['合计'] = $news_publish_total_english;
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			} else if ($statistic_type === 'channel') {
				$channel_name = $channel_array[$statistic_value];
				if (!$channel_name) {
					$channel_name = '其他';
				}
				$returned_data = [
					$channel_name => [],
				];
				// 对于每个上传的资讯，先匹配对应资讯的频道，看看是不是需要返回的数据
				foreach ($news_publish_total as $news_publish_item) {
					if ($news_publish_item["nchannelid"] === $statistic_value || ($channel_name === '其他' && !($channel_array[$news_publish_item["nchannelid"]]))) {
						// 匹配了频道，现在进行部门分类
						$dept_name = null;
						foreach ($dept_arr as $dept_admin) {
							if ($news_publish_item["nmanageid"] == $dept_admin["userid"]) {
								$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
								if ($returned_data[$channel_name][$dept_name]) {
									$returned_data[$channel_name][$dept_name] += 1;
								} else {
									$returned_data[$channel_name][$dept_name] = 1;
								}
								if ($returned_data[$channel_name]['合计']) {
									$returned_data[$channel_name]['合计'] += 1;
								} else {
									$returned_data[$channel_name]['合计'] = 1;
								}
								break;
							}
						}
						if (!$dept_name) {
							if ($returned_data[$channel_name]['其他']) {
								$returned_data[$channel_name]['其他'] += 1;
							} else {
								$returned_data[$channel_name]['其他'] = 1;
							}
							if ($returned_data[$channel_name]['合计']) {
								$returned_data[$channel_name]['合计'] += 1;
							} else {
								$returned_data[$channel_name]['合计'] = 1;
							}
						}
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			}
		} else if ($data_type === 'news_accessed') {
			// 资讯访问量
			$sql = 'SELECT `memberip`, `newsid`, `obj`, `nmanageid`, `channelid` FROM steelhome.';
			if ($statistic_date >= '2022-01-01') {
				$sql .= 'memberaction';
			} else if ($statistic_date >= '2021-01-01') {
				$sql .= 'memberaction_2021';
			} else if ($statistic_date >= '2020-01-01') {
				$sql .= 'memberaction_2020';
			} else if ($statistic_date >= '2019-01-01') {
				$sql .= 'memberaction_2019';
			} else if ($statistic_date >= '2018-01-01') {
				$sql .= 'memberaction_2018';
			} else if ($statistic_date >= '2017-01-01') {
				$sql .= 'memberaction_2017';
			} else {
				$sql .= 'memberaction_h';
			}
			$sql .= ' where acttime >= "' . $statistic_date . ' 00:00:00" and acttime <= "' . $statistic_date . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
			$news_access_total = $this->_dao->query($sql);
			$sql = 'SELECT `memberip`,`newsid`,`obj`,`nmanageid` FROM steelhomeen.memberaction where acttime >= "' . $statistic_date . ' 00:00:00" and acttime <= "' . $statistic_date . ' 23:59:59" and membaction = "浏览" and obj = "新闻" and title not like "%Market Price%" and memberip NOT LIKE "172.16.%" and memberip != "**************" and memberip != "*************" and memberip != "*************" and memberip != "**************" and memberid != "1"';
			$news_access_total_english = count($this->_dao->query($sql));
			$dept_array = $this->dept_array;
			$channel_array = $this->news_channel_array;
			$all_dept_arr = $this->_dao->query("SELECT * FROM `dept`");
			$dept_arr = [];
			foreach ($all_dept_arr as $dept_item) {
				$bm_id = $dept_item['did'] . "," . $dept_item['dzid'] . "," . $dept_item['dzzid'];
				foreach ($dept_array as $dk => $dv) {
					if ($bm_id == $dk) {
						$dept_arr[] = $dept_item;
					}
				}
			}
			if ($statistic_type === 'dept') {
				$returned_data = [
					$dept_array[$statistic_value] => [],
				];
				// 对于每个资讯访问，先匹配发布人的部门，看看是不是需要返回的数据
				foreach ($news_access_total as $news_access_item) {
					$dept_name = null;
					$channel_name = $channel_array[$news_access_item['channelid']];
					if (!$channel_name) {
						$channel_name = '其他';
					}
					foreach ($dept_arr as $dept_admin) {
						if ($news_access_item["nmanageid"] == $dept_admin["userid"]) {
							// 能匹配上就说明 dept 表里能查到这人的部门
							$dept_name = $dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid'];
							if ($dept_name == $statistic_value) {
								// 匹配了部门，现在进行频道分类
								if ($returned_data[$dept_array[$statistic_value]][$channel_name]) {
									$returned_data[$dept_array[$statistic_value]][$channel_name] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]][$channel_name] = 1;
								}
								if ($returned_data[$dept_array[$statistic_value]]['合计']) {
									$returned_data[$dept_array[$statistic_value]]['合计'] += 1;
								} else {
									$returned_data[$dept_array[$statistic_value]]['合计'] = 1;
								}
							}
							break;
						}
					}
					// 如果是其他
					if (!$dept_name && $statistic_value === 'other') {
						// 匹配了部门，现在进行频道分类
						if ($returned_data['其他'][$channel_name]) {
							$returned_data['其他'][$channel_name] += 1;
						} else {
							$returned_data['其他'][$channel_name] = 1;
						}
						if ($returned_data['其他']['合计']) {
							$returned_data['其他']['合计'] += 1;
						} else {
							$returned_data['其他']['合计'] = 1;
						}
					}
				}
				// 英文的处理
				if ($dept_array[$statistic_value] === '国际部') {
					if ($returned_data['国际部']['英文频道']) {
						$returned_data['国际部']['英文频道'] += $news_access_total_english;
					} else {
						$returned_data['国际部']['英文频道'] = $news_access_total_english;
					}
					if ($returned_data['国际部']['合计']) {
						$returned_data['国际部']['合计'] += $news_access_total_english;
					} else {
						$returned_data['国际部']['合计'] = $news_access_total_english;
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			} else if ($statistic_type === 'channel') {
				$channel_name = $channel_array[$statistic_value];
				if (!$channel_name) {
					$channel_name = '其他';
				}
				$returned_data = [
					$channel_name => [],
				];
				// 对于每个资讯访问，先匹配对应资讯的频道，看看是不是需要返回的数据
				foreach ($news_access_total as $news_access_item) {
					if ($news_access_item["channelid"] === $statistic_value || ($channel_name === '其他' && !($channel_array[$news_access_item["channelid"]]))) {
						// 匹配了频道，现在进行部门分类
						$dept_name = null;
						foreach ($dept_arr as $dept_admin) {
							if ($news_access_item["nmanageid"] == $dept_admin["userid"]) {
								$dept_name = $dept_array[$dept_admin['did'] . "," . $dept_admin['dzid'] . "," . $dept_admin['dzzid']];
								if ($returned_data[$channel_name][$dept_name]) {
									$returned_data[$channel_name][$dept_name] += 1;
								} else {
									$returned_data[$channel_name][$dept_name] = 1;
								}
								if ($returned_data[$channel_name]['合计']) {
									$returned_data[$channel_name]['合计'] += 1;
								} else {
									$returned_data[$channel_name]['合计'] = 1;
								}
								break;
							}
						}
						if (!$dept_name) {
							if ($returned_data[$channel_name]['其他']) {
								$returned_data[$channel_name]['其他'] += 1;
							} else {
								$returned_data[$channel_name]['其他'] = 1;
							}
							if ($returned_data[$channel_name]['合计']) {
								$returned_data[$channel_name]['合计'] += 1;
							} else {
								$returned_data[$channel_name]['合计'] = 1;
							}
						}
					}
				}
				$this->responseSuccess($returned_data, '获取成功');
			}
		}
	}
}