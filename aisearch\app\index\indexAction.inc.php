<?php
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
include_once( "/etc/steelconf/env/wwwcom_env.php" );
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
include_once(BASE_DIR."/../app/master/masterAction.inc.php");
include_once(BASE_DIR."/../project.conf.php");
include_once("AiTools.php");
include_once("McpManager.php");
class indexAction extends AbstractAction{
    public $logs;

    private int $userid = -1;
    private int $maxpower = 0; // 7:vip
    private string $username = "";
    private array $member = [];
    private string $truename = "";
    private bool $haveAiPower = false;

    private string $jscss_version = "08121840";
    // 来源类型，与主表中的from字段，用于区分不同位置的调用
    private array $fromType = [
        "www-web" => 0,  // www网页端
        "datacenter-web" => 1,  // 数据中心网页版
        "weixin-steelhomeresearch" => 2,  // 小程序钢之家研究
        "weixin-steelhomedata" => 3,  // 微信小程序钢之家数据
        "oa-web" => 4, // OA后台
        "datacenter-cli" => 5, // 数据中心客户端
    ];
    private array $saveChatMoreParams = [
        "from" => 0,
    ];
    // 新价格ID拼接的那些基础数据，品种、城市等数据
    private array $baseData = [];

    private array $aiModels = [
        "ernie" =>[
            "model"=>"ernie-speed-128k",
            "company"=>"baidu",
            "remark"=>"ernie-speed-128k",
            "showName"=>"百度",
            "isShow"=>false,
            "max_tokens"=>128000, //上下文窗口长度
        ],
        "deepseek-v3"=>[
            "model"=>"deepseek-chat",
            "company"=>"deepseek",
            "remark"=>"deepseek的普通模型",
            "showName"=>"deepseek-v3",
            "isShow"=>true,
            "max_tokens"=>64000, //上下文窗口长度
        ],
        "deepseek-r1"=>[
            "model"=>"deepseek-reasoner",
            "company"=>"deepseek",
            "remark"=>"deepseek的推理模型",
            "showName"=>"deepseek-r1",
            "isShow"=>true,
            "max_tokens"=>64000, //上下文窗口长度
        ]
    ];

    private string $defaultModel = "deepseek-v3";

    // 早期开发时用到了这个
    private array $aiPowerUserid = ["17","492160","501692","444830","147803"];

    private AiTools $AiTools;
    private McpManager $McpManager;

    public function __construct() {
        parent :: __construct();
        if( !in_array($_GET['action'], ["textCorrection", "getTextKeywords", "checkTextReference"])) {
            $this->setUserInfo();
            $this->setAiPower();
            if($this->userid == -1 || $this->userid == "") {
                // 查询余额的接口，不需要验证登录信息，方便后续定时程序的调用
                if($_GET['action'] != "getAllBalance") {
                    goURL("/MemberLogin.php?urlstr=%2Faisearch%2Findex.php");
                    exit;
                }
            }
        }
        $this->AiTools = new AiTools();
        $this->McpManager = new McpManager($this->AiTools, $this);
    }

    /**
     * MCP文本纠错API接口 - 对外提供的统一接口
     * 前端可以通过此接口调用文本纠错功能
     * @param array $request 请求参数
     * @return void 直接输出JSON响应
     */
    public function mcpTools(array $params) {
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            // 设置用户信息（如果需要权限验证）
            $this->setUserInfo();
            $this->setAiPower();
            
            // 获取请求参数
            $requestBody = file_get_contents('php://input');
            $params = json_decode($requestBody, true);
            
            // 参数验证
            if (empty($params['content'])) {
                echo json_encode([
                    'success' => false,
                    'message' => '文本内容不能为空',
                    'code' => 400
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // 权限检查（可选，根据需要启用）
            if ($this->userid <= 1) {
                echo json_encode([
                    'success' => false,
                    'message' => '用户权限不足',
                    'code' => 403
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // 确定MCP服务类型
            $action = $_GET['action'] ?? 'textCorrection';
            $mcpType = McpManager::MCP_TEXT_CORRECTION;
            
            if ($action === 'getTextKeywords') {
                $mcpType = McpManager::MCP_TEXT_KEYWORDS;
            } else if ($action === 'checkTextReference') {
                $mcpType = McpManager::MCP_CHECK_TEXT_REFERENCE;
            }

            // 调用MCP服务
            $result = $this->McpManager->executeMcp($mcpType, $params);
            // 返回结果
            echo json_encode($result, JSON_UNESCAPED_UNICODE);

            // if() {

            // }
            
        } catch (Exception $e) {
            // 错误处理
            echo json_encode([
                'success' => false,
                'message' => '服务异常: ' . $e->getMessage(),
                'code' => 500
            ], JSON_UNESCAPED_UNICODE);
        }
        
        exit;
    }

    /**
     * 获取支持的MCP服务列表
     * @param array $request
     * @return void
     */
    public function getMcpServices(array $request) {
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $services = $this->McpManager->getSupportedMcps();
            
            echo json_encode([
                'success' => true,
                'message' => '获取MCP服务列表成功',
                'data' => $services
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取服务列表失败: ' . $e->getMessage(),
                'code' => 500
            ], JSON_UNESCAPED_UNICODE);
        }
        
        exit;
    }

    /**
     * 文本纠错V1方式。供AI助手页面使用
     * @param array $params
     * @param array $data
     * @return never
     */
    public function textCorrection(array $params, array $data=[]) {
        $this->setUserInfo();
        $this->setAiPower();
        // $this->userid = 0;
        if($this->userid <= 1) {
            $this->_dao->responseError("账号错误！");
        }
        $params['stream'] ??= 1;
        // $is_save_chat = true;
        if(empty($data)) {
            // api调用方式
            $request_body = file_get_contents('php://input');
            $data = json_decode($request_body, true);
            $data['session_id'] = "";
            $params['stream'] = 0;  // 关闭流式
            // $is_save_chat = false;
        }
        $session_id = $data['session_id']??"";
        if(!in_array($data['from'], ['oa-web', 'datacenter-cli', 'www-web']) && $data['from']!=0) {
            $this->_dao->responseError("from参数错误！");
        }
        $from = $this->fromType[$data['from'] ?? 'www-web'] ?? 0;
        $data['from'] = $from;
        $data['isSearch'] = 0;
        $this->saveChatMoreParams['from'] = $from;
        // $params['model'] = "qwen-max";
        $params['prompt_type'] = 9; // 文本纠错
        [$content, $reasoning_content, $tool_calls] = $this->chat_by_oneapi($params, $data);
        $this->_dao->responseSuccess([
            'content' => $content,
        ]);
        exit;
    }

    public function index(array $request) {
        $this->saveChatMoreParams['from'] = 0;
        $chat_list = $this->getChatList($this->userid);
        $this->assign('chat_list', $chat_list);
        $this->assign('username', $this->username);
        // 文档地址：https://cloud.baidu.com/doc/WENXINWORKSHOP/s/clntwmv7t
        // $this->setChannelList();
        $this->assign("jscss_version", $this->jscss_version);
        $this->assign("aiModelsOfUser", $this->aiModels);
        $this->assign("defaultModel", $this->defaultModel);
        $this->assign("haveMoreAiPower", $this->haveAiPower);

        // $websearch = false;
        // if(in_array($this->userid , ["452459", "492160"])) {
        //     $websearch = true;
        // }

        // $this->assign("websearch", $websearch);
        if($this->userid != -1 && $this->userid != "") {
            $sql = <<<SQL
                WITH latest_list AS (
                    SELECT * FROM (
                        SELECT *,
                            ROW_NUMBER() OVER (PARTITION BY title ORDER BY createtime DESC, id DESC) AS rn
                        FROM ai_chat_list
                        WHERE isdel = 0
                        AND `from` = 0
                        AND userid = {$this->userid}
                    ) AS sub
                    WHERE rn = 1
                ),
                oldest_detail AS (
                    SELECT *
                    FROM (
                        SELECT *,
                            ROW_NUMBER() OVER (PARTITION BY chat_id ORDER BY created ASC, id ASC) AS rn
                        FROM ai_chat_detail
                        WHERE role=1
                    ) AS sub
                    WHERE rn = 1
                )
                SELECT l.*,d.*
                FROM latest_list l
                RIGHT JOIN oldest_detail d ON l.id = d.chat_id and (d.all_document_filepath IS NULL OR d.all_document_filepath = '')
                ORDER BY l.createtime DESC
                LIMIT 3
            SQL;
        }
        $quickChatList = $this->logs->query($sql);
        if(!$quickChatList) {
            $quickChatList = [];
        }
        $this->assign("quickChatList", $quickChatList);
    }
    
    public function dialog(array $request) {
        $this->saveChatMoreParams['from'] = 0;
        $chat_list = $this->getChatList($this->userid);
        $this->assign('chat_list', $chat_list);
        $this->assign('username', $this->username);
        $this->assign("request", $request);
        // $this->setChannelList();
        $content = "";
        if(($request['ntype'] == "n" || $request['ntype'] == "m") && $request['nid'] != "" ) {
            $nid = intval($request['nid']);
            if($nid > 0) {
                $cache = new memcache();
                $cache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
                $content = $cache->get($request['ntype'].$nid);
                if(!$content) {
                    $content = $this->getNewsInfo($nid, $request['ntype']);
                    $cache->set($request['ntype'].$nid, $content, 0, 3600);
                }
                $cache->close();
            }
        }

        $title = "";
        if($content != "") {
            $pattern = '/(?:资讯|行情)标题：(.+)/';
            preg_match($pattern, $content, $matches);
            if (isset($matches[1])) {
                $title = $matches[1];
            }
        }
        $this->assign("content", $content);
        $this->assign("title", $title);
        $this->assign("jscss_version", $this->jscss_version);
        $this->assign("aiModelsOfUser", $this->aiModels);
        $this->assign("defaultModel", $this->defaultModel);
        $this->assign("haveMoreAiPower", $this->haveAiPower);

        // $websearch = false;
        // if(in_array($this->userid , ["452459", "492160"])) {
        //     $websearch = true;
        // }

        // $this->assign("websearch", $websearch);
    }

    public function getChatListApi(array $request) {
        $page = $request['page']??1;
        $page_size = $request['pageSize']??15;
        $this->saveChatMoreParams['from'] = $this->fromType[$request['from']?? 'www-web']?? 0;
        $page = intval($page);
        $page_size = intval($page_size);
        $chat_list = $this->getChatList($this->userid, $page, $page_size);
        $data = [
            "data" => $chat_list,
            "total" => count($chat_list)
        ];
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    public function uploadFile(array $request) {
        $this->AiTools->uploadFile($request);
    }
    
    /**
     * 对外的流式对话接口
     * @param array action=chat&prompt_type=0&model=
     * @body 参数
     * {
     * chat_num: 0
     * content: "你好"
     * feature_type: "6"
     * filepathList: {}
     * from: "www-web"
     * isSearch: 0
     * session_id: "958dfef5-d19c-4df1-b4c3-7b59e3993d52"
     * }
     */
    public function chat(array $params) {
        if(!$params['testapi'])
        $this->AiTools->setupHeaders();
        $request_body = file_get_contents('php://input');
        $data = json_decode($request_body, true);
        $originData = $data;
        $session_id = $data['session_id']??"";
        $from = $this->fromType[$data['from'] ?? 'www-web'] ?? 0;
        $data['from'] = $from;
        $this->saveChatMoreParams['from'] = $from;
        $params['model'] = !empty($params['model']) ? $params['model'] : $this->defaultModel;
        $params['model'] = empty($this->aiModels[$params['model']]) && $params['model']!="newsDetail"? $this->defaultModel : $params['model'];
        $feature_type = "";
        // 文字纠错的功能暂不加权限验证
        if($data['feature_type'] == "6") {
            $feature_type = "6"; 
        }
        // 如果账号没有AI权限，则关闭一些参数
        if($this->haveAiPower !== true) {
            $data['isSearch'] = 0; // 关闭联网搜索功能
            $data['filepathList'] = []; // 关闭上传文件功能
            $data['feature_type'] = $feature_type; // 关闭首页功能区选择的功能
        }

        // $this->testResponse(0);exit;
        $isSearch = $data['isSearch']??0;

        if(in_array($params['prompt_type'], ["0", "9"])) {
            $searchType = 0;
            // 自由对话的时候可能会出现询问价格的情况
            // 昨天的上海螺纹钢HRB400EΦ16-25mm的价格是多少呢
            $filepathList = $data['filepathList']??[];

            // 如果有上传的文档，先进行文档处理，就不在查询行情价格
            if( is_array($filepathList) && count($filepathList) > 0) {
                if(!$this->haveAiPower) {
                    $this->AiTools->printInfo("请升级您的账号权限使用此功能。客服电话：021-50581010(总机) 4008115058。\n");
                    exit;
                }

                $document = $this->AiTools->getAllDocumentContent($filepathList)."\n";
                // dd($document);
                $documentTokens = $this->AiTools->countTokens($document);
                $data['document'] = $document;
                $maxTokens = $this->aiModels[$params['model']]['max_tokens'] ?? 56000;
                if ($documentTokens <= $maxTokens) {
                    // 文档未超限，直接传递
                    $data['document'] = $document;
                } elseif ($documentTokens <= $maxTokens * 1.5) {
                    // 超出但不多，生成摘要
                    $data['document'] = $this->generateSummary($document, $maxTokens);
                } else {
                    // 超出较多，进行分块处理
                    $data['document'] = $this->splitAndSummarize($document, $maxTokens);
                }
                $data['other']['all_path'] = implode("^|-|^", $data['filepathList']);
                $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);
                exit;
            }

            // 走网络搜索的情况
            if(intval($isSearch) == 1) {
                // 通过AI判断是否需要网络搜索并优化搜索词
                $searchCheck = $this->checkNeedWebSearch($params, $data);

                // 如果AI判断不需要搜索，则禁用搜索功能
                if (!$searchCheck['needSearch']) {
                    $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);
                    exit;
                }
                // 使用优化后的搜索关键词进行网络搜索
                $result = $this->AiTools->webSearch($searchCheck['searchQuery']);
                $this->chat_by_oneapi($params, $data, ['webSearchResult' => $result, 'is_save_chat' => true]);
                exit;
            }

            $words = [];
            // 报告查询
            $fenciType = 4; //需要使用的提示词
            if($data['feature_type'] == "6") {
                $searchType = "6";
            }
            if($searchType == 0) {
                if($this->AiTools->checkReport($data['content'])) {
                    $fenciType = 7;
                }
    
                if(mb_strlen($data['content'])>=4 && count($words) == 0) {
                    $words = $this->fenciByAi($data['content'], $fenciType);
                }
                // 当没有匹配到关键词时，直接走默认 AI 逻辑
                if (empty($words)) {
                    $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);
                    exit;
                }
                $searchType = $this->AiTools->getDatatype($words);
            }
            // dump($words);
            // dd($searchType);
            switch ($searchType) {
                case "1": // 行情价格
                    if(!$this->haveAiPower) {
                        $this->noPower($params, $data);
                    }
                    $this->priceInfoToAiResult($words, $params, $data);
                    break;
                case "2": // 库存
                case "3": // 产能利用率
                case "4": // 报告
                    if(!$this->haveAiPower) {
                        $this->noPower($params, $data);
                    }
                    $this->newsToAiResult($words, $params, $data, intval($searchType));
                    break;
                case "6": 
                    $data['from'] = $originData['from'] ?? "";
                    $this->textCorrection($params, $data);
                    break;
                default:
                    $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);
                    break;
            }
        } else if($params['model'] == "newsDetail" && $params['prompt_type'] == "3") {
            // 三级页面的AI解读功能会进入到这个逻辑
            $params['model'] = "ernie";
            $newinfo = explode("|^|", $data['content']);
            if(count($newinfo) == 2) {
                $news_id = $newinfo[0];
                $news_type = $newinfo[1];
                if($news_id == "" || $news_type == "") return;
                $cache = new memcache();
                $cache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
                $content = $cache->get($news_type.$news_id);
                if(!$content) {
                    $content = $this->getNewsInfo($news_id, $news_type);
                    $cache->set($news_type.$news_id, $content, 0, 3600);
                }
                $cache->close();
                $data['content'] = str_replace("\t","",$content);
                $data['content'] = str_replace("\r\n","",$data['content']);
            }
            $this->chat_by_oneapi($params, $data, ['is_save_chat' => false]);
            exit;
        } else {
            $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);
        }
        exit;
    }

    private function priceInfoToAiResult($words, $params, $data) {
        $assistant_text = "";
        $session_id = $data['session_id']??"";
        // dump($words);
        $replace = [
            "螺纹" => "螺纹钢",
            "热卷" => "热轧板卷",
            "建材" => "建筑钢材",
            // 后续可以继续添加其他替换规则
        ];
        
        foreach ($words as &$w) {
            foreach ($replace as $old => $new) {
                // 如果替换结果是原词的扩展，例如"螺纹" -> "螺纹钢"
                if (strpos($new, $old) === 0) {
                    // 计算出新增的后缀部分
                    $suffix = substr($new, strlen($old));
                    // 构造正则，匹配后面没有紧跟新增后缀的情况
                    $pattern = '/' . preg_quote($old, '/') . '(?!' . preg_quote($suffix, '/') . ')/u';
                    $w = preg_replace($pattern, $new, $w);
                } else {
                    // 对于不容易产生叠加问题的替换，直接用 str_replace 即可
                    $w = str_replace($old, $new, $w);
                }
            }
        }
        $code_info = $this->wordsToCode($words);
        // $code_info = [
        //     "sdate" => "2025-03-04",
        //     "edate" => "2025-03-04",
        //     "codes" => [
        //         "A4301030____________",
        //         "A4301031____________",
        //         "A4301032____________",
        //         "A4301255____________"
        //     ],
        //     "variety_name" => "建筑钢材",
        //     "material_name" => "",
        //     "specification_name" => "",
        //     "city_name" => "长沙市",
        // ];
        // file_put_contents("./app/index/info.txt", json_encode($info, JSON_UNESCAPED_UNICODE));
        // dump($info);
        // dd($code_info);
        // 如果关键词转换没有结果，同样调用默认 AI 逻辑
        if (empty($code_info)) {
            // $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);
            // exit;
            unset($words[0]);
            $t = implode("", $words);
            $customCon = "没有找到相关的{$t}信息，请换个日期或品种等信息试试！";
            $this->AiTools->printInfo($customCon, 0);
            exit;
        }
        if($session_id!="" && $session_id !=null) {
            $this->saveChat(["content"=>$data['content'], "session_id"=>$session_id, "role"=>"user", "prompt_type"=>0, "self"=>1, "model" => $params['model'], "content_type" => 0]);
        }
        // $customContent = "根据输入内容匹配到以下关键词：[".implode(" ", $words)."]，正在根据关键词进行数据搜索。\n";
        // $this->AiTools->printInfo($customContent);
        // $assistant_text .= $customContent;
        $this->generatePriceResponse($code_info, $assistant_text, $session_id, $params['model']);
    }

    private function newsToAiResult($words, $params, $data, $type=2) {
        $typeName = match($type) {
            2 => "库存",
            3 => "利用率",
            4 => "报告",
            default => "库存",
        };
        $customCon = "";

        $session_id = $data['session_id']??"";
        $chat_num = $data['chat_num']??0;
        $date_list[] = $words[0]=="最近"||$words[0]=="最新"?"今天":$words[0];
        // dump($words[0]);
        unset($words[0]);
        [$start_date, $end_date] = $this->strToDate($date_list);
        // $keys = implode(" ", $words); //喷吹煤库存使用空格分开无法被搜索到
        $keys = implode("", $words);
        $title = "";
        $printContent = "";
        $tips = "";
        // 是否是整月
        $is_whole_month = false;
        if($start_date == $end_date) {
            $new_start_date = date("Y-m-d", strtotime($end_date." -21 days"));
        } else {
            $new_start_date = date("Y-m-d", strtotime($start_date." -21 days"));

            if($type == 4) {
                // 匹配这类报告标题：钢之家：3月份国际钢材市场策略报告
                if(date("d", strtotime($end_date." +1 day")) == "01" && date("Y-m-d", strtotime($start_date." +25 day")) < $end_date) {
                    $keys = date("n月份", strtotime($start_date)).str_replace("最近", "", $keys);
                    $is_whole_month = true;
                }
            }
        }

        $master = new masterAction();
        // dump($new_start_date);
        // dump($start_date);
        // dump($end_date);
        // dump($keys);
        // exit;
        $list = $master->getElasticSearchList(['redisKeyString'=> '','type'=>0, 'ntitle'=>$keys, 'matchContents'=>0, 'matchKeyWords'=>1, 'startTime'=>$new_start_date, 'endTime'=>$end_date]);
        // dump($list);
        // dd();
        // 过滤结果
        $list = match($type){
            2 => array_values(array_filter($list, fn($item) => !str_contains($item['ntitle'], "调查】"))),
            3 => $list,
            4 => $list,
            default => $list,
        };
        $ncolumnid_field = match($type){
            2 => "(ncolumnid like '%002,063%' or ncolumnid like '%002,235%')",
            3 => "(ncolumnid like '%006,263%')", // 003,283,006,263
            4 => "(ncolumnid like '%002,058%' or ncolumnid like '%007,064%' or ncolumnid like '%007,065%' or ncolumnid like '%002,062%') and ntitle like '%报告%'",
            default => "(ncolumnid like '%002,063%' or ncolumnid like '%002,235%')",
        };
        // $list = array_values(array_filter($list, fn($item) => !str_contains($item['ntitle'], "调查】")));
        // dump($list);
        // dd($list);
        if(count($list) == 0) {
            $customCon = "没有找到相关的{$typeName}信息！";
            $this->AiTools->printInfo($customCon);
            if($chat_num > 1) {
                // $data['dataType'] = "2";
                // $this->chat_by_oneapi($params, $data, ['is_save_chat' => true]);exit;
            }
        } else {
            $news = [];
            // dump($start_date);
            // dump($end_date);
            if($start_date == $end_date) {
                $nid = $list[0]['nid'];
                $sql = "SELECT filepath,ntitle,ndate FROM `news` WHERE nid='".$nid."'";
                $inventory_news = $this->_dao->getrow($sql);
                if($inventory_news['filepath']) {
                    $news[0]['path'] = $inventory_news['filepath'];
                    if( $start_date == date("Y-m-d", strtotime($new_start_date))) {
                        $news[0]['title'] = "## ".$inventory_news['ntitle'];
                    } else {
                        // $news[0]['title'] = "未找到".date("n月j日", strtotime($end_date))."的库存信息！为您查到的最接近该日期的库存信息:\n ## ".$inventory_news['ntitle'];
                        // $news[0]['title'] = "为您查到的最接近该日期的信息:\n ## ".$inventory_news['ntitle'];
                        $news[0]['title'] = "## ".$inventory_news['ntitle'];
                    }
                }
            } else {
                $ids = [];
                foreach ($list as $item) {
                    if(date("Y-m-d", strtotime($item['ndate'])) > $start_date || $is_whole_month) {
                        $ids[] = $item['nid'];
                    }
                }
                // dd($ids);
                if(count($ids)>0) {
                    $sql = "SELECT filepath,ntitle,ndate FROM `news` WHERE nid in ('". implode("','", $ids) ."') and {$ncolumnid_field} order by ndate desc";
                    // dd($sql);
                    $inventory_news = $this->_dao->query($sql);
                    foreach ($inventory_news as $key=>$item) {
                        if($item['filepath'] && $key < 5) {
                            $news[] = [
                                "path" => $item['filepath'],
                                "title" => "## ".$item['ntitle'],
                                "ndate" => $item['ndate']
                            ];
                        }
                    }
                } else {
                    $customCon = "没有找到指定时间内相关的{$typeName}信息！";
                    $this->AiTools->printInfo($customCon, 50);
                    exit;
                }
            }
            // dd($news);
            if(count($news) > 0) {
                $timeout = 25;
                foreach ($news as $info) {
                    $filepath = $info['path'];
                    $title = $info['title'];
                    $html = $this->getContentByFilepath($filepath);
                    if(in_array($type, [2, 3])) {
                        [$table, $imagesMarkdown] = $this->AiTools->getConFromHtml($html);
                        if(!$table) continue;
                        $printContent .= $title."\n".$table."\n".$imagesMarkdown."\n";
                        $timeout = 0; // 0 不使用打字机效果
                    } else {
                        $con = $this->AiTools->getConFromHtml2($html);
                        $printContent .= $title."\n".$con."\n";
                    }
                }

                $tips = "\n ###### 数据仅供参考，更加详细准确的信息请前往[钢之家网站](".APP_URL_WWW.")查询。\n";
                $customCon = $printContent.$tips;
                $this->AiTools->printInfo($customCon, $timeout, null, "stop");
            } else {
                $customCon = "没有找到相关的{$typeName}信息！";
                $this->AiTools->printInfo($customCon, 0);
            }
        }

        if($session_id!="" && $session_id !=null) {
            $this->saveChat(["content"=>$data['content'], "session_id"=>$session_id, "role"=>"user", "prompt_type"=>0, "self"=>1, "model" => $params['model'], "content_type" => 0]);
            $this->saveChat(["content"=>$customCon, "session_id"=>$session_id, "role"=>"assistant", "prompt_type"=>0, "self"=>1, "model" => $params['model'], "content_type" => 2]);
        }
        echo "data: [DONE]\n\n";
        ob_flush();
        flush();
        exit;
    }

    private function fenciByAi(string $content, $prompt_type=4): array {
        // [$body] = $this->chat_by_oneapi(['prompt_type' => 4, 'model' => 'ernie', 'stream' => false], ["content"=>$content], ['is_save_chat' => false]);
        // dd($body);
        // [$body] = $this->chat_by_oneapi(['prompt_type' => 4, 'model' => 'deepseek-v3-baidu', 'stream' => false], ["content"=>$content], ['is_save_chat' => false]);
        [$body] = $this->chat_by_oneapi(['prompt_type' => $prompt_type, 'model' => 'qwen-max-0125', 'stream' => false], ["content"=>$content], ['is_save_chat' => false]);
        $w = [];
        // dump($body);
        // file_put_contents("./app/index/words.txt", json_encode($body, JSON_UNESCAPED_UNICODE));
        if($body != "无") {
            // $tmp_str = mb_substr($body, 0, 10);
            $tmp_str = "";
            $allinfo = explode("\n", $body);
            foreach ($allinfo as $value) {
                if(str_contains($value, "@@")) {
                    $tmp_str = $value;
                    break;
                }
            }
            $tmp_cnt = explode("@@", $tmp_str);
            // 返回的内容出现不符合要求的情况时，需要另外处理
            if(count($tmp_cnt) > 2) {
                $w = $tmp_cnt;
                if(count($w)!=0) {
                    $w = array_unique($w);
                }
            }
        }
        return $w;
    }

    private function wordsToCode(array $words):array {
        $tempJson = file_get_contents(Authorization_DOMAIN."/admincpv2/pidsearch.php?action=getBaseInfoToAI");
        $this->baseData = json_decode($tempJson, true);
        // dd($this->baseData['channelList']);
        // 新价格ID编码规则：频道1 + 城市4 + 品种3 + 材质3 + 规格3 + 企业4 + 注释2
        $channel_code = "_";
        $city_code = "____";
        $variety_code = "___";
        $material_code = "___";
        $specification_code = "___";
        $dateStrList = [];
        $is_latest = 0;
        $city_name = "";
        $variety_name = "";
        $material_name = "";
        $specification_name = "";
        foreach($words as $key => $t_word) {
            $t_word = trim($t_word);
            if($t_word == "" || $t_word == "价格") {
                unset($words[$key]);
                continue; 
            }
            // words[0] 存放日期
            // 处理日期，第一步：获取到时间的文字描述
            // if($key == 0 || $key == 1) {
            if($key == 0) { //目前只取一个日期，暂时不做区间日期的处理
                if(str_contains($t_word, "日") || str_contains($t_word, "月") || str_contains($t_word, "年") || str_contains($t_word, "号") || str_contains($t_word, "周") || str_contains($t_word, "星期") || str_contains($t_word, "天")) {
                    $dateStrList[] = $t_word;
                    unset($words[$key]);
                    continue;
                } else if (str_contains($t_word, "最近") || str_contains($t_word, "最新")) {
                    $dateStrList[] = "今天";
                    // $is_latest = 1;
                    unset($words[$key]);
                    continue;
                }
            }

            // 处理城市
            foreach($this->baseData['cityList'] as $cityInfo) {
                if(str_contains($cityInfo['name'], $t_word)) {
                    $city_code = $cityInfo['code'];
                    $city_name = $cityInfo['name'];
                    unset($words[$key]);
                    continue 2;
                    // break;
                }
            }

            // 处理品种
            foreach($this->baseData['varietyList'] as $varietyInfo) {
                if(str_contains($varietyInfo['name'], $t_word) && $t_word!="价格") {
                    $variety_code = $varietyInfo['code'];
                    $channel_code = $varietyInfo['channel'];
                    $variety_name = $varietyInfo['name'];
                    unset($words[$key]);
                    continue 2;
                    // break;
                }
            }

            // 处理材质；AI分词可能无法将材质与规格拆分出来
            foreach($this->baseData['materialList'] as $materialInfo) {
                if(str_contains(strtoupper($materialInfo['name']), strtoupper($t_word))) {
                    $material_code = $materialInfo['code'];
                    $material_name = $materialInfo['name'];
                    unset($words[$key]);
                    continue 2;
                }
            }

            // 处理规格；AI分词可能无法将材质与规格拆分出来
            foreach($this->baseData['specificationList'] as $specificationInfo) {
                if(str_contains(strtoupper($specificationInfo['name']), strtoupper($t_word))) {
                    $specification_code = $specificationInfo['code'];
                    $specification_name = $specificationInfo['name'];
                    unset($words[$key]);
                    continue 2;
                }
            }
        }
        // 进一步细化材质与规格
        // if($variety_code != "___") {
        //     if($material_code == "___"){
        //         if(isset($rules[$variety_code]['m'][0])) {
        //             foreach($this->baseData['materialList'] as $varietyInfo) {
        //                 if($rules[$variety_code]['m'][0] == $varietyInfo['name'] && $varietyInfo['channel'] == $channel_code) {
        //                     $material_code = $varietyInfo['code'];
        //                 }
        //             }
        //         }
        //     }
        //     if($specification_code == "___") {
        //         if(isset($rules[$variety_code]['s'][0])) {
        //             foreach($this->baseData['specificationList'] as $specInfo) {
        //                 if($rules[$variety_code]['s'][0] == $specInfo['name']&& $specInfo['channel'] == $channel_code) {
        //                     $specification_code = $specInfo['code'];
        //                 }
        //             }
        //         }
        //     }
        // }
        // dump($words);
        $all_variety_code = [];
        $codes = [];
        if($variety_code == "___") {  //如果没有匹配到品种的话就匹配大品种
            // gc,011,101,c01
            $key_code = "";
            $channelname = "";
            $sql = "SELECT channelname,channelcode,pid,mvid,class,mvname FROM markvariety left join channel on markvariety.channelid = channel.channelid where channelcode!='' and channelcode is not null";
            $all_big_variety = $this->_dao->query($sql);
            foreach ($words as $wvalue) {
                foreach ($all_big_variety as $all_big_variety_value) {
                    if(str_contains($all_big_variety_value['mvname'], $wvalue)) {
                        $key_code = $all_big_variety_value['channelcode'].",".$all_big_variety_value['pid'].",".$all_big_variety_value['mvid'].",".$all_big_variety_value['class'];
                        $channelname = $all_big_variety_value['channelname'];
                        $variety_name = $all_big_variety_value['mvname'];
                        break 2;
                    }
                }
            }
            if($key_code != "") {
                foreach ($this->baseData['channelList'] as $channelValue) {
                    if($channelValue['name'] == $channelname) {
                        $channel_code = $channelValue['code'];
                    }
                }

                $sql = "select model_code from sth_variety_model where key_code='".$key_code."' and type=1 limit 5";
                $aaa = $this->_dao->query($sql);
                foreach ($aaa as $value) {
                    $all_variety_code[] = $value['model_code'];
                }

                foreach ($all_variety_code as $t_code) {
                    $code = $channel_code.$city_code.$t_code.$material_code.$specification_code;
                    $code = str_pad($code, 20, '_', STR_PAD_RIGHT);
                    $codes[] = $code;
                }
            } else {
                return []; 
            }
        } else {
            $code = $channel_code.$city_code.$variety_code.$material_code.$specification_code;
            if($code == "____________________") {
                return [];
            }
            if($variety_code == "___" && $channel_code == "_") {
                return [];
            }
            $code = str_pad($code, 20, '_', STR_PAD_RIGHT);
            $codes[] = $code;
        }

        if(count($codes) == 0) {
            return [];
        }

        [$startDate, $endDate] = $this->strToDate($dateStrList);

        if($city_code=="____") {
            return [];
        } else {
            return ['sdate'=>$startDate, 'edate'=>$endDate, 'codes'=>$codes, 'variety_name'=>$variety_name, 'material_name'=>$material_name, 'specification_name'=>$specification_name, 'city_name'=>$city_name];
        }
    }

    private function setChannelList() {
        $master = new masterAction();
        $channelNavigation = $master->getSteelHomeDataPictureList(array("pageSize" => 15, "type" => 3));
        $channelNavigation = json_decode($channelNavigation, true);
        foreach ($channelNavigation as $channelKey => $channelValue) {
            if ($channelValue['name']) {
                $channelList[$channelValue['id']] = $channelValue;
            }
        }
        $this->assign("channelList", $channelList);
    }

    private function generatePriceResponse($code_info, $assistant_text, $session_id, $model)
    {
        $customCon = "";
        $showNum = 200;
        $sdate = $code_info['sdate'];
        $edate = $code_info['edate'];
        $codes = $code_info['codes'];
        $variety_name = $code_info['variety_name'];
        $material_name = $code_info['material_name'];
        $specification_name = $code_info['specification_name'];
        $city_name = str_replace("市", "", $code_info['city_name'])."市场";
        $tmpdata = [];
        foreach ($codes as $code) {
            if( abs(strtotime($edate) - strtotime($sdate)) > 6*24*3600) {
                $edate = date("Y-m-d", strtotime($sdate) + 6*24*3600);
            }
            $url = Authorization_DOMAIN."/admincpv2/pidsearch.php?action=searchToAI&sdate=".$sdate."&edate=".$edate."&pageSize=1000&pid=".$code."&isxs=1";
            // file_put_contents("./app/index/url.txt", $url);
            $res_data = file_get_contents($url);
            $res_data = json_decode($res_data??"", true);
            // dump($res_data);
            if($data == null || empty($data) || $data == false) {
               $tmpdata = array_merge($tmpdata, $res_data);
            }
        }
        // exit;
        $data = $tmpdata;
        // dd($data);
        if( $data == null || empty($data) || $data == false) {
            $customCon = "没有找到相关的数据，请换个日期或品种等信息试试！";
            $this->AiTools->printInfo($customCon);
            $assistant_text .= $customCon;
        } else {
            $more = false;
            $moreChannel = "";
            $num = count($data);
            $num_isxs = 0; //主流价格数量
            foreach($data as $tval) {
                if($tval['isxs']==1) {
                    $num_isxs++;
                }
            }

            foreach ($data as $keyindex => &$value11) {
                [$value11['channel'], $value11['city'], $value11['variety'], $value11['material'], $value11['specification'], $value11['manufactor'], $value11['remark']] = $this->getBaseinfoByPricecode($value11['price_code']);
            }
            
            $data = sortByCols($data, ['mconmanagedate' => SORT_DESC, 'variety' => SORT_ASC, 'material' => SORT_ASC, 'specification' => SORT_ASC]);

            $firstInfo = $data[0];
            if($sdate == $edate)
            $customCon = "## ".date("Y年n月j日", strtotime($sdate)).$city_name.$variety_name."价格 \n";
            else
            $customCon = "## ".date("Y年n月j日", strtotime($sdate))."至".date("Y年n月j日", strtotime($firstInfo['mconmanagedate'])).$city_name.$variety_name."价格 \n";
            $assistant_text .= $customCon;
            $this->AiTools->printInfo($customCon, 25);
            $dataStr = <<<STR
            |市场|品种|材质|规格|钢厂/产地|价格|涨跌|日期|
            |:---:|:---:|:---:|:---:|:---:|:---:|:---:|:---:|\n
            STR;
            $customCon = $dataStr;
            $assistant_text .= $customCon;
            $this->AiTools->printInfo($customCon, 0);
            usleep(0.1*1000*1000);

            foreach($data as $kindex => &$val) {
                // 超过25条数据，不再显示
                if(intval($kindex)+1 >= $showNum) {
                    continue;
                }
                if( abs(strtotime($edate) - strtotime($sdate)) > 6*24*3600) {
                    $edate = date("Y-m-d", strtotime($sdate) + 6*24*3600);
                }
                $val['dateStr'] = date("Y年m月d日", strtotime($val['mconmanagedate']));
                $moreChannel = $val['channel'];
                $val['city'] = str_replace("市", "", $val['city'])."";
                $zd = round($val['price']-$val['oldprice'], 2);
                if($zd<0) {
                    // $zd = "<font color='#388e3c'>↓".(-1*$zd)."</font>";
                    $zd = "↓".(-1*$zd)."";
                } else if($zd>0) {
                    // $zd = "<font color='#c62828'>↑".$zd."</font>"; 
                    $zd = "↑".$zd."";
                } else {
                    $zd = "-";
                }
                
                $dataStr = <<<STR
                |{$val['city']}|{$val['variety']}|{$val['material']}|{$val['specification']}|{$val['manufactor']}|{$val['price']}|{$zd}|{$val['mconmanagedate']}|\n
                STR;
                if($kindex== count($data)-1) {
                    $is_end = true;
                }
                $customCon = $dataStr;
                $assistant_text .= $customCon;
                $this->AiTools->printInfo($customCon, 5);
                usleep(0.5*1000*1000); // 延迟0.5秒
                if($kindex+1 == $showNum) {
                    $more = true;
                }
            }
            $customCon = "\n ###### 以上为当地市场，主流钢厂代表品种规格主流价格，数据仅供参考，更加详细准确的信息请前往[钢之家网站](".APP_URL_WWW.")查询。\n";
            $assistant_text .= $customCon;
            $this->AiTools->printInfo($customCon);
            if($more) {
                // $customCon = "，更多详细数据，请前往{$moreChannel}查询！";
                // $assistant_text .= $customCon;
                // $this->AiTools->printInfo($customCon);
            }
        }
        if($session_id!="" && $session_id !=null) {
            $this->saveChat(["content"=>$assistant_text, "session_id"=>$session_id, "role"=>"assistant", "prompt_type"=>0, "self"=>1, "model" => $model, "content_type" => 2]);
        }
        echo "data: [DONE]\n\n";
        ob_flush();
        flush();
        exit;
    }

    private function getBaseinfoByPricecode(string $pid): array{
        $returnArray = ["", "", "", "", "", "", ""];
        $channel = substr($pid, 0, 1);
        $city = substr($pid, 1, 4);
        $variety = substr($pid, 5, 3);
        $material = substr($pid, 8, 3);
        $specification = substr($pid, 11, 3);
        $manufactor = substr($pid, 14, 4);
        $remark = substr($pid, 18, 2);
        foreach($this->baseData['channelList'] as $tmpInfo) {
            if($tmpInfo['code'] == $channel) {
                $returnArray[0] = str_replace('|', '&#124;',$tmpInfo['name']);
                break;
            }
        }

        foreach($this->baseData['cityList'] as $tmpInfo) {
            if($tmpInfo['code'] == $city) {
                $returnArray[1] = str_replace('|', '&#124;', $tmpInfo['name']);
                break;
            }
        }

        foreach($this->baseData['varietyList'] as $tmpInfo) {
            if($tmpInfo['code'] == $variety) {
                $returnArray[2] = str_replace('|', '&#124;', $tmpInfo['name']);
                break;
            }
        }

        foreach($this->baseData['materialList'] as $tmpInfo) {
            if($tmpInfo['code'] == $material) {
                $returnArray[3] = str_replace('|', '&#124;', $tmpInfo['name']);
                break;
            }
        }

        foreach($this->baseData['specificationList'] as $tmpInfo) {
            if($tmpInfo['code'] == $specification) {
                $returnArray[4] = str_replace('|', '&#124;', $tmpInfo['name']);
                break;
            }
        }

        foreach($this->baseData['manufactorList'] as $tmpInfo) {
            if($tmpInfo['code'] == $manufactor) {
                $returnArray[5] = str_replace('|', '&#124;', $tmpInfo['name']);
                break;
            }
        }

        foreach($this->baseData['remarkList'] as $tmpInfo) {
            if($tmpInfo['code'] == $remark) {
                $returnArray[6] = str_replace('|', '&#124;', $tmpInfo['name']);
                break;
            }
        }

        return $returnArray;
    }

    private function testResponse($f=1) {
        $filename = "chat.txt";
        if($f==0) {
            $filename = "chat_small.txt";
        }
        // 打开文件
        $handle = fopen($filename, "r");
        if (!$handle) {
            http_response_code(500);
            echo "data: [DONE]\n\n";
            exit;
        }

        $text = "";

        while (!feof($handle)) {
            usleep(0.05*1000*1000); // 延迟0.1秒
            // 读取一行
            $line = fgets($handle);
            if ($line === false) break;

            $text .= $line;
            echo $line;
            flush(); // 确保数据立即输出
        }

        // 关闭文件
        fclose($handle);
    }

    /**
     * 获取新闻信息
     *
     * 根据新闻 ID 和类型获取新闻的标题和内容。
     * 如果内容为空，则尝试从指定的 URL 获取内容。
     *
     * @param int $news_id 新闻的唯一标识符
     * @param string $news_type 新闻类型，"n" 表示资讯，其他值表示行情
     * @return string 返回格式化的新闻标题和内容
     */
    private function getNewsInfo($news_id, $news_type)
    {
        $type_text = "";
        if($news_type == "n") {
            $sql = "select ntitle, filepath from news where nid = {$news_id} ";
            $news = $this->_dao->getrow($sql);
            $type_text = "资讯";
        } else {
            $sql = "select ntitle, filepath from news_mrhq where newsid = {$news_id} ";
            $news = $this->_dao->getrow($sql);
            $type_text = "行情";
        }
        $news['content'] = $this->getContentByFilepath($news['filepath']);
        // if($news['content']!="")
        // $news['content'] = strip_tags($news['content']);
        $news['content'] = str_replace("\t", "", $news['content']);
        $news['content'] = str_replace("\r\n", "", $news['content']);
        $content = $type_text."标题：\n".$news['ntitle']."\n".$type_text."内容：\n".$news['content'];
        return $content;
    }

    private function getContentByFilepath($filepath) {
        $content = "";
        if($filepath)
        $content = file_get_contents($filepath);
        if($content== "" || $content == null) {
            $a =  file_get_contents(APP_URL_WWW."/_v2app/get_information_content.php?filepath=".$filepath."&getToken=".CODE_CONTAIN_TOKEN_PUBLIC, false, stream_context_create(["ssl" => ["verify_peer"=>false,"verify_peer_name"=>false]]));
            $arr = json_decode($a, true);
            $content = $arr['contents']??"";
        }
        return $content;
    }

    /**
     * 获取聊天详情
     *
     * 此方法接收请求中的会话 ID，调用 chatDetail 方法获取聊天数据，并将结果以 JSON 格式返回。
     *
     * @param array $request 请求参数，包含会话 ID。
     * @return void
     */
    public function getChatDetail($request) {
        $session_id  = $request['session_id']??"";
        if($session_id == "") {
            $par = file_get_contents("php://input");
            $par = json_decode($par, true);
            $session_id = $par['session_id']??"";
            $request['session_id'] = $session_id;
        }
        $chatData = [];
        [$chatData, $arr['prompt_type'], $arr['chat_id']] = $this->chatDetail($session_id, 1);
        $tmpdata = array_filter($chatData, function($value) {
            return ($value['role'] == "assistant" && ($value['content'] == "[系统提示] AI 回复缺失。" || $value['content'] == "[系统提示] 服务异常。") ) == false;
        });
        $arr['data'] = array_values($tmpdata);
        $arr['session_id'] = $request['session_id'];
        header('Content-Type: application/json;');
        echo json_encode($arr, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 获取聊天详情，包括用户和助手的对话记录。
     *
     * @param string $session_id 会话 ID，用于标识特定的聊天会话。
     * @param int $isapi 标识是否为 API 调用，默认为 0。为1的话是给AI助手的页面的对话展示使用的
     * @param int $max_turns 最大保留的对话轮数，默认为 10。
     * 
     * @return array 返回包含聊天记录、提示类型和聊天 ID 的数组。
     *               - 第一个元素为聊天记录数组，每个元素包含内容和角色。
     *               - 第二个元素为提示类型。
     *               - 第三个元素为聊天 ID。
     *
     * 该方法会检查连续的用户输入，并在缺失 AI 回复时插入系统提示。
     * 如果对话轮数超过最大限制，将会进行摘要处理。
     */
    private function chatDetail($session_id, $isapi = 0, $max_turns = 5){
        $sql = "SELECT acd.content, acl.prompt_type, acd.role, acl.id, acd.all_document_filepath, acd.reasoning_content, acd.content_type, acd.created
        FROM ai_chat_detail acd
        JOIN ai_chat_list acl ON acd.chat_id = acl.id and acl.isdel = 0
        WHERE acl.session_id = '{$session_id}' AND acl.userid = {$this->userid} ORDER BY acd.created asc LIMIT 1000";
        $data = $this->logs->query($sql);
        $all_chat = [];
        $prompt_type = 0;
        $chat_id = -1;
        $last_role = null;
        // $system_message = null;
        foreach ($data as $everyone_chat) {
            $prompt_type = $everyone_chat['prompt_type'];
            $chat_id = $everyone_chat['id'];
            $current_role = $everyone_chat['role'] == "1" ? "user" : "assistant";

            // 检查是否连续两个 "user"，如果是，则补充 AI 回复
            if ($last_role === "user" && $current_role === "user") {
                $all_chat[] = [
                    "content" => "[系统提示] 服务异常。",
                    "role" => "assistant",
                ];
            }

            if($isapi == 1) {
                $filename_list = [];
                if($everyone_chat['all_document_filepath']) {
                    $tmp_path = explode("^|-|^", $everyone_chat['all_document_filepath']);
                    foreach ($tmp_path as $pathv) {
                        $filename_list[] = $this->AiTools->getFileName($pathv);
                    }
                }
                $all_chat[] = [
                    "content" => $everyone_chat['content'],
                    "role" => $current_role,
                    "filename_list" => $filename_list,
                    "reasoning_content" => $everyone_chat['reasoning_content'],
                    "content_type" => $everyone_chat['content_type'],
                    "created" => $everyone_chat['created']
                ];
            } else {
                $all_chat[] = [
                    "content" => $everyone_chat['content'],
                    "role" => $current_role,
                ];
            }

            $last_role = $current_role;
        }

        // 处理最后一个用户输入没有 AI 回复的情况
        if ($last_role === "user") {
            $all_chat[] = [
                "content" => "[系统提示] 服务异常。",
                "role" => "assistant",
            ];
        }

        // ** 只保留最近 N 轮对话**
        if (count($all_chat) > $max_turns && $isapi == 0) {
            $summary = $this->summarizeChat($all_chat);
            $all_chat = array_slice($all_chat, -$max_turns);  // 只取最近 $max_turns 轮
            array_unshift($all_chat, [
                "role" => "system",
                "content" => "[摘要] 之前的对话: " . $summary,
            ]);
        }

        return [$all_chat, $prompt_type, $chat_id];
    }

    private function summarizeChat($chatHistory) {
        // 取前 5 条聊天记录，并拼接成一个简短摘要
        $summary = [];
        $count = 0;
        foreach ($chatHistory as $chat) {
            if ($count >= 5) break; // 只取前 5 条
            $summary[] = "{$chat['role']}: {$chat['content']}";
            $count++;
        }
        return implode(" | ", $summary);  // 用 `|` 分隔
    }

    /**
     * 删除指定会话的聊天记录
     *
     * @param array $request 请求参数，包含会话 ID
     * @return void 返回 JSON 格式的响应，包含操作结果和消息
     *
     * 响应示例：
     * - 成功：{"success":1, "msg":"删除成功", "error":"0"}
     * - 失败：{"success":0, "msg":"删除失败", "error":"用户信息错误"}
     */
    public function deleteChat(array $request){
        header('Content-Type: application/json');
        $session_id = $request['session_id']??"";
        if($session_id == "") {
            $par = file_get_contents("php://input");
            $par = json_decode($par, true);
            $session_id = $par['session_id']??"";
        }
        $userid = $this->userid;
        $arr = [];
        if($userid==-1 || $userid=="") {
            $arr = ["success"=>0, "msg"=>"删除失败", "error"=>"用户信息错误"];
        } else {
            $sql = "update `ai_chat_list` set isdel=1 where `userid` = {$userid} and `session_id` = '{$session_id}'";
            $this->logs->execute($sql);
            $arr = ["success"=>1, "msg"=>"删除成功", "error"=>"0"];
        }

        echo json_encode($arr, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 保存聊天记录
     *
     * 此方法用于保存用户的聊天内容到数据库中。如果该用户在当前会话中尚未创建聊天记录，则会先插入聊天列表。
     * 然后将聊天内容插入聊天详情表。根据传入的参数决定返回的结果。
     *
     * @param array $params 包含以下键值的数组：
     * - content: 聊天内容
     * - session_id: 会话 ID
     * - role: 用户角色（"user" 或其他）
     * - prompt_type: 提示类型（可选）
     * - self: 是否为php函数内部调用，而不是接口调用（可选）
     *
     * @return int|void 返回 1 表示内部调用保存成功，或输出 JSON 格式的成功消息。
     */
    public function saveChat(array $params){
        $content_type = $params['content_type']??0;
        $userid = $this->userid;
        $content = $params['content'];
        $session_id = $params['session_id']??"";
        $role = $params['role']=="user"?1:2;
        $prompt_type = $params['prompt_type']??0;
        $all_path = $params['all_path']??null;
        $reasoning_content = $params['reasoning_content']??null;
        $from = $this->saveChatMoreParams['from'];
        if($role == 1) $reasoning_content = null;
        if($session_id == "") {
            return;
        }
        $model = null;
        if(isset($params['model']) && $params['model'] != "")
        [$other_payload, $model] = $this->AiTools->setOtherPayload($params['model']);
        $nowtime = time();
        $now = date('Y-m-d H:i:s', $nowtime);
        $sql = "select id from `ai_chat_list` where `userid` = '{$userid}' and `session_id` = '{$session_id}'";
        $chat_id = $this->logs->getone($sql);
        $title = $content;
        if($chat_id == "") {
            $sql = "insert into `ai_chat_list` (`userid`, `session_id`, `title`, `createtime`, `prompt_type`, `from`) values ({$userid}, '{$session_id}', ?, '{$now}', {$prompt_type}, {$from})";
            if (mb_strlen($content) > 80) {
                $title = mb_substr($content, 0, 80) . '...';
            }

            if($content=="") {
                $firstFile = explode("^|-|^", $all_path);
                $fileInfo = pathinfo($this->AiTools->getFileName($firstFile[0]));
                $title = $fileInfo['filename'];
            }

            $this->logs->query($sql, [$title]);
            $chat_id = $this->logs->insert_id();
        }
        $more_field = "";
        $placeholder_list = [$content];
        if($reasoning_content!=null && $reasoning_content!="") {
            $more_field .= ",`reasoning_content` = ?";
            $placeholder_list[] = $reasoning_content;
        }
        if($all_path!=null && $all_path!="" && $role == 1) {
            $more_field .= ",`all_document_filepath` = ?";
            $placeholder_list[] = $all_path;
        }
        if($model!=null && $model!="") {
            $more_field .= ",`model_name` = ?";
            $placeholder_list[] = $model;
        }
        if($content_type!=null && $content_type!=="") {
            $more_field .= ",`content_type` = ?";
            $placeholder_list[] = $content_type;
        }
        $sql = "insert into `ai_chat_detail` set `chat_id` = {$chat_id}, `content` = ?, `role` = {$role}, `created` = FROM_UNIXTIME({$nowtime}) $more_field";
        $this->logs->query($sql, $placeholder_list);
        if(isset($params['self']) && $params['self']==1) {
            return 1;
        } else {
            echo json_encode(["success"=>1, "msg"=>"保存成功", "data"=>['chat_id'=>$chat_id, 'title'=>$title]], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }

    /**
     * 获取用户的聊天列表
     *
     * @param int $userid 用户ID
     * @return array 聊天列表，包含所有未删除的聊天记录
     */
    private function getChatList($userid, $page=1, $pageSize=1500){
        if ($userid == -1 || $userid == "")
            return [];
        $from = $this->saveChatMoreParams['from']??0;
        $sql = "select * from `ai_chat_list` where `userid` = {$userid} and `isdel` = 0 and `from` in ({$from}) order by `createtime` desc";

        if($page>=1) {
            $offset = ($page - 1) * $pageSize;
            $sql .= " limit {$offset}, {$pageSize}"; 
        }
        $chat_list = $this->logs->query($sql);
        return $chat_list;
    }

    /**
     * 设置用户信息
     *
     * 该方法通过登录 Cookie 获取用户信息，并将用户的 ID、真实姓名和用户名存储在类的属性中。
     * 如果获取到的用户信息有效且存在，则会将相关信息赋值给类的属性。
     *
     * @return void
     */
    public function setUserInfo() {
        $master = new masterAction();
        $au = $_SERVER['HTTP_MP_WEIXIN_COOKIE_AUTHORIZATION']??"";
        $userInfo = $master->getUserInfoByLoginCookie($au);
        // www站点网页端账户验证
        if($userInfo!=null && count($userInfo)>0 && isset($userInfo[1]) && !empty($userInfo[1])) {
            $user = $userInfo[1];
            $this->userid = $user['sysUser']['userId']??-1;
            // $this->userid = 29220;
            $this->truename = $user['sysUser']['trueName']??"";
            $this->username = $user['sysUser']['userName']??"";
            $this->member = $user['memberBo']??[];
        } else {
            // 数据中心网页版账户验证 
            $par = file_get_contents("php://input");
            $par = json_decode($par, true);
            $guid = $par['GUID']??$_GET['GUID']??"";
            if(mb_strlen($guid) > 10 && !str_contains($guid, "|||")) {
                $bd = file_get_contents( APP_URL_DC . "/v2.0/user.php?action=getUserProfile&GUID=".$guid, false, stream_context_create(["ssl" => ["verify_peer"=>false,"verify_peer_name"=>false]]));
                $arr = json_decode($bd, true);
                if($arr['data']!=null && $arr['data']!="") {
                    /*"Mid": 1,
                    "Uid": 452459,
                    "ComName": "上海钢之家",
                    "UserName": "zhengnaibin",
                    "TrueName": "郑乃斌",
                    "MobileNumber": "17628725464",
                    "ExpiredDate": "2030-12-31",
                    "web_token": "59863b6da4ad3da147db3fa3c22e09dd4d34547b7607a73df041458f399ca309",
                    "token_expire": "1743468727",
                    "compfname": "上海钢之家信息科技有限公司",
                    "adminid": "17",
                    "adminTruename": "吴文章",
                    "adminMobile": ""*/
                    $user = $arr['data'];
                    $this->userid = $user['Uid']??-1;
                    $this->truename = $user['TrueName']??"";
                    $this->username = $user['UserName']??"";
                    $this->member = [
                        "mid" => $user['Mid'],
                    ];
                }
            } else {
                // 目前后台的文本纠错接口会进入到这一套验证逻辑
                $guidList = explode("|||", $guid);
                if(count($guidList) == 2) {
                    $userid = (int)$guidList[0];
                    $user = $this->_dao->getUserInfoByUserId($userid);
                    if(!empty($user['username']) && $user['username']==$guidList[1]) {
                        $this->userid = $userid;
                        $this->truename = $user['truename'];
                        $this->username = $user['username'];
                        $this->member = [
                            "mid" => 1,
                        ];
                    }
                }
            }
        }

        // 获取会员最高权限，试用不计入权限
        if($this->member['mid']!=null && $this->member['mid']!="") {
            $mid = $this->member['mid'];
            // $mid = 1267260;
            $t_power = $master->getRedisDataObjectByRedisKey(["redisKeyString"=>REDIS_CACHE_KEY_PER_STRING.":MemberPower:".$mid]);
            $member_power = $t_power['data'][1]??[];
            // dd($member_power);
            if($member_power){
                foreach ($member_power as $channel_code => $channel_power_info) {
                    if(intval($channel_power_info['tryFlag']) == 0) {
                        if($channel_power_info['power'] > $this->maxpower) {
                            $this->maxpower = $channel_power_info['power'];
                        }
                    }
                }
            }
        }
    }

    private function noPower(&$params, &$data): never {
        $session_id = $data['session_id']??"";
        $assistant_text = "请升级您的账号权限使用此功能。客服电话：021-50581010(总机) 4008115058。\n";
        if($session_id!="" && $session_id !=null) {
            $this->saveChat(["content"=>$data['content'], "session_id"=>$session_id, "role"=>"user", "prompt_type"=>0, "self"=>1, "model" => $params['model'], "content_type" => 0]);
            $this->saveChat(["content"=>$assistant_text, "session_id"=>$session_id, "role"=>"assistant", "prompt_type"=>0, "self"=>1, "model" => $params['model'], "content_type" => 2]);
        }
        $this->AiTools->printInfo($assistant_text);
        exit;
    }

    public function chat_by_oneapi_old(array $params, array $data, array $moreParams = []) {
        $session_id = $data['session_id']??"";
        $prompt_type = $params['prompt_type']??0;
        $document = $data['document']??"";
        // 默认以流式输出，主要是代码内部调用的时候可能无需流式输出，只需要响应文本
        $stream = isset($params['stream']) && in_array($params['stream'], [false,true,0,1])?$params['stream']:true;
        $content = "";
        $content = $document."\n".$data['content']??"";
        $message = [];
        if($content==null || $content=="") return;
        $this->AiTools->setPrompt($prompt_type, $content);
        $history = "";
        if($session_id != "") {
            [$history] = $this->chatDetail($session_id);
            $this->saveChat(["content"=>$data['content'], "session_id"=>$session_id, "role"=>"user", "prompt_type"=>$prompt_type, "self"=>1, "all_path"=>$data['other']['all_path']??null, "model" => $params['model'], "content_type" => 0]);
        }
        if($history=="") {
            $message[] = [
                "role" => "user",
                "content" => $content
            ];
        } else {
            $message = $history;
            $message[] = [
                "role" => "user",
                "content" => $content
            ];
        }
        foreach ($message as &$message_value) {
            if(trim($message_value['content'])=="") {
                $message_value['content'] = "[系统提示] 服务异常。";
            }
        }

        $client = new Client([
            'base_uri' => ONEAPI_URL
        ]);
        $info = [
            "model" => $params['model']??"ernie-speed-128k",
            "messages" => $message,
            "prompt_type" => $prompt_type,
            "stream" => $stream
        ];
        if($moreParams['temperature']!=null) $info['temperature'] = $moreParams['temperature'];
        if($moreParams['top_p']!=null) $info['top_p'] = $moreParams['top_p'];
        try {
            $response = $client->request('POST', "/v1/chat/completions", [
                'json'         => $this->AiTools->setPayload($info),
                'headers'      => [
                    'Content-Type' => 'application/json',
                    'Accept'       => $stream ? 'text/event-stream' : 'application/json',
                    'Authorization'=> 'Bearer '.ONEAPI_APIKEYS
                ],
                'http_errors'  => false,   // 避免 4xx/5xx 直接抛异常
                'stream'       => $stream,
                'timeout' => 20,
            ]);

            if ($stream) {
                // 获取响应的状态码
                $statusCode = $response->getStatusCode();
                // 将该状态码输出到客户端
                if($statusCode!=200) {
                    http_response_code($statusCode);
                    $errorBody = $response->getBody()->getContents();
                    // echo "data: {\"errorCode\":".$statusCode."}\n\n";
                    echo "data: ".$errorBody."\n\n";
                    echo "data: [DONE]\n\n";
                    exit;
                }

                // 逐行读取响应并输出到客户端
                $body = $response->getBody();
                $res_content = $this->AiTools->outputAndAccumulateStream($body, $session_id);
            } else {
                $res_content = $response->getBody()->getContents();
            }
        } catch (ConnectException $e) {
            $this->saveChat([
                "content" => "[系统提示] 服务异常。",
                "reasoning_content" => null,
                "session_id" => $session_id,
                "prompt_type" => $prompt_type,
                "role" => "assistant",
                "self" => 1, "model" => $params['model'], "content_type" => 2
            ]);
            // 连接超时或拒绝连接
            http_response_code(504); // 504 Gateway Timeout
            echo "data: {\"errorCode\":504, \"message\":\"连接超时或目标服务器拒绝连接\"}\n\n";
            echo "data: [DONE]\n\n";
            exit;
        } catch (RequestException $e) {
            $this->saveChat([
                "content" => "[系统提示] 服务异常。",
                "reasoning_content" => null,
                "session_id" => $session_id,
                "prompt_type" => $prompt_type,
                "role" => "assistant",
                "self" => 1, "model" => $params['model'], "content_type" => 2
            ]);
            // 其他 Guzzle 请求异常，比如 500 服务器错误
            http_response_code(500);
            echo "data: {\"errorCode\":500, \"message\":\"请求失败: " . $e->getMessage() . "\"}\n\n";
            echo "data: [DONE]\n\n";
            exit;
        } catch (\Exception $e) {
            $this->saveChat([
                "content" => "[系统提示] 服务异常。",
                "reasoning_content" => null,
                "session_id" => $session_id,
                "prompt_type" => $prompt_type,
                "role" => "assistant",
                "self" => 1, "model" => $params['model'], "content_type" => 2
            ]);
            // 其他未知错误
            http_response_code(520); // 520: Cloudflare Unknown Error
            echo "data: {\"errorCode\":520, \"message\":\"未知错误: " . $e->getMessage() . "\"}\n\n";
            echo "data: [DONE]\n\n";
            exit;
        }
        $content = "";
        $reasoning_content = "";
        if(!$stream) {
            // dd($res_content);
            $da = json_decode($res_content, true);
            $content .= $da['choices'][0]['message']['content'] ?? "";
            if($da['choices'][0]['message']['reasoning']) {
                $reasoning_content .= $da['choices'][0]['message']['reasoning'];
            } else {
                $reasoning_content .= $da['choices'][0]['message']['reasoning_content'] ?? "";
            }
        } else {
            foreach (explode("data: ", $res_content) as $value) {
                $resp = trim($value);
                if ($resp == "" || $resp == "[DONE]") continue;
                $da = json_decode($resp, true);
                $content .= $da['choices'][0]['delta']['content'] ?? "";
                if($da['choices'][0]['delta']['reasoning']) {
                    $reasoning_content .=  str_replace("\\n", "\n", $da['choices'][0]['delta']['reasoning']);
                } else {
                    $reasoning_content .= $da['choices'][0]['delta']['reasoning_content'] ?? "";
                }
            }
        }
        
        $logDir = './logs/'.date('Y-m-d')."/chat/";
        // 检查目录是否存在，不存在则创建（递归创建子目录）
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        if($session_id) file_put_contents($logDir.$session_id.".log", $content."\n----------------------\n", FILE_APPEND);

        $this->saveChat([
            "content" => $content,
            "reasoning_content" => $reasoning_content,
            "session_id" => $session_id,
            "prompt_type" => $prompt_type,
            "role" => "assistant",
            "self" => 1, "model" => $params['model'], "content_type" => 0
        ]);
        // 结束 SSE 连接（仅流模式需要）
        if ($stream) {
            header("Connection: close");
            exit;
        } else {
            return [$content, $reasoning_content];
        }
    }

    /**
     * Summary of chat_by_oneapi
     * 注意$params['model']参数，此参数有效值参考AiTools类中setOtherPayload()的model_name值
     * @param array $params，
     * @param array $data
     * @param array $moreParams
     * @return array<array|string>
     */
    public function chat_by_oneapi(array $params, array $data, array $moreParams = []) {
        $session_id = $data['session_id']??"";
        $prompt_type = $params['prompt_type']??0;
        $document = $data['document']??"";
        // 部分内部调用需要获取历史的对话记录，但是需要控制是否保存对话记录
        $is_save_chat = $moreParams['is_save_chat']??true;
        // 默认以流式输出，主要是代码内部调用的时候可能无需流式输出，只需要响应文本
        $stream = isset($params['stream']) && in_array($params['stream'], [false,true,0,1])?$params['stream']:$moreParams['stream']??true;
        $content = "";
        $content = $document."\n".$data['content']??"";
        $message = [];
        if($content==null || $content=="") return;
        $this->AiTools->setPrompt($prompt_type, $content);
        $history = "";
        if($session_id != "") {
            [$history] = $this->chatDetail($session_id);
            if($is_save_chat) {
                $this->saveChat(["content"=>$data['content'], "session_id"=>$session_id, "role"=>"user", "prompt_type"=>$prompt_type, "self"=>1, "all_path"=>$data['other']['all_path']??null, "model" => $params['model'], "content_type" => 0]);
            }
        }

        $content_type = 0;
        $webSearchResult = [];
        if(isset($moreParams['webSearchResult'])) {
            $webSearchResult = $moreParams['webSearchResult']??[];
            unset($moreParams['webSearchResult']);
            if(isset($data['isSearch']) && $data['isSearch']=="1") {
                $this_systemMessage = "";
                if(isset($webSearchResult['data']['webPages']['value']) && count($webSearchResult['data']['webPages']['value'])>0) {
                    $today = date("Y年n月j日");
                    $this_systemMessage .= "今天是".$today."。\n\n请根据以下提供的所有搜索结果进行一些比较人性化的回答。请综合不同来源的信息，去除冗余部分，并给出清晰、准确的答案。确保回答简洁、逻辑清晰，并且如有必要，提供适当的数据或背景信息来支持答案。如果搜索结果存在矛盾，请标注可能的不同观点或解释。禁止在回答中说出搜索结果和来源。以下是一些搜索引擎返回的搜索结果：\n";
                    foreach ($webSearchResult['data']['webPages']['value'] as $key => $value) {
                        // $this_systemMessage .= "搜索结果".($key+1).":\n链接地址：".$value['url']."\n标题：".$value['name']."\n摘要：".$value['summary']."\n\n";
                        $this_systemMessage .= "搜索结果".($key+1).":\n标题：".$value['name']."\n摘要：".$value['summary']."\n\n";
                    }
                }
                if($history=="") {
                    $history = [];
                    $history[] = [
                        "role" => "system",
                        "content" => $this_systemMessage
                    ];
                } else {
                    // **插入新的 system 消息**
                    array_unshift($history, [
                        "role" => "system",
                        "content" => $this_systemMessage
                    ]);
                }
                $content_type = 1;
            }
        }

        if($history=="") {
            $message[] = [
                "role" => "user",
                "content" => $content
            ];
        } else {
            $message = $history;
            $message[] = [
                "role" => "user",
                "content" => $content
            ];
        }
        foreach ($message as &$message_value) {
            if(trim($message_value['content'])=="") {
                $message_value['content'] = "[系统提示] 服务异常。";
            }
        }

        $model = $params['model']??"ernie-speed-128k";

        $info = [
            "model" => $model,
            "messages" => $message,
            "prompt_type" => $prompt_type,
            "stream" => (bool)$stream,
            "content_type" => $content_type,
        ];
        $info = array_replace_recursive($info, $moreParams);
        // dd($info);
        $content = "";
        $reasoning_content = "";
        $tool_calls = [];
        $streamResponseData = null;
        try {
            $payload = $this->AiTools->setPayload($info);
            $client = OpenAI::factory()
            ->withApiKey(ONEAPI_APIKEYS)
            ->withBaseUri(ONEAPI_URL.'/v1')
            ->make();
            // dd($payload);
            // file_put_contents("./app/index/info.txt.bak", json_encode($payload, JSON_UNESCAPED_UNICODE), FILE_APPEND);
            if ($stream) {
                // 流式输出
                $streamResponseData = $client->chat()->createStreamed($payload);
            } else {
                // 非流式输出
                $response = $client->chat()->create($payload);
                // $response = $client->models()->list();
                // dd($response->data);
            }
        } catch (ConnectException $e) {
            if($is_save_chat) {
                $this->saveChat([
                    "content" => "[系统提示] 服务异常。",
                    "reasoning_content" => null,
                    "session_id" => $session_id,
                    "prompt_type" => $prompt_type,
                    "role" => "assistant",
                    "self" => 1,
                    "model" => $model,
                    "content_type" => 2
                ]);
            }
            // 连接超时或拒绝连接
            http_response_code(504); // 504 Gateway Timeout
            echo "data: {\"errorCode\":504, \"message\":\"连接超时或目标服务器拒绝连接\"}\n\n";
            echo "data: [DONE]\n\n";
            exit;
        } catch (RequestException $e) {
            if($is_save_chat) {
                $this->saveChat([
                    "content" => "[系统提示] 服务异常。",
                    "reasoning_content" => null,
                    "session_id" => $session_id,
                    "prompt_type" => $prompt_type,
                    "role" => "assistant",
                    "self" => 1,
                    "model" => $model,
                    "content_type" => 2
                ]);
            }
            // 其他 Guzzle 请求异常，比如 500 服务器错误
            http_response_code(500);
            echo "data: {\"errorCode\":500, \"message\":\"请求失败: " . $e->getMessage() . "\"}\n\n";
            echo "data: [DONE]\n\n";
            exit;
        } catch (\Exception $e) {
            if($is_save_chat) {
                $this->saveChat([
                    "content" => "[系统提示] 服务异常。",
                    "reasoning_content" => null,
                    "session_id" => $session_id,
                    "prompt_type" => $prompt_type,
                    "role" => "assistant",
                    "self" => 1,
                    "model" => $model,
                    "content_type" => 2
                ]);
            }
            // 其他未知错误
            http_response_code(520); // 520: Cloudflare Unknown Error
            echo "data: {\"errorCode\":520, \"message\":\"未知错误: " . $e->getMessage() . "\"}\n\n";
            echo "data: [DONE]\n\n";
            exit;
        }

        if ($stream) {
            // 流式输出
            // $response = $client->models()->list();
            try{
                foreach($streamResponseData as $response){
                    $data = $response->toArray();
                    // dd($response);
                    if(isset($data['choices'][0]['delta']['content'])) {
                        $content .= $data['choices'][0]['delta']['content'];
                    }
                    if(isset($data['choices'][0]['delta']['reasoning_content'])) {
                        $reasoning_content .= $data['choices'][0]['delta']['reasoning_content'];
                    }
                    if(isset($data['choices'][0]['delta']['reasoning'])) {
                        $reasoning_content .= $data['choices'][0]['delta']['reasoning'];
                    }
                    $filteredData = array_diff_key($data, array_flip(['model', 'id']));
                    $filteredData['content_type'] = $content_type;
                    echo "data: ".json_encode($filteredData, JSON_UNESCAPED_UNICODE) . "\n\n";
                }
            } catch (\Throwable $e) {
                // print_r($e->getMessage());
                // 调用deepseek-r1官方的模型时会有以下错误，暂不修改第三方插件，所以本地捕获后不处理：
                // 该错误的出现是因为usage的响应结果中，usage的摸个参数不存在
                // OpenAI\Responses\Chat\CreateResponseUsageCompletionTokensDetails::__construct(): Argument #3 ($acceptedPredictionTokens) must be of type int, null given, called in /www/wwwroot/libs/vendor/openai-php/client/src/Responses/Chat/CreateResponseUsageCompletionTokensDetails.php on line 35
                    $errorDir = './logs/'.date('Y-m-d')."/error/";
                    // 检查目录是否存在，不存在则创建（递归创建子目录）
                    if (!is_dir($errorDir)) {
                        mkdir($errorDir, 0777, true);
                    }
                    if($session_id) file_put_contents($errorDir.$session_id.".log", $e->getMessage()."\n----------------------\n", FILE_APPEND);
                    else file_put_contents($errorDir.$this->userid."_no_session_id.log", $content."\n----------------------\n", FILE_APPEND);
            }
            echo "data: [DONE]";
        } else {
            // 非流式输出
            // $response->model;
            // $response->toArray();
            // dd($response->toArray());
            foreach ($response->choices as $choice) {
                // $choice->finishReason; // 'stop'、'tool_calls'
                if($choice->finishReason == "tool_calls") {
                    $tool_calls = $choice->message->toArray()['tool_calls']??null;
                } else {
                    $content .= $choice->message->content??"";
                    $reasoning_content .= $choice->message->reasoning_content??$choice->message->reasoning??"";
                }
            }
        }

        /*$logDir = './logs/'.date('Y-m-d')."/chat/";
        // 检查目录是否存在，不存在则创建（递归创建子目录）
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        if($session_id) file_put_contents($logDir.$session_id.".log", $content."\n----------------------\n", FILE_APPEND);
        else file_put_contents($logDir.$this->userid."_no_session_id.log", $content."\n----------------------\n", FILE_APPEND);*/
        if($is_save_chat) {
            $this->saveChat([
                "content" => $content,
                "reasoning_content" => $reasoning_content,
                "session_id" => $session_id,
                "prompt_type" => $prompt_type,
                "role" => "assistant",
                "self" => 1,
                "model" => $model,
                "content_type" => $content_type,
            ]);
        }
        // 结束 SSE 连接（仅流模式需要）
        if ($stream) {
            header("Connection: close");
            exit;
        } else {
            return [$content, $reasoning_content, $tool_calls];
        }
    }

    private function generateSummary($document, $maxTokens) {
        $prompt = "请对以下文档生成摘要，字数不超过 " . ($maxTokens / 4) . " 个字：\n\n" . $document;
        [$body] = $this->chat_by_oneapi(['prompt_type' => 6, 'model' => 'ernie', 'stream' => false], ["document"=>$prompt], ['is_save_chat' => false]);
        return $body ?? '摘要生成失败';
    }

    private function splitAndSummarize($document, $maxTokens) {
        $chunks = $this->splitDocument($document, (int)($maxTokens * 0.8)); // 预留一些 token 空间
        $summary = "";
        foreach ($chunks as $index => $chunk) {
            $prompt = "文档第 " . ($index + 1) . " 部分：\n\n" . $chunk . "\n\n"
                    . "请基于之前的内容，更新当前的总结：\n" . $summary;
            [$body] = $this->chat_by_oneapi(['prompt_type' => 6, 'model' => 'ernie', 'stream' => false], ["document"=>$prompt], ['is_save_chat' => false]);
            $summary = $body ?? $summary; // 逐步累积摘要
        }
    
        return $summary;
    }
    
    private function splitDocument($document, $maxLength) {
        $parts = [];
        while (mb_strlen($document, 'utf-8') > $maxLength) {
            $parts[] = mb_substr($document, 0, $maxLength, 'utf-8');
            $document = mb_substr($document, $maxLength, null, 'utf-8');
        }
        if (!empty($document)) {
            $parts[] = $document;
        }
        return $parts;
    }

    private function strToDate($date_list){
        $startDate = date("Y-m-d", strtotime("lastday"));
        $endDate = date("Y-m-d", strtotime("lastday"));
        if(!empty($date_list)) {
            $dateStr = "";
            // dump($date_list);
            // [$dateStr, $reaeon] = $this->chat_by_oneapi(['prompt_type' => 5, 'model' => 'ernie', 'stream' => false], ["content"=>"当前日期为".date("Y年m月d日")."，计算日期：".implode(" ", $date_list)], ['is_save_chat' => false]);
            // [$dateStr, $reaeon] = $this->chat_by_oneapi(['prompt_type' => 5, 'model' => 'deepseek-v3-openrouter', 'stream' => false], ["content"=>"当前日期为".date("Y年m月d日")."，计算日期：".implode(" ", $date_list)], ['is_save_chat' => false]);
            [$dateStr, $reaeon] = $this->chat_by_oneapi(['prompt_type' => 5, 'model' => 'qwen-max-0125', 'stream' => false], ["content"=>"当前日期为".date("Y年m月d日")."，计算日期：".implode(" ", $date_list)], ['is_save_chat' => false]);
            $dateStr = trim($dateStr);
            // dd($dateStr);
            if (preg_match('/(\d{4}-\d{2}-\d{2})(?:\s*~\s*(\d{4}-\d{2}-\d{2}))?/', $dateStr, $matches)) {
                $startDate = $matches[1] ?? $date_list;
                $endDate = $matches[2] ?? $matches[1] ?? $date_list;
            }
        }
        return [$startDate, $endDate];
    }
    
    private function setAiPower() {
        if($this->maxpower > 4 || intval($this->member['mid']??0) === 1) {
            $this->haveAiPower = true;
        }
    }

    /**
     * 通过AI判断是否需要进行网络搜索并优化搜索关键词
     * 
     * @param string $content 用户输入的内容
     * @return array 包含是否需要搜索的标志和优化后的搜索关键词
     */
    private function checkNeedWebSearch(array $params, array $data): array
    {
        // 默认结果
        $result = [
            "needSearch" => false,
            "searchQuery" => $data['content']
        ];

        // 如果内容为空，不需要搜索
        if (empty(trim($data['content']))) {
            return $result;
        }

        // 添加功能调用定义
        $tools = [
            [
                "type" => "function",
                "function" => [
                    "name" => "determine_search_need",
                    "description" => "判断是否需要网络搜索并提取关键搜索词",
                    "parameters" => [
                        "type" => "object",
                        "properties" => [
                            "needSearch" => [
                                "type" => "boolean",
                                "description" => "是否需要进行网络搜索来回答问题"
                            ],
                            "searchQuery" => [
                                "type" => "string",
                                "description" => "如果需要搜索，提供最优化的搜索关键词，应简洁明了，只保留必要词汇；如果不需要搜索，返回空字符串"
                            ],
                            "reason" => [
                                "type" => "string",
                                "description" => "简要说明判断理由"
                            ]
                        ],
                        "required" => ["needSearch", "searchQuery", "reason"]
                    ]
                ]
            ]
        ];

        $thismodel = 'qwen-max';
        // $thismodel = 'deepseek-v3';
        [$body,$reason,$call_tools] = $this->chat_by_oneapi(['prompt_type' => 8], ["content"=>$data['content'], "session_id"=>$data['session_id']], ['model' => $thismodel, 'stream' => false, 'tools' => $tools, "tool_choice" => "auto", "is_save_chat" => false]);
        // dump($body);
        // dump($call_tools);
        try {
            if (isset($call_tools[0]['function']['arguments'])) {
                $functionArgs = json_decode($call_tools[0]['function']['arguments'], true);
                // dump($functionArgs);
                if (isset($functionArgs['needSearch'])) {
                    $result['needSearch'] = $functionArgs['needSearch'];
                }
                
                if (isset($functionArgs['searchQuery']) && !empty($functionArgs['searchQuery'])) {
                    $result['searchQuery'] = $functionArgs['searchQuery'];
                }
                
                // 可选：记录AI的判断理由
                if (isset($functionArgs['reason'])) {
                    $result['reason'] = $functionArgs['reason'];
                }
            }
        } catch (\Throwable $e) {
            // 发生异常时，默认为需要搜索，使用原始内容
        }
        // dd($result);
        return $result;
    }

    public function getAllBalance($params) {
        if($params['keys'] == 'this_steelhome'){
            $qy_con = '';
            // 博查AI的余额
            $money1 = $this->AiTools->getBalanceOfBOCHA();
            $qy_con = '博查账户余额：'.$money1;
            if($money1!=null && $money1>-100 && $money1<1){
                $qy_con .= '\n博查账户余额不足，请及时充值！';
            }
            // deepseek的余额
            $money2 = $this->AiTools->getBalanceOfDeepseek();
            $qy_con .= '\ndeepseek账户余额：'.$money2;
            if($money2!=null && $money2>-100 && $money2<1){
                $qy_con .= '\ndeepseek账户余额不足，请及时充值！';
            }
            echo $qy_con;
        } else {
            header("HTTP/1.1 502 Bad Gateway");
        }
        exit;
    }
}