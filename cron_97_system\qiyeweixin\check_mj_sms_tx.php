<?php
ini_set('display_errors',1);
error_reporting(1);
include_once ("/etc/steelconf/config/isholiday.php");
//include_once( '../include/configall.php');
include('/etc/steelconf/config/config.php');
require_once("/etc/steelconf/env/www_env.php");
//require("../include/adodb/adodb.inc.php");
require_once("/usr/local/www/libs/vendor/autoload.php");

include_once('/usr/local/www/libs/phpQuery/phpQuery.php');

include('/etc/steelconf/sthframe/steelhome_db_config.php');

//未上传资讯提醒到各个群    策略报告、月报、一周综述...
$connall = ADONewConnection('mysql');  # create a connection
//$connall->Connect("$hostname_steelhome","$username_steelhome","$password_steelhome","$database_steelhome");
$connall->Connect($hostname_steelhome_r, $username_steelhome_r, $password_steelhome_r, $database_steelhome_r);

$conn_oa = ADONewConnection('mysql');
$conn_oa->Connect(HOST_NAME_OA.":".HOST_PORT_OA, USER_NAME_OA, USER_PASSWORD_OA, DATABASE_NAME_OA);

$conn_sms = ADONewConnection('mysql');
$conn_sms->Connect(HOST_NAME_SMSWRITE.":".HOST_PORT_SMSWRITE, USER_NAME_SMSWRITE, USER_PASSWORD_SMSWRITE, DATABASE_NAME_SMSWRITE);

//煤焦群短信提醒(10-12  18-22 十分钟执行一次）

//煤焦群
$WEBHOOK15=WEBHOOK15;
//测试
// $hook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80310365-89bc-4adc-a967-2eaa1ab67bce";
// $WEBHOOK15=$hook;



$debug =$_REQUEST['debug'];
$date = date("Y-m-d");
if(isset($_REQUEST['date'])){
	$date = $_REQUEST['date'];
}


//当前时分
$nowtime=date('H:i:s');
if(isset($_REQUEST['time'])){
	$nowtime = $_REQUEST['time'];
}
$time=date('H:i',strtotime($nowtime));
$weekday=array(7,1,2,3,4,5,6);
$week=date("w",strtotime($date));
$t=$weekday[$week];

/****************************************************************** */
//短信发送未发送   到点提醒
$sms_list=array(
	"0"=>array(
			"time"=>array("10:00","10:30","11:00"),//每天
			"t"=>array(1,2,3,4,5,6,7),//每天
			"sms"=>array(
				"8264"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				),
				"224"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				),
				"556"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				),
				"4629"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				),
			)
	),
	"1"=>array(
		//焦粉焦粒短信
			"time"=>array("10:50","11:20","11:50"),//每天
			"t"=>array(1,3,5),//周一、三、五
			"sms"=>array(
				"912"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('479693'),//@张全勇
				),
				"913"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('479693'),//@张全勇
				)
			)
		
	),
	"2"=>array(
			"time"=>array("10:50","11:20","11:50"),//每天
			"t"=>array(2,4),//周二、四
			"sms"=>array(
				"628"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('215955'),//@陈云
				),
				"914"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('215955'),//@陈云
				),
				"911"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('215955'),//@陈云
				)
			)
	),
	"3"=>array(
			"time"=>array("19:00","20:00","21:00"),//每天
			"t"=>array(4),//四
			"sms"=>array(
				"3153"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('479693'),//@张全勇
				),
				"4352"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				),
				"4350"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				)
			)
	),
	"4"=>array(
			"time"=>array("10:00","11:00","11:50"),//每天
			"t"=>array(1,2,3,4,5,6,7),//每天
			"sms"=>array(
				"426"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('479693'),//@张全勇
				),
				"427"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('479693'),//@张全勇
				),
				"548"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('215955'),//@陈云
				),
				"547"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('215955'),//@陈云
				),
				"567"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('215955'),//@陈云
				)
			)
	),
	"5"=>array(
			"time"=>array("18:00","19:00","20:00"),//每天
			"t"=>array(1,2,3,4,5,6,7),//每天
			"sms"=>array(
				"9173"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('476235'),//@魏俊婷
				),
				"8888"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('476235'),//@魏俊婷
				),
				"861"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('174516'),//@万烨
				)
			)
	),
	"6"=>array(
			"time"=>array("10:30","11:00","11:50"),//每天
			"t"=>array(1,2,3,4,5,6,7),//每天
			"sms"=>array(
				"8936"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('476235'),//@魏俊婷
				),
				"8937"=>array(
					"dept"=>array('WEBHOOK15'),// 发煤焦群
					"user"=>array('476235'),//@魏俊婷
				),
			)
	),
);

// echo '<pre>';
// print_r($sms_list);exit;
$sms_message=array(
	"8264"=>"一级冶金焦",
	"224"=>"二级冶金焦",
	"556"=>"准一级冶金焦",
	"4629"=>"准一级干熄焦",
	"912"=>"焦粉",
	"913"=>"焦粒",
	"628"=>"甲醇",
	"914"=>"液氨",
	"911"=>"天然气",
	"3153"=>"独立焦企产能利用率@全国+分地区",
	"4352"=>"独立焦企100家@产能利用率+煤焦库存",
	"4350"=>"焦炭库存@港口",
	"426"=>"粗苯",
	"427"=>"煤焦油",
	"548"=>"粗苯",
	"547"=>"煤焦油",
	"567"=>"硫酸铵",
	"9173"=>"喷吹煤指数",
	"8888"=>"钢之家炼焦煤指数",
	"8936"=>"国内代表市场炼焦煤",
	"8937"=>"国内代表市场喷吹煤",
	"861"=>"钢之家焦炭指数"
);

$remind_message_sms=array();
if(!_isholiday($date) ||(_isholiday($date) && $t>=1 && $t<=5)){
	//工作日 到点提醒
	foreach($sms_list as $key=>$sms_arr){
		if(!empty($sms_arr['time']) && in_array($time,$sms_arr['time']) && isset($sms_arr['t']) && in_array($t,$sms_arr['t'])){
			if(!empty($sms_arr['sms'])){
				foreach($sms_arr['sms'] as $sms_id=>$sms_data){
					$is_send=check_sms_send($conn_sms,$sms_id,$date);
					if(!$is_send){
						foreach($sms_data['dept'] as $dept){
							foreach($sms_data['user'] as $user){
								$remind_message_sms[$dept][$user][]=$sms_id;
							}
						}
					}
				}
			}
		}
	}
}

// echo '<pre>';
// print_r($remind_message_sms);exit;
$qyuser_list= get_qyuserid($conn_oa);
$str="# **短信未发布提醒**\n\n";
foreach($remind_message_sms as $hook=>$v){
	$webhook=getVariableByName($hook); 
	$sms_content="";
	foreach($v as $userid=>$v2){
		$sms_content.="<@".$qyuser_list[$userid].">";
		foreach($v2 as $k3=>$sms_id){
			$sms_content.=$sms_message[$sms_id]."【".$sms_id."】";
			if($k3<count($v2)-1){
				$sms_content.="、";
			}
		}
		$sms_content.="\n";
	}
	echo $webhook.'<br>';
	echo $str.'<br>'.$sms_content.'<br>';

	if ($sms_content!="" && $debug!=1) {
		$content=$str.$sms_content;
        $template = '{
            "msgtype": "markdown",
            "markdown": {
                "content": "'.$content.'"
            }
        }';
        $dataRes1 = request_post( $webhook, $template );
		send_log($conn_oa,"煤焦部",$content,$webhook);
    }
}

if(empty($remind_message_sms)){
	echo "<br>**无未发送短信**";
}




function getVariableByName($name) {
	global $$name;
	return $$name;
}

//获取企业微信用户id
function get_qyuserid($conn_oa){
	$qyuserid_all=$conn_oa->query("select userid,qyuserid from qywxbindadminuser  where qyuserid!='' order by id desc ");
	$qyuserid_arr=array();
	foreach($qyuserid_all as $key=>$value){
		if(!isset($qyuserid_arr[$value['userid']])){
			$qyuserid_arr[$value['userid']]=$value['qyuserid'];
		}
	}
	return $qyuserid_arr;
}



//检测是否发送过短信
function check_sms_send($conn_sms,$sms_id,$date){
	$last_send_date=$conn_sms->getOne("select MS_LAST_DATE from MESSAGE where MS_ID='".$sms_id."' ");
	if(date("Y-m-d",strtotime($last_send_date))!=$date){
		return false;
	}else{
		return true;
	}	
}



//提醒记录
function send_log($conn_oa,$dept,$content,$senduser){
	if($content!=""){
		$sql="insert into qywx_qr_sp set logid=0,qjtaskid=0,type=30,createtime=NOW(),title='".$dept."未上传资讯提醒',description='$content',senduser='$senduser',msgtype='textcard'";
		$conn_oa->execute($sql);
	}
}


function request_post($url = '', $param = '')  {
	if (empty($url) || empty($param))
	{
		return false;
	}
	$curl = curl_init(); // 启动一个CURL会话
	curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
	curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
	curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
	curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
	curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
	curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
	curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
	curl_setopt($curl, CURLOPT_POSTFIELDS, $param); // Post提交的数据包
	curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
	curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
	$data = curl_exec($curl); // 执行操作
	if (curl_errno($curl)) {
		echo 'Errno'.curl_error($curl);//捕抓异常
	}
	curl_close($curl); // 关闭CURL会话
	return $data; // 返回数据，json格式
}