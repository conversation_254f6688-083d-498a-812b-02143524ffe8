<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");
include_once(APP_DIR . "/master/masterController.inc.php");
include_once(APP_DIR . "/master/masterDao.inc.php");

class special_columnController extends masterController
{

    public function __construct()
    {
        parent::__construct();
    }

    // 用户SESSION检测
    public function _dopre()
    {
        $this->_action->checkSessionStation($this->_request);
    }

    //专栏录入页面
    public function v_index()
    {
        $this->_action->index($this->_request);
    }

    public function do_addnews()
    {
        $this->_action->addnews($this->_request);
    }

    //专栏管理页面
    public function v_manage()
    {
        $this->_action->manage($this->_request);
    }

    //专栏收入明细页面
    public function v_income()
    {
        $this->_action->income($this->_request);
    }


    /**
     *
     * @return void
     * <AUTHOR> 2024-03-05
     * todo 获取信息列表
     */
    public function do_getarticlelist()
    {
        $this->_action->getarticlelist($this->_request);
    }

    /**
     *
     * @return void
     * <AUTHOR> 2024-03-08
     * todo 获取支付的明细
     */
    public function do_getincomelist()
    {
        $this->_action->getincomelist($this->_request);
    }

    /**
     *
     * @return void
     * <AUTHOR> 2024-03-06
     * todo 执行删除专栏信息
     */
    public function do_updatearticle()
    {
        $this->_action->updatearticle($this->_request);
    }

     /**
     * @Author: shizg
     * @Date: 2025-08-20 09:41:32
     * @parms: 
     * @return: SYMBOL_TYPE 
     * @Description: 我购买的信息页面
    */
     public function v_my_purchase()
     {
         $this->_action->my_purchase($this->_request);
     }

     /**
     * @Author: shizg
     * @Date: 2025-08-20 09:41:32
     * @parms: 
     * @return: SYMBOL_TYPE 
     * @Description: 获取我购买的信息
    */
     public function do_getpurchasenewslist()
     {
         $this->_action->getpurchasenewslist($this->_request);
     }

}
