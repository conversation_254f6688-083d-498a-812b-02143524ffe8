<?php

	//update by shizg for wap/web 切换 started 2017/06/13
	if($_REQUEST['memberid']&&$_REQUEST['adminid']){
	}else{
		// if (  empty($_COOKIE['Authorization']) ){
		// $_SESSION['id'] = "417037";
			if (!isset( $_SESSION['id'])){
				echo "<script>window.location='/MemberLogin.php?urlstr=/Member/index.php';</script>";
				exit;
			}else{
				if ($_SESSION['id']==0){
					echo "<script>window.location='/MemberLogin.php?urlstr=/Member/index.php';</script>";
					exit;
				}
			}
		// }

	}
	//update by shizg for wap/web 切换 started 2017/06/13
	$arr_wszy=array("7"=>3000,"6"=>2000,"5"=>1000,"4"=>1000,"3"=>1000,"2"=>0,"0"=>"0","other"=>"0");
	$arr_level=array("7"=>"vip","6"=>"甲级","5"=>"乙级","4"=>"丙级","3"=>"资源","2"=>"免费","other"=>"非法登陆");
	$wszy_power=3;
	$wsly_power=3; $wsxh_power=6;
	$array_settop=array("84"=>"10","113"=>"9","194"=>"8","326"=>"7","46885"=>"6");
  function member_memo($gcpower,$llpower)
  {  
     global $arr_level,$arr_wszy; 
     switch ($gcpower){
	 case 7: $arr_memo["gc"]="您是钢材频道".$arr_level["7"]."会员,可发布钢材频道资源".$arr_wszy["7"]."条。";break;
	 case 6: $arr_memo["gc"]="您是钢材频道".$arr_level["6"]."会员,可发布钢材频道资源".$arr_wszy["6"]."条。";break;
	 case 5: $arr_memo["gc"]="您是钢材频道".$arr_level["5"]."会员,可发布钢材频道资源".$arr_wszy["5"]."条。";break;
	 case 4: $arr_memo["gc"]="您是钢材频道".$arr_level["4"]."会员,可发布钢材频道资源".$arr_wszy["4"]."条。";break;
	 case 2: $arr_memo["gc"]="您是钢材频道".$arr_level["2"]."会员,可发布钢材频道资源".$arr_wszy["2"]."条。";break;
	 default: $arr_memo["gc"]="您不是钢材频道会员,您不可发布钢材频道资源。";
	 }
	 switch ($llpower){
	 case 7: $arr_memo["ll"]="您是炉料频道".$arr_level["7"]."会员,可发布炉料频道资源".$arr_wszy["7"]."条。";break;
	 case 6: $arr_memo["ll"]="您是炉料频道".$arr_level["6"]."会员,可发布炉料频道资源".$arr_wszy["6"]."条。";break;
	 case 5: $arr_memo["ll"]="您是炉料频道".$arr_level["5"]."会员,可发布炉料频道资源".$arr_wszy["5"]."条。";break;
	 case 4: $arr_memo["ll"]="您是炉料频道".$arr_level["4"]."会员,可发布炉料频道资源".$arr_wszy["4"]."条。";break;
	 case 2: $arr_memo["ll"]="您是炉料频道".$arr_level["2"]."会员,可发布炉料频道资源".$arr_wszy["2"]."条。";break;
	 default: $arr_memo["ll"]="您不是炉料频道会员,您不可发布炉料频道资源。";
	 }
	 return $arr_memo;
  }
  function member_arr($conn,$id)
	{
	   $sql="SELECT adminuser.truename,adminuser.isadmin,adminuser.ismaster,adminuser.zyflag, member.mbid,member.compfname,member.compabb,
	   member.gcpower,member.llpower,member.yspower,member.gctryendtime,member.gctrypower,member.lltryendtime,member.lltrypower,member.ystryendtime,member.ystrypower,member.mright,member.xmlstr_rel,member.guest_tag  FROM adminuser,`member` WHERE adminuser.mid=member.mbid and id=$id limit 1";
	   $rs=$conn->Execute($sql);
	   while (!$rs->EOF)
	   {
	      $arr_member["truename"]=$rs->fields("truename");		  $arr_member["isadmin"]=$rs->fields("isadmin");
		  $arr_member["ismaster"]=$rs->fields("ismaster");        $arr_member["company"]=$rs->fields("compfname");
		  $arr_member["compabb"]=$rs->fields("compabb");          $arr_member["zyflag"]=$rs->fields("zyflag");
		  $arr_member["gcpower"]=$rs->fields("gcpower");          
		  $arr_member["mright"]=$rs->fields("mright");
		  $arr_member["llpower"]=$rs->fields("llpower");
		  $arr_member["guest_tag"]=$rs->fields("guest_tag");
			if($rs->fields('gcpower')=="1" and $rs->fields('gctryendtime')>date("Y-m-d H:i:s")){
				$arr_member["gcpower"]=$rs->fields('gctrypower');
			}
			if($rs->fields('gcpower')=="1" and $rs->fields('gctryendtime')<date("Y-m-d H:i:s")){
				$arr_member["gcpower"]=2;
			}
			if($rs->fields('gcpower')<>"1"){
				$arr_member["gcpower"]=$rs->fields('gcpower');
			}
			if($rs->fields('llpower')=="1" and $rs->fields('lltryendtime')>date("Y-m-d H:i:s")){
				$arr_member["llpower"]=$rs->fields('lltrypower');//试用会员等于乙级会员
			}
			if($rs->fields('llpower')=="1" and $rs->fields('lltryendtime')<date("Y-m-d H:i:s")){
				$arr_member["llpower"]="2";
			}
			if($rs->fields('llpower')<>"1"){
				$arr_member["llpower"]=$rs->fields('llpower');
			}	
			
		  if($rs->fields('yspower')=="1" and $rs->fields('ystryendtime')>date("Y-m-d H:i:s")){
				$arr_member["yspower"]=$rs->fields('ystrypower');//试用会员等于乙级会员
			}
			if($rs->fields('yspower')=="1" and $rs->fields('ystryendtime')<date("Y-m-d H:i:s")){
				$arr_member["yspower"]="2";
			}	 
			if($rs->fields('yspower')<>"1"){
				$arr_member["yspower"]=$rs->fields('yspower');
			}	 
			$arr_member["mbid"]=$rs->fields("mbid");
		  		  $arr_member["xmlstr_rel"]=$rs->fields("xmlstr_rel");
		  $rs->MoveNext();
	   }
	   return $arr_member;
	}
	 function wszy_today($conn,$memberid)
	{
	   $today=date("Y-m-d");
	   $sql="SELECT * FROM memberzb WHERE  messagedate>'".$today." 00:00' and messagedate<'".$today." 23:59' and memberid='".$memberid."' limit 4";
	   $rs=$conn->Execute($sql);
	   $arr_num["ygc"]=0;$arr_num["yll"]=0;$arr_num["yys"]=0;
	   while (!$rs->EOF)
	   {
	     $wtype=$rs->fields("wtype");$ch=$rs->fields("mchannelid");$num=$rs->fields("messagenumb");
		 if ($ch=="02"){
		   $arr_num["ygc"]+=$num;
		 }elseif ($ch=="04")
		 {$arr_num["yll"]+=$num;}
		 elseif ($ch=="08")
		 {$arr_num["yys"]+=$num;}
		 $rs->MoveNext();
	   }
	   return $arr_num;
	}
	function gqkx_today($conn,$memberid)
	{ global $newarray;
	   $today=date("Y-m-d");
	   $sql="SELECT COUNT(*)  FROM memberboard WHERE  updatetime>'".$today." 00:00:00' and updatetime<'".$today." 23:59:59' and memberid='".$memberid."' and  bchannelid='02' ";
	 
	    $rs=$conn->Execute($sql);
	    $result=$rs->FetchRow();
		$alreadygangcai=$result[0];
		
		$sql="SELECT COUNT(*)  FROM memberboard WHERE  updatetime>'".$today." 00:00:00' and updatetime<'".$today." 23:59:59' and memberid=$memberid and  bchannelid='04'";
	       $rs2=$conn->Execute($sql);
	    $result2=$rs2->FetchRow();
		$alreadyluliao=$result2[0];
		
if($_SESSION['gcpower']==4){ $maxnumbgc=1;}
if($_SESSION['gcpower']==5){ $maxnumbgc=1;}
if($_SESSION['gcpower']==6){ $maxnumbgc=3;}
if($_SESSION['gcpower']==7){ $maxnumbgc=5;}
if($_SESSION['gcpower']>7){ $maxnumbgc=1000;}
if($_SESSION['llpower']==4){ $maxnumbll=1;}
if($_SESSION['llpower']==5){ $maxnumbll=1;}
if($_SESSION['llpower']==6){ $maxnumbll=3;}
if($_SESSION['llpower']==7){ $maxnumbll=5;}
if($_SESSION['llpower']>7){ $maxnumbll=1000;}
		
		$newarray=array("0"=>$alreadygangcai,"1"=>$alreadyluliao,"2"=>$maxnumbgc,"3"=>$maxnumbll);  
		
		}
	
	function boption_str($value,$num=1,$bid="")
	{
	  global $array1,$array2a;
	  if ($num==1){
	     foreach ($array1 as $keys=>$name1)
		 {
		    if ($value==$keys){$sel="selected";}else{$sel="";}
			$str.="<option $sel value='".$keys."'>$name1</option>";
		 }
	  }
	 $array_2=$array2a[$bid];
	 if ($num==2){
	     $array_2=$array2a[$bid];
	     foreach ($array_2 as $keysa=>$name1a)
		 {
		    if ($value==$keysa){$sel="selected";}else{$sel="";}
			$str.="<option $sel value='".$keysa."'>$name1a</option>";
		 }
	  }
	  return $str;
	}
	function bnoption_str($value,$num=1,$bid="")
	{
	  global $array1,$array2a;
	  if ($num==1){
	     foreach ($array1 as $keys=>$name1)
		 {
		    if ($value==$name1){$sel="selected";}else{$sel="";}
			$str.="<option $sel value='".$keys."'>$name1</option>";
		 }
	  }
	 $array_2=$array2a[$bid];
	 if ($num==2){
	     $array_2=$array2a[$bid];
	     foreach ($array_2 as $keysa=>$name1a)
		 {
		    if ($value==$name1a){$sel="selected";}else{$sel="";}
			$str.="<option $sel value='".$keysa."'>$name1a</option>";
		 }
	  }
	  return $str;
	}
	function script_str($conn,$isbt)
	{
	  global $array1,$array2a;
	  $sql="SELECT bid,bname FROM `business_class` where isbig=1 and isbt=$isbt ORDER BY `id`  ";
	  //echo $sql;
	  $rs=$conn->Execute($sql);
	  $i=0;
	  $str='var array1=new Array();';
	  $str.='var array1v=new Array();';
	  $str.='var array2=new Array();';
	  $str.='var array2v=new Array();';
	  while(!$rs->EOF)
	  {
	     $bid=$rs->fields("bid"); $bname=$rs->fields("bname");
		 $array1[$bid]=$bname;
		 if ($i==0){	 $str1='array1=new Array("'.$bname.'"'; $str2='array1v=new Array("'.$bid.'"'; } 
		 else { $str1.=',"'.$bname.'"'; $str2.=',"'.$bid.'"'; }
		 $sql1="SELECT bid,bname FROM `business_class` where isclass='".$bid."' and isbt=$isbt ORDER BY `id`  ";
		 $rs1=$conn->Execute($sql1);$j=0;
		 while(!$rs1->EOF)
	  	 {
		     $bid1=$rs1->fields("bid"); $bname1=$rs1->fields("bname");
			 $array2a[$bid][$bid1]=$bname1;
			 if ($j==0){	 $str3.='array2['.$i.']=new Array("'.$bname1.'"'; $str4.='array2v['.$i.']=new Array("'.$bid1.'"'; } 
		     else { $str3.=',"'.$bname1.'"'; $str4.=',"'.$bid1.'"'; }
			 $str0.='group['.$i.']['.$j.']=new Option("'.$bname1.'","'.$bid1.'"); ';
			 $j++;
	         $rs1->MoveNext();
		 }
		 if ($j<>0){$str3.=');';$str4.=');';}
		 $i++;
	     $rs->MoveNext();
	  }
	  if ($i<>0){$str1.=');';$str2.=');';}
	  $arr_str[$i]=$str.$str0.$str1.$str2.$str3.$str4;
	  return $arr_str;
	}
	function get_value($arr_title)
  { 
	 $str0=' function get_lmvalue(cmd,xx) { '."\n";
	 $str1=' for (i=start;i<totala;i++){ '."\n";
	 $str2=' if (cmd=="add") {  '."\n";
	 $str3=' if (cmd=="dele") {  '."\n";
	 $str3.=' for(i=xx;i<totala;i++) {  '."\n";
	 $str4=' if (totala==start){ i=start; '."\n";
	 $i=0;
     foreach ($arr_title as $keys=>$name1)
	 {
	     if ($i==0){ $stra='var lmtitle=new Array("'.$name1.'"'; }else
		   { 
		     if ($keys=="2"){$flagk=1;$k=0;}
			 if ($flagk==1){
			      if ($k==0){$strb='var lmtitle1=new Array("'.$name1.'"'; }else{$strb.=',"'.$name1.'"';}
				  $k++;
			   }else{
			      $stra.=',"'.$name1.'"';
			   }
		     }
		if (($keys=="0")||($keys=="1")||($keys=="2")){$next=1;}else{
		   $str.='var '.$keys.'value=new Array(); ';
		   $str1.=$keys.'value[i]=eval("form2.'.$keys.'"+i+".value"); '."\n";
		   $str2.=$keys.'value[i]='.$keys.'value[i-1]; ';
		   $str3.=$keys.'value[i]='.$keys.'value[i+1]; ';
		   $str4.=$keys.'value[i]=""; ';
		}
		$i++;
	 }
	$stra.="); \n"; $strb.="); \n";$str.="\n"; $str1.='}'."\n";$str2.='}'."\n";$str3.='}}'."\n";$str4.='}'."\n";
    return $stra.$strb.$str.$str0.$str1.$str2.$str3.$str4.'}'."\n";
}
  function member_op($connall,$memberid,$gcwszy,$llwszy){
	 $messagenumbgc=0;$messagenumbll=0;$str="";$date=date("Y-m-d");
	 //$sql="select * from wszy_title where memberid=$memberid and messagedate>'".$date." 00:00' and messagedate<'".$date." 23:59'";
	 $str='<table width=100% 
            height=1 
            border=0 cellPadding=0 cellSpacing=0 bgColor=F0F0F0 style="BORDER-RIGHT: #000000 0px dashed; BORDER-TOP: #666666 1px dashed; BORDER-LEFT: #000000 0px dashed; BORDER-BOTTOM: #000000 0px dashed">
              <tr>
                <td>&nbsp;</td>
              </tr>
            </table><table width=100% bgColor=F0F0F0><tr><td><table width=70% align=center><tr><td>';
	 $sql="select * from memberzb where memberid=$memberid order by messagedate desc ";
	 $rsb=$connall->Execute($sql);
	 if ($rsb->EOF){$str="您没有发布网上资源";}
	 while(!$rsb->EOF){ $wszy_id =$rsb->fields("id"); 
	    $mchannelid =$rsb->fields("mchannelid"); $wtype =$rsb->fields("wtype");	$nums=$rsb->fields("messagenumb");
		 $messagezbid=$rsb->fields("messagezbid");$reszl =$rsb->fields("reszl"); 	$title=$rsb->fields("title"); 
		 	$mid=$rsb->fields("memberid"); 
		if ($wtype==1){$zb="供应专版";$n="gqzb";$news_str="";$zbzy="供应资源";}else{$zb="采购专版";$n="cgzb";$zbzy="采购资源";}
		if ($messagezbid==0){ $gqzb="发布$zb";$news_str="";}else{$gqzb="发布$zb";
		  $sqlc="select * from news where nid=$messagezbid";
		  $rsc=$connall->Execute($sqlc);
		  if (!$rsc->EOF){  $titlec=$rsc->fields("ntitle"); $nid=$rsc->fields("nid");
		  $news_str="<a href='/MessageShow.php?nid=".$nid."' target='_blank'>".$titlec."<a><br><br>";}else{$news_str="";}
		}
		if ($mchannelid=="02"){
		   $messagenumbgc += $nums;
		   $str.="<form action='Member_wszyinput.php' method='post' name='a".$n."'>&nbsp;&nbsp;<font color='#f15401'><B>您已发布钢材频道$zbzy</B></font> <br>
		    <input name='channelid' type='hidden' value='".$mchannelid."'><input name='bgycg' type='hidden' value='".$wtype."'>
		    <a href='/Business/BusinessRes_show.php?res_id=".$wszy_id."&mid=".$mid."'  target='_blank'> ".$title.$nums."条共".$reszl."吨</a> "."<input type=submit value='修改' class='td4'>
			   <input name='wszy_id' type='hidden' value='".$wszy_id."'>
			  <input type=button value='".$gqzb."' class='td4'  onclick='".$n."();'></form>
			   <script language='JavaScript' type='text/JavaScript'>
			    function ".$n."(){
				   a".$n.".action='Member_gqzb.php';
				   a".$n.".submit();
				}
			 </script>
			  "."<br>".$news_str;
		}
		if ($mchannelid=="04"){
		   $messagenumbll += $nums;
		   $str.="<form action='Member_wszyinput.php' method='post' name='form6a".$n."'>&nbsp;&nbsp;<font color='#f15401'><B>您已发布炉料频道$zbzy</B></font> <br>
		   <input name='channelid' type='hidden' value='".$mchannelid."'><input name='bgycg' type='hidden' value='".$wtype."'>
		   <input name='wszy_id' type='hidden' value='".$wszy_id."'>
		   <a href='/Business/BusinessRes_show.php?res_id=".$wszy_id."&mid=".$mid."' target='_blank'> ".$title.$nums."条共".$reszl."吨</a> "."<input type=submit value='修改' class='td4'>
			 <input type=button value='".$gqzb."' class='td4' onclick='gqzb".$n."();'></form>
			 <script language='JavaScript' type='text/JavaScript'>
			    function gqzb".$n."(){
				   form6a".$n.".action='Member_gqzb.php';
				   form6a".$n.".submit();
				}
			 </script>
			 "."<br>".$news_str;
		}
				if ($mchannelid=="08"){
		   $messagenumbll += $nums;
		   $str.="<form action='Member_wszyinput.php' method='post' name='form6a".$n."'>&nbsp;&nbsp;<font color='#f15401'><B>您已发布有色金属频道$zbzy</B></font> <br>
		   <input name='channelid' type='hidden' value='".$mchannelid."'><input name='bgycg' type='hidden' value='".$wtype."'>
		   <input name='wszy_id' type='hidden' value='".$wszy_id."'>
		   <a href='/Business/BusinessRes_show.php?res_id=".$wszy_id."&mid=".$mid."' target='_blank'> ".$title.$nums."条共".$reszl."吨</a> "."<input type=submit value='修改' class='td4'>
			 <input type=button value='".$gqzb."' class='td4' onclick='gqzb".$n."();'></form>
			 <script language='JavaScript' type='text/JavaScript'>
			    function gqzb".$n."(){
				   form6a".$n.".action='Member_gqzb.php';
				   form6a".$n.".submit();
				}
			 </script>
			 "."<br>".$news_str;
		}
	    $rsb->MoveNext();
	 }
	/* if ($gcwszy>$messagenumbgc){$fnum=$gcwszy-$messagenumbgc;$str.="您还可以发布钢材频道网上资源 $fnum 条 
	 <form action='Member_wszyinput.php' method='post' name='form7'><input type=submit value=发布供应><input name='channelid' type='hidden' value='02'><input name='bgycg' type='hidden' value='1'></form>
	 <form action='Member_wszyinput.php' method='post' name='form8'> <input type=submit value=发布采购><input name='channelid' type='hidden' value='02'><input name='bgycg' type='hidden' value='2'></form> <br>";}else{$str.="";}
	 if ($llwszy>$messagenumbll){$fnum=$llwszy-$messagenumbll;$str.="您还可以发布炉料频道网上资源 $fnum 条 
	 <form action='Member_wszyinput.php' method='post' name='form9'><input type=submit value=发布供应><input name='channelid' type='hidden' value='04'><input name='bgycg' type='hidden' value='1'></form>
	  <form action='Member_wszyinput.php' method='post' name='form10'><input type=submit value=发布采购><input name='channelid' type='hidden' value='04'><input name='bgycg' type='hidden' value='2'></form>
	 <br>";}else{$str.="";}*/
	 $str.="</td></tr></table></td></tr></table>";
	 return $str;
 }
 function city_class($class,$fname,$value="",$num=0){
		  global $connall,$city_keys;
      	  $query_variety_gc ="SELECT cityid, cityname FROM city WHERE city.".$city_keys." like '".$class."%' and provincename!='' ORDER BY id ASC";
          $rs=$connall->Execute($query_variety_gc);
		  $a=0;
		$str='<table>'; 
		while (!$rs->EOF) {
		      $cityid=$rs->fields('cityid');$cityname=$rs->fields('cityname');
			  if(strstr($value,$cityid))
			  {$checked=" checked ";}else{$checked=" ";}
			  if ($num==0){
			    $str.='<tr><td><input type="checkbox"  name="'.$fname.'" value="'.$cityid.'" '.$checked.' onclick="ct_form()"> '.$cityname.'</td></tr>'; 
			  }else{
			    if ($a==0){$str.='<tr><td><input type="checkbox"  name="'.$fname.'" value="'.$cityid.'" '.$checked.' onclick="ct_form()"> '.$cityname.'</td>';}
				   else{ 
					   if ($a%$num){   $str.='<td><input type="checkbox"  name="'.$fname.'" value="'.$cityid.'" '.$checked.' onclick="ct_form()"> '.$cityname.'</td>';}
					   else     {   $str.='</tr><tr><td><input type="checkbox"  name="'.$fname.'" value="'.$cityid.'" '.$checked.' onclick="ct_form()"> '.$cityname.'</td>';}
				   }
			     $a++;
			  }
			 $rs->MoveNext();
		 } 
		$str.='</table>';
		return $str;
}
function variety_class($class,$fname,$value="",$num=0){
		  global $connall,$vari_keys; 
      	  $query_variety_gc = "SELECT varietyid, varietyname,class FROM variety WHERE variety.".$vari_keys." like '".$class."%' and varietyid<>'088' ORDER BY id ASC";
		  $rs = $connall->Execute($query_variety_gc);
		  $a=0;
		$str='<table>'; 
		while (!$rs->EOF) {
		      $varietyid=$rs->fields('varietyid');$classid=$rs->fields('class');$varietyname=$rs->fields('varietyname');
			  if(strstr($value,$varietyid))
			  {$checked=" checked ";}else{$checked=" ";}
			  if ($num==0){
			    $str.='<tr><td><input type="checkbox"  name="'.$fname.'" value="'.$varietyid.",".$classid.'" '.$checked.' > '.$varietyname.'</td></tr>'; 
			  }else{
			    if ($a==0){$str.='<tr><td><input type="checkbox"  name="'.$fname.'" value="'.$varietyid.",".$classid.'" '.$checked.'> '.$varietyname.'</td>';}
				   else{ 
					   if ($a%$num){   $str.='<td><td><input type="checkbox"   name="'.$fname.'" value="'.$varietyid.",".$classid.'" '.$checked.'> '.$varietyname.'</td>';}
					   else     {   $str.='</tr><tr><td><input type="checkbox"  name="'.$fname.'" value="'.$varietyid.",".$classid.'" '.$checked.'> '.$varietyname.'</td>';}
				   }
			     $a++;
			  }
		  $rs->MoveNext();
		 } 
		$str.='</table>';
		return $str;
}
	function arr_div($num){
	   global $connall,$array1,$array2a;$i=0;
	   foreach($array1 as $keys1=>$name1){
	       $array3=$array2a[$keys1];$j=0;
		   foreach ($array3 as $keys2=>$name2){
		       $str.='divcz['.$i.']['.$j.']="';
			   $sql="select * from business_cz where pz_class='".$keys2."' order by id "; 
			   $rs=$connall->Execute($sql);$a=0;
			    $str.="<table>";
			   while (!$rs->EOF){
			     // $cz_name="<nobr><span style='cursor:hand' onclick='get_cz(".'"+'."'".'"'.$rs->fields("cz_name").'"'."'+".'"'.")' >".$rs->fields("cz_name")."</span></nobr>";
			     $cz_name="<nobr><span class=hand id=cz".$i.$j." onMouseOver=get_cz(this)>".$rs->fields("cz_name")."</span></nobr>";
				 // $str.=" ".$rs->fields("cz_name")." <br>";
				  if ($num==0){
					$str.='<tr><td>'.$cz_name.'</td></tr>'; 
				  }else{
					if ($a==0){$str.='<tr><td> '.$cz_name.'</td>';}
					   else{ 
						   if ($a%$num){   $str.='<td> '.$cz_name.'</td>';}
						   else     {   $str.='</tr><tr><td> '.$cz_name.'</td>';}
					   }
					 $a++;
				  }
			      $rs->MoveNext();
			   }
			   $str.='</table>";';
			   $j++;
		   }
		   $i++;
	   }
	   return $str;
	}
  function ly_show($resid,$num,$mbid){
	    global $connall,$hf;
		if ($num==1){
		$sql="select * from res_lyb  where llwszy_id='".$resid."' and ly_type=1 and (memberid=$mbid or hfmemberid=$mbid)";
		}
		if ($num==2){
		  $sql="select * from res_lyb  where llgq_id='".$resid."' and ly_type=0 and (memberid=$mbid or hfmemberid=$mbid)";
		}
		if ($num==3){
		  $sql="select * from res_lyb  where order_id='".$resid."' and ly_type=3 and memberid=$mbid ";
		}
	    $rs=$connall->Execute($sql);
		while(!$rs->EOF){ 
		  $lid=$rs->fields("id");
		   $title=$rs->fields("title"); $ndate=$rs->fields("ndate");
		    $content=strip_tags($rs->fields("ncontent"));
			$content = str_replace(" ", "&nbsp;",$content);
			$content = str_replace("\n","<br>",$content);
		   $ly_type=$rs->fields("ly_type");    $ishf=$rs->fields("ishf"); 
		   $lyz_m=$rs->fields("memberid");
		  
		   if($lyz_m == $_SESSION['memberid'])    //判断有没有“修改，删除”；
		   
		   $bianji='&nbsp;&nbsp;&nbsp;&nbsp;<a href="/Member/update_lyb.php?ly_id='.$lid.'" class="h12pxtop">修改</a>&nbsp;<a href="/Member/lyb_add.php?did='.$lid.'" class="h12pxtop">删除</a> ';
		   
		   if ($ishf==0){$hf_str="<input type=\"button\" name=\"Submit\" value=\"回复\" onclick=hf(".$lid.") style='color:#f15401'>";}else{$hf_str="";}
		   if ($lyz_m==$mbid){$hf_str="";}
		   $memberida=$rs->fields("memberid");		  
		   $sql1="select compfname,xmlstr_rel from member where mbid='".$memberida."' ";
		   $rs1=$connall->Execute($sql1);
		   $compfname=$rs1->fields("compfname");
		   $str.='<table width="680" align="center"  height="73" border="0" cellpadding="0" cellspacing="1" >
  <tr>
    <td width="863" height="30">
	<table width="100%" height="20" border="0" cellpadding="0" cellspacing="1"  bgcolor="#CCCCCC">
        <tr width=100% > 
          <td background="/images/Member/wsly.gif">';
		   $str.="<table width=100% ><tr><td width=50%><img src=\"/images/Member/wsly1.gif\" width=\"11\" height=\"11\"> <strong> ".$compfname."</strong><font color=\"#FF0000\">留言</font> </td><td>留言时间：".$ndate."　　".$hf_str.$bianji."</td></tr></table>";
		   $str.="</td>
        </tr>
      </table></td>
  </tr>";   
			   $str.='<tr><td><table width="680" align="center" border=1 cellspacing=0 cellpadding=1 bordercolorlight=#cccccc bordercolordark=#FFFFFF>';
 			  // $str.="<tr><td> <div align=\"left\">标题: </div></td>";
			  // $str.="<td width=\"91%\">".$title."</td></tr>";
			   $str.="<tr><td valign=\"top\"> <div align=\"left\">内容: </div></td>";
			   $str.="<td width=\"91%\"  valign=\"top\">".$content."</td></tr>";
		       $str.="</table></td></tr>";
		   if ($ishf==1){
		      $hf_id=$rs->fields("hf_id");
		      $sqlb="select * from res_lyb  where id='".$hf_id."'";
		      $rsbb=$connall->Execute($sqlb);
			 if (!$rsbb->EOF){
		      $titleb=$rsbb->fields("title"); $ndateb=$rsbb->fields("ndate"); $content=$rsbb->fields("ncontent");
			  $memberidb=$rsbb->fields("memberid");
			  
			  if( $memberidb == $_SESSION['memberid'])
			    $bianjib='&nbsp;&nbsp;&nbsp;&nbsp;<a href="/Member/update_lyb.php?ly_id='.$hf_id.'" class="h12pxtop">
			          修改</a>&nbsp;<a href="/Member/lyb_add.php?did='.$hf_id.'" class="h12pxtop">删除</a> ';
			  $sqlb1="select compfname,xmlstr_rel from member where mbid='".$memberidb."' ";
		      $rsb1=$connall->Execute($sqlb1);
		      $compfnameb=$rsb1->fields("compfname");
				  $str.='<tr><td><table width="680" align="center" border=1 cellspacing=0 cellpadding=1 bordercolorlight=#cccccc bordercolordark=#FFFFFF>';
				  $str.="<tr><td width=\"50%\" colspan=2  background=\"/images/Member/wsly.gif\"> <table width=100%  border=\"0\" cellpadding=\"0\" cellspacing=\"1\"><tr><td width=50%><img src=\"/images/Member/wsly1.gif\" width=\"11\" height=\"11\"> <strong> ".$compfnameb."</strong><font color=\"#FF0000\">回复</font> </td><td>回复时间：".$ndateb.$bianjib."　　</td></tr></table>
</td></tr>";
				   $str.="<tr><td valign=\"top\" > <div align=\"left\">回复内容: </div></td><td width=\"91%\" valign=\"top\">".$content." &nbsp;</td></tr>";
				   $str.="</table></td></tr>";
				}
		   }else{
		      $hf[$lid]=hf_str($lid);
			  $str.="<tr><td><span id='show_hf".$lid."'></span></td></tr>
			  ";
		   }
		   $str.="<tr  height=30><td></td></tr></table>";
		   $rs->MoveNext();
		}
		return $str;
	}
	function hf_str($id){
	  $str='<form name="hy'.$id.'" method="post" action="hf_add.php"><table width="450" align="center" border=1 cellspacing=0 cellpadding=1 bordercolorlight=#CCCCCC bordercolordark=#FFFFFF><tr><td>回复</td></tr><tr><td><div align="center"><textarea name="content" cols="40" rows="8"></textarea><input type="hidden" name="ly_id" value='.$id.'><input type="submit" name="Submit" value="提交"></div></td></tr></table></form>';
	 return $str;
	}
	
	function g_array($conn)
	{
	  global $array1,$array2;
	  $sql="SELECT bid,bname FROM `business_class` where isbig=1 ORDER BY `id`  ";
	  $rs=$conn->Execute($sql);
	  $i=0;
	  while(!$rs->EOF)
	  {
	     $bid=$rs->fields("bid"); $bname=$rs->fields("bname");
		 $array1[$bid]=$bname;
		 $sql1="SELECT bid,bname FROM `business_class` where isclass='".$bid."'  ORDER BY `id`  ";
		 $rs1=$conn->Execute($sql1);$j=0;
		 //$str0.='group['.$i.']['.$j.']=new Option("品种选择",""); ';
		
		 while(!$rs1->EOF)
	  	 {
		     $bid1=$rs1->fields("bid"); $bname1=$rs1->fields("bname");
			 $array2[$bid][$bid1]=$bname1;
			 $str0.='group['.$i.']['.$j.']=new Option("'.$bname1.'","'.$bid1.'"); ';
			 $j++;
	         $rs1->MoveNext();
		 }
		 $i++;
	     $rs->MoveNext();
	  }
	  return $str0;
 }
	function link_str($steelhome,$memberid,$num)
{
   $sql="select * from `member`,adminuser where member.mbid=adminuser.mid and ismaster=1 and member.mbid=$memberid";
   $rs=$steelhome->Execute($sql);
   if ($num==1){$su_str="<B>供　　方</B>";}
   if ($num==2){$su_str="<B>需　　方</B>";}
   $str.='<table width=100% border=1 cellspacing=0 cellpadding=1 bordercolorlight=#cccccc bordercolordark=#FFFFFF>
  <tr align=center> <td colspan=2 height="25" bgcolor="#e9f6fe"><div align="center">'.$su_str.'</div></td></tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>单　　位</td>
          <td>&nbsp;'.$rs->fields("compfname").'</td>
        </tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>电　　话</td>
          <td>&nbsp;'.$rs->fields("telnumber").'</td>
        </tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>邮　　编</td>
          <td>&nbsp;'.$rs->fields("postcode").'</td>
        </tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>通讯地址</td>
          <td>&nbsp;'.$rs->fields("mailaddress").'</td>
        </tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>开 户 行</td>
          <td>&nbsp;'.$rs->fields("openbank").'</td>
        </tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>账　　号</td>
          <td>&nbsp;'.$rs->fields("bankaccount").'</td>
        </tr>
        <tr > 
          <td height="25" bgcolor="f0f0f0" align=center>代　　表</td>
          <td>&nbsp;'.$rs->fields("truename").'</td>
        </tr>
		</table>';
	  return $str;
}
	//xiangbin add20180428 start 

	function check_keyword($conn,$keyword)
	{
		global $MEMCACHE_SERVER, $MEMCACHE_PORT;
		$cache = new Memcache();  
		$cache->connect($MEMCACHE_SERVER, $MEMCACHE_PORT);
		$type1=$cache->get('key_word_type1');
		$type2=$cache->get('key_word_type2');

		if(!$type1)//若缓存无数据请求数据库将数据存缓存
		{
			
			$sql="SELECT key_word,type FROM ly_keyword WHERE type=1 or type='2' ";
			$rs=$conn->Execute($sql);
			$type1=array();
			$type2=array();
			foreach ($rs as $tmp) 
			{
				if($tmp["type"]==1)
				{ 
					$type1[]=str_replace(array(".","/","*","+"),array("\.","\/","\*","\+"),$tmp["key_word"]);
				} else {
					$type2[]='((?=.*'.str_replace("+",")(?=.*",str_replace(array(".","/","*"),array("\.","\/","\*"),$tmp["key_word"])).'))';
				}
			}
			$cache->set('key_word_type1',$type1,MEMCACHE_COMPRESSED,86400);
			$cache->set('key_word_type2',$type2,MEMCACHE_COMPRESSED,86400);

		}
	 	$cache->close();
		// echo preg_match_all("/(\d|内)/i",  $keyword, $matches);
		// print_r($matches);exit;
	 	// $type1array=array_chunk($type1,500);
		// if(is_array($type1array) && !empty($type1array)) {
		// 	foreach($type1array as $banned_words) {
		// 		$zhengze=implode('|',$banned_words);
		// 		$zhengze='/('.$zhengze.')/i';
		// 		if(preg_match_all($zhengze,  $keyword, $matches)) {
		// 			return true;
		// 		}
		// 		foreach()
		// 	}
		// }
		// print_r($type1);exit;
		// www\.tjsmjd\.com
		if(is_array($type1) && !empty($type1)) {
			foreach($type1 as $banned_words) {
				if(empty(trim($banned_words))) continue;
				$tmpw_explode = explode("|", $banned_words);
				$tmpw = array_filter($tmpw_explode);
				if(count($tmpw)>1){
					$banned_words = implode("|", $tmpw);
				} else {
					$banned_words = $tmpw[0]??$tmpw[1];
				}
				
				$zhengze='/('.trim($banned_words).')/i';
				if(preg_match_all($zhengze,  $keyword, $matches)) {
					return true;
				}
			}
		}
	
		// $type2array=array_chunk($type2,500);
	  	// if(is_array($type2array) && !empty($type2array)) {
		// 	foreach($type2array as $banned_words) {
		// 		$zhengze=implode('|',$banned_words);
		// 		$zhengze='/('.$zhengze.')/i';
		// 		if(preg_match_all($zhengze,  $keyword, $matches)) {
		// 			return true;
		// 		}
		// 	}
		// } 

		if(is_array($type2) && !empty($type2)) {
			foreach($type2 as $banned_words2) {
				if(empty(trim($banned_words2))) continue;
				$tmpw_explode2 = explode("|", $banned_words2);
				$tmpw2 = array_filter($tmpw_explode2);
				if(count($tmpw2)>1){
					$banned_words2 = implode("|", $tmpw2);
				} else {
					$banned_words2 = $tmpw2[0]??$tmpw2[1];
				}
				$zhengze='/('.trim($banned_words2).')/i';
				if(preg_match_all($zhengze,  $keyword, $matches)) {
					return true;
				}
			}
	    } 
	  
	   	return false;
	}
	//xiangbin add20180428 end
?>
