<!DOCTYPE html>
<html>
<head>
    <meta name="verify-v1" content="rfRws4Y1mwg6EQn44sY6gX/gxhKUSQteND7cp5jrAoY=" />
    <meta name="Description" content="钢之家网站从事钢铁行业信息服务,钢材交易,网站建设,准确及时的反映各地区钢材市场的钢材价格,钢铁价格以及钢材行情信息,是一个专注于钢铁行业的专业钢铁网站." />
    <meta name="Keywords" content="钢之家,钢材价格,现货报价,钢材市场,钢材行情,钢铁价格走势,钢材价格指数,钢之家钢铁网" />
    <meta name="Copyright" content="钢之家钢铁信息 - 钢材价格 - 钢材市场 - 钢铁价格 - 行情资讯网 - 钢之家资讯" />
    <meta property="wb:webmaster" content="8c9c4eb4c25b011c" />
    <meta http-equiv="Expires" CONTENT= "0">
    <meta http-equiv="Pragma" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-store">
    <meta http-equiv="Cache-Control" CONTENT="must-revalidate">
    <meta http-equiv = "X-UA-Compatible" content = "IE=Edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta content="text/html; charset=utf-8" http-equiv=Content-Type>

    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/js/www/layui/css/layui.css?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>">
    <title>我购买的信息-钢之家网站</title>

    <script>
        document.createElement('header');
        document.createElement('section');
        document.createElement('footer');
        document.createElement('aside');
        document.createElement('article');
        document.createElement('figure');
    </script>
    <style>
        header, section, footer, aside, article, figure {
            display: block;
        }

    </style>
</head>

<body >
<div class="layui-container" style="width: 100%">
    <div class="layui-progress" >
        <div class="layui-progress-bar" lay-percent="100%"></div>
    </div>
    <blockquote class="layui-elem-quote">我购买的信息</blockquote>

    <hr class="layui-border-green">
    <table class="layui-table"  lay-filter="purchasenewslist" id="purchasenewslist">
    </table>
</div>


<script src="/js/jquery-1.12.3.min.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>

<script src="<{$smarty.const.STATIC_URL}>/js/www/layui/layui.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<script type="text/javascript">
layui.use(['table','jquery','form','laydate'],function(){
    var layer = layui.layer,form = layui.form,laydate=layui.laydate,table = layui.table,$=layui.jquery;

    //第一个实例
    table.render({
        elem: '#purchasenewslist'
        // ,height: 312
        ,url: 'special_column.php?action=getpurchasenewslist&limit=200' //数据接口
        ,page: false //开启分页
        ,cols: [[ //表头
            {field: 'ntitle', title: '标题', minWidth: 180, templet: '<div><a href="/detailspage.php?nid={{= d.nid }}&ntype={{=d.newstype}}" target="_blank;">{{= d.ntitle }}</a> </div>'}
            ,{field: 'completetime', title: '购买时间', width: 160}
            ,{field: 'price', title: '支付金额', width: 90}
            ,{field: 'ndate', title: '信息时间', width: 160}
            ,{field: 'statusstr', title: '支付类型', width: 90, templet: function (d) {
                    if (d.statusstr === 'SUCCESS') {
                        return '支付宝';
                    } else if (d.statusstr === '支付成功')  {
                        return '微信';
                    } else if (d.statusstr === '预付款支付')  {
                        return '余额';
                    } else {
                        return d.statusstr;
                    }
                }}
            ,{field: 'wherefrom', title: '支付平台', width: 100, templet: function (d) {
                    if (d.wherefrom === '1') {
                        return '电脑浏览器';
                    } else if (d.wherefrom === '2')  {
                        return '手机浏览器';
                    } else if (d.wherefrom === '3')  {
                        return '掌上钢之家';
                    } else if (d.wherefrom === '4')  {
                        return '大宗商品部落';
                    } else {
                        return '其他';
                    }
                }
            }
        ]]
    });
});
</script>

</body>
<html>
