<?php
require_once('../english/include/config.php');


$nid = $_REQUEST['nid'];
$type = $_REQUEST['type']; 
$mid = $_REQUEST['mid'];
function get_ip()
{
    $ip = false;
    if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
        $ip = $_SERVER["HTTP_CLIENT_IP"];
    }
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(",", $_SERVER['HTTP_X_FORWARDED_FOR']);
        if ($ip) {
            array_unshift($ips, $ip);
            $ip = FALSE;
        }
        for ($i = 0; $i < count($ips); $i++) {
            if (!preg_match("/^(10|172\.16|172\.17|192\.168)\./i", $ips[$i])) {
                $ip = $ips[$i];
                break;
            }
        }
    }
    return ($ip ? $ip : $_SERVER['REMOTE_ADDR']);
}
if(!isset($_COOKIE['enm']) && !isset($_COOKIE['ce']))
	{
	  $mid=0;
	}
	if(isset($_COOKIE['ce']) && isset($_COOKIE['ce'])!=0)
	{
	  $mid=$_COOKIE['ce'];
	}
	if(isset($_COOKIE['enm']) && isset($_COOKIE['enm'])!=0)
	{
	  $mid=$_COOKIE['enm'];
	}
    //xiangbin add 20190428 strat
    
    if(!isset($_COOKIE['euserid']) && !isset($_COOKIE['userid']))
    {
        $userid=0;
    }
    if(isset($_COOKIE['userid']) && isset($_COOKIE['userid'])!=0)
    {
        $userid=$_COOKIE['userid'];
    }
    if(isset($_COOKIE['euserid']) && isset($_COOKIE['euserid'])!=0)
    {
        $userid=$_COOKIE['euserid'];
    }
    
    if(empty($mid))
    {
        $userid=0;
    }

if ( empty($userid) || empty($mid) ) {
    echo "登录信息失效，避免支付后出问题，请重新登陆再支付。";
    return;
}


$member_ip = get_ip();
$NewsPrice=0.01;
if (empty($NewsPrice)) {
    $NewsPrice = '-1';
}

if ($NewsPrice <= 0) {
    echo "抱歉，当前信息不支持购买！";
    exit;
}
 $newsTitle = "购买数据";

$sql_havepay = "SELECT * FROM PurchasedNewsInfo where userid='".$userid."'  and Type='".$type."' and newsid='".$nid."' ";
//file_put_contents("/tmp/shi.txt", "sql_havepay:::".print_r($sql_havepay, true)."/n", FILE_APPEND);
$Orhavepay=$conn->Execute($sql_havepay);
$result = $Orhavepay->FetchRow();
$TestOrComfirm='00';
$Dateday = date("YmdHis");
$OrderNo1 = sprintf("%s%s",$TestOrComfirm,$Dateday);
$OrderNo2 = sprintf("%04d",1);

if(empty($result)){
		$sql_purchased = "insert into PurchasedNewsInfo set OrderNo1='".$OrderNo1."', IP='".$member_ip."',newsid='".$nid."',userid='".$userid."',mid='".$mid."',Type='".$type."',Price='".$NewsPrice."',status=-1";
		//echo $sql_purchased;exit;
        $conn->Execute($sql_purchased);
		$sql_purchased = "SELECT max(id) as id FROM PurchasedNewsInfo ";
		$Orid=$conn->Execute($sql_purchased); 
		$Orderids = $Orid->FetchRow();
		$Orderid=$Orderids["id"];
		$sql_slPN1 =  "SELECT max(OrderNo2) as OrderNo2 FROM PurchasedNewsInfo where OrderNo1='".$OrderNo1."' group by OrderNo1";
		$Oridnum=$conn->Execute($sql_slPN1); 
		$dingdan = $Oridnum->FetchRow();
		$dingdannum = $dingdan['OrderNo2'];
		$OrderNo2 = sprintf("%04d",intval($dingdannum)+1);
		$sql_upPN2="UPDATE PurchasedNewsInfo SET OrderNo2='".$OrderNo2."' where id='".$Orderid."' ";
		$conn->Execute($sql_upPN2);
	}elseif ($result['status']==0){
		echo "当前信息已有您的支付记录，为了避免重复支付，请重新进入信息页面查看。";
		return;
	}else{
		$Orderid = $result['id'];
		$sql_upPN1="UPDATE PurchasedNewsInfo SET OrderNo1='".$OrderNo1."', IP='".$member_ip."' where id='".$Orderid."' ";
		$conn->Execute($sql_upPN1);
		$sql_slPN1 =  "SELECT max(OrderNo2) as OrderNo2 FROM PurchasedNewsInfo where OrderNo1='".$result['OrderNo1']."' group by OrderNo1";
		$Oridnum=$conn->Execute($sql_slPN1); 
		$dingdan = $Oridnum->FetchRow();
		$dingdannum = $dingdan['OrderNo2'];
		$OrderNo2 = sprintf("%04d",$dingdannum+1);
		$sql_upPN2="UPDATE PurchasedNewsInfo SET OrderNo2='".$OrderNo2."' where id='".$Orderid."' ";
		$conn->Execute($sql_upPN2);
	}
$OrderNo=$OrderNo1.$OrderNo2;
file_put_contents('ipn.log', date('[Y-m-d H:i:s]')." OrderNo: $OrderNo\n", FILE_APPEND);
$returnUrl=APP_URL_WWW."/paypal/return_notify.php";
?>
<html><head>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<title>Paypal订单支付</title></head><body><form action="https://www.sandbox.paypal.com/cgi-bin/webscr" method="POST" name="form_starPay"> <!-- // Live https://www.paypal.com/cgi-bin/webscr -->

<input type='hidden' name='cmd' value='_xclick'> <!-- //告诉paypal该表单是立即购买 -->

<input type='hidden' name='business' value='<EMAIL>'> <!-- //卖家帐号 也就是收钱的帐号 -->

<input type='hidden' name='item_name' value='<?php echo $newsTitle;?>'> <!-- //商品名称 item_number -->

<input type='hidden' name='item_number' value='20180828080706000033'> <!-- //物品号 item_number -->

<input type='hidden' name='amount' value='<?php echo $NewsPrice;?>'> <!-- .// 订单金额 -->

<input type='hidden' name='shipping' value='0.0'> <!-- .// 运费 -->

<input type='hidden' name='currency_code' value='USD'> <!-- .// 货币 -->

<input type='hidden' name='return' value='<?php echo $returnUrl;?>'> <!-- .// 支付成功后网页跳转地址 -->

<input type='hidden' name='notify_url' value='https://www.steelhome.cn/paypal/notify.php'> <!-- .//支付成功后paypal后台发送订单通知地址 -->

<input type='hidden' name='cancel_return' value='<?php echo $returnUrl;?>'> <!-- .//用户取消交易返回地址 -->

<input type='hidden' name='invoice' value='<?php echo $OrderNo;?>'> <!-- .//自定义订单号 -->

<input type='hidden' name='charset' value='utf-8'> <!-- .// 字符集 -->

<input type='hidden' name='no_shipping' value='1'> <!-- .// 不要求客户提供收货地址 -->

<input type='hidden' name='no_note' value='1'> <!-- .// 付款说明 -->

<input type='hidden' name='rm' value='2'> <!-- 不知道是什么 -->

<input type='hidden' name='custom' value='paypal'> <!-- 自定义字段 --></form>

正在跳转Paypal支付，请稍等。。。<script>

function sub(){

document.form_starPay.submit();

}

onload(sub());</script></body></html>