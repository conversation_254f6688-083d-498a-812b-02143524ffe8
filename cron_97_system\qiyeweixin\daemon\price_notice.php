<?php
ini_set('display_errors',1);
error_reporting(1);
include_once ("/etc/steelconf/config/isholiday.php");
include('/etc/steelconf/config/config.php');
require_once("/etc/steelconf/env/www_env.php");
require_once("/usr/local/www/libs/vendor/autoload.php");
include_once('/usr/local/www/libs/phpQuery/phpQuery.php');
include('/etc/steelconf/sthframe/steelhome_db_config.php');
use PhpAmqpLib\Connection\AMQPStreamConnection;

$WEBHOOK30=WEBHOOK30;
$depts=array(
	"WEBHOOK1"=>"员工群",
	"WEBHOOK7"=>"中西部",
	"WEBHOOK9"=>"北方组",
	"WEBHOOK11"=>"华东组",
	"WEBHOOK15"=>"煤焦",
	"WEBHOOK16"=>"合金辅料部",
	"WEBHOOK17"=>"炉料部",
	"WEBHOOK23"=>"特钢",
	"WEBHOOK24"=>"研究院",
	"WEBHOOK25"=>"国际部",
	"WEBHOOK26"=>"建材报告群",
	"WEBHOOK27"=>"中板报告群",
	"WEBHOOK28"=>"热轧报告群",
	"WEBHOOK29"=>"冷轧报告群 涂镀报告群",
	"WEBHOOK30"=>"晨会在线编辑群&市场报告会",
);

$invalid_steels=[];

//17点后国内重点城市价格行情有修改的话提醒到晨会在线编辑群&市场报告会
$connall = ADONewConnection('mysql');  # create a connection
//$connall->Connect("$hostname_steelhome","$username_steelhome","$password_steelhome","$database_steelhome");
//$connall->Connect($hostname_steelhome_r, $username_steelhome_r, $password_steelhome_r, $database_steelhome_r);

$conn_oa = ADONewConnection('mysql');

/**
 * @param string $url
 * @param string $param
 * @return bool|string
 * user:shizg
 * time:2021/1/12 11:58
 * TODO 发送请求到企业微信
 */
function request_post($url = '', $param = '')  {
    if (empty($url) || empty($param))
    {
        return false;
    }
    $curl = curl_init(); // 启动一个CURL会话
    curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
    curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
    curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
    curl_setopt($curl, CURLOPT_POSTFIELDS, $param); // Post提交的数据包
    curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
    curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
    $data = curl_exec($curl); // 执行操作
    if (curl_errno($curl)) {
        echo 'Errno'.curl_error($curl);//捕抓异常
    }
    curl_close($curl); // 关闭CURL会话
    return $data; // 返回数据，json格式
}


function send_log($conn_oa,$title,$content,$senduser){
	if($content!=""){
		$sql="insert into qywx_qr_sp set logid=0,qjtaskid=0,type=30,createtime=NOW(),title='$title 存在价格为空或者负数的情况',description='$content',senduser='$senduser',msgtype='textcard'";
		$conn_oa->execute($sql);
	}
}

function get_invalid_steel($connall){
	global $invalid_steels;

	if(empty($invalid_steels)){
		$sql = "select channel, code, name from `sth_manufactor`  where status<>1";
		$datas = $connall->getArray($sql);
		foreach($datas as $item){
			$key = $item['code'];
			$invalid_steels[$key] = $item['name'];
		}
	}
	return $invalid_steels;
}

function check_invalid_price($connall,$marketrecordid,$title){
	$content = "";
	$sql = "select * from marketconditions where marketrecordid = '$marketrecordid' and isview=0 and (price<'0' or price='')";
	$datas = $connall->getArray($sql);
	if(!empty($datas)){
		$content = "【".$title."】中包含价格小于0或者为空的数据";
	}
	return $content;
}
function check_invalid_steel($connall,$marketrecordid, $title){
	$content = "";
	$invalid_steels = get_invalid_steel($connall);
	$sql = "select marketconditions_price_code.price_code,marketconditions.factoryarea from marketconditions left join marketconditions_price_code on marketconditions.id=marketconditions_price_code.marketconditions_id where marketrecordid = '$marketrecordid' and marketconditions.isview=0";
	$datas = $connall->getArray($sql);
	$invalid_name = [];
	foreach($datas as $item){
		if(empty($item['price_code'])){
			$factoryarea = $item['factoryarea'];
			foreach($invalid_steels as $steel_code=>$name){
				if($name == $factoryarea){
					$invalid_name[$steel_code] = $factoryarea;
					break;
				}
			}
		} else {
			$steel_code = substr($item['price_code'],14,4);
			if(!empty($invalid_steels[$steel_code])){
				$invalid_name[$steel_code] = $invalid_steels[$steel_code];
				//send_log($conn_oa,$title,$content,$manageid);
			}
		}
	}
	if(!empty($invalid_name)){
		$steel_content = implode(',', $invalid_name);
		$content = "【".$title."】中包含失效的钢厂【".$steel_content."】";
	}

	return $content;
}

function send_notice_message($title, $ncontent, $qyuserid,$conn_oa){
	$template = '{
		"msgtype": "text",
		"text": {
			"content": "'.$ncontent.'",
			"mentioned_list":["'.$qyuserid.'"]
		}
	}';	
	$hook = WEBHOOK3;
	//$hook = WEBHOOK31;
	$dataRes1 =  request_post ( $hook, $template );
	$dataRes=json_decode($dataRes1);
	send_log($conn_oa,$title,$content,$hook);
}

//连接rabbitmq, 监听价格表消息
$connection = new AMQPStreamConnection('**************', 5672, 'mqadmin', 'Sth@50581010');
$channel = $connection->channel();
$channel->exchange_declare('SteelHome_price', 'topic', false, false, false);
list($queue_name, ,) = $channel->queue_declare("", false, false, true, false);
$binding_key = "steelhome.marketrecord";
$channel->queue_bind($queue_name, 'SteelHome_price', $binding_key);

//接收到消息的回调函数
$callback = function ($msg) {
	global $keyCitiesInChinaPriceIdArray, $connall, $conn_oa;
	global $hostname_steelhome_r, $username_steelhome_r, $password_steelhome_r, $database_steelhome_r;

	$Ignore_variety = [
		'ys,062,222,c21',
		'ys,062,223,c21',
		'ys,062,247,c21'
	];
	
	
    $data = json_decode($msg->getBody(),true);
    $operateType = $data['operateType'];
    $tableName = $data['tableName'];
    if($operateType == "UPDATE" && $tableName == "marketrecord" ) {
		$new_row = $data["current"];
		$old_row = $data["before"];

		$connall->Connect($hostname_steelhome_r, $username_steelhome_r, $password_steelhome_r, $database_steelhome_r);
		$conn_oa->Connect(HOST_NAME_OA.":".HOST_PORT_OA, USER_NAME_OA, USER_PASSWORD_OA, DATABASE_NAME_OA);

		if($new_row['newsid'] != $old_row['newsid']) {
			$marketrecordid = $new_row['id'];
			$title = $new_row['title'];
			$manageid = $new_row['manageid'];
			$qyuserid = $conn_oa->getOne("select qyuserid from steelhome_oa.qywxbindadminuser where userid = '$manageid'");

			//检查是否使用了无效的钢厂
			$invild_steel = check_invalid_steel($connall,$marketrecordid,$title);
			if(!empty($invild_steel)){
				send_notice_message($title,$invild_steel,$qyuserid,$conn_oa);
			}

			//检查价格是否小于0或者为空
			$varietyid= $new_row['varietyid'];
			if(!in_array($varietyid,$Ignore_variety)){
				$invalid_price = check_invalid_price($connall,$marketrecordid,$title);
				if(!empty($invalid_price)){
					send_notice_message($title,$invalid_price,$qyuserid,$conn_oa);
				}
			}

			//处理检查行情中是否有不合法的数据
			$handle_illegel_data_url = "/_v2app/marketrecordcheck.php?action=check_illegal_data&marketrecordid={$marketrecordid}";
			file_get_contents($handle_illegel_data_url);
		}
	    $connall->Close();
	    $conn_oa->Close();

    } else {
		//	echo "operate is not insert or table is not marketconditions, return.\n";
    }
   
};

$channel->basic_consume($queue_name, '', false, true, false, false, $callback);

try {
    $channel->consume();
} catch (\Throwable $exception) {
    echo $exception->getMessage();
}

$channel->close();
$connection->close();

