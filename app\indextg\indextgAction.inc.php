<?php
include_once(APP_DIR . "/master/masterAction.inc.php");

class indextgAction extends masterAction
{
    private $channelId = "12";
    private $channelCode = "tg";
    private $pageName = "特钢频道";

    public function __construct()
    {
        parent::__construct();
    }

    public function index($params)
    {
        //处理百川账号在网站正常访问时的注销
        $this->handleBaiInfoLoginOut();

        $channelId = $this->channelId;
        $newsListData = [];
        //栏目
        $columnNavigation = $this->getSteelHomeDataPictureList(["type" => 8, "pageSize" => 21 ]);
        $columnNavigation = json_decode($columnNavigation, true);
        // 品种
        $varietyNavigation = $this->getSteelHomeDataPictureList([ "type" => 9, "pageSize" => 29]);
        $varietyNavigation = json_decode($varietyNavigation, true);
        // 城市
        $cityNavigation = $this->getSteelHomeDataPictureList([ "type" => 10,"pageSize" => 67]);
        $cityNavigation = json_decode($cityNavigation, true);
        // 热点专题
        $newsListData['spotQuotationList'] = $this->getSpotQuotationList([ "pageType" => $channelId,"pageSize" => 15]);
        // 走势图最后一个放的期货
        $forwardQuotationList = $this->getForwardQuotationList([ "pageType" => $channelId,"pageSize" => 1]);
        $newsListData['spotQuotationList'][] = [
            "xAxis" => $forwardQuotationList[0]['xAxis'],
            "yAxis" => $forwardQuotationList[0]['yAxis'],
            "price" => $forwardQuotationList[0]['price'],
            "rise_and_fall" => $forwardQuotationList[0]['riseAndFall'],
            "price_name" => $forwardQuotationList[0]['priceName']."<br>".$forwardQuotationList[0]['priceContract'],
            "price_id" => $forwardQuotationList[0]['priceId'],
            "id" => $forwardQuotationList[0]['id'],
            "url" => APP_URL_WWW."/showgraph_new.php?qh_type=".$GLOBALS['forward_ID_SHPIID'][$forwardQuotationList[0]['priceId']],
        ];

        // 热点专题（暂无频道区分、下方三个的那个通用的热点专题）
        $newsListData['specialList'] = $this->handleSpecialZhiDingList('12','12');
        // 热点聚焦<此处频道id注意，可能不传当前频道ID，具体看接口定义>
        $specZhiDingList = $this->getSpecZhiDingList(["channelId" => '12', "pageSize" => 1]);
        $pageSize1 = 8;
        if(empty($specZhiDingList)) {
            $pageSize1 = 9;
        }
        $specZhiDingList = empty($specZhiDingList) ? array() : $specZhiDingList;
        $newsFocusList = $this->getNewsFocusList(["channelId" => '12', "pageSize" => $pageSize1],$specZhiDingList);
        $newsFocusList = empty($newsFocusList) ? array() : $newsFocusList;
        //$specZhiDingList = empty($specZhiDingList) ? array() : $specZhiDingList;
        //$newsListData['newsFocusList'] = array_merge($specZhiDingList, $newsFocusList);
        $newsListData['newsFocusList'] =$newsFocusList;
        // 热点导读(暂无频道区分)
        $newsListData['hotReadList'] = $this->getHotReadList(["pageSize" => 9]);
        // 资讯推荐
        $newsListData['recommendationList'] = $this->getRecommendationList(["channelId" => $channelId, "pageSize" => 9]);
        // print_r($newsListData['recommendationList']);

        // 推荐资源
        $newsListData['recommendResourceList'] = $this->getRecommendResourceList(["channel" => $channelId, "type" => 1, "lb"=>"501", "pageSize" => 20]);
        if (is_array($newsListData['recommendResourceList'])) {
            $newsListData['recommendResourceList'] = array_unique($newsListData['recommendResourceList'],SORT_REGULAR);
            //特钢单独处理条数
            $newsListData['recommendResourceList'] = array_slice($newsListData['recommendResourceList'],0,10);
        }
        // 热点聚焦、资讯推荐、热点导读后面的其他栏目(特钢价格行情、行情汇总、市场分析......)
        $redisKeyHomePageList = $this->makePageListRedisKey($GLOBALS['PAGE_TYPE'][2], $channelId, "0", "", "0");
        $redisNewsList = $this->getRedisNewsList($redisKeyHomePageList);

        // 百家诚信特钢(独有)
        $newsListData['baiJiaChengXinList'] = $this->getBaiJiaChengXinList(["jie" => BAI_JIA_CHENG_XIN_JIE,"status"=>1,"deptkey"=>"2,3,6","isruwei"=>1]);
        // 推荐经销商(暂无频道区分)
        $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(["pageSize" => 9]);
        // 热点资源
        $newsListData['hotResourceList'] = $this->getRecommendResourceList(["channel" => $channelId, "type" => 2, "lb"=>"501", "pageSize" => 20 ]);
        if (is_array($newsListData['hotResourceList'])) {
            $newsListData['hotResourceList'] = array_unique( $newsListData['hotResourceList'],SORT_REGULAR);
            //特钢单独处理条数
            $newsListData['hotResourceList'] = array_slice($newsListData['hotResourceList'],0,10);
        }
        $this->assign("varietyNavigation", $varietyNavigation);
        $this->assign("columnNavigation", $columnNavigation);
        $this->assign("cityNavigation", $cityNavigation);
        $this->assign("spotEchartsData", json_encode($newsListData['spotQuotationList']));
        $this->assign("newsListData", $newsListData);
        $this->assign("redisNewsList", $redisNewsList);
        $this->assign("pageName", $this->pageName);
        $this->assign("channelId", $channelId);
        $this->assign("channelCode", $this->channelCode);
        $this->handlePublicData($params);

        //页面头部banner
        $home_banner = file_get_contents(APP_URL_WWW."/js/adv/picnew.html");
        // var_dump(APP_URL_WWW."/js/adv/picnew.html");
        if(date("Y-m-d") <= "2025-10-09") {
            $home_banner = "<div carousel-item><div onclick=\"window.open('https://www.steelhome.com/conference/index.php?view=index&WebGuid=masmeeting20250918', '_blank');\"><img src=\"https://www.steelhome.com/stgfiles/202509/tgtjh.jpg\"></div></div>";
        }
        if ( $home_banner ) {
            $this->assign("home_banner_flag", 1);
        }
        $this->assign("home_banner", $home_banner);
    }
    public function variety($params)
    {
        $channelId = $params['channelId'] ? $params['channelId'] : $this->channelId;
        $varietyId = $params['varietyId'] ? $params['varietyId'] : '201';
        $newsListData = [];
       
        $newsListData['spotQuotationList'] = $this->getSpotQuotationList([ "pageType" => $channelId,"pageSize" => 15]);
        // 走势图最后一个放的期货
        $forwardQuotationList = $this->getForwardQuotationList([ "pageType" => $channelId,"pageSize" => 1]);
        $newsListData['spotQuotationList'][] = [
            "xAxis" => $forwardQuotationList[0]['xAxis'],
            "yAxis" => $forwardQuotationList[0]['yAxis'],
            "price" => $forwardQuotationList[0]['price'],
            "rise_and_fall" => $forwardQuotationList[0]['riseAndFall'],
            "price_name" => $forwardQuotationList[0]['priceName']."<br>".$forwardQuotationList[0]['priceContract'],
            "price_id" => $forwardQuotationList[0]['priceId'],
            "id" => $forwardQuotationList[0]['id'],
            "url" => APP_URL_WWW."/showgraph_new.php?qh_type=".$GLOBALS['forward_ID_SHPIID'][$forwardQuotationList[0]['priceId']],
        ];
          //推荐资源
        // 推荐资源
        $newsListData['recommendResourceList'] = $this->getRecommendResourceList(["channel" => $channelId, "type" => 1, "lb"=>"501", "pageSize" => 20]);
        $newsListData['recommendResourceList'] = array_unique($newsListData['recommendResourceList'],SORT_REGULAR);
        //特钢单独处理条数
        $newsListData['recommendResourceList'] = array_slice($newsListData['recommendResourceList'],0,10);
        // 推荐经销商(暂无频道区分)
        $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(["pageSize" => 11]);
        //热点资源
         // 热点资源
         $newsListData['hotResourceList'] = $this->getRecommendResourceList(["channel" => $channelId, "type" => 2, "lb"=>"501", "pageSize" => 20 ]);
         $newsListData['hotResourceList'] = array_unique( $newsListData['hotResourceList'],SORT_REGULAR);
         //特钢单独处理条数
         $newsListData['hotResourceList'] = array_slice($newsListData['hotResourceList'],0,10);
        
        //调取品种通用逻辑
        $this->handlevarietypage($channelId,$varietyId);

        
        
        $this->assign("spotEchartsData", json_encode($newsListData['spotQuotationList']));
       
        $this->assign("newsListData", $newsListData);
        $this->assign("varietyId", $params['varietyId']);
        
        $this->assign("channelId", $channelId);
        $this->assign("channelName", $this->pageName);
        //频道编码
        $this->assign("channelCode", $this->channelCode);

        $this->handlePublicData($params);

    }
    /** 城市页面 */
    public function city($params)
    {
        $channelId = $params['channelId'] ? $params['channelId'] : $this->channelId;
        $cityId = $params['cityId'] ? $params['cityId'] : '0007';
        $cityData = $this->getCityList();
        //print_r($cityData);
        $cityArray = array();
        $areaArray=array();
        $provinceArray=array();
        $allcityArray=array();
        foreach ($cityData as $cityKey => $cityValue) {
            $cityArray[$cityValue['cityid']] = $cityValue;
            if(!empty($cityValue['cityKeys'])&&strlen($cityValue['cityid'])==4)
            {
                $areaArray[$cityValue['cityKeys']][]=$cityValue;
                $allcityArray[$cityValue['cityKeys']][]=$cityValue;
            }
            if(!empty($cityValue['province']))
            {
                $provinceArray[$cityValue['province']][]=$cityValue;
                $allcityArray[$cityValue['province']][]=$cityValue;
            }
        }
        $citytype = 1;//1城市2区域3省份
        if ($cityArray[$cityId]['cityKeys'] == "") {
            $citytype = 2;
        } elseif (strlen($cityId) == 2) {
            $citytype = 3;
        }
        if($cityId=='0102'||$cityId=='000A')
        {
            $citytype = 2;
            $cityId='000A';
            $allcityArray[$cityId]=$GLOBALS['02']['City']['000A'];
        }
        $daohangcityarr=array();
        if($citytype==1)
        {
            $provinceId=$cityArray[$cityId]['province'];
            $areaId=$cityArray[$cityId]['cityKeys'];
            if(isset($allcityArray[$provinceId])&&count($allcityArray[$provinceId])==1)
            {
                $daohangcityarr=$allcityArray[$areaId];
            }
            else
            {
                $daohangcityarr=$allcityArray[$provinceId];
            }
        }
        else
        {
            $daohangcityarr=$allcityArray[$cityId];
        }


        $cityId_new=$cityId;
        if($cityId=='000A')//华中区域特殊处理
        {
            $allcitykeyarr=array();
            foreach($allcityArray[$cityId] as $k=>$v)
            {
                $allcitykeyarr[]=$v['cityid'];
            }
           $cityId_new=implode('|',$allcitykeyarr);
        }

        //栏目
        $columnNavigation = $this->getSteelHomeDataPictureList(["type" => $GLOBALS['PAGE_HEADER_SETTING'][$channelId]['column'], "pageSize" => 21 ]);
        $columnNavigation = json_decode($columnNavigation, true);
        // 品种
        $varietyNavigation = $this->getSteelHomeDataPictureList([ "type" => $GLOBALS['PAGE_HEADER_SETTING'][$channelId]['variety'], "pageSize" => 29]);
        $varietyNavigation = json_decode($varietyNavigation, true);
        foreach($varietyNavigation as $k=>$v)
        {
            if(str_replace('varietyId=','',$v['picture_url'])!=$v['picture_url'])
            {
                $varietyNavigation[$k]['picture_url']=$v['picture_url']."&cityId=".$cityId;
            }
        }
       
        // 城市
        $cityNavigation = $this->getSteelHomeDataPictureList([ "type" => $GLOBALS['PAGE_HEADER_SETTING'][$channelId]['city'],"pageSize" => 68]);
        $cityNavigation = json_decode($cityNavigation, true);
        $cityNavigationnew=array();
        //市场导航只保留一半
        foreach($cityNavigation as $k=>$v)
        {
            $pattern = "/city-([^\s]{2,4})\//";
            preg_match($pattern, $v['picture_url'], $matches);
            $cityCode = $matches[1];
            if(!empty($cityCode))
            {
                if($cityCode=='all')
                {
                    continue;
                }
                $cityCode=$cityCode=='0101'?'41':$cityCode;
                $cityNavigation[$k]['picture_url']='market.php?cityId='.$cityCode;
                if($cityCode==$cityId)
                {
                    $cityNavigation[$k]['picture_url']='market.php?cityId='.$cityCode.'" style="color:#ff0000;font-weight:bold;';
                }
            }
            $pattern = "/cityId=([^\s]{2,4})/";
            preg_match($pattern, $cityNavigation[$k]['picture_url'], $matches);
            $cityCode = $matches[1];
            if(!empty($cityCode))
            {
                $cityCode=$cityCode=='0101'?'41':$cityCode;
                $cityNavigation[$k]['picture_url']='market.php?cityId='.$cityCode;
                if($cityCode==$cityId)
                {
                    $cityNavigation[$k]['picture_url']='market.php?cityId='.$cityCode.'" style="color:#ff0000;font-weight:bold;';
                }
            }
            $cityNavigation[$k]['type']='1';
            $cityNavigationnew[]=$cityNavigation[$k];
            if($cityCode=='0055')
            {
                break;
            }
        }
        //获取指定频道市场
        $getCityListByChannel = $this->getCityListByChannel(["channelid" =>$channelId]);
        //print_r($getCityListByChannel);
        $channelCity=array();
        foreach($getCityListByChannel as $k=>$v)
        {
            $citytmparr=explode(',',$v['cityid']);
            if(!in_array($citytmparr[1],$channelCity))
            {
                $channelCity[]=$citytmparr[1];
            }
        }
        //print_r($channelCity);
        //市场导航拼接关联城市
        $citychongfuarr=array();
        $i=0;
        foreach($daohangcityarr as $k=>$v)
        {
            if(in_array($v['cityid'],$channelCity))
            {
                if($i>24)
                {
                    break;
                }
                $cityNavigationinfo=array();
                $cityNavigationinfo['name']=str_replace(array('乌鲁木齐','呼和浩特'),array('乌市','呼市'),$v['cityname']);
                $cityNavigationinfo['picture_url']='market.php?cityId='.$v['cityid'];
            if($v['cityid']==$cityId)
            {
                $cityNavigationinfo['picture_url']='market.php?cityId='.$v['cityid'].'" style="color:#ff0000;font-weight:bold;';
            }
                $cityNavigationinfo['type']='0';
                $cityNavigationnew[]= $cityNavigationinfo;
                $citychongfuarr[]=$v['cityid'];
                $i++;
            }
            
        }
        if($channelId==12)
        {
            if($i<25)
            {
                $areaId=$cityArray[$cityId]['cityKeys'];
                $daohangcityarr=$allcityArray[$areaId];
                foreach($daohangcityarr as $k=>$v)
                {
                    if(in_array($v['cityid'],$channelCity)&&!in_array($v['cityid'],$citychongfuarr))
                    {
                        if($i>24)
                        {
                            break;
                        }
                        $cityNavigationinfo=array();
                        $cityNavigationinfo['name']=str_replace(array('乌鲁木齐','呼和浩特'),array('乌市','呼市'),$v['cityname']);
                        $cityNavigationinfo['picture_url']='market.php?cityId='.$v['cityid'];
                        $cityNavigationinfo['type']='0';
                        if($v['cityid']==$cityId)
                        {
                            $cityNavigationinfo['picture_url']='market.php?cityId='.$v['cityid'].'" style="color:#ff0000;font-weight:bold;';
                        }
                        $cityNavigationnew[]= $cityNavigationinfo;
                        $i++;
                    }
                }
                
            }
        }
        
        //市场导航拼接区域
        $arealist=array(
            array('cityname' => '华东', 'cityid' => '0001'),
            array('cityname' => '华北', 'cityid' => '0003'),
            array('cityname' => '东北', 'cityid' => '0004'),
            array('cityname' => '中南', 'cityid' => '0002'),
            array('cityname' => '华中', 'cityid' => '000A'),
            array('cityname' => '西南', 'cityid' => '0005'),
            array('cityname' => '西北', 'cityid' => '0006')
         );
        foreach($arealist as $k=>$v)
        {
            $cityNavigationinfo=array();
            $cityNavigationinfo['name']=$v['cityname'];
            $cityNavigationinfo['picture_url']='market.php?cityId='.$v['cityid'];
            $cityNavigationinfo['type']='1';
            if($v['cityid']==$cityId)
            {
                $cityNavigationinfo[$k]['picture_url']='market.php?cityId='.$v['cityid'].'" style="color:#ff0000;font-weight:bold;';
            }
            $cityNavigationnew[]= $cityNavigationinfo;
        }
        

        $this->assign("varietyNavigation", $varietyNavigation);
        $this->assign("columnNavigation", $columnNavigation);
        $this->assign("cityNavigation", $cityNavigationnew);

        //现货报价
        $newsListData['spotQuotationList'] = $this->getSpotQuotationList([ "pageType" => $channelId,"pageSize" => 15]);
        // 走势图最后一个放的期货
        $forwardQuotationList = $this->getForwardQuotationList([ "pageType" => $channelId,"pageSize" => 1]);
        $newsListData['spotQuotationList'][] = [
            "xAxis" => $forwardQuotationList[0]['xAxis'],
            "yAxis" => $forwardQuotationList[0]['yAxis'],
            "price" => $forwardQuotationList[0]['price'],
            "rise_and_fall" => $forwardQuotationList[0]['riseAndFall'],
            "price_name" => $forwardQuotationList[0]['priceName']."<br>".$forwardQuotationList[0]['priceContract'],
            "price_id" => $forwardQuotationList[0]['priceId'],
            "id" => $forwardQuotationList[0]['id'],
            "url" => APP_URL_WWW."/showgraph_new.php?qh_type=".$GLOBALS['forward_ID_SHPIID'][$forwardQuotationList[0]['priceId']],
        ];

        //推荐资源
        $newsListData['recommendResourceList'] = $this->getRecommendResourceList(array("channel" => $channelId, "type" => 1,"cityId"=>$cityId,"pageSize" => 8));
        $newsListData['recommendResourceList'] = array_unique($newsListData['recommendResourceList'],SORT_REGULAR);
        //特钢单独处理条数
        $newsListData['recommendResourceList'] = array_slice($newsListData['recommendResourceList'],0,8);
        //推荐经销商
        if( $citytype==1||$citytype==3)
        {
            $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(array("cityId"=>$cityId,"pageSize" => 9));
        }
        elseif( $citytype==2)
        {
            if($cityId!='000A')
            {
                $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(array("areaId"=>$cityId,"pageSize" => 9));
            }
            else
            {
                $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(array("cityId"=>$cityId_new,"pageSize" => 9));
            }
        }
        else
        {
            $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(array("pageSize" => 9));
        }
        //热点资源
        
        $newsListData['hotResourceList'] = $this->getRecommendResourceList(array("channel" => $channelId, "type" => 2,"cityId"=>$cityId,"pageSize" => 10));
        $newsListData['hotResourceList'] = array_unique($newsListData['hotResourceList'],SORT_REGULAR);
        //特钢单独处理条数
        $newsListData['hotResourceList'] = array_slice($newsListData['hotResourceList'],0,10);



        $tjlist = array();

        $cityname = $cityArray[$cityId]['cityname'];
        //$tjlist['show_title'] = $cityname . '市场<br>价格行情';


        $redisKeyHomePageList = "5=".$channelId."=0==" . $cityId;
        $defaultredisKey = REDIS_PAGE_SETTING_INDEX_KEY . ":5=".$channelId."=0==0007";
        //echo  $defaultredisKey;

        $redisNewsList = $this->getRedisNewsList_new($redisKeyHomePageList, $defaultredisKey, "", "", "", $cityId_new, ($citytype==1?false:true));
        $tjlist['title_url'] = "/1=".$channelId."=002,055==".$cityId_new."=/1.html";
        //print_r($allcityArray[$cityId]);
        //print_r($redisNewsList);exit;
        /*
        if(($citytype==2&&!isset($redisNewsList[0]['list']))||$citytype==3)
        {
            $i=0;
            foreach($allcityArray[$cityId] as $k=>$v)
            {

                $ufrValue="1=".$channelId."=002,055==".$v['cityid']."=";
                //echo $v['cityname'].$ufrValue."<br/>";
                $mrhq = $this->getElasticSearchList(array("redisKeyString" => $ufrValue,"pageSize"=>'20'));
                if(!empty($mrhq))
                {
                    $tjlist['show_secondary'][]=array('title'=>$v['cityname'],'url'=>"market.php?cityId=".$v['cityid']);
                    $tjlist['list'][]=$mrhq;
                    if($i>12)
                    {
                        break;
                    }
                    $i++;
                }
            }
            $redisNewsList[0]['show_secondary']=$tjlist['show_secondary'];
            $redisNewsList[0]['list']=$tjlist['list'];
        }
        */
        if(empty($redisNewsList[0]['list'])&&empty($redisNewsList[1]['list'])&&empty($redisNewsList[2]['list'])&&empty($redisNewsList[3]['list']))
        {
            header("location:".$tjlist['title_url']);
            exit;
        }
        $showdivindex=3;
        if(!empty($redisNewsList[0]['list']))
        {
            $showdivindex=0;
        }
        elseif(!empty($redisNewsList[1]['list']))
        {
            $showdivindex=1;
        }
        elseif(!empty($redisNewsList[2]['list']))
        {
            $showdivindex=2;
        }




        //处理首页现货报价的小走势图
        $this->assign("spotEchartsData", json_encode($newsListData['spotQuotationList']));
        //$this->assign("tjlist", $tjlist);
        $this->assign("redisNewsList", $redisNewsList);
        $this->assign("newsListData", $newsListData);

        $this->assign("cityId", $cityId);
        $this->assign("cityname", $cityname);
        $this->assign("pageName", $cityname."<br/>特钢市场");
        $this->assign("channelId", $channelId);
        $this->assign("channelName", $this->pageName);
        //频道编码
        $this->assign("channelCode", $this->channelCode);
        $this->assign("showdivindex", $showdivindex);
        $this->handlePublicData($params);
    }

    public function market($params)
    {
        $channelId = $params['channelId'] ? $params['channelId'] : $this->channelId;
        $cityId = $params['cityId'] ? $params['cityId'] : '0007';
        $varietyId = $params['varietyId'] ? $params['varietyId'] : '201';
        $newsListData = [];
       
        $newsListData['spotQuotationList'] = $this->getSpotQuotationList([ "pageType" => $channelId,"pageSize" => 15]);
        // 走势图最后一个放的期货
        $forwardQuotationList = $this->getForwardQuotationList([ "pageType" => $channelId,"pageSize" => 1]);
        $newsListData['spotQuotationList'][] = [
            "xAxis" => $forwardQuotationList[0]['xAxis'],
            "yAxis" => $forwardQuotationList[0]['yAxis'],
            "price" => $forwardQuotationList[0]['price'],
            "rise_and_fall" => $forwardQuotationList[0]['riseAndFall'],
            "price_name" => $forwardQuotationList[0]['priceName']."<br>".$forwardQuotationList[0]['priceContract'],
            "price_id" => $forwardQuotationList[0]['priceId'],
            "id" => $forwardQuotationList[0]['id'],
            "url" => APP_URL_WWW."/showgraph_new.php?qh_type=".$GLOBALS['forward_ID_SHPIID'][$forwardQuotationList[0]['priceId']],
        ];
          //推荐资源
        // 推荐资源
        $newsListData['recommendResourceList'] = $this->getRecommendResourceList(["channel" => $channelId, "type" => 1, "lb"=>"501", "pageSize" => 20]);
        $newsListData['recommendResourceList'] = array_unique($newsListData['recommendResourceList'],SORT_REGULAR);
        //特钢单独处理条数
        $newsListData['recommendResourceList'] = array_slice($newsListData['recommendResourceList'],0,10);
        // 推荐经销商(暂无频道区分)
        $newsListData['recommendDistributorList'] = $this->getRecommendDistributorList(["pageSize" => 11]);
        //热点资源
         // 热点资源
         $newsListData['hotResourceList'] = $this->getRecommendResourceList(["channel" => $channelId, "type" => 2, "lb"=>"501", "pageSize" => 20 ]);
         $newsListData['hotResourceList'] = array_unique( $newsListData['hotResourceList'],SORT_REGULAR);
         //特钢单独处理条数
         $newsListData['hotResourceList'] = array_slice($newsListData['hotResourceList'],0,10);
        
       
          //调取品种通用逻辑
        $this->handlevarietycitypage($channelId,$varietyId,$cityId);

        
        
        $this->assign("spotEchartsData", json_encode($newsListData['spotQuotationList']));
       
        $this->assign("newsListData", $newsListData);
        $this->assign("varietyId", $params['varietyId']);
        
        $this->assign("channelId", $channelId);
        $this->assign("channelName", $this->channelName);
        //频道编码
        $this->assign("channelCode", $this->channelCode);

        $this->handlePublicData($params);

    }

}
