<?php
/**
 * 竞拍短信提醒——可以在企业微信做一个提醒，时间为每一期竞拍时间的58分钟之后（如：本期竞拍时间为9月30日10:00可以在9月30日10:58提醒一次）
 */
require_once("/etc/steelconf/env/www_env.php");
$webhook31 = WEBHOOK31;
$webhook15 = WEBHOOK15;//煤焦
$webhook17=WEBHOOK17;//炉料
require_once("/etc/steelconf/config/isholiday.php");
require_once("/usr/local/www/libs/vendor/autoload.php");

//$db->debug=true;
$dbgc = ADONewConnection('mysqli');
$dbgc->Connect(HOST_NAME_GC . ":" . HOST_PORT_GC, USER_NAME_GC, USER_PASSWORD_GC, DATABASE_NAME_GC);

//蒙古煤炭竞拍
$date = date("Y-m-d H:i:00",strtotime("-58 Minute"));
$data = $dbgc->GetAll("select `com_name`, `com_short_name`, `variety_name`,  `norm`, `auction_quantity`, `start_price`, `jingjia_time` from sc_mse_coal_jingjia_price WHERE jingjia_spider_time='$date' and delete_flag='0' ");

if(!empty($data)){
    $remindMsg = "";
    foreach ($data as $index => $item) {
        $remindMsg .= "蒙煤竞拍：".date("n月j日H:i ",strtotime($item['jingjia_time'])).$item['com_short_name'];
        $remindMsg .= $item['variety_name'].$item['norm']." 竞拍量".$item['auction_quantity']." 起拍价".$item['start_price']."。\n";
    }
    if($remindMsg!=""){
        $template = '{
			"msgtype": "text",
			"text": {
				"content": "' . $remindMsg . '"
			}
		}';
        echo $remindMsg;
        request_post($webhook15, $template);
        request_post($webhook31, $template);
    }
}

//煤炭竞拍预告 每日实时
$date = date("Y-m-d H:i:00");
$data = $dbgc->GetAll("select `other_point_name`, `com_name`, `product_name`,  `norm`, `auction_quantity`, `starting_price`,jingjia_start_time from sc_othercoal_jingjia_price WHERE jingjia_start_time='$date' and delete_flag='0' ");
if(!empty($data)){
    $remindMsg = "";
    foreach ($data as $index => $item) {
        //离柳焦煤集团竞拍：3月12日12:00 兑镇焦煤A10.5S3V25G85，竞拍量4万吨，起拍价900。开始竞拍
        $remindMsg .= $item['com_name']."竞拍：".date("n月j日H:i ",strtotime($item['jingjia_start_time'])).$item['other_point_name'];
        $remindMsg .= $item['product_name'].$item['norm']."，竞拍量".$item['auction_quantity']."，起拍价".$item['starting_price']."。开始竞拍\n";
    }
    if($remindMsg!=""){
        $template = '{
			"msgtype": "text",
			"text": {
				"content": "' . $remindMsg . '"
			}
		}';
        echo $remindMsg;
        request_post($webhook15, $template);
        request_post($webhook31, $template);
    }
}

//煤炭竞拍结束通知 需要在竞拍结束的前两分钟提醒
$date = date("Y-m-d H:i:00",strtotime("+2 Minute"));
$data = $dbgc->GetAll("select `other_point_name`, `com_name`, `product_name`,  `norm`, `auction_quantity`, `starting_price`,jingjia_end_time from sc_othercoal_jingjia_price WHERE jingjia_end_time='$date' and delete_flag='0' ");

if(!empty($data)){
    $remindMsg = "";
    foreach ($data as $index => $item) {
        //离柳焦煤集团竞拍：3月12日12:00 兑镇焦煤A10.5S3V25G85，竞拍量4万吨，起拍价900。已结束
        $remindMsg .= $item['com_name']."竞拍：".date("n月j日H:i ",strtotime($item['jingjia_end_time'])).$item['other_point_name'];
        $remindMsg .= $item['product_name'].$item['norm']."，竞拍量".$item['auction_quantity']."，起拍价".$item['starting_price']."。已结束\n";
    }
    if($remindMsg!=""){
        $template = '{
			"msgtype": "text",
			"text": {
				"content": "' . $remindMsg . '"
			}
		}';
        echo $remindMsg;
        request_post($webhook15, $template);
        request_post($webhook31, $template);
    }
}

//煤炭竞拍公告汇总 每天17:30定时汇总提醒
$time = date("H:i");
if($time=="17:30") {
    $date = date("Y-m-d");
    $data = $dbgc->GetAll("select `other_point_name`, `com_name`, `product_name`,  `norm`, `auction_quantity`, `starting_price`,jingjia_end_time from sc_othercoal_jingjia_price WHERE completion_date='$date' and delete_flag='0' ");
    if (!empty($data)) {
        $remindMsg = "";
        foreach ($data as $index => $item) {
            //离柳焦煤集团竞拍：3月12日12:00 兑镇焦煤A10.5S3V25G85，竞拍量4万吨，起拍价900。已结束
            $remindMsg .= $item['com_name'] . "竞拍：" . date("n月j日H:i ", strtotime($item['jingjia_end_time'])) . $item['other_point_name'];
            $remindMsg .= $item['product_name'] . $item['norm'] . "，竞拍量" . $item['auction_quantity'] . "，起拍价" . $item['starting_price'] . "。\n";
        }
        if ($remindMsg != "") {
            $template = '{
                "msgtype": "text",
                "text": {
                    "content": "' . $remindMsg . '"
                }
            }';
            echo $remindMsg;
            request_post($webhook15, $template);
            request_post($webhook31, $template);
        }
    }
}
$day = date("d");
if($day=="18" || $day=="25"){
    if($time=="14:00") {
        $remindMsg = " 四大矿山折扣需要上传了 ";

        $template = '{
                "msgtype": "text",
                "text": {
                    "content": "' . $remindMsg . '",
				    "mentioned_mobile_list":["15395601312","15551783351"]
                }
            }';
        request_post($webhook17, $template);
        request_post($webhook31, $template);
    }
}

function request_post($url = '', $param = '')
{
    if (empty($url) || empty($param)) {
        return false;
    }
    $curl = curl_init(); // 启动一个CURL会话
    curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
    curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
    curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
    curl_setopt($curl, CURLOPT_POSTFIELDS, $param); // Post提交的数据包
    curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
    curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
    $data = curl_exec($curl); // 执行操作
    if (curl_errno($curl)) {
        echo 'Errno' . curl_error($curl);//捕抓异常
    }
    curl_close($curl); // 关闭CURL会话
    return $data; // 返回数据，json格式
}