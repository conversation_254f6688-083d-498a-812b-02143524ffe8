<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <!-- 让IE浏览器用最高级内核渲染页面 还有用 Chrome 框架的页面用webkit 内核-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 让360双核浏览器用webkit内核渲染页面-->
    <meta name="renderer" content="webkit">
    <title>钢之家商务平台</title>
    <link href="images/css.css" rel="stylesheet" type="text/css" />

    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/jquery-migrate-3.3.2.js"></script>

    <!-- 
    <script src="/_v2js/jquery-1.9.1.min.js"></script>
    <script src="/_v2js/jquery-migrate-1.1.0.min.js"></script>
    -->
    <script type="text/javascript" language='javascript'>
        /****   网页跳转到页面顶部  ****/
        function parentGoTop(){
            $(function(){$('html, body').animate({
                            scrollTop: 0
            },0);});
        }

        function $(element) {
            return document.getElementById(element);
        }
        // 获得浏览器信息
        var Agent = new Object();
        //is firefox
        Agent.isGecko = navigator.userAgent.indexOf("Gecko") != -1;
        //is opera
        Agent.isOpera = navigator.userAgent.indexOf("Opera") != -1;
        //is ie
        Agent.isIe = navigator.appName.indexOf("Microsoft",0) != -1;

        if(Agent.isIe)
            document.styleSheets[0].disabled=true;
        else{
            if (document.styleSheets.length > 1 ){
                document.styleSheets[1].disabled=true;
            }
        }


        var oldid=-1;

        function clickmenu(tag,id){
            if(id==oldid) return;

            if(oldid> -1){
            //	$(tag+'_'+oldid).style.display = 'none';
            //	$(tag+'_last').style.display = 'none';
            }
                oldid=id;
                $(tag+'_'+oldid).style.display = 'block';
                if(id == 2) $(tag+'_last').style.display = 'block';
                    $('menu').style.border='1px solid #716E5D';
        }
        oldmid='m-0-0';
        function clicklist(uri,cid,id){

            if("{ismaster_fg}"=="no"){
                if(cid==5){
                    $('iframe').src=uri;
                }else{
                    alert('您没有权限使用此操作');
                    $('iframe').src='_v2Member_index.php';
                }
                $(oldmid).className='list';
                oldmid='m-'+cid+'-'+id;
                $(oldmid).className='listactive';
                clickmenu('tag',cid);
                return;
            }else{
                // console.log(uri);
                $('iframe').src=uri;
                $(oldmid).className='list';
                oldmid='m-'+cid+'-'+id;
                $(oldmid).className='listactive';
                clickmenu('tag',cid);
            }
        }

        function iframeload(){
            {stoponload}
            clicklist('{firstpage}','{firstcid}','{firstid}');
        }
        // iframeload();
    </script>
    <style type="text/css">
        .top1_dot {
            width: 7px;
            height: 8px;
        }

        #head_logo {
            BORDER-TOP: #9e9e9e 1px solid; WIDTH: 1000px; BORDER-BOTTOM: #cdcdcd 1px solid; HEIGHT: 74px
        }
        #logo {
            FLOAT: left
        }
        #GG_T1 {
            FLOAT: left; MARGIN: 1px 1px 0px 0px
        }
        #GG_T2 {
            FLOAT: left; MARGIN: 1px 0px 0px
        }
        a{
            font-size:12px;
        }
        .stylered{
            color:red
        }
    </style>
</head>
<!--<body onload="setiframe();">-->
<body onload="{onload}" style="text-align: center;">
<div>
    <iframe src="/index.php?view=header&pageTitle=会员中心" width="100%" height="280" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" ></iframe>
</div>
<div class="container">

    <!--<div><iframe src="/denglu.php" width="1000" height="27" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" ></iframe></div>-->

    <!--<DIV id=head_logo>
        <DIV id=logo><img src="/_v2image/logo_ny.jpg" border="0" alt="钢之家"><img src="/_v2image/logo-n2.gif" border="0" alt=""></DIV>
        <DIV id="GG_T1"><a href="/tech/sms.php" target="_blank"><img src="images/dxpt_541x72.gif"  alt="" width="541" height="72" border="0" ></a></DIV>
        <DIV id="GG_T2">	<a href="{app_url_news}/gc/standards_list.shtml" target="_blank"><img src="images/gtcphcybzdq.jpg" border="0" alt=""></a>
        </DIV>
    </DIV>-->
    <div class="top">
        <div class="fl"><img src="images/hylogo.gif" onclick="window.location.href='index.php';"/></div>
        <div class="fr">
            <a onClick="clicklist('/_v2app/prepay.php?view=prepay','7','0');" id="yfkcz3">我的资产</a>
            <a id="zyzb3" onClick="clicklist('/biz/reszb.php?view=index_biz&memberid={memberid}','1','0');">资源专版快讯管理</a>
            <a onClick="clicklist('/Member_v2/memberprice.php?view=index','6','0');" id="hybj3">会员报价管理</a>
            <a onClick="clicklist('_v2Member_wsld.php','0','0');" id="dddz3">订单定制管理</a>
            <a onClick="clicklist('../_v2MemberCenter_hyzccl_update.php','2','0');" id="hyda3">会员档案管理</a>
            <a onClick="clicklist('{app_url_job}/index.php?view=zhaoping','3','0');" id="rczp3">人才招聘管理</a>
            <a onClick="clicklist('/Member/kehu_col_add.php','4','0');" id="wzgl3">网站管理</a>
            <a onClick="clicklist('/Member_v2/member.php?view=service_index','5','0');" id="khyj3">意见与建议管理</a>
        </div>
    </div>
<!----left begin---->
<script>
    jQuery(document).ready(function(){
        jQuery("#zyzb1").click(function(){
        jQuery("#zyzb2").slideToggle();
        });
        if ("{special_column_power}" == true ) {
            jQuery("#zlgl1").show();
            jQuery("#zlgl2").show();
        }else {
            jQuery("#zlgl1").hide();
            jQuery("#zlgl2").hide();
        }
        jQuery("#zlgl1").click(function(){
        jQuery("#zlgl2").slideToggle();
        });
        jQuery("#hybj1").click(function(){
        jQuery("#hybj2").slideToggle();
        });
        jQuery("#dddz1").click(function(){
        jQuery("#dddz2").slideToggle();
        });
        jQuery("#hyda1").click(function(){
        jQuery("#hyda2").slideToggle();
        });
        jQuery("#rczp1").click(function(){
        jQuery("#rczp2").slideToggle();
        });
        jQuery("#wzgl1").click(function(){
        jQuery("#wzgl2").slideToggle();
        });
        jQuery("#khyj1").click(function(){
        jQuery("#khyj2").slideToggle();
        });
        jQuery("#yfkcz1").click(function(){
        jQuery("#yfkcz2").slideToggle();
        });

        jQuery("#zyzb3").click(function(){
        jQuery("#zyzb2").show();
        });
        jQuery("#hybj3").click(function(){
        jQuery("#hybj2").show();
        });
        jQuery("#dddz3").click(function(){
        jQuery("#dddz2").show();
        });
        jQuery("#hyda3").click(function(){
        jQuery("#hyda2").show();
        });
        jQuery("#rczp3").click(function(){
        jQuery("#rczp2").show();
        });
        jQuery("#wzgl3").click(function(){
        jQuery("#wzgl2").show();
        });
        jQuery("#yfkcz3").click(function(){
        jQuery("#yfkcz2").show();
        });
        jQuery("#khyj3").click(function(){
        jQuery("#khyj2").show();
        });
    });
    function clicklist(uri,cid,id){
        var req1_n = new XMLHttpRequest();
        var urlstr_n="ajax.php";
        req1_n.onreadystatechange = function()
        {
            if(req1_n.readyState==4 &&req1_n.status==200)
            {
                    var txet_n_str= req1_n.responseText;
                    if(txet_n_str=='1'){
                        window.location='index.php';//update by wupeiyue  2017/02/23
                    }
                }
        }
        req1_n.open('get',urlstr_n,true);
        req1_n.send(null);
        document.getElementById("content").src=uri;
        parentGoTop();
    }

    function setiframe() {

        if ("{zyflag}" == "1" || "{ismaster_fg}" == "yes") {

            document.getElementById("content").src = '/biz/reszb.php?view=index_biz&memberid={memberid}';
        } else {

            document.getElementById("content").src = '_v2Member_index.php';
        }
    }

function Versioncheck(str) {
    var gIE = getBrowserVersion();
      if(gIE<"IE9"){
          alert("您的浏览器IE版本过低，请升级后重试！");
          return false;
      }else{
          if(str==1){
              clicklist('/_v2app/resource.php?view=resource','4','4');
          }else{
              clicklist('/_v2app/resource.php?view=updateresource','4','5');
          }
      }

}
function getBrowserVersion(){
    var userAgent = navigator.userAgent.toLowerCase();
        if(userAgent.match(/msie ([\d.]+)/)!=null){//ie6--ie9
            uaMatch = userAgent.match(/msie ([\d.]+)/);
            return 'IE'+uaMatch[1];
        }else if(userAgent.match(/(trident)\/([\w.]+)/)){
            uaMatch = userAgent.match(/trident\/([\w.]+)/);
            switch (uaMatch[1]){
                case "4.0": return "IE8" ;break;
                case "5.0": return "IE9" ;break;
                case "6.0": return "IE10";break;
                case "7.0": return "IE11";break;
                default:	return "undefined" ;
                }
        }
        return "undefined";
}
function setIframeHeight(content) {
    if (content) {
        var iframeWin = content.contentWindow || content.contentDocument.parentWindow;
        if (iframeWin.document.body) {
            content.height = iframeWin.document.documentElement.scrollHeight || iframeWin.document.body.scrollHeight;
        }
    }
};


</script>
    <div id="left" class="fl">
        <div class="cccbox myfollow" style="border-bottom:none;">
            <a onClick="clicklist('/_v2app/myfollow.php?index=index&memberid={memberid}&id={id}','1','0');"><strong>我的关注</strong></a>
        </div>
        <div class="cccbox myfollow" style="border-bottom:none;">
            <a onClick="clicklist('/_v2app/myaccount.php?index=index&memberid={memberid}&id={id}','1','0');"><strong>账号信息</strong></a>
        </div>
        <div class="cccbox sidtag" style="border-bottom:none;">


            <a id="zlgl1"><strong>专栏管理</strong></a>
            <span id="zlgl2">
            <a  id='m-8-0' class='list' onClick="clicklist('/special_column.php?view=index','8','0');">・专栏发布</a>
            <a  id='m-8-1' class='list' onClick="clicklist('/special_column.php?view=manage','8','2');">・专栏列表</a>
            </span>

            <a id="yfkcz1"><strong>我的资产</strong></a>
            <span id="yfkcz2" style="display:block;">
            <a class='list' onClick="clicklist('/_v2app/prepay.php?view=prepay','7','0');">・ 我的资产管理</a>
            <a class='list' onClick="clicklist('/special_column.php?view=my_purchase','7','0');">・ 我购买的信息</a>
            <!--
            <a class='list' onClick="clicklist('/_v2app/prepay.php?view=prepaymoney','7','1');">・ 预付款查询</a>
            -->
            </span>
            <a id="zyzb1"><strong>资源专版快讯管理</strong></a>
            <span id="zyzb2">
            <a  id='m-1-0' class='list' onClick="clicklist('/biz/reszb.php?view=index_biz&memberid={memberid}','1','0');">・发布网上资源/供求专版{zy_zb_post}</a>
            <a id='m-1-2' class='list' onClick="clicklist('_v2Member_tjzysel.php','1','2');">・推荐资源发布</a>
            <a id='m-1-3' class='list' onClick="clicklist('_v2Member_gqks.php','1','3');">・供求快讯发布</a>
            <a id='m-1-4' class='list' onClick="clicklist('_v2Member_gqks_update.php','1','4');">・供求快讯重发/修改/删除{kx_post}</a>
            </span>

            <a id="dddz1"><strong>订单订制管理</strong></a>
            <span id="dddz2" style="display:block;">
            <a id='m-0-0' class='list' onClick="clicklist('_v2Member_wsld.php','0','0');">・我的订单管理{order_post}</a>
            <a id='m-0-1' class='list' onClick="clicklist('_v2Member_wsly.php','0','1');">・我的留言管理{lyb_post}</a>
            <a id='m-0-2' class='list' onClick="clicklist('_v2Member_zydz.php','0','2');">・我的资源订制</a>
            <a id='m-0-3' class='list' onClick="clicklist('_v2Member_zydzmanage.php','0','3');">・资源订制管理</a>
            <a id='m-0-4' class='list' onClick="clicklist('_v2Member_gqdz.php','0','4');">・我的快讯订制</a>
            <a id='m-0-5' class='list' onClick="clicklist('_v2Member_gqdzmanage.php','0','5');">・快讯订制管理</a>
            </span>
            <a id="hyda1"><strong>会员档案管理</strong></a>
            <span id="hyda2" style="display:block;">
            <a id='m-2-0' class='list' onClick="clicklist('../_v2MemberCenter_hyzccl_update.php','2','0');">・查看/更改注册资料</a>
            <a id='m-2-1' class='list' onClick="clicklist('../_v2MemberCenter_hyzccl_add.php','2','1');">・增加用户账号</a>
            <a id='m-2-2' class='list' onClick="clicklist('../_v2MemberCenter_company.php','2','2');">・分公司/部门管理</a>
            <a id='m-2-3' class='list' onClick="clicklist('../_v2MemberCenter_linkman.php','2','3');">・分公司/部门联系人管理</a>
            <!--<a id='m-2-4' class='list' onClick="clicklist('../_v2memberlink.php','2','4');">・即时通讯方式管理</a>-->
            <!--add update 把账号绑定手机邮箱功能放到 第三方登陆账号绑定管理 里面 changhong 2017/3/16-->
            <!--<a id='m-2-5' class='list' onClick="clicklist('../_v2MemberCenter_union.php','2','5');">・账号绑定手机邮箱</a>-->
            <!--//end update 把账号绑定手机邮箱功能放到 第三方登陆账号绑定管理里面 changhong 2017/3/16-->
            <a id='m-2-6' class='list' onClick="clicklist('/_v2app/user.php?view=thirdjiebang','2','6');">・第三方登陆账号绑定管理</a>
            <a id='m-2-7' class='list' onClick="clicklist('map.php','2','7');">・地图标识</a>
            </span>

            <a id="rczp1"><strong>人才招聘管理</strong></a>
            <span id="rczp2" style="display:block;">
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=zhaoping','3','0');">・应聘简历管理</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=jiben','3','1');">・公司信息管理</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=seatmanager','3','2');">・招聘职位管理</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=fabu','3','3');">・发布职位</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=mianshi','3','3');">・面试通知</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=qiyerencai','3','3');">・企业人才夹</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=xiazaijianli','3','3');">・人才下载夹</a>
            <a class='list' onClick="clicklist('{app_url_job}/index.php?view=rencaisearch','3','3');">・人才搜索器</a>
            </span>
            <a id="hybj1"><strong>会员报价管理</strong></a>
            <span id="hybj2" style="display:block;">
            <a class='list' onClick="clicklist('/Member_v2/memberprice.php?view=index','6','0');">・发布我的会员报价</a>
            <a class='list' onClick="clicklist('/Member_v2/memberprice.php?view=manage','6','0');">・修改我的会员报价</a>
            <a class='list' onClick="clicklist('/Member_v2/memberprice.php?view=releaseinfo','6','0');">・会员报价发布流程</a>
            <a class='list' onClick="clicklist('/Member_v2/projectprice.php?view=marketselect','6','0');">・发布我的工程报价</a> 
            <a class='list' onClick="clicklist('/Member_v2/projectprice.php?view=marketmanage','6','0');">・修改我的工程报价</a>
            </span>
            <a id="wzgl1"><strong>网站管理</strong></a>
            <span id="wzgl2" style="display:block;">
            <a class='list' onClick="clicklist('/Member/kehu_col_add.php','4','0');">・栏目管理</a>
            <a class='list' onClick="clicklist('/Member/kehu_news_add.php','4','1');">・新闻录入</a>
            <a class='list' onClick="clicklist('/Member/kehu_news_manage.php','4','2');">・新闻管理</a>
            <!-- <a class='list' onClick="Versioncheck(1);">・招商资料上传</a>
            <a class='list' onClick="Versioncheck(2);">・招商资料修改</a> -->
            {dhkg_zszl}
            <!-- <a class='list' onClick="clicklist('/Member/kehu_moban.php','4','3');">・网站模板</a>
            <a class='list' onClick="clicklist('Mobile/mobilemoban.php','4','4');">・手机网站</a> -->
            <a class='list' onClick="clicklist('Mobile/websiteset.php','4','5');">・网站设置</a>
            </span>

            <a id="khyj1"><strong>客户意见与建议</strong></a>
            <span id="khyj2" style="display:block;">
            <a class='list' onClick="clicklist('/Member_v2/member.php?view=service_index','5','0');">・意见与建议管理</a>
            </span>
        </div>
        <a href="/help/help.php" target="_blank"><img src="images/helpcenter.gif" width="220" height="32" /></a>

  </div>
<!----left end---->

<!----right begin setIframeHeight(this)---->
   <div id="right" class="fr">

   <IFRAME id="content"  src=""  height="1500" style="width: 100%" frameBorder=0 scrolling=yes onload="{onload}"></IFRAME>
  </div>
<!----right end---->
<script type="text/javascript" src="/_v2js/FPS_ActivePlug.js"></script>
<script>
//$('menu').style.border='1px solid #716E5D';
//$('content').style.border='1px solid #716E5D';
var ad_float_right_src1 = "/_v2image/qq127x47.png";
var ad_float_left_src1 = "";
function jobheight(){
        //document.getElementById('content').height = 1000;
        document.getElementById('content').height = 900;
}
jobheight();

//added by shizg started 2019/12/16
var jiazhai_flag = true;
var iframe = document.getElementById("content");
iframe.onload = function(){
    if( jiazhai_flag == true ){
        jiazhai_flag = false;
        clicklist('{firstpage}','{firstcid}','{firstid}');
    }
    //alert("iframe load  done");
};
//added by shizg ended 2019/12/16
</script>
{gotokfzx}
{str}

</div>
{footer}
<div style="margin-top:10px;">
    <iframe src="/index.php?view=footer" width="100%" height="270" frameborder="no" marginwidth="0" marginheight="0" scrolling="no" ></iframe>
</div>
</body>
</html>
