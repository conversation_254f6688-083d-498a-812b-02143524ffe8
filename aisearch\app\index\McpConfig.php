<?php

/**
 * MCP系统配置类
 * 管理MCP相关的配置信息，包括提示词模板、模型配置等
 */
class McpConfig
{
    // MCP服务配置
    const MCP_SERVICES = [
        'text_correction' => [
            'name' => '文本纠错',
            'description' => '检测并纠正文本中的语法错误、拼写错误等',
            'prompt_type' => 10, // 新的优化提示词类型
            'default_model' => 'deepseek-v3',
            'max_tokens' => 8192,
            'temperature' => 0.1, // 低温度确保输出稳定
            'timeout' => 30
        ],
        'text_keywords' => [
            'name' => '关键词提取',
            'description' => '从文本中提取关键信息和主题词',
            'prompt_type' => 11, // 关键词提取提示词类型
            'default_model' => 'deepseek-v3',
            'max_tokens' => 8192,
            'temperature' => 0.3, // 适中温度确保关键词多样性
            'timeout' => 30
        ],
        'text_reference' => [
            'name' => '检测文本引用',
            'description' => '检测文本是否引用了外部的资讯',
            'prompt_type' => 12, // 检测文本引用提示词类型
            'default_model' => 'deepseek-v3', // v3 太蠢了，这个是要发企业微信通知的，用 r1 能够降低错误率……？
            'temperature' => 0.1, // 低温度确保输出稳定
            'timeout' => 30
        ]
    ];

    // 响应验证规则
    const VALIDATION_RULES = [
        'required_fields' => ['has_errors', 'corrected_text', 'highlighted_text', 'corrections', 'summary'],
        'max_corrections' => 500, // 最大纠错数量限制
        'max_text_length' => 50000, // 最大文本长度限制
        'allowed_error_types' => ['语法错误', '拼写错误', '标点错误', '用词不当', '逻辑错误'],
        'required_keyword_fields' => ['keywords', 'summary'],
        'required_reference_fields' => ['has_external_reference', 'references', 'summary']
    ];

    // 错误代码定义
    const ERROR_CODES = [
        'INVALID_JSON' => 1001,
        'MISSING_FIELDS' => 1002,
        'INVALID_FORMAT' => 1003,
        'TEXT_TOO_LONG' => 1004,
        'TOO_MANY_CORRECTIONS' => 1005,
        'AI_RESPONSE_ERROR' => 1006,
        'TIMEOUT_ERROR' => 1007
    ];

    /**
     * 获取MCP服务配置
     * @param string $serviceType
     * @return array|null
     */
    public static function getServiceConfig(string $serviceType): ?array
    {
        return self::MCP_SERVICES[$serviceType] ?? null;
    }

    /**
     * 获取验证规则
     * @return array
     */
    public static function getValidationRules(): array
    {
        return self::VALIDATION_RULES;
    }

    /**
     * 获取错误代码信息
     * @param string $errorCode
     * @return int
     */
    public static function getErrorCode(string $errorCode): int
    {
        return self::ERROR_CODES[$errorCode] ?? 9999;
    }

    /**
     * 验证AI响应格式
     * @param mixed $response
     * @return array [bool $isValid, string $errorMessage, int $errorCode]
     */
    public static function validateResponse($response): array
    {
        // 检查是否为数组
        if (!is_array($response)) {
            return [false, 'AI响应不是有效的数组格式', self::getErrorCode('INVALID_FORMAT')];
        }

        // 检查是否为关键词提取响应（包含keywords字段）
        if (isset($response['keywords'])) {
            // 验证关键词提取响应
            $rules = self::getValidationRules();
            foreach ($rules['required_keyword_fields'] as $field) {
                if (!isset($response[$field])) {
                    return [false, "缺少必需字段: {$field}", self::getErrorCode('MISSING_FIELDS')];
                }
            }
            
            // 验证keywords字段必须是数组
            if (!is_array($response['keywords'])) {
                return [false, 'keywords字段必须是数组格式', self::getErrorCode('INVALID_FORMAT')];
            }
            
            return [true, '', 0];
        }

        // 检查是否为文本引用检测响应（包含has_external_reference字段）
        if (isset($response['has_external_reference'])) {
            // 验证文本引用检测响应
            $rules = self::getValidationRules();
            foreach ($rules['required_reference_fields'] as $field) {
                if (!isset($response[$field])) {
                    return [false, "缺少必需字段: {$field}", self::getErrorCode('MISSING_FIELDS')];
                }
            }
            
            // 验证references字段必须是数组
            if (!is_array($response['references'])) {
                return [false, 'references字段必须是数组格式', self::getErrorCode('INVALID_FORMAT')];
            }
            
            // 验证references数组中的每个元素
            foreach ($response['references'] as $reference) {
                if (!is_array($reference) || 
                    !isset($reference['source']) || 
                    !isset($reference['position']) || 
                    !isset($reference['text'])) {
                    return [false, 'references数组中的元素格式不正确', self::getErrorCode('INVALID_FORMAT')];
                }
                
                // 验证position格式
                if (!is_array($reference['position']) ||
                    !isset($reference['position']['start']) ||
                    !isset($reference['position']['end']) ||
                    !is_numeric($reference['position']['start']) ||
                    !is_numeric($reference['position']['end']) ||
                    $reference['position']['start'] < 0 ||
                    $reference['position']['end'] < $reference['position']['start']) {
                    return [false, 'position格式不正确，必须包含有效的start和end数值', self::getErrorCode('INVALID_FORMAT')];
                }
            }
            
            return [true, '', 0];
        }

        // 检查必需字段（文本纠错）
        $rules = self::getValidationRules();
        foreach ($rules['required_fields'] as $field) {
            if (!isset($response[$field])) {
                return [false, "缺少必需字段: {$field}", self::getErrorCode('MISSING_FIELDS')];
            }
        }

        // 检查文本长度
        if (isset($response['corrected_text']) && 
            mb_strlen($response['corrected_text']) > $rules['max_text_length']) {
            return [false, '纠错后文本过长', self::getErrorCode('TEXT_TOO_LONG')];
        }

        // 检查纠错数量
        if (isset($response['corrections']) && 
            is_array($response['corrections']) && 
            count($response['corrections']) > $rules['max_corrections']) {
            return [false, '纠错数量超出限制', self::getErrorCode('TOO_MANY_CORRECTIONS')];
        }

        // 验证纠错项格式
        if (isset($response['corrections']) && is_array($response['corrections'])) {
            foreach ($response['corrections'] as $correction) {
                if (!is_array($correction) || 
                    !isset($correction['original']) || 
                    !isset($correction['corrected']) || 
                    !isset($correction['type']) ||
                    !isset($correction['position'])) {
                    return [false, '纠错项格式不正确', self::getErrorCode('INVALID_FORMAT')];
                }

                // 验证错误类型
                if (!in_array($correction['type'], $rules['allowed_error_types'])) {
                    return [false, "无效的错误类型: {$correction['type']}", self::getErrorCode('INVALID_FORMAT')];
                }

                // 验证position格式
                if (!is_array($correction['position']) ||
                    !isset($correction['position']['start']) ||
                    !isset($correction['position']['end']) ||
                    !is_numeric($correction['position']['start']) ||
                    !is_numeric($correction['position']['end']) ||
                    $correction['position']['start'] < 0 ||
                    $correction['position']['end'] < $correction['position']['start']) {
                    return [false, 'position格式不正确，必须包含有效的start和end数值', self::getErrorCode('INVALID_FORMAT')];
                }
            }
        }

        // 验证highlighted_text字段
        if (isset($response['highlighted_text']) && !is_string($response['highlighted_text'])) {
            return [false, 'highlighted_text必须是字符串格式', self::getErrorCode('INVALID_FORMAT')];
        }

        return [true, '', 0];
    }
}