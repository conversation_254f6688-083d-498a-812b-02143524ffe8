<?php
/**
 * Project: Costumer relationship management system
 *
 * File:function.inc.php
 *
 * @link http://www.steelhome.cn
 * @version $Id$
 * @copyright 2005-
 * <AUTHOR>
 * @package admcp
 */

/**
 * verify user & passwrod. if OK return true,else return false
 *
 * @param string $username the username
 * @param string $password the password
 * @return bool
 */
class shpi{
  var $connshpi;
  var $newsid;
  var $avgpz_arr;
  var $avgwpz_arr;
  //add changhong 2018-06-12
  var $dot_size = 1;
	// var $dot_type = MARK_SQUARE;
	var $dot_type = 1;
	//end changhong 2018-06-12
	function __construct($conn){
	 $this->connshpi=$conn;
  }
  /*
  ; 得到需要计算的品种规格数组
  ; 价格ID编码以6位数字进行编制，城市（2位）品种（2位）材质（1位）规格（1位）
  */
  function arr_pzgg(){
     global $catid;
     $ggcz="select * from shpi_svw ";  //用来计算的品种规格表
	 //================== //得到数组
	 $rs=$this->connshpi->Execute($ggcz);
	 $i=0;
	 while (!$rs->EOF){
	    $catid[$i]=$rs->fields("vclass");
		//$pzid[$i]=$rs->fields("ProductID");
		//$czid[$i]=$rs->fields("Material");
		//$ggid[$i]=$rs->fields("Specification");
		$pzgg[$rs->fields("vid")]=$rs->fields("vclass");
		$i++;
	    $rs->MoveNext();
	 }
	 //==================
     return $pzgg;
  }
  function getpreday($qdate,$table){
	  $date_arr = explode("-",$qdate);
	  if (!checkdate($date_arr[1],$date_arr[2],$date_arr[0])){
		  $qdate = date('Y-m-t',strtotime($date_arr[0]."-".$date_arr[1]."-01"));
	  }
  	$sqldate="select dateday  from $table where dateday='".$qdate."' limit 1";
  	$days = $this->connshpi->getrow($sqldate);
  	//print_r($days);

  	if(empty($days)){
  		$sqldate2="select dateday  from $table where dateday<'".$qdate."' order by dateday desc limit 1";
  		$days = $this->connshpi->getrow($sqldate2);
  	}
  	if($days['dateday'] != "" ){
  		$qdate = $days['dateday'];
  	}
  	return $qdate;
  }
  /*
  ; 得到需要计算的城市数组
  ; 价格ID编码以6位数字进行编制，城市（2位）品种（2位）材质（1位）规格（1位）
  */
  // 中国钢材价格指数英文版
  function arr_flashab_en($arr){
  	global $stime,$etime,$max,$min;
  	if (!is_array($arr)){
  		return;
  	}
  	// print_r($arr);
	$arr_pidcc = array (
		"0" => "Steel Products",
		"1" => "Long Products",
		"2" => "Flat Products",
		"3" => "Quality",
		"4" => "Stainless Steel",
		"5" => "Seamless Pipe",
		"6" => "Strip",
		"7" => "Welded Pipe",
		"j1" => "Common Wire Rod",
		"j2" => "Rebar",
		"j3" => "Section Products",
		"j4" => "Medium Plate",
		"j5" => "HR Plate/coil",
		"j6" => "CR Sheet/coil",
		"j8" => "Quality Wire Rod",
		"j9" => "Structural Steel" ,
		"b1" => "Pickled Sheet/Coil",
		"b2" => "Hot Dipped Galvanized Sheet/Coil",
		"b3" => "Hot Dipped Galvalume Sheet/Coil",
		"b4" => "Electro Galvanized Sheet & Coil ",
		"b5" => "Pre-painted Galvanized Sheet/Coil",
		"b6" => "Electrolytic Tinplate",
		"b7" => "Oriented Silicon Steel",
		"b8" => "Non-oriented Silicon Steel",
	);
	$news = 0;
  	foreach ($arr as $nn=>$vv){
  		$Data1[0]="";$xi=1;
  		if( $news == 0 ){
  			$Data2[0]=$arr_pidcc[$nn] ;
  		}else{
  			$Data2[0]=$arr_pidcc[$nn];
  		}
  		$xi=1;
  		foreach ($vv as $ndate=>$nprice){
  			if ($news==0){
  				$Data1[$xi]=date("y/m/d",strtotime($ndate));
  			}
  			$Data2[$xi]=$nprice;
  			if ($min>$Data2[$xi]){
  				$min=$Data2[$xi];
  			}
  			if ($max<$Data2[$xi]){
  				$max=$Data2[$xi];
  			}
  			$xi++;
  		}

  		// power由7改成6，甲级会员可以显示均价
  		if((isset($_SESSION['mid'])&&$_SESSION['mid'])==1 || (isset($_SESSION['maxpower'])&&$_SESSION['maxpower']>=6) || (isset($_SESSION['memberid'])&&$_SESSION['memberid']==1) || (isset($_SESSION['enpower'])&&$_SESSION['enpower']>=6)){
  			if( $news == 0 ){
  				$Data2[0]=$arr_pidcc[$nn]."(AVG:".round(array_sum($Data2)/(count($Data2)-1)).")";
  			}else{
  				$Data2[0]=$arr_pidcc[$nn]."(AVG:".round(array_sum($Data2)/(count($Data2)-1)).")";
  				//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
  			}
  		}
  		if ($news==0){
  			$chart=array($Data1,$Data2);
  		}else{ array_push($chart,$Data2);
  		}
  		$news++;
  	}
  	return $chart;
  }
  //单品种专用3种类型专用
 function arr_flashab_dwm($arr,$leixing,$avgprice,$isenglish=0){

  	global $stime,$etime,$max,$min;
  	if (!is_array($arr)){
  		return;
  	}
  	 //print_r($arr);


		$str_lx = array("1"=>"每天","2"=>"周均价","3"=>"月均价");

	 $str_lx_en=array("1"=>'Daily',"2"=>'Weekly',"3"=>'Monthly');
	 if($isenglish==1)
	 {
		$str_lx= $str_lx_en;
	 }
  	$news=0;
  	foreach ($arr as $nn=>$vv){
  		$Data1[0]="";$xi=1;
  		if( $news == 0 ){
  			$Data2[0]=$str_lx[$leixing[0]];
  		}
  		if($news == 1){
  			$Data2[0]=$str_lx[$leixing[1]];
  		}
  		if($news == 2){
  			$Data2[0]=$str_lx[$leixing[2]];
  		}

  		$xi=1;
  		foreach ($vv as $ndate=>$nprice){
  			if ($news==0){

  				$Data1[$xi]=date("y/m/d",strtotime($ndate));

  			}
  			$Data2[$xi]=$nprice;
  			if ($min>$Data2[$xi]){
  				$min=$Data2[$xi];
  			}
  			if ($max<$Data2[$xi]){
  				$max=$Data2[$xi];
  			}
  			$xi++;
  		}
  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
  			if($isenglish==1){
				if ($news == 0) {
					$Data2[0] = $str_lx[$leixing[0]] . "(avg:" . round(array_sum($Data2) / (count($Data2) - 1)) . ")";
				} else {
					if ($news == 1) {
						$Data2[0] = $str_lx[$leixing[1]] . "(avg:" . round(array_sum($Data2) / (count($Data2) - 1)) . ")";
					} else {
						$Data2[0] = $str_lx[$leixing[2]] . "(avg:" . round(array_sum($Data2) / (count($Data2) - 1)) . ")";
					}

					//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
				}
			} else {
					if ($news == 0) {
						$Data2[0] = $str_lx[$leixing[0]] . "(平均:" . round(array_sum($Data2) / (count($Data2) - 1)) . ")";
					} else {
						if ($news == 1) {
							$Data2[0] = $str_lx[$leixing[1]] . "(平均:" . round(array_sum($Data2) / (count($Data2) - 1)) . ")";
						} else {
							$Data2[0] = $str_lx[$leixing[2]] . "(平均:" . round(array_sum($Data2) / (count($Data2) - 1)) . ")";
						}

						//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
					}
  			}
  		}
		//add by zfy started 2017/7/19
				if($avgprice){
				foreach ($avgprice as $k=>$v){
					if($news == 1 && $k == 0){
						$Data2[0]=$v."日均线";break;
					}else if($news == 2 && $k == 1){
						$Data2[0]=$v."日均线";break;
					}else if($news == 3 && $k == 2){
						$Data2[0]=$v."日均线";break;
					}else if($news == 4 && $k == 3){
						$Data2[0]=$v."日均线";break;
					}else if($news == 5 && $k == 4){
						$Data2[0]=$v."日均线";break;
					}else if($news == 6 && $k == 5){
						$Data2[0]=$v."日均线";break;
					}
				}
				}
				//add by zfy ended 2017/7/19
  		if ($news==0){
  			$chart=array($Data1,$Data2);
  		}else{ array_push($chart,$Data2);
  		}
  		$news++;
  	}

  	return $chart;
  }
  function arr_flashab_dwm_d($arr,$leixing,$pz='',$roundnum=0,$avgprice=''){

  	global $stime,$etime,$max,$min;
  	if (!is_array($arr)){
  		return;
  	}
  	// echo "<pre>";print_r($pz);


  	$str_lx = array("1"=>"每天","2"=>"周均价","3"=>"月均价");
	$str_pz = array("3"=>"62%","9"=>"65%","16"=>"63%","18"=>"61%","20"=>"58%");


  	$news=0;
  	foreach ($arr as $nn=>$vv){
  		$Data1[0]="";$xi=1;

      if(count($pz)>'1')

	  {
          if( $news == 0 ){
  			$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['0']];
  		    }
			if($news == 1){
				$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['1']];
			}
			if($news == 2){
				$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['2']];
			}
	  }
	  else
		{
           if( $news == 0 ){
  			$Data2[0]=$str_lx[$leixing[0]];
  		    }
			if($news == 1){
				$Data2[0]=$str_lx[$leixing[1]];
			}
			if($news == 2){
				$Data2[0]=$str_lx[$leixing[2]];
			}
	    }



  		$xi=1;
  		foreach ($vv as $ndate=>$nprice){
  			if ($news==0){

  				$Data1[$xi]=date("y/m/d",strtotime($ndate));

  			}
  			$Data2[$xi]=$nprice;
  			if ($min>$Data2[$xi]){
  				$min=$Data2[$xi];
  			}
  			if ($max<$Data2[$xi]){
  				$max=$Data2[$xi];
  			}
  			$xi++;
  		}
  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
			if(count($pz)>'1'){
				if( $news == 0 ){
					$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['0']]."(平均:".round(array_sum($Data2)/(count($Data2)-1),$roundnum)."美元)";
				}else{
					if($news == 1){
						$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['1']]."(平均:".round(array_sum($Data2)/(count($Data2)-1),$roundnum)."美元)";
					}else{
						$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['2']]."(平均:".round(array_sum($Data2)/(count($Data2)-1),$roundnum)."美元)";
					}
					//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
				}
			}else{
				if( $news == 0 ){
					$Data2[0]=$str_lx[$leixing[0]]."-".$str_pz[$pz['0']]."(平均:".round(array_sum($Data2)/(count($Data2)-1),$roundnum)."美元)";
				}else{
					if($news == 1){
						$Data2[0]=$str_lx[$leixing[1]]."-".$str_pz[$pz['0']]."(平均:".round(array_sum($Data2)/(count($Data2)-1),$roundnum)."美元)";
					}else{
						$Data2[0]=$str_lx[$leixing[2]]."-".$str_pz[$pz['0']]."(平均:".round(array_sum($Data2)/(count($Data2)-1),$roundnum)."美元)";
					}
					//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
				}
			}
  		}
		//add by zfy started 2017/7/19
				if($avgprice){
				foreach ($avgprice as $k=>$v){
					if($news == 1 && $k == 0){
						$Data2[0]=$v."日均线";break;
					}else if($news == 2 && $k == 1){
						$Data2[0]=$v."日均线";break;
					}else if($news == 3 && $k == 2){
						$Data2[0]=$v."日均线";break;
					}else if($news == 4 && $k == 3){
						$Data2[0]=$v."日均线";break;
					}else if($news == 5 && $k == 4){
						$Data2[0]=$v."日均线";break;
					}else if($news == 6 && $k == 5){
						$Data2[0]=$v."日均线";break;
					}
				}
				}
				//add by zfy ended 2017/7/19
  		if ($news==0){
  			$chart=array($Data1,$Data2);
  		}else{ array_push($chart,$Data2);
  		}
  		$news++;
  	}

  	return $chart;
  }

	// 中国钢材价格指数
	function arr_flashab($arr, $leixing, $tag = "", $isenglish = 0)
	{
		global $stime, $etime, $max, $min;
		if (!is_array($arr)) {
			return;
		}
		//echo "<pre/>"; print_r($arr);
		$arr = $this->handleDataDiff($arr);
		//echo "1111";print_r($arr);exit;
		$arr_pidcc = array("0" => "钢材SHSPI", "1" => "长材SHSPI-L", "2" => "扁平材SHSPI-F", "3" => "优特钢SHSPI-S", "4" => "不锈钢SHSPI-SS", "5" => "无缝钢管SHSPI-SP", "6" => "带钢SHSPI-Strip", "7" => "焊管SHSPI-WP", "9" => "65进口铁矿石SHIOPI", "16" => "63进口铁矿石SHIOPI", "18" => "61进口铁矿石SHIOPI", "20" => "58进口铁矿石SHIOPI", "j1" => "普线材SHSPI-WR", "j2" => "螺纹钢SHSPI-RB", "j3" => "型钢SHSPI-SB",
			"j4" => "中厚板SHSPI-MP", "j5" => "热轧板卷SHSPI-HR", "j6" => "冷轧板卷SHSPI-CR", "j8" => "优线SHSPI-QW", "j9" => "结构钢SHSPI-CA", "m1" => "原料SHMPI", "k0" => "铁矿石SHOPI", "m3" => "二级冶金焦SHCKI-G2", "m34" => "焦炭成本SHCKI-COST", "m36" => "华东地区SHCKI-E", "m37" => "华北地区SHCKI-N", "m38" => "东北地区SHCKI-NE", "m39" => "中南地区SHCKI-CS", "m40" => "西南地区SHCKI-WS", "m41" => "西北地区SHCKI-WN", "m43" => "山西地区SHCKI-SX", "m42" => "河北地区SHCKI-HB", "m44" => "山东地区SHCKI-SD", "m45" => "焦炭出口SHCKI-QG1-E", "m4" => "废钢SHFSPI", "m5" => "铁合金SHFPI", "h0" => "焦煤综合SHJMPI", "m7" => "硅铁SHFPI-FESI", "m8" => "硅锰SHFPI-MNSI", "m9" => "高碳铬铁SHFPI-FECR",
			"b1" => "酸洗", "b2" => "热镀锌", "b3" => "热镀铝锌", "b4" => "电镀锌", "b5" => "彩涂板卷", "b6" => "无取向电工钢", "b7" => "取向电工钢", "b8" => "电镀锡",
			"xg11" => "角钢", "xg12" => "槽钢", "xg13" => "工字钢", "xg14" => "H型钢");
		$news = 0;
		if ($isenglish != 1) {
			if ($tag == 1) {
				$tag_nema = "均值";
			} else {
				$tag_nema = "均价";
			}
		} else {
			$tag_nema = "(AVG:";
		}


		$Data1 = array();
		foreach ($arr as $nn => $vv) {
			if ($news == 0) {
				$Data2[0] = $arr_pidcc[$nn];
			} else {
				$Data2[0] = $arr_pidcc[$nn];
			}

			$Data1[0] = ""; 
			$xi = 1;
			foreach ($vv as $ndate => $nprice) {
				if ($news == 0) {
					if ($leixing == '2') {
						$week = '周';
						//$week = iconv( "gb2312 ", "utf-8",'周');
						//echo $week;
						$Data1[$xi] = $ndate . $week;
					} else if ($leixing == '3') {
						$Data1[$xi] = date("y/m", strtotime($ndate));
					} else {
						$Data1[$xi] = date("y/m/d", strtotime($ndate));
					}
				}
				$Data2[$xi] = $nprice;
				if ($min > $Data2[$xi]) {
					$min = $Data2[$xi];
				}
				if ($max < $Data2[$xi]) {
					$max = $Data2[$xi];
				}
				$xi++;
			}
			// power由7改成6，甲级会员可以显示均价
			if ($_SESSION['mid'] == 1 || $_SESSION['maxpower'] >= 6) {
				if ($news == 0) {
					$Data2[0] = $arr_pidcc[$nn] . $tag_nema . round(array_sum($Data2) / (count($Data2) - 1)) . ($isenglish == 1 ? ')' : "");
				} else {
					$Data2[0] = $arr_pidcc[$nn] . $tag_nema . round(array_sum($Data2) / (count($Data2) - 1)) . ($isenglish == 1 ? ')' : "");
					//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
				}
			}
			if ($news == 0) {
				$chart = array($Data1, $Data2);
			} else {
				array_push($chart, $Data2);
			}
			$news++;
		}
		return $chart;
	}

  function handleDataDiff( $arr ) {
  	$returnArray = array();
  	$keyArray = array();
  	$keyItemArray = array();
  	$diffArray = array();
	foreach ( $arr as $key => $item ) {
		$keyArray[] = $key;
		foreach ( $item as $date => $value ) {
			$keyItemArray[$key][] = $date;
		}
	}
  	switch ( count($arr) ){
		case 1 :
			$returnArray = $arr;
			break;
		case 2 :
			$diffArray[0] = array_diff( $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]] );
			$diffArray[1] = array_diff( $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[0]] );
			$diffArray[0] = array_unique( array_merge( $diffArray[0], $diffArray[1] ) );
			break;

		case 3 :
			$diffArray[0] = array_diff( $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[2]] );
			$diffArray[1] = array_diff( $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[2]] );
			$diffArray[2] = array_diff( $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]] );
			$diffArray[0] = array_unique( array_merge( $diffArray[0], $diffArray[1], $diffArray[2] ) );
			break;

		case 4 :
			$diffArray[0] = array_diff( $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[3]] );
			$diffArray[1] = array_diff( $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[3]] );
			$diffArray[2] = array_diff( $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[3]] );
			$diffArray[3] = array_diff( $keyItemArray[$keyArray[3]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[2]] );
			$diffArray[0] = array_unique( array_merge( $diffArray[0], $diffArray[1], $diffArray[2], $diffArray[3] ) );
			break;

		case 5 :
			$diffArray[0] = array_diff( $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[3]], $keyItemArray[$keyArray[4]] );
			$diffArray[1] = array_diff( $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[3]], $keyItemArray[$keyArray[4]] );
			$diffArray[2] = array_diff( $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[3]], $keyItemArray[$keyArray[4]] );
			$diffArray[3] = array_diff( $keyItemArray[$keyArray[3]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[4]] );
			$diffArray[4] = array_diff( $keyItemArray[$keyArray[4]], $keyItemArray[$keyArray[0]], $keyItemArray[$keyArray[1]], $keyItemArray[$keyArray[2]], $keyItemArray[$keyArray[3]] );
			$diffArray[0] = array_unique( array_merge( $diffArray[0], $diffArray[1], $diffArray[2], $diffArray[3], $diffArray[4] ) );
			break;

		default :
	}
	//有差异的元素需要补数据
	if ( !empty($diffArray[0]) ){
		foreach ( $arr as $bukey => $buvalue ) {
			foreach ( $diffArray[0] as $dateData ){
				if ( !isset($buvalue[$dateData]) ) {
					$buvalue[$dateData] = "";
				}
			}
			ksort($buvalue);
			$returnArray[$bukey] = $buvalue;
		}
	}else{
		$returnArray = $arr;
	}

  	return $returnArray;
  }

  //原料
  function arr_flash_cc($arr,$leixing,$tag=0,$lang=""){
  	global $stime,$etime,$max,$min;
  	if (!is_array($arr)){
  		return;
  	}
  if($lang=="en"){
		  $arr_pidcc=array("0"=>"钢材SHSPI","1"=>"长材SHSPI-L","2"=>"扁平材SHSPI-F","3"=>"优特钢SHSPI-S","4"=>"不锈钢SHSPI-SS","5"=>"无缝钢管SHSPI-SP","6"=>"带钢SHSPI-Strip","7"=>"焊管SHSPI-WP","j1"=>"普线材SHSPI-WR","j2"=>"螺纹钢SHSPI-RB","j3"=>"型钢SHSPI-SB","k1"=>"国产铁矿石SHDOPI","k3"=>"进口铁矿石SHIOPI",
			  "j4"=>"中厚板SHSPI-MP","j5"=>"热轧板卷SHSPI-HR","j6"=>"冷轧板卷SHSPI-CR","j8"=>"优线SHSPI-QW","j9"=>"结构钢SHSPI-CA","m1"=>"原料SHMPI","k0"=>"铁矿石SHOPI","m3"=>"Coke SHCKI","m34" => "Coke Cost SHCKI-COST","m36" => "East China SHCKI-E","m37" => "North China SHCKI-N","m38" => "Northeast China SHCKI-NE","m39" => "Central South China SHCKI-CS","m40" => "Southwest China SHCKI-WS","m41" => "Northwest China SHCKI-WN","m43" => "Shanxi SHCKI-SX", "m42" => "Hebei SHCKI-HB", "m44" => "Shandong SHCKI-SD", "m45" => "Coke Export SHCKI-QG1-E","m4"=>"Scrap SHFSPI","m5"=>"Ferroalloy SHFPI","m51"=>"ferromolybdenum FEMO","h0"=>"焦煤综合SHJMPI","m7"=>"Ferrosilicon SHFPI-FESI","m8"=>"SiMn SHFPI-MNSI","m9"=>"高碳铬铁SHFPI-FECR",
			  "11"=>"Semis SHSPPI","12"=>"Slab SHSPPI-Slab","13"=>"Q235 Billet SHSPPI-Billet","14"=>"20MnSi Billet SHSPPI-20MnSi","21"=>"Q235 Billet(Tangshan)","22"=>"20MnSi Billet(Tangshan)","23"=>"Q235 Billet Cost(Tangshan)","m10"=>"East China SHFSPI-E","m11"=>"North China SHFSPI-N","m12"=>"Northeast China SHFSPI-NE","m13"=>"Central South China SHFSPI-CS","m14"=>"西部SHFSPI-W","m15"=>"SHNMPI-CU","m16"=>"Aluminium SHNMPI-AL","m17"=>"Zinc SHNMPI-ZN","m18"=>"Lead SHNMPI-PB",
			  "m19"=>"镍SHNMPI-NI","m20"=>"Tin SHNMPI-SN","m21"=>"Gold SHNMPI-AU","m22"=>"Silver SHNMPI-AG","m23"=>"锑锭SHNMPI-SB","m24"=>"海绵钛SHNMPI-TI","m25"=>"镁锭SHNMPI-MG","m26"=>"Nonferrous metal SHNMPI","m27" => "Cement SHCEMENTPI","m28" => "East China SHCEMENTPI-E","m29" => "North China SHCEMENTPI-N","m30" => "Northeast China SHCEMENTPI-NE","m31" => "Central South China SHCEMENTPI-CS","m32" => "Southwest China SHCEMENTPI-WS","m33" => "Northwest China SHCEMENTPI-WN","ts1" => "铁水成本");
	 }else{
		  $arr_pidcc=array("0"=>"钢材SHSPI","1"=>"长材SHSPI-L","2"=>"扁平材SHSPI-F","3"=>"优特钢SHSPI-S","4"=>"不锈钢SHSPI-SS","5"=>"无缝钢管SHSPI-SP","6"=>"带钢SHSPI-Strip","7"=>"焊管SHSPI-WP","j1"=>"普线材SHSPI-WR","j2"=>"螺纹钢SHSPI-RB","j3"=>"型钢SHSPI-SB","k1"=>"国产铁矿石SHDOPI","k3"=>"进口铁矿石SHIOPI",
			  "j4"=>"中厚板SHSPI-MP","j5"=>"热轧板卷SHSPI-HR","j6"=>"冷轧板卷SHSPI-CR","j8"=>"优线SHSPI-QW","j9"=>"结构钢SHSPI-CA","m1"=>"原料SHMPI","k0"=>"铁矿石SHOPI","m3"=>"二级冶金焦SHCKI-G2","m34" => "焦炭成本SHCKI-COST","m36" => "华东地区SHCKI-E","m37" => "华北地区SHCKI-N","m38" => "东北地区SHCKI-NE","m39" => "中南地区SHCKI-CS","m40" => "西南地区SHCKI-WS","m41" => "西北地区SHCKI-WN","m43" => "山西地区SHCKI-SX", "m42" => "河北地区SHCKI-HB", "m44" => "山东地区SHCKI-SD", "m45" => "焦炭出口SHCKI-QG1-E","m4"=>"废钢SHFSPI","m5"=>"铁合金SHFPI","m51"=>"钼铁SHFPI-FEMO","h0"=>"焦煤综合SHJMPI","m7"=>"硅铁SHFPI-FESI","m8"=>"硅锰SHFPI-MNSI","m9"=>"高碳铬铁SHFPI-FECR",
			  "11"=>"钢坯SHSPPI","12"=>"板坯SHSPPI-Slab","13"=>"普碳方坯SHSPPI-Billet","14"=>"20MnSi方坯SHSPPI-20MnSi","21"=>"普碳方坯（唐山）","22"=>"20MnSi方坯（唐山）","23"=>"普碳方坯成本（唐山，不含税）","s23"=>"普碳方坯成本（唐山，含税）","m10"=>"华东SHFSPI-E","m11"=>"华北SHFSPI-N","m12"=>"东北SHFSPI-NE","m13"=>"中南SHFSPI-CS","m14"=>"西部SHFSPI-W","m15"=>"铜SHNMPI-CU","m16"=>"铝SHNMPI-AL","m17"=>"锌SHNMPI-ZN","m18"=>"铅SHNMPI-PB",
			  "m19"=>"镍SHNMPI-NI","m20"=>"锡SHNMPI-SN","m21"=>"黄金SHNMPI-AU","m22"=>"白银SHNMPI-AG","m23"=>"锑锭SHNMPI-SB","m24"=>"海绵钛SHNMPI-TI","m25"=>"镁锭SHNMPI-MG","m26"=>"有色金属SHNMPI","m27" => "水泥SHCEMENTPI","m28" => "华东地区SHCEMENTPI-E","m29" => "华北地区SHCEMENTPI-N","m30" => "东北地区SHCEMENTPI-NE","m31" => "中南地区SHCEMENTPI-CS","m32" => "西南地区SHCEMENTPI-WS","m33" => "西北地区SHCEMENTPI-WN","ts1" => "铁水成本");
	  }

  if($lang=="en"){
	  if($tag==1){
		  $tag_name = "(avg:";
	  }else{
		  $tag_name = "(avg:";
	  }
  }else{
	  if($tag==1){
		  $tag_name = "均值";
	  }else{
		  $tag_name = "均价";
	  }
  }
  $datearr=array();
	foreach ($arr as $nn=>$vv){
		foreach ($vv as $ndate=>$nprice){
			$tmpdate=$ndate;
			if(!in_array($tmpdate,$datearr))
			{
				$datearr[]=$tmpdate;
			}
		}
	}
	sort($datearr);
	$Data1=array();
	foreach($datearr as $k=>$v)
	{
		$Data1[0]="";
		if($leixing=='2'){
				$week = '周';
				//$week = iconv( "gb2312 ", "utf-8",'周');
				//echo $week;
				$tmpdate=$v.$week;
				if($lang=="en")
				{
					$tmpdate=$v;
				}
			}else if($leixing=='3'){
				$tmpdate=date("y/m",strtotime($v));
			}else{
				$tmpdate=date("y/m/d",strtotime($v));
			}
		$Data1[]=$tmpdate;
	}
	$news=0;
	foreach ($arr as $nn=>$vv)
	{
		
		$Data2 = array();
		$Data2[0]=$arr_pidcc[$nn];
		foreach($datearr as $k=>$v)
	{
		$ndate=$v;
		
		if(isset($vv[$v]))
		{
			$Data2[]=$vv[$v];
		}
		else{
				$Data2[]="";
		}
	}
	// power由7改成6，甲级会员可以显示均价
			if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
				if( $news == 0 ){
					$Data2[0]=$arr_pidcc[$nn].$tag_name.round(array_sum($vv)/(count($vv)-1)).($lang=="en"?")":" ");
				}else{
					$Data2[0]=$arr_pidcc[$nn].$tag_name.round(array_sum($vv)/(count($vv)-1)).($lang=="en"?")":" ");
					//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
				}
			}
	       if ($news==0){
				$chart=array($Data1,$Data2);
			}else{ array_push($chart,$Data2);
			}
		$news++;
	
	}

return $chart;
  }
  function arr_flashgj($arr,$tag=""){
  	global $stime,$etime,$max,$min;
  	if (!is_array($arr)){
  		return;
  	}
  	$arr_pidcc=array("0"=>"钢材SHSPI","1"=>"长材SHSPI-L","2"=>"扁平材SHSPI-F","3"=>"优特钢SHSPI-S","4"=>"不锈钢SHSPI-SS","5"=>"无缝钢管SHSPI-SP","6"=>"带钢SHSPI-Strip","7"=>"焊管SHSPI-WP","j1"=>"普线材SHSPI-WR","j2"=>"螺纹钢SHSPI-RB","j3"=>"型钢SHSPI-SB",
  			"j4"=>"中厚板SHSPI-MP","j5"=>"热轧板卷SHSPI-HR","j6"=>"冷轧板卷SHSPI-CR","j8"=>"优线SHSPI-QW","j9"=>"结构钢SHSPI-CA","m1"=>"原料SHMPI","k0"=>"铁矿石SHOPI","m3"=>"二级冶金焦SHCKI-G2","m34" => "焦炭成本SHCKI-COST","m36" => "华东地区SHCKI-E","m37" => "华北地区SHCKI-N","m38" => "东北地区SHCKI-NE","m39" => "中南地区SHCKI-CS","m40" => "西南地区SHCKI-WS","m41" => "西北地区SHCKI-WN","m43" => "山西地区SHCKI-SX", "m42" => "河北地区SHCKI-HB", "m44" => "山东地区SHCKI-SD", "m45" => "焦炭出口SHCKI-QG1-E","m4"=>"废钢SHFSPI","m5"=>"铁合金SHFPI","h0"=>"焦煤综合SHJMPI");
  	$news=0;
  	if($tag==1){
  		$tag_name = "均值";
  	}else{
  		$tag_name = "均价";
  	}
  	foreach ($arr as $nn=>$vv){
  		$Data1[0]="";$xi=1;
  		if( $news == 0 ){
  			$Data2[0]=$arr_pidcc[$nn] ;
  		}else{
  			$Data2[0]=$arr_pidcc[$nn];
  			//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
  		}
  		foreach ($vv as $ndate=>$nprice){
  			if ($news==0){
  				$Data1[$xi]=date("y/m/d",strtotime($ndate));
  			}
  			$Data2[$xi]=$nprice;
  			if ($min>$Data2[$xi]){
  				$min=$Data2[$xi];
  			}
  			if ($max<$Data2[$xi]){
  				$max=$Data2[$xi];
  			}
  			$xi++;
  		}
  		//print_r($Data2);
  		//echo count($Data2)-1;

  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
  		if( $news == 0 ){
  			$Data2[0]=$arr_pidcc[$nn].$tag_name.round(array_sum($Data2)/(count($Data2)-1))." ";
  		}else{
  			$Data2[0]=$arr_pidcc[$nn].$tag_name.round(array_sum($Data2)/(count($Data2)-1))." ";
  			//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
  		   }
  		}
  		if ($news==0){
  			$chart=array($Data1,$Data2);
  		}else{ array_push($chart,$Data2);
  		}
  		$news++;
  	}
  	return $chart;
  }

	function creat_image($arr_pzprice, $stime, $etime, $title, $pingjun = "", $dian = array(), $is_dot_img = 0, $is_jx = 0, $isenglish = 0, $array_value = array(), $columns = 3)
	{

		$mflag = 1;
		$n = count($arr_pzprice) - 1;
		$sum_date = array_shift($arr_pzprice['0']);
		$numall = (int)(count($arr_pzprice['0']) - 1);
		$sum = (int)((count($arr_pzprice['0'])-1) / 5);
		if ($sum < 1) {
			$sum = 1;
		}
		$date_last = $arr_pzprice['0'];
		$date_lastone = $date_last[$numall];
		// echo "<pre>";print_r($arr_pzprice);
		if (is_array($arr_pzprice['1'])) {
			$lend_title_1 = array_shift($arr_pzprice['1']);
		}
		if (is_array($arr_pzprice['2'])) {
			$lend_title_2 = array_shift($arr_pzprice['2']);
		}
		if (is_array($arr_pzprice['3'])) {
			$lend_title_3 = array_shift($arr_pzprice['3']);
		}
		if (is_array($arr_pzprice['4'])) {
			$lend_title_4 = array_shift($arr_pzprice['4']);
		}
		if (is_array($arr_pzprice['5'])) {
			$lend_title_5 = array_shift($arr_pzprice['5']);
		}
		if (is_array($arr_pzprice['6'])) {
			$lend_title_6 = array_shift($arr_pzprice['6']);
		}
		if (is_array($arr_pzprice['7'])) {
			$lend_title_7 = array_shift($arr_pzprice['7']);
		}
		//add by zfy ended 2017/7/19
		//$title = "钢之家(中国)钢铁原料基准价格指数(SH_CMPI)走势图(".$stime."至".$etime.")";
		switch ($n) {
			case 1:
			case 2:
			case 3:
				$graph = new Graph(600, 370);
				break;
			case 4:
			case 5:
				$graph = new Graph(600, 390);
				break;
			//add by zfy started 2017/7/19
			case 6:
			case 7:
				$graph = new Graph(600, 410);
				break;
			//add by zfy ended 2017/7/19
		}
		$graph->SetScale("textlin");
		$graph->title->Set($title);//$_SESSION['bt_title'] 走势图标题
		$graph->title->SetMargin(10);
		$graph->title->SetFont(FF_SIMSUN, FS_NORMAL, 10.5);
		//print_r($arr_pzprice['0']);
		if (strstr($arr_pzprice['0']['1'], '周')) {
			$graph->xaxis->SetFont(FF_SIMSUN, FS_NORMAL, 8);
		} else {
			$graph->xaxis->SetFont(FF_ARIAL, FS_NORMAL, 8);
		}
		$graph->yaxis->SetFont(FF_ARIAL, FS_NORMAL, 8);
		$graph->yaxis->HideZeroLabel();
		$graph->yaxis->HideLine(false);
		$graph->yaxis->HideTicks(true, false);
		$graph->ygrid->Show();
		// $graph->xaxis->SetLabelFormatCallback($date_lastone);
		$graph->xgrid->SetLineStyle("solid");
		$graph->xgrid->SetColor('#E3E3E3');
		$graph->xaxis->SetTickLabels($date_last);//$_SESSION['data_date'] 所有日期
		$graph->xaxis->SetTextTickInterval($sum, 0);//$_SESSION['sum_date'] 所有日期除以5所得数
		$graph->xaxis->SetPosAbsDelta(5);

		$graph->ygrid->SetFill(false);
		$graph->yaxis->HideLine(false);
		//   $graph->yaxis->HideTicks(false,false);
		$graph->xaxis->HideLine(false);
		$graph->xaxis->HideTicks(false, false);
		$graph->SetBox(false);

		if ($isenglish != 1) {
			$graph->legend->SetColumns($columns);
			$graph->legend->SetColumns(4);
		} else {
			$graph->legend->SetColumns(2);
		}
		// echo "<pre>";print_r($graph);exit;
		switch ($n) {
			case 1:
				$graph->img->SetMargin(50, 50, 50, 70);
				if ($is_dot_img == 1) {
					$splot = new ScatterPlot($arr_pzprice['1']);
					$splot->mark->SetType($this->dot_type);
					$splot->mark->SetWidth($this->dot_size);
					$splot->mark->SetFillColor("#fb0404");
					$splot->mark->SetColor("#fb0404");
					$splot->SetLegend($lend_title_1);
					$p1 = $splot;
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(2);
				}
				$graph->Add($p1);
				if ($dian) {
					$pn = new LinePlot($dian);
					
					$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
					//$pn->SetWeight(2);
				}
				break;
			case 2:
				$graph->img->SetMargin(50, 50, 50, 70);
				if ($is_dot_img == 1) {
					if ($is_jx == 1) {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
						$p2->SetColor("#8a04fc");
						$p2->SetLegend($lend_title_2);
						$p2->SetWeight(2);
					} else {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;
					}
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(3);

					$p2 = new LinePlot($arr_pzprice['2']);
					$p2->SetColor("#8a04fc");
					$p2->SetLegend($lend_title_2);
					$p2->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);

				if ($dian) {
					$pn = new LinePlot($dian);
					
					$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
					$pn->SetWeight(2);

				}
				break;
			case 3:
				$imgmb = 70;
				if ($columns == "2") {
					$imgmb = 85;
				}
				$graph->img->SetMargin(50, 50, 50, $imgmb);
				if ($is_dot_img == 1) {
					if ($is_jx == 1) {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
						$p2->SetColor("#8a04fc");
						$p2->SetLegend($lend_title_2);
						$p2->SetWeight(2);
						$p3 = new LinePlot($arr_pzprice['3']);
						$p3->SetColor("#0309fe");
						$p3->SetLegend($lend_title_3);
						$p3->SetWeight(2);
					} else {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;
					}
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(3);
					$p2 = new LinePlot($arr_pzprice['2']);
					$p2->SetColor("#8a04fc");
					$p2->SetLegend($lend_title_2);
					$p2->SetWeight(2);
					$p3 = new LinePlot($arr_pzprice['3']);
					$p3->SetColor("#0309fe");
					$p3->SetLegend($lend_title_3);
					$p3->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
				if ($dian) {
					$pn = new LinePlot($dian);
					$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
					$pn->SetWeight(2);
				}
				break;
			case 4:
				$imgmb = 90;
				if ($columns == "2") {
					$imgmb = 85;
				}
				$graph->img->SetMargin(50, 50, 50, $imgmb);
				if ($is_dot_img == 1) {
					if ($is_jx == 1) {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
						$p2->SetColor("#8a04fc");
						$p2->SetLegend($lend_title_2);
						$p2->SetWeight(2);

						$p3 = new LinePlot($arr_pzprice['3']);
						$p3->SetColor("#0309fe");
						$p3->SetLegend($lend_title_3);
						$p3->SetWeight(2);

						$p4 = new LinePlot($arr_pzprice['4']);
						$p4->SetColor("#03fbf8");
						$p4->SetLegend($lend_title_4);
						$p4->SetWeight(2);
					} else {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;
					}
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(3);

					$p2 = new LinePlot($arr_pzprice['2']);
					$p2->SetColor("#8a04fc");
					$p2->SetLegend($lend_title_2);
					$p2->SetWeight(2);

					$p3 = new LinePlot($arr_pzprice['3']);
					$p3->SetColor("#0309fe");
					$p3->SetLegend($lend_title_3);
					$p3->SetWeight(2);

					$p4 = new LinePlot($arr_pzprice['4']);
					$p4->SetColor("#03fbf8");
					$p4->SetLegend($lend_title_4);
					$p4->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
				$graph->Add($p4);

				break;
			case 5:
				$imgmb = 90;
				if ($columns == "2") {
					$imgmb = 110;
				}
				$graph->img->SetMargin(50, 50, 50, $imgmb);
				if ($is_dot_img == 1) {
					if ($is_jx == 1) {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
						$p2->SetColor("#8a04fc");
						$p2->SetLegend($lend_title_2);
						$p2->SetWeight(2);

						$p3 = new LinePlot($arr_pzprice['3']);
						$p3->SetColor("#0309fe");
						$p3->SetLegend($lend_title_3);
						$p3->SetWeight(2);

						$p4 = new LinePlot($arr_pzprice['4']);
						$p4->SetColor("#03fbf8");
						$p4->SetLegend($lend_title_4);
						$p4->SetWeight(2);

						$p5 = new LinePlot($arr_pzprice['5']);
						$p5->SetColor("#03f09e");
						$p5->SetLegend($lend_title_5);
						$p5->SetWeight(2);
					} else {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;
					}
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(3);

					$p2 = new LinePlot($arr_pzprice['2']);
					$p2->SetColor("#8a04fc");
					$p2->SetLegend($lend_title_2);
					$p2->SetWeight(2);

					$p3 = new LinePlot($arr_pzprice['3']);
					$p3->SetColor("#0309fe");
					$p3->SetLegend($lend_title_3);
					$p3->SetWeight(2);

					$p4 = new LinePlot($arr_pzprice['4']);
					$p4->SetColor("#03fbf8");
					$p4->SetLegend($lend_title_4);
					$p4->SetWeight(2);

					$p5 = new LinePlot($arr_pzprice['5']);
					$p5->SetColor("#03f09e");
					$p5->SetLegend($lend_title_5);
					$p5->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
				$graph->Add($p4);
				$graph->Add($p5);
				break;
			//add by zfy started 2017/7/19
			case 6:
				$graph->img->SetMargin(50, 50, 50, 90);
				if ($is_dot_img == 1) {
					if ($is_jx == 1) {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
						$p2->SetColor("#8a04fc");
						$p2->SetLegend($lend_title_2);
						$p2->SetWeight(2);

						$p3 = new LinePlot($arr_pzprice['3']);
						$p3->SetColor("#0309fe");
						$p3->SetLegend($lend_title_3);
						$p3->SetWeight(2);

						$p4 = new LinePlot($arr_pzprice['4']);
						$p4->SetColor("#03fbf8");
						$p4->SetLegend($lend_title_4);
						$p4->SetWeight(2);

						$p5 = new LinePlot($arr_pzprice['5']);
						$p5->SetColor("#03f09e");
						$p5->SetLegend($lend_title_5);
						$p5->SetWeight(2);

						$p6 = new LinePlot($arr_pzprice['6']);
						$p6->SetColor("#B3EE3A");
						$p6->SetLegend($lend_title_6);
						$p6->SetWeight(2);
					} else {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;

						$splot6 = new ScatterPlot($arr_pzprice['6']);
						$splot6->mark->SetType($this->dot_type);
						$splot6->mark->SetWidth($this->dot_size);
						$splot6->mark->SetFillColor("#B3EE3A");
						$splot6->mark->SetColor("#B3EE3A");
						$splot6->SetLegend($lend_title_6);
						$p6 = $splot6;
					}
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(3);

					$p2 = new LinePlot($arr_pzprice['2']);
					$p2->SetColor("#8a04fc");
					$p2->SetLegend($lend_title_2);
					$p2->SetWeight(2);

					$p3 = new LinePlot($arr_pzprice['3']);
					$p3->SetColor("#0309fe");
					$p3->SetLegend($lend_title_3);
					$p3->SetWeight(2);

					$p4 = new LinePlot($arr_pzprice['4']);
					$p4->SetColor("#03fbf8");
					$p4->SetLegend($lend_title_4);
					$p4->SetWeight(2);

					$p5 = new LinePlot($arr_pzprice['5']);
					$p5->SetColor("#03f09e");
					$p5->SetLegend($lend_title_5);
					$p5->SetWeight(2);

					$p6 = new LinePlot($arr_pzprice['6']);
					$p6->SetColor("#B3EE3A");
					$p6->SetLegend($lend_title_6);
					$p6->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
				$graph->Add($p4);
				$graph->Add($p5);
				$graph->Add($p6);
				break;
			case 7:
				$graph->img->SetMargin(50, 50, 50, 100);
				if ($is_dot_img == 1) {
					if ($is_jx == 1) {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
						$p2->SetColor("#8a04fc");
						$p2->SetLegend($lend_title_2);
						$p2->SetWeight(2);

						$p3 = new LinePlot($arr_pzprice['3']);
						$p3->SetColor("#0309fe");
						$p3->SetLegend($lend_title_3);
						$p3->SetWeight(2);

						$p4 = new LinePlot($arr_pzprice['4']);
						$p4->SetColor("#03fbf8");
						$p4->SetLegend($lend_title_4);
						$p4->SetWeight(2);

						$p5 = new LinePlot($arr_pzprice['5']);
						$p5->SetColor("#03f09e");
						$p5->SetLegend($lend_title_5);
						$p5->SetWeight(2);

						$p6 = new LinePlot($arr_pzprice['6']);
						$p6->SetColor("#B3EE3A");
						$p6->SetLegend($lend_title_6);
						$p6->SetWeight(2);

						$p7 = new LinePlot($arr_pzprice['7']);
						$p7->SetColor("#68838B");
						$p7->SetLegend($lend_title_7);
						$p7->SetWeight(2);
					} else {
						$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;

						$splot6 = new ScatterPlot($arr_pzprice['6']);
						$splot6->mark->SetType($this->dot_type);
						$splot6->mark->SetWidth($this->dot_size);
						$splot6->mark->SetFillColor("#B3EE3A");
						$splot6->mark->SetColor("#B3EE3A");
						$splot6->SetLegend($lend_title_6);
						$p6 = $splot6;

						$splot7 = new ScatterPlot($arr_pzprice['7']);
						$splot7->mark->SetType($this->dot_type);
						$splot7->mark->SetWidth($this->dot_size);
						$splot7->mark->SetFillColor("#68838B");
						$splot7->mark->SetColor("#68838B");
						$splot7->SetLegend($lend_title_7);
						$p7 = $splot7;
					}
				} else {
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
					$p1->SetLegend($lend_title_1);
					$p1->SetWeight(3);

					$p2 = new LinePlot($arr_pzprice['2']);
					$p2->SetColor("#8a04fc");
					$p2->SetLegend($lend_title_2);
					$p2->SetWeight(2);

					$p3 = new LinePlot($arr_pzprice['3']);
					$p3->SetColor("#0309fe");
					$p3->SetLegend($lend_title_3);
					$p3->SetWeight(2);

					$p4 = new LinePlot($arr_pzprice['4']);
					$p4->SetColor("#03fbf8");
					$p4->SetLegend($lend_title_4);
					$p4->SetWeight(2);

					$p5 = new LinePlot($arr_pzprice['5']);
					$p5->SetColor("#03f09e");
					$p5->SetLegend($lend_title_5);
					$p5->SetWeight(2);

					$p6 = new LinePlot($arr_pzprice['6']);
					$p6->SetColor("#B3EE3A");
					$p6->SetLegend($lend_title_6);
					$p6->SetWeight(2);

					$p7 = new LinePlot($arr_pzprice['7']);
					$p7->SetColor("#68838B");
					$p7->SetLegend($lend_title_7);
					$p7->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
				$graph->Add($p4);
				$graph->Add($p5);
				$graph->Add($p6);
				$graph->Add($p7);
				break;
			//add by zfy ended 2017/7/19
		}
		//   echo "<pre>";print_r($graph);exit;
		$arr_color = array("#fb0404", "#8a04fc", "#0309fe", "#03fbf8", "#03f09e", "#B3EE3A", "#68838B");
		for ($i = 1; $i <= $n; $i++) {
			$p = "p" . $i;
			$$p->SetColor($arr_color[$i - 1]);
			// $$p->SetFillColor($arr_fillcolor[$i-1]);
		}
		//echo $pingjun;
		if ($isenglish != 1) {
			if ($pingjun == 'a') {
				$graph->SetBackgroundImage(APP_URL_WWW . "/images/gglg.png", BGIMG_COPY); //设置背景
			} else {
				//add by zfy started 2020/06/04 基准日
				if ($array_value['baseDaydate']) {
					$baseDaydate = date("Y.m.d", strtotime($array_value['baseDaydate']));
					$graph->subtitle->Set("　　　　　　　　　　　　基准日为" . $baseDaydate);
					$graph->subtitle->SetFont(FF_SIMSUN, FS_BOLD, 9.5);
					$graph->subtitle->SetColor('gray');
					$graph->subtitle->SetMargin("10.5");
					$graph->subtitle->SetAlign("left");
					$graph->SetBackgroundImage(APP_URL_WWW . "/images/gglg.png", BGIMG_COPY); //设置背景
				} else {
					//add by zfy ended 2020/06/04 基准日
					$graph->SetBackgroundImage(APP_URL_WWW . "/images/gglgy.png", BGIMG_COPY); //设置背景
				}
			}
		} else {
			if ($pingjun == 'a') {
				$graph->SetBackgroundImage(APP_URL_WWW . "/images/gglg01en.png", BGIMG_COPY); //设置背景
			} else {
				$graph->SetBackgroundImage(APP_URL_WWW . "/images/gglg01en1.png", BGIMG_COPY); //设置背景 //设置背景
			}
		}
		// echo "<pre>";print_r($graph);exit;
		$graph->SetBackgroundImageMix(15);
		$graph->SetMarginColor("white");
		$graph->SetFrame(false);//是否显示边框
		$graph->legend->SetFillColor('#ffffff');
		$graph->legend->SetFrameWeight(0);//图例外框
		$graph->legend->SetShadow(false);
		if ($isenglish != 1) {
			$graph->legend->SetFont(FF_SIMSUN, FS_NORMAL, 9);
		} else {
			$graph->legend->SetFont(FF_SIMSUN, FS_NORMAL, 9);
		}
		$graph->legend->Pos(0.5, 0.97, "center", "bottom");
		// echo "<pre>";print_r($graph);exit;
		ob_clean();
		$graph->Stroke();

	}

  //xiangbin add 20190220 start
 function creat_image_english($arr_pzprice,$stime,$etime,$title,$pingjun="",$dian="",$is_dot_img=0,$is_jx=0){

  	$mflag=1;
  	$n = count($arr_pzprice)-1;
  	$sum_date = array_shift($arr_pzprice['0']);
  	$numall =(int)(count($arr_pzprice['0'])-1);
  	$sum = (int)((count($arr_pzprice['0'])-1)/5);
  	if($sum<1)
  	{
  		$sum=1;
  	}
  	$date_last=$arr_pzprice['0'];
  	$date_lastone = $date_last[$numall];
  	$lend_title_1 =	is_array($arr_pzprice['1']) ? array_shift($arr_pzprice['1']):array();
  	$lend_title_2 = is_array($arr_pzprice['2']) ? array_shift($arr_pzprice['2']):array();
  	$lend_title_3 = is_array($arr_pzprice['3']) ? array_shift($arr_pzprice['3']):array();
  	$lend_title_4 = is_array($arr_pzprice['4']) ? array_shift($arr_pzprice['4']):array();
  	$lend_title_5 = is_array($arr_pzprice['5']) ? array_shift($arr_pzprice['5']):array();
	//add by zfy started 2017/7/19
	$lend_title_6 = is_array($arr_pzprice['6']) ? array_shift($arr_pzprice['6']):array();
	$lend_title_7 = is_array($arr_pzprice['7']) ? array_shift($arr_pzprice['7']):array();
	//add by zfy ended 2017/7/19
  	//$title = "钢之家(中国)钢铁原料基准价格指数(SH_CMPI)走势图(".$stime."至".$etime.")";
  	switch($n){
  		case 1:
  			$graph = new Graph(600,370);
  			break;
  		case 2:
  			$graph = new Graph(600,370);
  			break;
  		case 3:
  			$graph = new Graph(600,390);
  			break;
  		case 4:
  			$graph = new Graph(600,390);
  			break;
  		case 5:
  			$graph = new Graph(600,410);
  			break;
			//add by zfy started 2017/7/19
		case 6:
  			$graph = new Graph(600,410);
  			break;
		case 7:
  			$graph = new Graph(600,430);
  			break;
			//add by zfy ended 2017/7/19
  	}
  	$graph->SetScale("textlin");
  	$graph->title->Set($title);//$_SESSION['bt_title'] 走势图标题
  	$graph->title->SetMargin(10);
  	$graph->title->SetFont(FF_SIMSUN,FS_NORMAL,10.5);
  	//print_r($arr_pzprice['0']);
  	if(strstr($arr_pzprice['0']['1'],'周')){
  		$graph->xaxis->SetFont(FF_SIMSUN,FS_NORMAL,8);
  	}else{
  	$graph->xaxis->SetFont(FF_ARIAL,FS_NORMAL,8);
  	}
  	$graph->yaxis->SetFont(FF_ARIAL,FS_NORMAL,8);
  	$graph->yaxis->HideZeroLabel();
  	$graph->yaxis->HideLine(false);
  	$graph->yaxis->HideTicks(true,false);
  	$graph->ygrid->Show();
  	//$graph->xaxis->SetLabelFormatCallback($date_lastone);
  	$graph->xgrid->SetLineStyle("solid");
  	$graph->xgrid->SetColor('#E3E3E3');
  	$graph->xaxis->SetTickLabels($date_last);//$_SESSION['data_date'] 所有日期
  	$graph->xaxis->SetTextTickInterval($sum,0);//$_SESSION['sum_date'] 所有日期除以5所得数
  	$graph->xaxis->SetPosAbsDelta(5);
    $graph->legend->SetColumns(2);

  	switch($n){
  		case 1:
  			$graph->img->SetMargin(50,50,50,70);
  			if($is_dot_img == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(2);
				}
  			$graph->Add($p1);
  			if($dian){
  				if($is_dot_img == 1){
						$pn = new ScatterPlot($dian);
						$pn->mark->SetType($this->dot_type);
						$pn->mark->SetWidth($this->dot_size);
						$pn->mark->SetFillColor("#8a04fc");
						$pn->mark->SetColor("#8a04fc");
						$graph->Add($pn);
  				}else{
  					$pn = new LinePlot($dian);
		  			$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
		  			$pn->SetWeight(2);
  				}

  			}
  			break;
  		case 2:
  			$graph->img->SetMargin(50,50,50,70);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
	  				$p2->SetColor("#8a04fc");
	  				$p2->SetLegend($lend_title_2);
	  				$p2->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;
  				}
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
  				$p1->SetLegend($lend_title_1);
  				$p1->SetWeight(3);

  				$p2 = new LinePlot($arr_pzprice['2']);
  				$p2->SetColor("#8a04fc");
  				$p2->SetLegend($lend_title_2);
  				$p2->SetWeight(2);
				}
  			$graph->Add($p1);
  			$graph->Add($p2);

  			if($dian){
  				if($is_dot_img == 1){
  					$pn = new ScatterPlot($dian);
						$pn->mark->SetType($this->dot_type);
						$pn->mark->SetWidth($this->dot_size);
						$pn->mark->SetFillColor("#8a04fc");
						$pn->mark->SetColor("#8a04fc");
						$graph->Add($pn);
  				}else{
  					$pn = new LinePlot($dian);
		  			$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
		  			$pn->SetWeight(2);
  				}

  			}
  			break;
  		case 3:
  			$graph->img->SetMargin(50,50,50,90);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);
		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;
  				}
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);
	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);
	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
  			if($dian){
  				if($is_dot_img == 1){
  					$pn = new ScatterPlot($dian);
						$pn->mark->SetType($this->dot_type);
						$pn->mark->SetWidth($this->dot_size);
						$pn->mark->SetFillColor("#8a04fc");
						$pn->mark->SetColor("#8a04fc");
						$graph->Add($pn);
  				}else{
  					$pn = new LinePlot($dian);
		  			$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
		  			$pn->SetWeight(2);
  				}

  			}
  			break;
  		case 4:
  			$graph->img->SetMargin(50,50,50,90);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);

  			break;
  		case 5:
  			$graph->img->SetMargin(50,50,50,100);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);

		  			$p5 = new LinePlot($arr_pzprice['5']);
		  			$p5->SetColor("#03f09e");
		  			$p5->SetLegend($lend_title_5);
		  			$p5->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);

	  			$p5 = new LinePlot($arr_pzprice['5']);
	  			$p5->SetColor("#03f09e");
	  			$p5->SetLegend($lend_title_5);
	  			$p5->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);
  			$graph->Add($p5);
  			break;
		//add by zfy started 2017/7/19
		case 6:
  			$graph->img->SetMargin(50,50,50,100);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);

		  			$p5 = new LinePlot($arr_pzprice['5']);
		  			$p5->SetColor("#03f09e");
		  			$p5->SetLegend($lend_title_5);
		  			$p5->SetWeight(2);

		  			$p6 = new LinePlot($arr_pzprice['6']);
		  			$p6->SetColor("#B3EE3A");
		  			$p6->SetLegend($lend_title_6);
		  			$p6->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;

						$splot6 = new ScatterPlot($arr_pzprice['6']);
						$splot6->mark->SetType($this->dot_type);
						$splot6->mark->SetWidth($this->dot_size);
						$splot6->mark->SetFillColor("#B3EE3A");
						$splot6->mark->SetColor("#B3EE3A");
						$splot6->SetLegend($lend_title_6);
						$p6 = $splot6;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);

	  			$p5 = new LinePlot($arr_pzprice['5']);
	  			$p5->SetColor("#03f09e");
	  			$p5->SetLegend($lend_title_5);
	  			$p5->SetWeight(2);

	  			$p6 = new LinePlot($arr_pzprice['6']);
	  			$p6->SetColor("#B3EE3A");
	  			$p6->SetLegend($lend_title_6);
	  			$p6->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);
  			$graph->Add($p5);
  			$graph->Add($p6);
  			break;
		case 7:
  			$graph->img->SetMargin(50,50,50,120);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);

		  			$p5 = new LinePlot($arr_pzprice['5']);
		  			$p5->SetColor("#03f09e");
		  			$p5->SetLegend($lend_title_5);
		  			$p5->SetWeight(2);

		  			$p6 = new LinePlot($arr_pzprice['6']);
		  			$p6->SetColor("#B3EE3A");
		  			$p6->SetLegend($lend_title_6);
		  			$p6->SetWeight(2);

		  			$p7 = new LinePlot($arr_pzprice['7']);
		  			$p7->SetColor("#68838B");
		  			$p7->SetLegend($lend_title_7);
		  			$p7->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;

						$splot6 = new ScatterPlot($arr_pzprice['6']);
						$splot6->mark->SetType($this->dot_type);
						$splot6->mark->SetWidth($this->dot_size);
						$splot6->mark->SetFillColor("#B3EE3A");
						$splot6->mark->SetColor("#B3EE3A");
						$splot6->SetLegend($lend_title_6);
						$p6 = $splot6;

						$splot7 = new ScatterPlot($arr_pzprice['7']);
						$splot7->mark->SetType($this->dot_type);
						$splot7->mark->SetWidth($this->dot_size);
						$splot7->mark->SetFillColor("#68838B");
						$splot7->mark->SetColor("#68838B");
						$splot7->SetLegend($lend_title_7);
						$p7 = $splot7;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);

	  			$p5 = new LinePlot($arr_pzprice['5']);
	  			$p5->SetColor("#03f09e");
	  			$p5->SetLegend($lend_title_5);
	  			$p5->SetWeight(2);

	  			$p6 = new LinePlot($arr_pzprice['6']);
	  			$p6->SetColor("#B3EE3A");
	  			$p6->SetLegend($lend_title_6);
	  			$p6->SetWeight(2);

	  			$p7 = new LinePlot($arr_pzprice['7']);
	  			$p7->SetColor("#68838B");
	  			$p7->SetLegend($lend_title_7);
	  			$p7->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);
  			$graph->Add($p5);
  			$graph->Add($p6);
  			$graph->Add($p7);
  			break;
		//add by zfy ended 2017/7/19
  	}
	    if($pingjun=='a'){
  		$graph->SetBackgroundImage(APP_URL_WWW."/images/gglg.png",BGIMG_COPY); //设置背景
		}else{
			$graph->SetBackgroundImage(APP_URL_WWW."/images/gglg01en1.png",BGIMG_COPY); //设置背景 //设置背景
		}


  	$graph->SetBackgroundImageMix(15);
  	$graph->SetMarginColor("white");
  	$graph->SetFrame(false);//是否显示边框
  	$graph->legend->SetFillColor('#ffffff');
  	$graph->legend->SetFrameWeight(0);//图例外框
  	$graph->legend->SetShadow(false);
	$graph->legend->SetFont(FF_SIMSUN,FS_NORMAL,9);
  	$graph->legend->Pos(0.5,0.97,"center","bottom");
  	ob_clean();
  	$graph->Stroke();

  }

  //xiangbin add 20190220 end


  function creat_imagepj($arr_pzprice,$stime,$etime,$title,$is_dot_img=0,$is_jx=0){
  	$mflag=1;
  	$n = count($arr_pzprice)-1;
  	$sum_date = array_shift($arr_pzprice['0']);
  	$numall =(int)(count($arr_pzprice['0'])-1);
  	$sum = (int)((count($arr_pzprice['0'])-1)/5);
  	if($sum<1)
  	{
  		$sum=1;
  	}
  	$date_last=$arr_pzprice['0'];
  	$date_lastone = $date_last[$numall];
  	if (is_array($arr_pzprice['1'])) {
		$lend_title_1 = array_shift($arr_pzprice['1']);
	}
	if (is_array($arr_pzprice['2'])) {
		$lend_title_2 = array_shift($arr_pzprice['2']);
	}
	if (is_array($arr_pzprice['3'])) {
		$lend_title_3 = array_shift($arr_pzprice['3']);
	}
	if (is_array($arr_pzprice['4'])) {
		$lend_title_4 = array_shift($arr_pzprice['4']);
	}
	if (is_array($arr_pzprice['5'])) {
		$lend_title_5 = array_shift($arr_pzprice['5']);
	}
	if (is_array($arr_pzprice['6'])) {
		$lend_title_6 = array_shift($arr_pzprice['6']);
	}
	if (is_array($arr_pzprice['7'])) {
		$lend_title_7 = array_shift($arr_pzprice['7']);
	}
  	//$title = "钢之家(中国)钢铁原料基准价格指数(SH_CMPI)走势图(".$stime."至".$etime.")";
  	switch($n){
  		case 1:
  			$graph = new Graph(600,370);
  			break;
  		case 2:
  			$graph = new Graph(600,370);
  			break;
  		case 3:
  			$graph = new Graph(670,370);
  			break;
  		case 4:
  			$graph = new Graph(670,390);
  			break;
  		case 5:
  			$graph = new Graph(670,390);
  			break;
		//add by zfy started 2017/7/21
  		case 6:
  			$graph = new Graph(680,410);
  			break;
		case 7:
  			$graph = new Graph(680,430);
  			break;
		//add by zfy ended 2017/7/21
  	}
  	$graph->SetScale("textlin");
  	$graph->title->Set($title);//$_SESSION['bt_title'] 走势图标题
  	$graph->title->SetMargin(10);
  	$graph->title->SetFont(FF_SIMSUN,FS_NORMAL,11);
  	if(strstr($arr_pzprice['0']['1'],'周')){
  		$graph->xaxis->SetFont(FF_SIMSUN,FS_NORMAL,8);
  	}else{
  		$graph->xaxis->SetFont(FF_ARIAL,FS_NORMAL,8);
  	}
  	$graph->yaxis->SetFont(FF_ARIAL,FS_NORMAL,8);
  	$graph->yaxis->HideZeroLabel();
  	$graph->yaxis->HideLine(false);
  	$graph->yaxis->HideTicks(true,false);
  	$graph->ygrid->Show();
  	// $graph->xaxis->SetLabelFormatCallback($date_lastone);
  	$graph->xgrid->SetLineStyle("solid");
  	$graph->xgrid->SetColor('#E3E3E3');
  	$graph->xaxis->SetTickLabels($date_last);//$_SESSION['data_date'] 所有日期
  	$graph->xaxis->SetTextTickInterval($sum,0);//$_SESSION['sum_date'] 所有日期除以5所得数
  	$graph->xaxis->SetPosAbsDelta(5);
  	$graph->legend->SetColumns(3);
	$graph->ygrid->SetFill(false);
	$graph->yaxis->HideLine(false);
//   $graph->yaxis->HideTicks(false,false);
	$graph->xaxis->HideLine(false);
	$graph->xaxis->HideTicks(false,false);
	$graph->SetBox(false);
  	switch($n){
  		case 1:
  			$graph->img->SetMargin(50,50,50,70);
  			if($is_dot_img == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(2);
				}
  			$graph->Add($p1);
  			if($dian){
  				if($is_dot_img == 1){
						$pn = new ScatterPlot($dian);
						$pn->mark->SetType($this->dot_type);
						$pn->mark->SetWidth($this->dot_size);
						$pn->mark->SetFillColor("#8a04fc");
						$pn->mark->SetColor("#8a04fc");
						$graph->Add($pn);
  				}else{
  					$pn = new LinePlot($dian);
		  			$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
		  			$pn->SetWeight(2);
  				}

  			}
  			break;
  		case 2:
  			$graph->img->SetMargin(50,50,50,70);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
	  				$p2->SetColor("#8a04fc");
	  				$p2->SetLegend($lend_title_2);
	  				$p2->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;
  				}
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
  				$p1->SetLegend($lend_title_1);
  				$p1->SetWeight(3);

  				$p2 = new LinePlot($arr_pzprice['2']);
  				$p2->SetColor("#8a04fc");
  				$p2->SetLegend($lend_title_2);
  				$p2->SetWeight(2);
				}
  			$graph->Add($p1);
  			$graph->Add($p2);

  			if($dian){
  				if($is_dot_img == 1){
  					$pn = new ScatterPlot($dian);
						$pn->mark->SetType($this->dot_type);
						$pn->mark->SetWidth($this->dot_size);
						$pn->mark->SetFillColor("#8a04fc");
						$pn->mark->SetColor("#8a04fc");
						$graph->Add($pn);
  				}else{
  					$pn = new LinePlot($dian);
		  			$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
		  			$pn->SetWeight(2);
  				}

  			}
  			break;
  		case 3:
  			$graph->img->SetMargin(50,50,50,70);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);
		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;
  				}
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);
	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);
	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);
				}
				$graph->Add($p1);
				$graph->Add($p2);
				$graph->Add($p3);
  			if($dian){
  				if($is_dot_img == 1){
  					$pn = new ScatterPlot($dian);
						$pn->mark->SetType($this->dot_type);
						$pn->mark->SetWidth($this->dot_size);
						$pn->mark->SetFillColor("#8a04fc");
						$pn->mark->SetColor("#8a04fc");
						$graph->Add($pn);
  				}else{
  					$pn = new LinePlot($dian);
		  			$graph->Add($pn);
					$pn->value->Show();
					$pn->value->SetFormat('%d');
					$pn->value->SetColor("#8a04fc");
		  			$pn->SetWeight(2);
  				}

  			}
  			break;
  		case 4:
  			$graph->img->SetMargin(50,50,50,90);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);

  			break;
  		case 5:
  			$graph->img->SetMargin(50,50,50,90);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);

		  			$p5 = new LinePlot($arr_pzprice['5']);
		  			$p5->SetColor("#03f09e");
		  			$p5->SetLegend($lend_title_5);
		  			$p5->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);

	  			$p5 = new LinePlot($arr_pzprice['5']);
	  			$p5->SetColor("#03f09e");
	  			$p5->SetLegend($lend_title_5);
	  			$p5->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);
  			$graph->Add($p5);
  			break;
		//add by zfy started 2017/7/19
		case 6:
  			$graph->img->SetMargin(50,50,50,90);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);

		  			$p5 = new LinePlot($arr_pzprice['5']);
		  			$p5->SetColor("#03f09e");
		  			$p5->SetLegend($lend_title_5);
		  			$p5->SetWeight(2);

		  			$p6 = new LinePlot($arr_pzprice['6']);
		  			$p6->SetColor("#B3EE3A");
		  			$p6->SetLegend($lend_title_6);
		  			$p6->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;

						$splot6 = new ScatterPlot($arr_pzprice['6']);
						$splot6->mark->SetType($this->dot_type);
						$splot6->mark->SetWidth($this->dot_size);
						$splot6->mark->SetFillColor("#B3EE3A");
						$splot6->mark->SetColor("#B3EE3A");
						$splot6->SetLegend($lend_title_6);
						$p6 = $splot6;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);

	  			$p5 = new LinePlot($arr_pzprice['5']);
	  			$p5->SetColor("#03f09e");
	  			$p5->SetLegend($lend_title_5);
	  			$p5->SetWeight(2);

	  			$p6 = new LinePlot($arr_pzprice['6']);
	  			$p6->SetColor("#B3EE3A");
	  			$p6->SetLegend($lend_title_6);
	  			$p6->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);
  			$graph->Add($p5);
  			$graph->Add($p6);
  			break;
		case 7:
  			$graph->img->SetMargin(50,50,50,100);
  			if($is_dot_img == 1){
  				if($is_jx == 1){
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

  					$p2 = new LinePlot($arr_pzprice['2']);
		  			$p2->SetColor("#8a04fc");
		  			$p2->SetLegend($lend_title_2);
		  			$p2->SetWeight(2);

		  			$p3 = new LinePlot($arr_pzprice['3']);
		  			$p3->SetColor("#0309fe");
		  			$p3->SetLegend($lend_title_3);
		  			$p3->SetWeight(2);

		  			$p4 = new LinePlot($arr_pzprice['4']);
		  			$p4->SetColor("#03fbf8");
		  			$p4->SetLegend($lend_title_4);
		  			$p4->SetWeight(2);

		  			$p5 = new LinePlot($arr_pzprice['5']);
		  			$p5->SetColor("#03f09e");
		  			$p5->SetLegend($lend_title_5);
		  			$p5->SetWeight(2);

		  			$p6 = new LinePlot($arr_pzprice['6']);
		  			$p6->SetColor("#B3EE3A");
		  			$p6->SetLegend($lend_title_6);
		  			$p6->SetWeight(2);

		  			$p7 = new LinePlot($arr_pzprice['7']);
		  			$p7->SetColor("#68838B");
		  			$p7->SetLegend($lend_title_7);
		  			$p7->SetWeight(2);
  				}else{
  					$splot = new ScatterPlot($arr_pzprice['1']);
						$splot->mark->SetType($this->dot_type);
						$splot->mark->SetWidth($this->dot_size);
						$splot->mark->SetFillColor("#fb0404");
						$splot->mark->SetColor("#fb0404");
						$splot->SetLegend($lend_title_1);
						$p1 = $splot;

						$splot2 = new ScatterPlot($arr_pzprice['2']);
						$splot2->mark->SetType($this->dot_type);
						$splot2->mark->SetWidth($this->dot_size);
						$splot2->mark->SetFillColor("#8a04fc");
						$splot2->mark->SetColor("#8a04fc");
						$splot2->SetLegend($lend_title_2);
						$p2 = $splot2;

						$splot3 = new ScatterPlot($arr_pzprice['3']);
						$splot3->mark->SetType($this->dot_type);
						$splot3->mark->SetWidth($this->dot_size);
						$splot3->mark->SetFillColor("#0309fe");
						$splot3->mark->SetColor("#0309fe");
						$splot3->SetLegend($lend_title_3);
						$p3 = $splot3;

						$splot4 = new ScatterPlot($arr_pzprice['4']);
						$splot4->mark->SetType($this->dot_type);
						$splot4->mark->SetWidth($this->dot_size);
						$splot4->mark->SetFillColor("#03fbf8");
						$splot4->mark->SetColor("#03fbf8");
						$splot4->SetLegend($lend_title_4);
						$p4 = $splot4;

						$splot5 = new ScatterPlot($arr_pzprice['5']);
						$splot5->mark->SetType($this->dot_type);
						$splot5->mark->SetWidth($this->dot_size);
						$splot5->mark->SetFillColor("#03f09e");
						$splot5->mark->SetColor("#03f09e");
						$splot5->SetLegend($lend_title_5);
						$p5 = $splot5;

						$splot6 = new ScatterPlot($arr_pzprice['6']);
						$splot6->mark->SetType($this->dot_type);
						$splot6->mark->SetWidth($this->dot_size);
						$splot6->mark->SetFillColor("#B3EE3A");
						$splot6->mark->SetColor("#B3EE3A");
						$splot6->SetLegend($lend_title_6);
						$p6 = $splot6;

						$splot7 = new ScatterPlot($arr_pzprice['7']);
						$splot7->mark->SetType($this->dot_type);
						$splot7->mark->SetWidth($this->dot_size);
						$splot7->mark->SetFillColor("#68838B");
						$splot7->mark->SetColor("#68838B");
						$splot7->SetLegend($lend_title_7);
						$p7 = $splot7;
  				}
  			}else{
  				$p1 = new LinePlot($arr_pzprice['1']);
	  			$p1->SetColor("#fb0404");
	  			$p1->SetLegend($lend_title_1);
	  			$p1->SetWeight(3);

	  			$p2 = new LinePlot($arr_pzprice['2']);
	  			$p2->SetColor("#8a04fc");
	  			$p2->SetLegend($lend_title_2);
	  			$p2->SetWeight(2);

	  			$p3 = new LinePlot($arr_pzprice['3']);
	  			$p3->SetColor("#0309fe");
	  			$p3->SetLegend($lend_title_3);
	  			$p3->SetWeight(2);

	  			$p4 = new LinePlot($arr_pzprice['4']);
	  			$p4->SetColor("#03fbf8");
	  			$p4->SetLegend($lend_title_4);
	  			$p4->SetWeight(2);

	  			$p5 = new LinePlot($arr_pzprice['5']);
	  			$p5->SetColor("#03f09e");
	  			$p5->SetLegend($lend_title_5);
	  			$p5->SetWeight(2);

	  			$p6 = new LinePlot($arr_pzprice['6']);
	  			$p6->SetColor("#B3EE3A");
	  			$p6->SetLegend($lend_title_6);
	  			$p6->SetWeight(2);

	  			$p7 = new LinePlot($arr_pzprice['7']);
	  			$p7->SetColor("#68838B");
	  			$p7->SetLegend($lend_title_7);
	  			$p7->SetWeight(2);
  			}
  			$graph->Add($p1);
  			$graph->Add($p2);
  			$graph->Add($p3);
  			$graph->Add($p4);
  			$graph->Add($p5);
  			$graph->Add($p6);
  			$graph->Add($p7);
  			break;
		//add by zfy ended 2017/7/19
  	}
	$arr_color = array("#fb0404","#8a04fc","#0309fe","#03fbf8","#03f09e","#B3EE3A","#68838B");
	for($i=1;$i<=$n;$i++)
	{
		$p="p".$i;
		$$p->SetColor($arr_color[$i-1]);
		// $$p->SetFillColor($arr_fillcolor[$i-1]);
	}
  	/*switch($n){
  		case 1:
  			$graph->img->SetMargin(50,50,50,70);
  			if($is_dot_img == 1){
					$splot = new ScatterPlot($arr_pzprice['1']);
					$splot->mark->SetType($this->dot_type);
					$splot->mark->SetWidth($this->dot_size);
					$splot->mark->SetFillColor("#fb0404");
					$splot->mark->SetColor("#fb0404");
					$splot->SetLegend($lend_title_1);
					$p1 = $splot;
				}else{
					$p1 = new LinePlot($arr_pzprice['1']);
					$p1->SetColor("#fb0404");
  				$p1->SetLegend($lend_title_1);
  				$p1->SetWeight(3);
				}
  			$graph->Add($p1);

  			break;
  		case 2:
  			$graph->img->SetMargin(50,50,50,70);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(3);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			break;
  		case 3:
  			$graph->img->SetMargin(50,100,50,70);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(3);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			break;
  		case 4:
  			$graph->img->SetMargin(50,100,50,90);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(3);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			$p4 = new LinePlot($arr_pzprice['4']);
  			$graph->Add($p4);
  			$p4->SetColor("#03fbf8");
  			$p4->SetLegend($lend_title_4);
  			$p4->SetWeight(2);
  			break;
  		case 5:
  			$graph->img->SetMargin(50,100,50,90);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(3);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			$p4 = new LinePlot($arr_pzprice['4']);
  			$graph->Add($p4);
  			$p4->SetColor("#03fbf8");
  			$p4->SetLegend($lend_title_4);
  			$p4->SetWeight(2);
  			$p5 = new LinePlot($arr_pzprice['5']);
  			$graph->Add($p5);
  			$p5->SetColor("#03f09e");
  			$p5->SetLegend($lend_title_5);
  			$p5->SetWeight(2);
  			break;
			//add by zfy started 2017/7/21
		case 6:
  			$graph->img->SetMargin(50,100,50,90);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(3);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			$p4 = new LinePlot($arr_pzprice['4']);
  			$graph->Add($p4);
  			$p4->SetColor("#03fbf8");
  			$p4->SetLegend($lend_title_4);
  			$p4->SetWeight(2);
  			$p5 = new LinePlot($arr_pzprice['5']);
  			$graph->Add($p5);
  			$p5->SetColor("#03f09e");
  			$p5->SetLegend($lend_title_5);
  			$p5->SetWeight(2);
			$p6 = new LinePlot($arr_pzprice['6']);
  			$graph->Add($p6);
  			$p6->SetColor("#B3EE3A");
  			$p6->SetLegend($lend_title_6);
  			$p6->SetWeight(2);
  			break;
		case 7:
  			$graph->img->SetMargin(50,100,50,90);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(3);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			$p4 = new LinePlot($arr_pzprice['4']);
  			$graph->Add($p4);
  			$p4->SetColor("#03fbf8");
  			$p4->SetLegend($lend_title_4);
  			$p4->SetWeight(2);
  			$p5 = new LinePlot($arr_pzprice['5']);
  			$graph->Add($p5);
  			$p5->SetColor("#03f09e");
  			$p5->SetLegend($lend_title_5);
  			$p5->SetWeight(2);
			$p6 = new LinePlot($arr_pzprice['6']);
  			$graph->Add($p6);
  			$p6->SetColor("#B3EE3A");
  			$p6->SetLegend($lend_title_6);
  			$p6->SetWeight(2);
			$p7 = new LinePlot($arr_pzprice['7']);
  			$graph->Add($p7);
  			$p7->SetColor("#68838B");
  			$p7->SetLegend($lend_title_7);
  			$p7->SetWeight(2);
  			break;
			//add by zfy ended 2017/7/21
  	}*/
  	$graph->SetBackgroundImage(APP_URL_WWW."/images/gglg.png",BGIMG_COPY); //设置背景
  	$graph->SetBackgroundImageMix(15);
  	$graph->SetMarginColor("white");
  	$graph->SetFrame(false);//是否显示边框
  	$graph->legend->SetFillColor('#ffffff');
  	$graph->legend->SetFrameWeight(0);//图例外框
  	$graph->legend->SetShadow(false);
  	$graph->legend->SetFont(FF_SIMSUN,FS_NORMAL,9);
  	$graph->legend->Pos(0.5,0.97,"center","bottom");
  	ob_clean();
  	$graph->Stroke();

  }

  function arr_city($flag){
     $csql="select cityid,cityname,class,weight from city where cityid>6";
	 //================== //得到数组
	 $rs=$this->connshpi->Execute($csql);
	 while (!$rs->EOF){
		if ($flag==1){
		  $arr_city[$rs->fields("cityid")]=$rs->fields("cityname");
		}else{
		  $arr_city[substr($rs->fields("cityid"),-2)]=$rs->fields("weight");
		}
	    $rs->MoveNext();
	 }
	 //==================
     return $arr_city;
  }
  /*
  ; 某日某一品种规格平均价格  avg(品种价格)
  ; 价格ID编码以6位数字进行编制，城市（2位）品种（2位）材质（1位）规格（1位）
  ; productsweight 权重表 Rj /需要订算价格指数的数据（品种规格材质）
  ; 平均价+加权价格
  */
  function ptaj($ndate="",$arr_pzgg="",$arr_bap=""){
     if ($ndate==""){$ndate=date("Y-m-d");}
	 $dsql="and C.mconmanagedate > '".$ndate." 00:00' and C.mconmanagedate < '".$ndate." 23:59'";
	 if ($isbxg==0) { $citysql=" and (shpi_city.isflag=0 or shpi_city.isflag=1)";}
	 if ($isbxg==1) { $citysql=" and (shpi_city.isflag=0 or shpi_city.isflag=2)";}
	 foreach ($arr_pzgg as $keys=>$values){
	     if (($keys=="9112") or ($keys=="4824") or ($keys=="4825")){$isbxg=1;}else{$isbxg=0;}
		 if ($isbxg==0) { $citysql=" and (shpi_city.isflag=0 or shpi_city.isflag=1)";$weight="cweight";}
	 	 if ($isbxg==1) { $citysql=" and (shpi_city.isflag=0 or shpi_city.isflag=2)";$weight="bxgweight";}
         $sql="select avg(price) avgprice,sum( price * ".$weight." ) / sum( ".$weight." ) AS cwavgprice,sum(".$weight.") as twe  
		 from marketconditions as C left join marketrecord as M on C.marketrecordid=M.id 
		 LEFT JOIN shpi_city ON RIGHT( M.cityid, 2 ) = shpi_city.cid
		 where  C.topicture like '__".$keys."' ".$dsql.$citysql;
		 $rs=$this->connshpi->Execute($sql);
		 if (!$rs->EOF){
		   if ($rs->fields("avgprice")>0){
		     if ($rs->fields("twe")>60){
				 $arr_avgpzgg["avg"][$keys]=round($rs->fields("avgprice"),2);  //平均价
				 $arr_avgpzgg["cwavg"][$keys]=round($rs->fields("cwavgprice"),2);  //加权价
				 $arr_avgpzgg["shavg"][$keys]=round(($arr_avgpzgg["avg"][$keys]*100)/$arr_bap["avg"][$keys],2);
				 $arr_avgpzgg["shcwavg"][$keys]=round(($arr_avgpzgg["cwavg"][$keys]*100)/$arr_bap["cwavg"][$keys],2);
				 $insertsql="insert into shpi_pzggp (bbid,svm_vid,aveprice,apindex,weiprice,wpindex,dateday,isbase) 
				 values ('".$values."','".$keys."','".$arr_avgpzgg["avg"][$keys]."','".$arr_avgpzgg["shavg"][$keys]."','".$arr_avgpzgg["cwavg"][$keys]."','".$arr_avgpzgg["shcwavg"][$keys]."','".$ndate."',0)";
				 //echo $insertsql."<br>";
				 $this->connshpi->Execute($insertsql);
			  }
		   }
		 }
	 }
	 return $arr_avgpzgg;
  }


  //英文版function
  function creat_image_en($arr_pzprice,$stime,$etime,$title,$pingjun="",$pztype=""){

  	$mflag=1;
  	$n = count($arr_pzprice)-1;

  	$sum_date = array_shift($arr_pzprice['0']);
  	$numall =(int)(count($arr_pzprice['0'])-1);
  	$sum = (int)((count($arr_pzprice['0'])-1)/5);
  	if($sum<1)
  	{
  		$sum=1;
  	}
  	$date_last=$arr_pzprice['0'];
  	$date_lastone = $date_last[$numall];
  	$lend_title_1 = array_shift($arr_pzprice['1']);
  	$lend_title_2 = array_shift($arr_pzprice['2']);
  	$lend_title_3 = array_shift($arr_pzprice['3']);
  	$lend_title_4 = array_shift($arr_pzprice['4']);
  	$lend_title_5 = array_shift($arr_pzprice['5']);
  	//$title = "钢之家(中国)钢铁原料基准价格指数(SH_CMPI)走势图(".$stime."至".$etime.")";
  	switch($n){
  		case 1:
  			$graph = new Graph(600,370);
  			break;
  		case 2:
  			$graph = new Graph(600,370);
			if($pztype=='yl')$graph = new Graph(600,390);
  			break;
  		case 3:
  			$graph = new Graph(600,390);
			if($pztype=='yl')$graph = new Graph(600,410);
  			break;
  		case 4:
  			$graph = new Graph(600,390);
			if($pztype=='yl')$graph = new Graph(600,430);
  			break;
  		case 5:
  			$graph = new Graph(600,410);
			if($pztype=='yl')$graph = new Graph(600,450);
  			break;
  	}
  	$graph->SetScale("textlin");
  	$graph->title->Set($title);//$_SESSION['bt_title'] 走势图标题
  	$graph->title->SetMargin(10);
  	$graph->title->SetFont(FF_ARIAL,FS_NORMAL,10);
  	$graph->xaxis->SetFont(FF_ARIAL,FS_NORMAL,9);
  	$graph->yaxis->SetFont(FF_ARIAL,FS_NORMAL,9);
  	$graph->yaxis->HideZeroLabel();
  	$graph->yaxis->HideLine(false);
  	$graph->yaxis->HideTicks(true,false);
  	$graph->ygrid->Show();
  	//$graph->xaxis->SetLabelFormatCallback($date_lastone);
  	$graph->xgrid->SetLineStyle("solid");
  	$graph->xgrid->SetColor('#E3E3E3');
  	$graph->xaxis->SetTickLabels($date_last);//$_SESSION['data_date'] 所有日期
  	$graph->xaxis->SetTextTickInterval($sum,0);//$_SESSION['sum_date'] 所有日期除以5所得数
  	$graph->xaxis->SetPosAbsDelta(5);
  	$graph->legend->SetColumns(2);
	if($pztype=='yl')$graph->legend->SetColumns(1);
  	switch($n){
  		case 1:
  			$graph->img->SetMargin(50,50,50,70);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(2);
  			break;
  		case 2:
  			$graph->img->SetMargin(50,50,50,70);
			if($pztype=='yl')$graph->img->SetMargin(50,50,50,90);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(2);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			break;
  		case 3:
  			$graph->img->SetMargin(50,50,50,90);
			if($pztype=='yl')$graph->img->SetMargin(50,50,50,110);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(2);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			break;
  		case 4:
  			$graph->img->SetMargin(50,50,50,90);
			if($pztype=='yl')$graph->img->SetMargin(50,50,50,130);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(2);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			$p4 = new LinePlot($arr_pzprice['4']);
  			$graph->Add($p4);
  			$p4->SetColor("#03fbf8");
  			$p4->SetLegend($lend_title_4);
  			$p4->SetWeight(2);
  			break;
  		case 5:
  			$graph->img->SetMargin(50,50,50,110);
			if($pztype=='yl')$graph->img->SetMargin(50,50,50,150);
  			$p1 = new LinePlot($arr_pzprice['1']);
  			$graph->Add($p1);
  			$p1->SetColor("#fb0404");
  			$p1->SetLegend($lend_title_1);
  			$p1->SetWeight(2);
  			$p2 = new LinePlot($arr_pzprice['2']);
  			$graph->Add($p2);
  			$p2->SetColor("#8a04fc");
  			$p2->SetLegend($lend_title_2);
  			$p2->SetWeight(2);
  			$p3 = new LinePlot($arr_pzprice['3']);
  			$graph->Add($p3);
  			$p3->SetColor("#0309fe");
  			$p3->SetLegend($lend_title_3);
  			$p3->SetWeight(2);
  			$p4 = new LinePlot($arr_pzprice['4']);
  			$graph->Add($p4);
  			$p4->SetColor("#03fbf8");
  			$p4->SetLegend($lend_title_4);
  			$p4->SetWeight(2);
  			$p5 = new LinePlot($arr_pzprice['5']);
  			$graph->Add($p5);
  			$p5->SetColor("#03f09e");
  			$p5->SetLegend($lend_title_5);
  			$p5->SetWeight(2);
  			break;
  	}
  	if($pingjun=='a'){
  		$graph->SetBackgroundImage(APP_URL_WWW."/images/gglg01en1.png",BGIMG_COPY); //设置背景
  	}else{
  		$graph->SetBackgroundImage(APP_URL_WWW."/images/gglg.png",BGIMG_COPY); //设置背景 //设置背景
  	}

  	$graph->SetBackgroundImageMix(15);
  	$graph->SetMarginColor("white");
  	$graph->SetFrame(false);//是否显示边框
  	$graph->legend->SetFillColor('#ffffff');
  	$graph->legend->SetFrameWeight(0);//图例外框
  	$graph->legend->SetShadow(false);
  	$graph->legend->SetFont(FF_ARIAL,FS_NORMAL,8.5);
  	$graph->legend->Pos(0.5,0.97,"center","bottom");
  	ob_clean();
  	$graph->Stroke();

  }

  /*
     ;基准日
  */
  function basic_avg($ndate,$arr_pzgg,$isbxg=0){
     if ($ndate==""){$ndate=date("Y-m-d");}
	 $dsql="and C.mconmanagedate > '".$ndate." 00:00' and C.mconmanagedate < '".$ndate." 23:59'";
     foreach ($arr_pzgg as $keys=>$values){
	     if (($keys=="9112") or ($keys=="4824") or ($keys=="4825")){$isbxg=1;}else{$isbxg=0;}
		 if ($isbxg==0) { $citysql=" and (shpi_city.isflag=0 or shpi_city.isflag=1)";$weight="cweight";}
	 	 if ($isbxg==1) { $citysql=" and (shpi_city.isflag=0 or shpi_city.isflag=2)";$weight="bxgweight";}
	     $sql="select avg(price) avgprice,sum( price * ".$weight." ) / sum( ".$weight." ) AS cwavgprice,sum(".$weight.") as twe   
		 from marketconditions as C left join marketrecord as M on C.marketrecordid=M.id 
		 LEFT JOIN shpi_city ON RIGHT( M.cityid,2 ) = shpi_city.cid
		 where  C.topicture like '__".$keys."' ".$dsql.$citysql;
		 //echo $sql."<br>";
		 $rs=$this->connshpi->Execute($sql);
		 if (!$rs->EOF){
		   if ($rs->fields("avgprice")>0){
		    if ($rs->fields("twe")>10){
				 $arr_avgpzgg["avg"][$keys]=round($rs->fields("avgprice"),2);  //平均价
				 $arr_avgpzgg["cwavg"][$keys]=round($rs->fields("cwavgprice"),2);  //加权价
				 $insertsql="insert into shpi_pzggp (bbid,svm_vid,aveprice,apindex,weiprice,wpindex,dateday,isbase) 
				 values ('".$values."','".$keys."','".$arr_avgpzgg["avg"][$keys]."',100,'".$arr_avgpzgg["cwavg"][$keys]."','100','".$ndate."',1)";
				 //echo $insertsql."<br>";
				 $this->connshpi->Execute($insertsql);
			 }
		   }else{
		     //echo $sql."<br>";
		   }
		 }
	 }
	 return true;
  }
  /*
     ;基准日对应的平均价和加权价
  */
  function basic_day(){
      $sql="select * from shpi_pzggp where isbase=1 ";
	  $rs=$this->connshpi->Execute($sql);
	  while (!$rs->EOF){
			$keysa=$rs->fields('svm_vid');
			$arr_avgb["avg"][$keysa]=$rs->fields('aveprice');
			$arr_avgb["cwavg"][$keysa]=$rs->fields('weiprice');
			$rs->MoveNext();
	  }
	  return $arr_avgb;
  }
   /*
  ; 某日某一品种规格加权价格  avg(品种价格)*加权
  ; 价格ID编码以6位数字进行编制，城市（2位）品种（2位）材质（1位）规格（1位）
  ; productsweight 权重表 Rj /需要订算价格指数的数据（品种规格材质）
  */
  function ptsj($arr_pzgg,$arr_city,$ndate=""){
      if ($ndate==""){$ndate=date("Y-m-d");}
	 $dsql="and C.mconmanagedate > '".$ndate." 00:00' and C.mconmanagedate < '".$ndate." 23:59'";
	 foreach ($arr_pzgg as $keys=>$values){
	        $SumVPjti=0;$SumUiRi=0;
		    $sql="select price,topicture from marketconditions as C left join marketrecord as M on C.marketrecordid=M.id where C.topicture like '__".$values."' ".$dsql;
			 $rs=$this->connshpi->Execute($sql);
			 while (!$rs->EOF){
			    if ($rs->fields('price')>0){
					$tmp=$rs->fields('price')*$arr_city[substr($rs->fields('topicture'),0,2)];
					$SumVPjti+=$tmp;
					$SumUiRi+=$arr_city[substr($rs->fields('topicture'),0,2)];
				}
				$rs->MoveNext();
			 }
			 if (($SumVPjti<>0)&&($SumUiRi<>0)){
			 $SHVPtSj[$keys]=round($SumVPjti/$SumUiRi,2);
			 }
	 }
	 return $SHVPtSj;
  }

  function bvar_mjshpi($array_value){
	  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3','4'));
	  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
  	foreach ($array_value["pid"] as $nkeys=>$nvalues)
  	{
  		switch ($array_value["flag"])
  		{
			//update by zfy started 2017/7/20
  			case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
  			case 2:
				$fields_name="weiprice";
				$namev="综合价格走势图";
				$fields_name_5="weiprice_avg_5";
				$fields_name_10="weiprice_avg_10";
				$fields_name_20="weiprice_avg_20";
				$fields_name_40="weiprice_avg_40";
				$fields_name_60="weiprice_avg_60";
				$fields_name_200="weiprice_avg_200";
				break;
  			case 3: $fields_name="apindex";$namev="价格指数走势图";break;
  			case 4:
				$fields_name="weiindex";
				$namev="综合指数走势图";
				$fields_name_5="weiindex_avg_5";
				$fields_name_10="weiindex_avg_10";
				$fields_name_20="weiindex_avg_20";
				$fields_name_40="weiindex_avg_40";
				$fields_name_60="weiindex_avg_60";
				$fields_name_200="weiindex_avg_200";
				break;
				//update by zfy ended 2017/7/20
  		}
		//add by zfy started 2020/06/19 基准日
		$sqldate = "select *  from shpi_coal where vid='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
		$basedays = $this->connshpi->getrow($sqldate);
		//add by zfy ended 2020/06/19 基准日
  		$sql="select * from shpi_coal where vid='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
  		// echo $sql;
  		$rs=$this->connshpi->Execute($sql);$i=1;
  		while (!$rs->EOF)
  		{
			//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
			if ($calcBaseIndex==1){
				$dataday=$rs->fields("dateday");
				// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
				$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
				$array_price["h".$nvalues][$dataday]=$price;
			}else {
				$dataday=$rs->fields("dateday");
				$price=$rs->fields($fields_name);
				//add by zfy started 2017/7/18
				$price_5 = $rs->fields($fields_name_5);
				$price_10 = $rs->fields($fields_name_10);
				$price_20 = $rs->fields($fields_name_20);
				$price_40 = $rs->fields($fields_name_40);
				$price_60 = $rs->fields($fields_name_60);
				$price_200 = $rs->fields($fields_name_200);
				//add by zfy ended 2017/7/18
				$array_price["h".$nvalues][$dataday]=$price;
			}
			//add by zfy ended 2020/06/04 基准日

			/*//add by zfy started 2017/7/18
				$i = 1;
				if($leixing_sum == 1&&in_array('1',$array_value['leixing'])){
					if(in_array('5',$array_value["avgprice"])){
						$array_price[$i][$dataday]=$price_5;
						$i++;
					}
					if(in_array('10',$array_value["avgprice"])){
						$array_price[$i][$dataday]=$price_10;
						$i++;
					}
					if(in_array('20',$array_value["avgprice"])){
						$array_price[$i][$dataday]=$price_20;
						$i++;
					}
					if(in_array('40',$array_value["avgprice"])){
						$array_price[$i][$dataday]=$price_40;
						$i++;
					}
					if(in_array('60',$array_value["avgprice"])){
						$array_price[$i][$dataday]=$price_60;
						$i++;
					}
				}
				//add by zfy ended 2017/7/18
				*/
  			$rs->MoveNext();
  		}
  	}

  	return $array_price;
  }


  /*
煤焦
*/
function arr_mj_pzp($pzggcz,$flag,$stime,$etime)
{
	global $min,$max,$tabledate,$tstime,$tetime;
	//data1 日期
	$min=999999;$max=-1;
	$namev="平均指数走势图";

	$var_mj=array('1'=>'国产炼焦煤指数','2'=>'进口炼焦煤指数','0'=>'炼焦煤综合指数');
	foreach($pzggcz as $news=>$values)
	{
		if ($news==0)
		{
			$Data1[0]="";
		}
		$Data2[0]=$var_mj[$values];
		$sql="select * from shpi_coal where vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
		$rs=$this->connshpi->Execute($sql);
		$i=1;
		while(!$rs->EOF)
		{
			if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("dateday")));}
			$xdate[]=$rs->fields("dateday");
			$tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);
			$Data2[$i]=ceil($rs->fields('weiindex'));
			$Dataaa[$i]=ceil($rs->fields('weiindex'));
			//$Data_usb[$i]=ceil($rs->fields('weipriceusb'));//美元
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		}
	$Data2[0]=$var_mj[$values];
	if($values==2)
	{
	//$usb_jj=round(array_sum($Data_usb)/count($Data_usb),1)."美元";
	//$Data2[0]=$var_tks[$values]."(期间均价:".$usb_jj.")";
	}
	unset($Dataaa);
	if (is_array($xdate))
	{
		$tstime=date("Y-m-d",strtotime($xdate[0]));
		$tetime=date("Y-m-d",strtotime(end($xdate)));
	}
	if ($news==0)
	{
		$chart=array($Data1,$Data2);
	}
	else
	{
		array_push($chart,$Data2);}
	}
	/*echo "<pre>";
	print_r($chart);
	exit;
	*/
	return $chart;
  }


/*
进口炼焦煤
*/
function arr_mj_pzp_jkjm($pzggcz,$flag,$stime,$etime)
{
	global $min,$max,$tabledate,$tstime,$tetime;
	//data1 日期
	$min=1000;
	$max=-1;
	$namev="平均价格走势图";

	$var_tks=array('1'=>'国产炼焦煤指数','2'=>'进口炼焦煤指数','0'=>'炼焦煤综合指数');
	foreach($pzggcz as $news=>$values)
	{
		if ($news==0)
		{
			$Data1[0]="";
		}
		$Data2[0]=$var_tks[$values];
		$sql="select * from shpi_coal where vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
		$rs=$this->connshpi->Execute($sql);
		$i=1;
		while(!$rs->EOF)
		{
			if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("dateday")));}
			$xdate[]=$rs->fields("dateday");
			$tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);
			$Data2[$i]=ceil($rs->fields('weiindex'));
			//$Dataaa[$i]=ceil($rs->fields('weipriceusb'));
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		}
	$Data2[0]=$var_tks[$values];

	unset($Dataaa);
	if (is_array($xdate))
	{
		$tstime=date("Y-m-d",strtotime($xdate[0]));
		$tetime=date("Y-m-d",strtotime(end($xdate)));
	}
	if ($news==0)
	{
		$chart=array($Data1,$Data2);
	}
	else
	{
		array_push($chart,$Data2);}
	}
	/*echo "<pre>";
	print_r($chart);
	exit;
	*/
	return $chart;
  }

//-------------钢材
  function arr_gj_pzp($pzggcz,$flag,$stime,$etime,$avgprice,$array_value)
  {
  	global $min,$max,$tabledate,$tstime,$tetime;
  	//data1 日期
  	$min=999999;$max=-1;
  	$namev="平均价格走势图";
  	$var_tks=array("全球"=>"全球SHGSPI","美洲"=>"美洲SHGSPI-America","欧洲"=>"欧洲 SHGSPI-Europe","亚洲"=>"亚洲SHGSPI-Asia","中国"=>"中国SHGSPI-China","板材"=>"扁平材SHGSPI-F","长材"=>"长材SHGSPI-L");
  	foreach($pzggcz as $news=>$values)
  	{
		//add by zfy started 2020/06/19 基准日
		$sqldate = "select *  from gjshpi where leibie='".$values."' and datetime<='" . $array_value['baseDaydate'] . "' order by datetime desc limit 1";
		$basedays = $this->connshpi->getrow($sqldate);
		//add by zfy ended 2020/06/19 基准日
  		if ($news==0)
  		{
  			$Data1[0]="";
  		}
  		//$Data2[0]=$var_tks[$values];
  		//echo $values;
  		$Data2[0]=$var_tks[$values];
  		$sql="select * from gjshpi  where leibie='".$values."' and datetime >='".$stime."' and datetime <='".$etime."' order by datetime asc";
  		//echo $sql;
  		/* if( $_GET['a'] == 'a'){

  		} */
  		$rs=$this->connshpi->Execute($sql);
//   		$counts=$rs->RecordCount();
//   		echo $counts;
  		$i=1;
  		while(!$rs->EOF)
  		{
  			if ($news==0){
  				$Data1[$i]=date("y/m/d",strtotime($rs->fields("datetime")));
  			}
  			$xdate[]=$rs->fields("datetime");
  			$tabledate[$values][$rs->fields("datetime")]=$rs->fields($fields_name);


			//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
			if ($array_value['baseDaydate']!='2004-09-28'&&in_array($flag,array('1'))){
				// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,'mvalue');
				$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,'mvalue');
				//update changhong 2018-04-25 走势图数据用原始数据
				//$Data2[$i]=ceil($rs->fields('mvalue'));
				//$Dataaa[$i]=ceil($rs->fields('mvalue'));
				$Data2[$i]=$price;
				$Dataaa[$i]=$price;
				//update changhong 2018-04-25 走势图数据用原始数据 end
			}else {
				//update changhong 2018-04-25 走势图数据用原始数据
				//$Data2[$i]=ceil($rs->fields('mvalue'));
				//$Dataaa[$i]=ceil($rs->fields('mvalue'));
				$Data2[$i]=$rs->fields('mvalue');
				$Dataaa[$i]=$rs->fields('mvalue');
				//update changhong 2018-04-25 走势图数据用原始数据 end
				//add by zfy started 2017/7/21
				$price_5 = ceil($rs->fields('mvalue_avg_5'));
				$price_10 = ceil($rs->fields('mvalue_avg_10'));
				$price_20 = ceil($rs->fields('mvalue_avg_20'));
				$price_40 = ceil($rs->fields('mvalue_avg_40'));
				$price_60 = ceil($rs->fields('mvalue_avg_60'));
				$price_200 = ceil($rs->fields('mvalue_avg_200'));
			}
			//add by zfy ended 2020/06/04 基准日

			if(is_array($avgprice)){
				if(in_array('5',$avgprice)){
						$Data_avg[5][0]="5日均线";
						if($price_5!=''&&$price_5!='0')
						$Data_avg[5][$i]=$price_5;
					}
					if(in_array('10',$avgprice)){
						$Data_avg[10][0]="10日均线";
						if($price_10!=''&&$price_10!='0')
						$Data_avg[10][$i]=$price_10;
					}
					if(in_array('20',$avgprice)){
						$Data_avg[20][0]="20日均线";
						if($price_20!=''&&$price_20!='0')
						$Data_avg[20][$i]=$price_20;
					}
					if(in_array('40',$avgprice)){
						$Data_avg[40][0]="40日均线";
						if($price_40!=''&&$price_40!='0')
						$Data_avg[40][$i]=$price_40;
					}
					if(in_array('60',$avgprice)){
						$Data_avg[60][0]="60日均线";
						if($price_60!=''&&$price_60!='0')
						$Data_avg[60][$i]=$price_60;
					}
					if(in_array('200',$avgprice)){
						$Data_avg[200][0]="200日均线";
						if($price_200!=''&&$price_200!='0')
						$Data_avg[200][$i]=$price_200;
					}
			}

		 //add by zfy ended 2017-07-21
  			//$Data_usb[$i]=ceil($rs->fields('weipriceusb'));//美元
  			if ($min>$Data2[$i]){
  				$min=$Data2[$i];
  			}
  			if ($max<$Data2[$i]){
  				$max=$Data2[$i];
  			}
  			$i++;
  			$rs->MoveNext();
  		}
  		//echo $var_tks[$values];

  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
  		$Data2[0]=$var_tks[$values]."均值".round(array_sum($Dataaa)/count($Dataaa))." ";
  		}
  		//echo $Data2[0];
  		if($values==3)
  		{
  			//$usb_jj=round(array_sum($Data_usb)/count($Data_usb),1)."美元";
  			//$Data2[0]=$var_tks[$values]."(期间均价:".$usb_jj.")";
  		}
  		unset($Dataaa);
  		if (is_array($xdate))
  		{
  			$tstime=date("Y-m-d",strtotime($xdate[0]));
  			$tetime=date("Y-m-d",strtotime(end($xdate)));
  		}
  		if ($news==0)
  		{
  			$chart=array($Data1,$Data2);
  		}
  		else
  		{
  			array_push($chart,$Data2);
  		}
		//add by zfy started 2017/7/21
		if($avgprice){
			foreach ($avgprice as $ak=>$av){
				array_push($chart,$Data_avg[$av]);
			}
		}
		//add by zfy ended 2017/7/21
  	}
  	/* echo "<pre>";
  	 print_r($chart);
  	exit; */


  	return $chart;
  }

  //英文版
  function arr_gj_pzp_en($pzggcz,$flag,$stime,$etime)
  {
  	global $min,$max,$tabledate,$tstime,$tetime;
  	//data1 日期
  	$min=999999;$max=-1;
  	$namev="平均价格走势图";
  	$var_tks=array("全球"=>"Global","美洲"=>"America","欧洲"=>"Europe","亚洲"=>"Asia","中国"=>"China","板材"=>"Flats","长材"=>"Longs");
  	foreach($pzggcz as $news=>$values)
  	{
  		if ($news==0)
  		{
  			$Data1[0]="";
  		}
  		$Data2[0]=$var_tks[$values];
  		//echo $values;
  		//$Data2[0]=$values;
  		$sql="select * from gjshpi  where leibie='".$values."' and datetime >='".$stime."' and datetime <='".$etime."' order by datetime asc";
  		//echo $sql;
  		/* if( $_GET['a'] == 'a'){

  		} */
  		$rs=$this->connshpi->Execute($sql);
  		//   		$counts=$rs->RecordCount();
  		//   		echo $counts;
  		$i=1;
  		while(!$rs->EOF)
  		{
  			if ($news==0){
  				$Data1[$i]=date("y/m/d",strtotime($rs->fields("datetime")));
  			}
  			$xdate[]=$rs->fields("datetime");
  			// $tabledate[$values][$rs->fields("datetime")]=$rs->fields($fields_name);//update zk 20220726 Warning: Undefined variable $fields_name i
  			$Data2[$i]=round($rs->fields('mvalue'),2);
  			$Dataaa[$i]=round($rs->fields('mvalue'),2);
  			// $Dataaa[$i]=ceil($rs->fields('mvalue'));
  			//$Data_usb[$i]=ceil($rs->fields('weipriceusb'));//美元
  			if ($min>$Data2[$i]){
  				$min=$Data2[$i];
  			}
  			if ($max<$Data2[$i]){
  				$max=$Data2[$i];
  			}
  			$i++;
  			$rs->MoveNext();
  		}
  		//echo $var_tks[$values];

  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6 || $_SESSION['memberid']==1 || $_SESSION['enpower']==6){
  			$Data2[0]=$var_tks[$values]."(AVG:".round(array_sum($Dataaa)/count($Dataaa)).")";
  		}
  		//echo $Data2[0];
  		if($values==3)
  		{
  			//$usb_jj=round(array_sum($Data_usb)/count($Data_usb),1)."美元";
  			//$Data2[0]=$var_tks[$values]."(期间均价:".$usb_jj.")";
  		}
  		unset($Dataaa);
  		if (is_array($xdate))
  		{
  			$tstime=date("Y-m-d",strtotime($xdate[0]));
  			$tetime=date("Y-m-d",strtotime(end($xdate)));
  		}
  		if ($news==0)
  		{
  			$chart=array($Data1,$Data2);
  		}
  		else
  		{
  			array_push($chart,$Data2);
  		}
  	}
  	/* echo "<pre>";
  	 print_r($chart);
  	exit; */


  	return $chart;
  }


  /*
   铁矿石加权平均价格英文
  */
  function arr_tks_pzp_en($pzggcz,$flag,$stime,$etime)
  {
  	global $min,$max,$tabledate,$tstime,$tetime;
  	//data1 日期
  	$min=999999;$max=-1;
  	$namev="平均价格走势图";
  	$var_tks=array('1'=>'China Domestic Iron Ore','3'=>'China Imported Iron Ore','0'=>'China Iron Ore Price ');
  	foreach($pzggcz as $news=>$values)
  	{
  		if ($news==0)
  		{
  			$Data1[0]="";
  		}
  		//$Data2[0]=$var_tks[$values];
  		//echo $values;
  		$Data2[0]=$var_tks[$values];
  		$sql="select * from shpi_material_pzp  where vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
  		//echo $sql;
  		$rs=$this->connshpi->Execute($sql);
  		$i=1;
  		while(!$rs->EOF)
  		{
  			if ($news==0){
  				$Data1[$i]=date("y/m/d",strtotime($rs->fields("dateday")));
  			}
  			$xdate[]=$rs->fields("dateday");
  			// $tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name); //zk update 20220729 注释掉了
  			$Data2[$i]=ceil($rs->fields('weiprice'));
  			$Dataaa[$i]=ceil($rs->fields('weiprice'));
  			$Data_usb[$i]=ceil($rs->fields('weipriceusb'));//美元
  			if ($min>$Data2[$i]){
  				$min=$Data2[$i];
  			}
  			if ($max<$Data2[$i]){
  				$max=$Data2[$i];
  			}
  			$i++;
  			$rs->MoveNext();
  		}
  		//echo $var_tks[$values];
  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6 || $_SESSION['memberid']==1 || $_SESSION['enpower']==6){
  			$Data2[0]=$var_tks[$values]."(AVG:".round(array_sum($Dataaa)/count($Dataaa))."RMB)";
  		}
  		unset($Dataaa);
  		if (is_array($xdate))
  		{
  			$tstime=date("Y-m-d",strtotime($xdate[0]));
  			$tetime=date("Y-m-d",strtotime(end($xdate)));
  		}
  		if ($news==0)
  		{
  			$chart=array($Data1,$Data2);
  		}
  		else
  		{
  			array_push($chart,$Data2);
  		}
  	}
  	/* echo "<pre>";
  	 print_r($chart);
  	exit; */


  	return $chart;
  }

  /*
铁矿石加权平均价格
*/
function arr_tks_pzp($pzggcz,$flag,$stime,$etime)
{
	global $min,$max,$tabledate,$tstime,$tetime;
	//data1 日期
	$min=999999;$max=-1;
	$namev="平均价格走势图";
	$var_tks=array('1'=>'国产铁矿石SHDOPI','3'=>'进口铁矿石SHIOPI','0'=>'铁矿石SHOPI');
     foreach($pzggcz as $news=>$values)
	{
		if ($news==0)
		{
			$Data1[0]="";
		}
		//$Data2[0]=$var_tks[$values];
		//echo $values;
		$Data2[0]=$var_tks[$values];
		$sql="select * from shpi_material_pzp  where vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
		//echo $sql;
		$rs=$this->connshpi->Execute($sql);
		$i=1;
		while(!$rs->EOF)
		{
			if ($news==0){$Data1[$i]=date("y/m/d",strtotime($rs->fields("dateday")));}
			$xdate[]=$rs->fields("dateday");
			$tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);
			$Data2[$i]=ceil($rs->fields('weiprice'));
			$Dataaa[$i]=ceil($rs->fields('weiprice'));
			$Data_usb[$i]=ceil($rs->fields('weipriceusb'));//美元
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		}
		//echo $var_tks[$values];
 		// power由7改成6，甲级会员可以显示均价
		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
	$Data2[0]=$var_tks[$values]."均价".round(array_sum($Dataaa)/count($Dataaa))."元";
		}
	unset($Dataaa);
	if (is_array($xdate))
	{
		$tstime=date("Y-m-d",strtotime($xdate[0]));
		$tetime=date("Y-m-d",strtotime(end($xdate)));
	}
	if ($news==0)
	{
		$chart=array($Data1,$Data2);
	}
	else
	{
		array_push($chart,$Data2);}
	}
	/* echo "<pre>";
	print_r($chart);
	exit; */

	return $chart;
  }
  function arr_tks_pzp_qq($array_value)
  {
  	foreach ($array_value["pid"] as $nkeys=>$nvalues){
  		switch ($array_value["flag"])
  		{
  			case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
  			case 2: $fields_name="weiprice";$namev="综合价格走势图";break;
  			case 3: $fields_name="apindex";$namev="价格指数走势图";break;
  			case 4: $fields_name="weiindex";$namev="综合指数走势图";break;
  			case 5: $fields_name="price";$namev="综合价格走势图";break;
  		}


  		$sql="select * from shpi_material_pzp  where vid=".$nvalues." and (dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."') order by dateday asc";
  		// $sql="select * from shpi_material where topicture='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
  		//echo $sql;
  	 if( $_GET['a'] == 'a' ){
  	 	//echo $sql;
  	 }
  	 $rs=$this->connshpi->Execute($sql);$i=1;
  	 while (!$rs->EOF){
  			$dataday=$rs->fields("dateday"); $price=$rs->fields($fields_name);
  			//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
  			$array_price["k".$nvalues][$dataday]=$price;
  			$rs->MoveNext();
  	 }
  	}

  	return $array_price;
  }
  /*
   进口现货矿英文版
  */
  function arr_tks_pzp_jkxhk_en($pzggcz,$flag,$stime,$etime)
  {
  	global $min,$max,$tabledate,$tstime,$tetime;
  	//data1 日期
  	$min=1000;
  	$max=-1;
  	$namev="平均价格走势图";

  	$var_tks=array('1'=>'国产现货矿加权价格','3'=>' Imported Iron Ore (Fe:62%, CFR)','0'=>'铁矿石综合价格');
  	foreach($pzggcz as $news=>$values)
  	{
  		if ($news==0)
  		{
  			$Data1[0]="";
  		}
  		$Data2[0]=$var_tks[$values];
  		$sql="select * from shpi_material_pzp  where vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
  		// if(isset($_GET['a'])&&$_GET['a'] == 'a' ){
  		 	// echo $sql;
  		// }
  		$rs=$this->connshpi->Execute($sql);
  		$i=1;
  		while(!$rs->EOF)
  		{
  			if ($news==0){
  				$Data1[$i]=date("y/m/d",strtotime($rs->fields("dateday")));
  			}
  			$xdate[]=$rs->fields("dateday");
  			// $tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);//zk update 20220729 注释掉了
  			// $Data2[$i]=ceil($rs->fields('weipriceusb'));
  			// $Dataaa[$i]=ceil($rs->fields('weipriceusb'));
  			$Data2[$i]=round($rs->fields('weipriceusb'),2);
  			$Dataaa[$i]=round($rs->fields('weipriceusb'),2);
  			if ($min>$Data2[$i]){
  				$min=$Data2[$i];
  			}
  			if ($max<$Data2[$i]){
  				$max=$Data2[$i];
  			}
  			$i++;
  			$rs->MoveNext();
  		}
  		// power由7改成6，甲级会员可以显示均价
  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6 || $_SESSION['memberid']==1 || $_SESSION['enpower']>=6){
  			$Data2[0]=$var_tks[$values]."(AVG:".round(array_sum($Dataaa)/count($Dataaa))."USD)";
  		}
  		unset($Dataaa);
  		if (is_array($xdate))
  		{
  			$tstime=date("Y-m-d",strtotime($xdate[0]));
  			$tetime=date("Y-m-d",strtotime(end($xdate)));
  		}
  		if ($news==0)
  		{
  			$chart=array($Data1,$Data2);
  		}
  		else
  		{
  			array_push($chart,$Data2);
  		}
  	}
  	/*echo "<pre>";
  	 print_r($chart);
  	exit;
  	*/
  	return $chart;
  }

/*
进口现货矿
*/
function arr_tks_pzp_jkxhk($pzggcz,$flag,$stime,$etime)
{
	global $min,$max,$tabledate,$tstime,$tetime;
	//data1 日期
	$min=1000;
	$max=-1;
	$namev="平均价格走势图";

	$var_tks=array('1'=>'国产现货矿加权价格','3'=>'进口铁矿石SHIOPI(Fe:62%,CFR)','0'=>'铁矿石综合价格');
	foreach($pzggcz as $news=>$values)
	{
		if ($news==0)
		{
			$Data1[0]="";
		}
		$Data2[0]=$var_tks[$values];
		$sql="select * from shpi_material_pzp  where vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
		$rs=$this->connshpi->Execute($sql);
		$i=1;
		while(!$rs->EOF)
		{
			if ($news==0){$Data1[$i]=date("y/m/d",strtotime($rs->fields("dateday")));}
			$xdate[]=$rs->fields("dateday");
			$tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);
			$Data2[$i]=ceil($rs->fields('weipriceusb'));
			$Dataaa[$i]=ceil($rs->fields('weipriceusb'));
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		}
 		// power由7改成6，甲级会员可以显示均价
		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
	$Data2[0]=$var_tks[$values]."均价".round(array_sum($Dataaa)/count($Dataaa))."美元";
		}
	unset($Dataaa);
	if (is_array($xdate))
	{
		$tstime=date("Y-m-d",strtotime($xdate[0]));
		$tetime=date("Y-m-d",strtotime(end($xdate)));
	}
	if ($news==0)
	{
		$chart=array($Data1,$Data2);
	}
	else
	{
		array_push($chart,$Data2);}
	}
	/*echo "<pre>";
	print_r($chart);
	exit;
	*/
	return $chart;
  }
  //铁矿石指数 qianyi 2010-08-11 start
function arr_tks_zs_index($pz,$flag,$stime,$etime)
{
	 foreach($pz as $k1=>$v1)
	{
		// echo substr($v1,0,1)."<br>";
		$pz_index=substr($v1,-1);
		switch (substr($v1,0,1))
		{
		   case 'm': $fields_name="mindex";
				$sql="select * from shpi_material where topicture='".$pz_index."' and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
					break;
		   case 'j': $fields_name="weiindex";
				$sql="select * from shpi_material_pzp  where vid=".$pz_index." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
					break;
		}
		//echo $sql."<br>";
		$rs=$this->connshpi->Execute($sql);
		while (!$rs->EOF)
		{
		$dataday=$rs->fields("dateday");
		$price=$rs->fields($fields_name);
		//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
		$array_price[$v1][$dataday]=$price;
		$rs->MoveNext();
		}
	}
return $array_price;
}
//2010-08-11 end
  function show_flash_xhk($Data,$min,$max,$flag=0,$row=0){
    global $SHPIIMAGE,$maminvaluestr,$array_avg;
	if ($SHPIIMAGE==""){
	   $SHPIIMAGE="gglg.jpg";
	}
    if ($flag==0){ $chart['chart_data']=$Data;$skip=5; $yy=50;}
	else{
	    //$chart['chart_data']=$Data;
	    $num=count($Data);

		if ($num==3){$yy=55; $ly=7;}else{$yy=50;
		if ($num==4){$ly=1;}
		if ($num==2){$ly=15;}}

		if ($row==1){
		  $lstr="horizontal";
		  $yy=50;$ly=15;
		}else{
		  $lstr="vertical";
		}
		//echo $lstr;
		//echo $num."num=";
		$num1=count($Data[0]);
		$skip=round($num1/10);
		$rr=""; $wdcs="";
		for ($x=0;$x<$num;$x++){
			foreach ($Data[$x] as $news=>$name1){
			   if ($news==0){$rr.="var r".$x."='".$name1.$maminvaluestr.$array_avg[$x-1];}else{$rr.=";".$name1;}
			}
			$rr.="';\n";
			$wdcs.="window.document.charts.SetVariable('r".$x."', r".$x.");";
		}
	    $script='<SCRIPT>
		function doPassVar_xhk(){
		    '.$rr.$wdcs.'
			window.document.charts.SetVariable("rows",'.$num.');
			window.document.charts.SetVariable("cols",'.$num1.');
			}
			//-->
			</SCRIPT> ';
			echo $script;
	}
     $chart[ 'axis_category' ] = array ( 'size'=>14, 'color'=>"000000", 'alpha'=>0, 'font'=>"arial", 'size'=>10, 'bold'=>false, 'skip'=>$skip,'orientation'=>"vertical" );
      //以上指： 左标(X 轴坐标)
	  #orientation:"horizontal", "diagonal_up", "diagonal_down", "vertical_up", and "vertical_down." The default value is "horizontal"
	$chart[ 'axis_value' ] = array (  'font'=>"arial", 'bold'=>true, 'size'=>10, 'color'=>"000000", 'alpha'=>100, 'steps'=>10, 'prefix'=>"", 'suffix'=>"", 'decimals'=>0, 'separator'=>"", 'show_min'=>true, 'min' =>$min+2, 'max'  =>$max+2,  );
	  //以上Y坐标
	  $uurrll="../../images/".$SHPIIMAGE;
	  $chart [ 'canvas_image' ] = array (   'url'  => $uurrll,'alpha'  =>  70);
      $chart[ 'canvas_bg' ] = array ( 'width'=>600, 'height'=>300, 'color'=>"ffe075" );
$chart[ 'chart_bg' ] = array ( 'positive_color'=>"ffffff", 'positive_alpha'=>70, 'negative_color'=>"ff0000",  'negative_alpha'=>10 );
$chart[ 'chart_border' ] =array ('color'=>"5e5e5e",'top_thickness'=>1,'bottom_thickness'=>1,'left_thickness'=>1,'right_thickness'=>1 );
	  //背景
$chart[ 'chart_grid' ] = array ( 'alpha'=>10, 'color'=>"000000", 'horizontal_thickness'=> 1, 'vertical_thickness'=> 1, 'horizontal_skip'=>0, 'vertical_skip'=>0 );
//以上是制图格式
$chart[ 'chart_line' ] = array ( 'line_thickness'=>2, 'point_shape'=>"none", 'fill_shape'=>false );
//以上是线的宽度
$chart[ 'chart_rect' ] = array ( 'x'=>45, 'y'=>$yy, 'width'=>535, 'height'=>200 );
$chart[ 'chart_type' ] = "Line";
	 //以上指数据: 三个数组： 数组1：为下标值 (x 坐标)  //数组2 :坐标值数据
	 $chart [ 'legend_rect' ] = array (
	                                 'x'=>25,
									 'y'=>$ly,
                                     'width'   =>  580,
                                     'height'  =>  5,
                                     'margin'  =>  2
                                 );
 	        //
	/*$chart [ 'legend_bg' ] = array (   'bg_color'          =>  "939393",
									   'bg_alpha'          =>  100,
									   'border_color'      =>  "939393",
									   'border_alpha'      =>  100,
									   'border_thickness'  =>  0
								   );*/
	$chart [ 'legend_label' ] = array (   'layout'  =>  $lstr,
										  'bullet'  =>  "line",
										  'font'    =>  "Arial",
										  'bold'    =>  true,
										  'size'    =>  12,
										  'color'   =>  "5e5e5e",
										  'alpha'   =>  90
									  );
	$chart[ 'series_color' ] = array (  "ff6000","0026ff","00ff68" );
	return $chart;
    //$f_str=DrawChart ( $chart );
  }

  //==========================================================
  /*
  ; 品种平均价格
  ; 品种加权价格
  */
  function ptak($ndate){
     global $avg,$wavg,$avgin,$wavgin;
     $getindex="select CategoryID from category ";
	 $rs=$this->connshpi->Execute($getindex); $i;
		while (!$rs->EOF){
		    $newsum=0;$newsum2=0;$sumweight=0;
			$CategoryID=$rs->fields('CategoryID');
			//====================================
			$sql="select p.CategoryID,p.ProductID,p.Material,p.Specification,p.Weight,s.AveragePrice,s.WeightPrice from specificationap as s,productsweight as p where s.ProductID=p.ProductID and s.Specification=p.Specification and p.Material=s.Material and p.CategoryID=".$CategoryID." and s.Date like '".$ndate."%'";
			$rsb=$this->connshpi->Execute($sql);
			while (!$rsb->EOF){
			   $tmp1=$rsb->fields('Weight')*$rsb->fields('AveragePrice');
			   $newsum+=$tmp1;
			    //SHVPtAK
			   $tmp2=$rsb->fields('Weight')*$rsb->fields('WeightPrice');
			   $newsum2+=$tmp2;                              //SHVPtAK
			   if($rsb->fields('AveragePrice')!=0)
					$sumweight+=$rsb->fields('Weight');
			   $rsb->MoveNext();
			}
			$avg[$CategoryID]=round($newsum/$sumweight,2);  //平均价
			$wavg[$CategoryID]=round($newsum2/$sumweight,2); //权重价
			//echo $avg[$CategoryID]."||".$wavg[$CategoryID]."<br>";
			//========  基准日
			 $sql="select AveragePrice,WeightPrice from productap where IsBase=1 and  
			CategoryID='".$CategoryID."' limit 1";
			 $rsbac=$this->connshpi->Execute($sql);
			 if (!$rsbac->EOF){
			    $bavg=$rsbac->fields("AveragePrice"); $wbavg=$rsbac->fields("WeightPrice");
			    $avgin[$CategoryID]=round(($avg[$CategoryID]*100)/$bavg,4);   //品种规格平均价格指数
				$wavgin[$CategoryID]=round(($wavg[$CategoryID]*100)/$wbavg,4);  //品种规格加权价格指数
			 }

			//====================================插入数据库
			  $sql="insert into productap(CategoryID,AveragePrice,WeightPrice,IsBase,Date,PriceIndex,WeightIndex ) values(".$CategoryID.",".$avg[$CategoryID].",".$wavg[$CategoryID].",0,'$ndate',".$avgin[$CategoryID].",".$wavgin[$CategoryID].") ";
        $insertact=$this->connshpi->Execute($sql);
			//=======================================
		    $rs->MoveNext();
	    }
  }
  //==============================================================================================
  /*
     ;品种规格材质价格指数
	 ;基准日
	 ; array(0=>品种规格的平均价格,1=>品种规格的加权价格,2=>品种的平均价格,3=>品种的加权价格
	         4=>综合平均价格,5=>综合加权价格);
  */
  function city_wei($isflag=1){
       $sqla="select * from shpi_city where isflag=0 or isflag=".$isflag;
	   //echo $sqla;
	   $rsb=$this->connshpi->Execute($sqla);
	   while (!$rsb->EOF){
	      if ($isflag==1){$cw="cweight";}else{$cw="bxgweight";}
		  $array_city[$rsb->fields("cid")]=$rsb->fields($cw);
		  $rsb->MoveNext();
	   }
	   return $array_city;
  }
  function arr_basic($arr_pzgg,$ndate="",$isbase=0){
     global $catid,$avg,$wavg,$avgin,$wavgin;
     if ($ndate==""){$ndate=date("Y-m-d");}
	 $arr_tsm=array("1"=>10,"2"=>10,"3"=>10,"4"=>1,"5"=>10,"6"=>10,"7"=>6,"8"=>3,"9"=>10,"10"=>5,"11"=>2);
	 foreach ($arr_pzgg as $keys=>$values){
	        $d=0;
		   if ($values==11){$arr_city=$this->city_wei(2);}else{$arr_city=$this->city_wei(1);}
	       foreach ($arr_city as $kcityid=>$kwei){
		     if ($d==0){$instr="('".$kcityid.$keys."'";}else{$instr.=",'".$kcityid.$keys."'";}
			  $d++;
		   }
		   if ($values=='11'){
		     $instr=str_replace("319112","314912", $instr); $instr=str_replace("314824","319023", $instr);
		   }
		   $instr.=")";
	        $SumVPjti=0;$SumUiRi=0; $totalprice=0;$tosum=0;
		    $sql="select price,topicture from marketconditions as C left join marketrecord as M on C.marketrecordid=M.id where  
			C.mconmanagedate>'".$ndate." 01:01' and C.mconmanagedate<'".$ndate." 23:59' and	C.topicture in ".$instr." order by topicture";
			//echo $sql."<br>";exit;
			$rs=$this->connshpi->Execute($sql);
			$arr_price=array();$arr_wei=array();
			while (!$rs->EOF){
			    $price=$rs->fields('price'); $priceid=$rs->fields('topicture');

				if (strstr($price,"-")){
	   				$oldprb=explode("-",$price);
	  				$price=ceil(($oldprb[1]+$oldprb[0])/2);
		        }
			    if ($price>10){
				    $arr_price[$priceid]=$price;
				    $arr_wei[$priceid]=$arr_city[substr($priceid,0,2)];
					//$tmp=$price*$wei;
					//$SumVPjti+=$tmp;
					//$SumUiRi+=$wei;
				}
				$rs->MoveNext();
			 }
			 //==============计算平均价,加权价

			 $tosum=count($arr_price);$totalprice=0;$SumVPjti=0;$SumUiRi=0;
			 foreach ($arr_price as $keyss=>$prices){
			    $totalprice+=$prices;
			    $tmp=$prices*$arr_wei[$keyss];
				$SumVPjti+=$tmp;
				$SumUiRi+=$arr_wei[$keyss];
			 }
			//===============
			// echo $values.":".$tosum." ==|||";
			 if (($totalprice>10)&&($tosum>$arr_tsm[$values])){
			   $SHVPtAj[$keys]=round($totalprice/$tosum,2);  //品种规格平均价格
			   $SHVPtSj[$keys]=round($SumVPjti/$SumUiRi,2);  //品种规格加权价格
				 //=====================基准日
				 if ($isbase==0){
					 $sql="select aveprice,weiprice from shpi_pzggp where isbase=1 and svm_vid='".$keys."' limit 1";
					 $rsbac=$this->connshpi->Execute($sql);
					 if (!$rsbac->EOF){
						$bavg=$rsbac->fields("aveprice"); $wbavg=$rsbac->fields("weiprice");
						$SHVPIAj[$keys]=round(($SHVPtAj[$keys]*100)/$bavg,4);   //品种规格平均价格指数
						$SHVPISj[$keys]=round(($SHVPtSj[$keys]*100)/$wbavg,4);  //品种规格加权价格指数
					 }
					 //=====================
					$inserttable="insert into shpi_pzggp (bbid,svm_vid,aveprice,apindex,weiprice,wpindex,dateday,tosum,isbase) values('".$values."','".$keys."','".$SHVPtAj[$keys]."','".$SHVPIAj[$keys]."',".$SHVPtSj[$keys].",".$SHVPISj[$keys].",'".$ndate."','".$tosum."',0)";
					$insertact=$this->connshpi->Execute($inserttable);
				}
				if ($isbase==1){
					$inserttable="insert into shpi_pzggp (bbid,svm_vid,aveprice,apindex,weiprice,wpindex,dateday,tosum,isbase) values('".$values."','".$keys."','".$SHVPtAj[$keys]."',100,".$SHVPtSj[$keys].",100,'".$ndate."','".$tosum."',1)";
					$insertact=$this->connshpi->Execute($inserttable);
				}
			}
			//if ($insertact){echo "插入成功";}else{echo "插入不成功";}
			 //=====================  //
	 }
  }
  /*
    ; 钢材指数，与平均价 //现数据不当
  */
  function shgcin($ndate){
      //=========
	     $sql="SELECT sum( `CategoryWeight` ) as total FROM `category` ";
		 $rss=$this->connshpi->Execute($sql);
		 $tsum= round($rss->fields("total"),1);
	  //=========
	  $sql="select distinct p.CategoryID,p.AveragePrice,p.WeightPrice,c.CategoryWeight from productap as p 
	  left join category as c on  
	  p.CategoryID=c.CategoryID  where p.Date like '".$ndate."%' limit 14";
	  $rs=$this->connshpi->Execute($sql);$gcinsum=0;$gcinwsum=0;$wsum=0;
	  while (!$rs->EOF){
	    $gcinsum+=($rs->fields('AveragePrice')*$rs->fields('CategoryWeight'))/$tsum;
        $gcinwsum+=($rs->fields('WeightPrice')*$rs->fields('CategoryWeight'))/$tsum;
		//if($rs->fields('WeightPrice')!=0 or $rs->fields('AveragePrice')!=0)
		//$wsum+=$rs->fields('CategoryWeight');
		//echo $wsum."||";
	     $rs->MoveNext();
	  }
	  //==============
	  echo $gcinsum."平均总数||".$gcinwsum."加权平均总数";
	 // $gcavgp=round($gcinsum,2);
	 // $gcavgw=round($gcinwsum,2);
	   /*//==========基准日
	    $sql="select s.AveragePrice,s.WeightPrice from steelindex as s  where IsBase=1 limit 1";
		$rsbac=$this->connshpi->Execute($sql);
		if (!$rsbac->EOF){
		    $bavg=$rsbac->fields("AveragePrice"); $wbavg=$rsbac->fields("WeightPrice");
			$gcpin=round(($gcavgp*100)/$bavg,4);   //钢材平均价格指数
	        $gcwin=round(($gcavgw*100)/$wbavg,4);  //钢材加权价格指数
		}*/
	   //==========
	   echo $wbavg."基准日加权||".$bavg."||".$gcavgp."||".$gcavgw."||".$gcpin."||".$gcwin;
  }
  function print_pzgg($ndate){
     $sql="SELECT * FROM `productsweight`";
	 $rs=$this->connshpi->Execute($sql);
	 $str="<table><tr>";
	 $str.="<td height='25'>序号</td>";$str.="<td>品种材质规格</td>";$str.="<td>平均</td>";$str.="<td>加权</td>";
	 $str.="<td>平均指数</td>";$str.="<td>加权指数</td>";
	 $str.="</tr>";$i=1;
	 while (!$rs->EOF){
	     $str.="<tr>";
		 $str.="<td height='25'>$i</td>";$str.="<td>".$bb."</td>";$str.="<td>平均</td>";$str.="<td>加权</td>";
	      $str.="<td>平均指数</td>";$str.="<td>加权指数</td>";
		 $str.="</tr>";$i++;
	     $rs->MoveNext();
	 }
  }
   /*
     ;品种对应表
   */
   function arr_pzbig($flag=0){
     if ($flag==0){
		 $sql="select * from shpi_bvw  ";
		 $rs=$this->connshpi->Execute($sql);
		 while(!$rs->EOF){
			$Data[$rs->fields("id")]=$rs->fields("bname");
			$rs->MoveNext();
		 }
	}else{
		 $sql="select id,varietyname from compilesid where id<11 ";
		 $rs=$this->connshpi->Execute($sql);
		 while(!$rs->EOF){
			$Data[$rs->fields("id")]=$rs->fields("varietyname");
			$rs->MoveNext();
		 }
	}
	 return $Data;
   }
   /*
     ;品种对应表
   */
   function arr_bpzbig($flag=0){
		 $sql="select * from shpi_bxw  ";
		 $rs=$this->connshpi->Execute($sql);
		 while(!$rs->EOF){
			$Data[$rs->fields("id")]=$rs->fields("xname");
			$rs->MoveNext();
		 }
	 return $Data;
   }
  /*
     ;某一时间段的价格走势图(品种,加权品种,指数)数据数组
  */
  function pz_price($stime,$etime,$pz,$flag){
     global $min,$max;
	 $min=999999;$max=-1;
     $bigpz=$this->arr_pzbig();
	 switch ($flag)
		{
		   case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
		   case 2: $fields_name="weiprice";$namev="综合价格走势图";break;
		   case 3: $fields_name="apindex";$namev="价格指数走势图";break;
		   case 4: $fields_name="wpindex";$namev="综合指数走势图";break;
		}
	 foreach ($pz as $news=>$values){
		 $pz_name= $bigpz[$values];
		 if ($news==0){	 $Data1[0]=""; }
		 $Data2[0]=$pz_name;
		 $sql="select * from shpi_pzp where bvm_id=$values and (dateday>'".$stime."' and dateday<'".$etime."') order by dateday asc";
		 //echo $sql;
		 $rs=$this->connshpi->Execute($sql);
		 $i=1;
		 while(!$rs->EOF){
			if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("dateday"))); }
			$Data2[$i]=$rs->fields($fields_name);
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		 }
		 if ($news==0){	 $chart=array($Data1,$Data2);}else{ array_push($chart,$Data2);}
	  }
	 return $chart;
  }
 /*
     ;某一时间段的价格走势图(大品种,加权品种,指数)数据数组
  */
  function bpz_price($stime,$etime,$pz,$flag){
     global $min,$max;
	 $min=999999;$max=-1;
     $bigpz=$this->arr_bpzbig();
	 switch ($flag)
		{
		   case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
		   case 2: $fields_name="weiprice";$namev="加权价格走势图";break;
		   case 3: $fields_name="apindex";$namev="价格指数走势图";break;
		   case 4: $fields_name="wpindex";$namev="加权指数走势图";break;
		}
	 foreach ($pz as $news=>$values){
		 $pz_name= $bigpz[$values]." [$stime] - [$etime] $namev";
		 if ($news==0){	 $Data1[0]=""; }
		 $Data2[0]=$pz_name;
		 $sql="select * from shpi_pp where bc_id=$values and (dateday>'".$stime."' and dateday<'".$etime."') order by dateday asc";
		 //echo $sql;
		 $rs=$this->connshpi->Execute($sql);
		 $i=1;
		 while(!$rs->EOF){
			if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("dateday"))); }
			$Data2[$i]=$rs->fields($fields_name);
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		 }
		 if ($news==0){	 $chart=array($Data1,$Data2);}else{ array_push($chart,$Data2);}
	  }
	 return $chart;
  }    /*
     ;某一时间段的价格综合指数
  */
  function steel_price($stime,$etime,$pz,$flag){
     global $min,$max;
	 $min=999999;$max=-1;
     //$bigpz=$this->arr_pzbig();
	 switch ($flag)
		{
		   case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
		   case 2: $fields_name="weiprice";$namev="加权价格走势图";break;
		   case 3: $fields_name="apindex";$namev="价格指数走势图";break;
		   case 4: $fields_name="wpindex";$namev="加权指数走势图";break;
		}
		 $pz_name= "钢材综合"." [$stime] - [$etime] $namev";
		 if ($news==0){	 $Data1[0]=""; }
		 $Data2[0]=$pz_name;
		 $sql="select * from shpi_pi where (dateday>'".$stime."' and dateday<'".$etime."') order by dateday asc";
		// echo  $sql."<br>";
		 $rs=$this->connshpi->Execute($sql);
		 $i=1;
		 while(!$rs->EOF){
			if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("dateday"))); }
			$Data2[$i]=$rs->fields($fields_name);
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		 }
		 if ($news==0){	 $chart=array($Data1,$Data2);}else{ array_push($chart,$Data2);}

	 return $chart;
  }

  function arr_average_price($price_arr, $class,$stime,$etime ,$avgprice){
  	$arr_date = array();
  	$isfirst = false;
  	if($price_arr){
  		$arr_date = $price_arr[0];
  	}else {
  		$isfirst = true;
  		$arr_date[] = "";
  	}

  	foreach($class as $classid){
		//update by zfy started 2017/7/21
  		$sql = "select * from average_price where classid='$classid' and dateday>='$stime' and dateday<='$etime'";
		$i = 1;
		//update by zfy ended 2017/7/21
  		$datas = $this->connshpi->GetAll($sql);

  		$pzname = $this->connshpi->getOne("select name from average_price_class where id='$classid'");
  		$tmpArr = array();
  		foreach($datas as $row){
  			if($isfirst){
  				$arr_date[] = date('y/m/d',strtotime($row['dateday']));
  			}
  			$date_key = date('y/m/d',strtotime($row['dateday']));
  			$tmpArr[$date_key] = $row['aveprice'];
			//add by zfy started 2017/7/21
			$price_5 = $row['aveprice_avg_5'];
			$price_10 = $row['aveprice_avg_10'];
			$price_20 = $row['aveprice_avg_20'];
			$price_40 = $row['aveprice_avg_40'];
			$price_60 = $row['aveprice_avg_60'];
			$price_200 = $row['aveprice_avg_200'];
			if(is_array($avgprice)){
				if(in_array('5',$avgprice)){
						$Data_avg[5][0]="5日均线";
						if($price_5!=''&&$price_5!='0')
						$Data_avg[5][$i]=$price_5;
					}
					if(in_array('10',$avgprice)){
						$Data_avg[10][0]="10日均线";
						if($price_10!=''&&$price_10!='0')
						$Data_avg[10][$i]=$price_10;
					}
					if(in_array('20',$avgprice)){
						$Data_avg[20][0]="20日均线";
						if($price_20!=''&&$price_20!='0')
						$Data_avg[20][$i]=$price_20;
					}
					if(in_array('40',$avgprice)){
						$Data_avg[40][0]="40日均线";
						if($price_40!=''&&$price_40!='0')
						$Data_avg[40][$i]=$price_40;
					}
					if(in_array('60',$avgprice)){
						$Data_avg[60][0]="60日均线";
						if($price_60!=''&&$price_60!='0')
						$Data_avg[60][$i]=$price_60;
					}
					if(in_array('200',$avgprice)){
						$Data_avg[200][0]="200日均线";
						if($price_200!=''&&$price_200!='0')
						$Data_avg[200][$i]=$price_200;
					}
			}
			$i++;
			//add by zfy ended 2017/7/21
  		}

  		$price_tmp = array();
  		foreach($arr_date as $date){
  			if($date== ""){
					if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
						$pzname=$pzname."(平均:".round(array_sum($tmpArr)/count($tmpArr))."元)";
						$price_tmp[] = $pzname;
					}
  			}else {
  				if(isset($tmpArr[$date])) {
  					$price_tmp[] = $tmpArr[$date];
  				}else {
  					$price_tmp[] = "";
  				}
  			}
  		}

			if($isfirst) {
				$price_arr[] = $arr_date;
			}
			$price_arr[] = $price_tmp;


			$isfirst =false;
  	}
		//add by zfy started 2017-07-21
			if($avgprice){
				foreach ($avgprice as $ak=>$av){
					array_push($price_arr,$Data_avg[$av]);
				}
			}
		 //add by zfy ended 2017-07-21
  	return $price_arr;
  }
  /*
    ;具体某品种材质规格数据
  */
  function arr_pzggcz($pzggcz,$flag,$stime,$etime,$avgprice){
     global $min,$max,$tabledate,$atosum,$tstime,$tetime,$array_avg;
	 $min=999999;$max=-1;
	 switch ($flag)
		{
		 //update by zfy started 2017/7/21
		   case 1:
			   $fields_name="aveprice";
			   $namev="平均价格走势图";
			   $fields_name_5="aveprice_avg_5";
			   $fields_name_10="aveprice_avg_10";
			   $fields_name_20="aveprice_avg_20";
			   $fields_name_40="aveprice_avg_40";
			   $fields_name_60="aveprice_avg_60";
			   $fields_name_200="aveprice_avg_200";
			   break;
		   case 2:
			   $fields_name="weiprice";
			   $namev="加权价格走势图";
			   $fields_name_5="weiprice_avg_5";
			   $fields_name_10="weiprice_avg_10";
			   $fields_name_20="weiprice_avg_20";
			   $fields_name_40="weiprice_avg_40";
			   $fields_name_60="weiprice_avg_60";
			   $fields_name_200="weiprice_avg_200";
			   break;
		   case 3:
			   $fields_name="apindex";
			   $namev="价格指数走势图";
			   $fields_name_5="apindex_avg_5";
			   $fields_name_10="apindex_avg_10";
			   $fields_name_20="apindex_avg_20";
			   $fields_name_40="apindex_avg_40";
			   $fields_name_60="apindex_avg_60";
			   $fields_name_200="apindex_avg_200";
			   break;
		   case 4:
			   $fields_name="wpindex";
			   $namev="加权指数走势图";
			   $fields_name_5="wpindex_avg_5";
			   $fields_name_10="wpindex_avg_10";
			   $fields_name_20="wpindex_avg_20";
			   $fields_name_40="wpindex_avg_40";
			   $fields_name_60="wpindex_avg_60";
			   $fields_name_200="wpindex_avg_200";
			   break;
		   //update by zfy ended 2017/7/21
		}
	 foreach ($pzggcz as $news=>$values){
	 	$Data2 = array();
	 	$Data3 = array();
	    $sql="select * from shpi_svw where vid='".$values."'";
		 $rsp=$this->connshpi->Execute($sql);
		 $pzname=$rsp->fields("vname");
	    /* //===得到品种
		 $sql="select * from graphvariety where gvid=".substr($values,0,2);
		 $rsp=$this->connshpi->Execute($sql);
		 $pzname=$rsp->fields("varietyname");
		 //===得到材质
		 $sql="select * from graphmaterial where materialid =".substr($values,2,1)." and pvid=".substr($values,0,2);
		 $rsc=$this->connshpi->Execute($sql);
		 $czname=$rsc->fields("materialname");
		 //===得到规格
		 $sql="select * from graphspeci  where sid =".substr($values,3,1)." and pmid=".substr($values,0,2);
		 $rsg=$this->connshpi->Execute($sql);
		 $ggname=$rsg->fields("specification");
		 $bigpz=$pzname.$czname.$ggname;*/
		 //===========
		 $pzggcz_name= $pzname;

		 if ($news==0){	 $Data1[0]=""; }
		 $Data2[0]=$pzggcz_name;
		 $sql="select * from shpi_pzggp where svm_vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
		 //echo $sql;
		 $rs=$this->connshpi->Execute($sql);
		 $i=1;
		 while(!$rs->EOF){
			if ($news==0){
				$Data1[$i]=date("y/m/d",strtotime($rs->fields("dateday")));
				$shunxu[$rs->fields("dateday")] = $i;
			}
			$xdate[]=$rs->fields("dateday");
			$tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);
			$atosum[$values][$rs->fields("dateday")]=$rs->fields("tosum");
			if ($news==0){
				$Data2[$i]=$rs->fields($fields_name);
				//add by zfy started 2017/7/21
				$price_5 = $rs->fields($fields_name_5);
				$price_10 = $rs->fields($fields_name_10);
				$price_20 = $rs->fields($fields_name_20);
				$price_40 = $rs->fields($fields_name_40);
				$price_60 = $rs->fields($fields_name_60);
				$price_200 = $rs->fields($fields_name_200);

				if(is_array($avgprice)){
					if(in_array('5',$avgprice)){
						$Data_avg[5][0]="5日均线";
						if($price_5!=''&&$price_5!='0')
						$Data_avg[5][$i]=$price_5;
					}
					if(in_array('10',$avgprice)){
						$Data_avg[10][0]="10日均线";
						if($price_10!=''&&$price_10!='0')
						$Data_avg[10][$i]=$price_10;
					}
					if(in_array('20',$avgprice)){
						$Data_avg[20][0]="20日均线";
						if($price_20!=''&&$price_20!='0')
						$Data_avg[20][$i]=$price_20;
					}
					if(in_array('40',$avgprice)){
						$Data_avg[40][0]="40日均线";
						if($price_40!=''&&$price_40!='0')
						$Data_avg[40][$i]=$price_40;
					}
					if(in_array('60',$avgprice)){
						$Data_avg[60][0]="60日均线";
						if($price_60!=''&&$price_60!='0')
						$Data_avg[60][$i]=$price_60;
					}
					if(in_array('200',$avgprice)){
						$Data_avg[200][0]="200日均线";
						if($price_200!=''&&$price_200!='0')
						$Data_avg[200][$i]=$price_200;
					}
				}
				//add by zfy ended 2017/7/21
			}else{
				$Data3[$rs->fields("dateday")]=$rs->fields($fields_name);
			}



			$Dataaa[$i]=$rs->fields($fields_name);
			if ($min>$Data2[$i]){$min=$Data2[$i];}
			if ($max<$Data2[$i]){$max=$Data2[$i];}
			$i++;
			$rs->MoveNext();
		 }
		 //高线8mmHPB300  历史数据不足处理
		 if ($news!=0){
		 	foreach ($shunxu as $k=>$v){
		 		if(empty($Data3[$k])){
		 			$Data2[$v]="";
		 		}else{
		 			$Data2[$v]= $Data3[$k];
		 		}
		 	}

		 }
		// print_r($Data2);
	 // power由7改成6，甲级会员可以显示均价
	 if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
		 $Data2[0]=$pzname."(平均:".round(array_sum($Dataaa)/count($Dataaa))."元)";
		 //$Data2[0]=$arr_pidcc[$nn]."(平均:".round(array_sum($Data2)/(count($Data2)-1)).")";
		$array_avg[$news]=" (平均:".round(array_sum($Dataaa)/count($Dataaa))."元)";
		 }
		 unset($Dataaa);
		 if (is_array($xdate)){
			   $tstime=date("Y-m-d",strtotime($xdate[0]));
			   $tetime=date("Y-m-d",strtotime(end($xdate)));
		 }
		 if ($news==0){	 $chart=array($Data1,$Data2);}else{ array_push($chart,$Data2);}
		 //add by zfy started 2017-07-21
		 if($avgprice){
			 foreach ($avgprice as $ak=>$av){
				array_push($chart,$Data_avg[$av]);
			 }
		 }
		 //add by zfy ended 2017-07-21
	  }
	  //echo "<pre>";print_r($avgprice);
	 //echo "<pre>";print_r($chart);
	 return $chart;

  }

  function arr_average_price_en($price_arr, $class,$stime,$etime){
  	$arrayen=array(
  		"1"=>"Industrial Wire",
  		"2"=>"Drawing Wire",
  		"3"=>"Cold Heading",
  	);
  	$arr_date = array();
  	$isfirst = false;
  	if($price_arr){
  		$arr_date = $price_arr[0];
  	}else {
  		$isfirst = true;
  		$arr_date[] = "";
  	}

  	foreach($class as $classid){
			//update by zfy started 2017/7/21
  		$sql = "select * from average_price where classid='$classid' and dateday>='$stime' and dateday<='$etime'";
			$i = 1;
			//update by zfy ended 2017/7/21
  		$datas = $this->connshpi->GetAll($sql);

  		//$pzname = $this->connshpi->getOne("select name from average_price_class where id='$classid'");
  		$pzname= $arrayen[$classid];
  		$tmpArr = array();
  		foreach($datas as $row){
  			if($isfirst){
  				$arr_date[] = date('y/m/d',strtotime($row['dateday']));
  			}
  			$date_key = date('y/m/d',strtotime($row['dateday']));
  			$tmpArr[$date_key] = $row['aveprice'];

				$i++;
  		}

  		$price_tmp = array();
  		foreach($arr_date as $date){
  			if($date== ""){
					if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
						$pzname=$pzname."(avg:".round(array_sum($tmpArr)/count($tmpArr))."RMB)";
						$price_tmp[] = $pzname;
					}else {
						$price_tmp[] = $pzname;
					}
  			}else {
  				if(isset($tmpArr[$date])) {
  					$price_tmp[] = $tmpArr[$date];
  				}else {
  					$price_tmp[] = "";
  				}
  			}
  		}

			if($isfirst) {
				$price_arr[] = $arr_date;
			}
			$price_arr[] = $price_tmp;


			$isfirst =false;
  	}
  	return $price_arr;
  }

  //平均价格英文版
  function arr_pzggcz_en($pzggcz,$flag,$stime,$etime){
  	global $min,$max,$tabledate,$atosum,$tstime,$tetime,$array_avg;
  	$min=999999;$max=-1;
  	$arrayen=array("1132"=>"High-speed Wire Rod 6.5mmQ235",
               "2013"=>"Rebar 20mmHRB335",
               "7011"=>"Angle Steel 5#Q235",
               "7112"=>"Channel Steel 16#Q235",
               "7311" => "H Beam 200*200Q235",
               "7313" => "H Beam 400*400Q235",
               "3011"=>"Medium Plate 8mmQ235",
               "3012"=>"Medium Plate 20mmQ235",
               "4112"=>"HR Plate/Coil 2.75mm",
               "3112"=>"HR Plate/Coil5.5mm",
               "4212"=>"CR Sheet/Coil 1.0mm",
               "9112"=>"Stainless Steel 304/2B2.0",
               "2244"=>"Structural Steel 50mm45#",
               "2254"=>"Structural Steel 50mm40Cr",
               "4611" => "0.476mm color coated sheet",
               "4412"=>"1.0mm HDG",
               "1103"=>"High-speed Wire Rod 8mmHPB300",
               "2023"=>"Rebar 20mmHRB400",
               "1312"=>"Coiled Rebar 8mmHRB400",
               "4312"=>"CR Sheet/Coil 1.0mm",
               "5011"=>"Electrical Steel 600 0.5mm",
               "5042"=>"Electrical Steel 30Q120",
               "3042"=>"Container Plate Q245R 20mm",
               "3032"=>"Shipbuilding Plate 20mm",
               "6011"=>"Strip 2.75*235mm",
               "7212"=>"I Beam 25#",
               "8023"=>"Welded Pipe 1.5\"*3.0mm",
               "7912"=>"Galvanized Pipe 4\"*3.75mm",
               "8122"=>"Seamless Pipe 108*4.5mm",
               "8124"=>"Seamless Pipe 219*8mm",
               "9132"=>"Stainless Steel 430/2B2.0mm",
               "4992"=>"Stainless Steel 201/2B2.0mm",
               "4824"=>"Stainless Steel 304/NO1 6.0mm",
               "2134"=>"Gear Steel 20CrMo 16-45mm",
               "2164"=>"Bearing Steel GCr15 16-45mm",
               "2411"=>"Pipe Blank 20# 50mm",
             );
  	switch ($flag)
  	{
  		case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
  		case 2: $fields_name="weiprice";$namev="加权价格走势图";break;
  		case 3: $fields_name="apindex";$namev="价格指数走势图";break;
  		case 4: $fields_name="wpindex";$namev="加权指数走势图";break;
  	}
  	foreach ($pzggcz as $news=>$values){
  		$Data2 = array();
  		$Data3 = array();
  		$sql="select * from shpi_svw where vid='".$values."'";
  		$rsp=$this->connshpi->Execute($sql);
  		$pzname=$rsp->fields("vname");
  		/* //===得到品种
  		 $sql="select * from graphvariety where gvid=".substr($values,0,2);
  		$rsp=$this->connshpi->Execute($sql);
  		$pzname=$rsp->fields("varietyname");
  		//===得到材质
  		$sql="select * from graphmaterial where materialid =".substr($values,2,1)." and pvid=".substr($values,0,2);
  		$rsc=$this->connshpi->Execute($sql);
  		$czname=$rsc->fields("materialname");
  		//===得到规格
  		$sql="select * from graphspeci  where sid =".substr($values,3,1)." and pmid=".substr($values,0,2);
  		$rsg=$this->connshpi->Execute($sql);
  		$ggname=$rsg->fields("specification");
  		$bigpz=$pzname.$czname.$ggname;*/
  		//===========
  		$pzggcz_name= $pzname;

  		if ($news==0){
  			$Data1[0]="";
  		}
  		$Data2[0]=$arrayen[$values];
  		$sql="select * from shpi_pzggp where svm_vid=".$values." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
  		//echo $sql;
  		$rs=$this->connshpi->Execute($sql);
  		$i=1;
  		while(!$rs->EOF){
  			if ($news==0){
  				$Data1[$i]=date("y/m/d",strtotime($rs->fields("dateday")));
  				$shunxu[$rs->fields("dateday")] = $i;
  			}
  			$xdate[]=$rs->fields("dateday");
  			$tabledate[$values][$rs->fields("dateday")]=$rs->fields($fields_name);
  			$atosum[$values][$rs->fields("dateday")]=$rs->fields("tosum");
  		   if ($news==0){
				$Data2[$i]=$rs->fields($fields_name);
			}else{
				$Data3[$rs->fields("dateday")]=$rs->fields($fields_name);
			}
  			$Dataaa[$i]=$rs->fields($fields_name);
  			if ($min>$Data2[$i]){
  				$min=$Data2[$i];
  			}
  			if ($max<$Data2[$i]){
  				$max=$Data2[$i];
  			}
  			$i++;
  			$rs->MoveNext();
  		}
  		//高线8mmHPB300  历史数据不足处理
  		if ($news!=0){
  			foreach ($shunxu as $k=>$v){
  				if(empty($Data3[$k])){
  					$Data2[$v]="";
  				}else{
  					$Data2[$v]= $Data3[$k];
  				}
  			}

  		}
  		// power由7改成6，甲级会员可以显示均价
  	 if((isset($_SESSION['mid'])&&$_SESSION['mid']==1) || (isset($_SESSION['maxpower'])&&$_SESSION['maxpower']>=6) || (isset($_SESSION['memberid'])&&$_SESSION['memberid']==1) || (isset($_SESSION['enpower'])&&$_SESSION['enpower']>=6)){
  		 $Data2[0]=$arrayen[$values]."(AVG:".round(array_sum($Dataaa)/count($Dataaa))."RMB)";
  		 //$Data2[0]=$arr_pidcc[$nn]."(平均:".round(array_sum($Data2)/(count($Data2)-1)).")";
  		 //$array_avg[$news]=" (期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
  	 }
  	 unset($Dataaa);
  	 if (is_array($xdate)){
  	 	$tstime=date("Y-m-d",strtotime($xdate[0]));
  	 	$tetime=date("Y-m-d",strtotime(end($xdate)));
  	 }
  	 if ($news==0){
  	 	$chart=array($Data1,$Data2);
  	 }else{ array_push($chart,$Data2);
  	 }
  	}
  	//print_r($chart);
  	return $chart;

  }


  //平均价格英文版==炉料/有色
  function arr_pzggyl_en($pzggcz,$flag,$stime,$etime){
  	global $min,$max,$tabledate,$atosum,$tstime,$tetime,$array_avg;
  	$min=999999;$max=-1;
  	$arrayen=array(
		"448610"=>"66% concentrates (Hebei Tangshan)",
		"458610"=>"66% concentrates (Liaoning Benxi)",
		"428620"=>"63% pellet (Hebei Chengde)",
		"188610"=>"63.5% Indian fines (Qingdao port)",
		"188611"=>"62% Australian fines (Qingdao port)",
		"188619"=>"65% Brazilian fines (Qingdao port)",
		"C18630"=>"63.5% fines import (India)",
		"C18690"=>"62% fines import (Australia)",
                "B98670" => "58% fines import (Australia)",
		"C18620"=>"65% fines import (Brazil)",
		"118210"=>"6-10mm scrap (Jiangsu Zhangjiagang)",
		"E98230"=>"HMS1 import (U.S.A)",
		"D98220"=>"H2 scrap import (Japan)",
		"168510"=>"L08-10 pig iron (Jiangsu Xuzhou)",
		"678411"=>"Q235 billet (Hebei Tangshan)",
		"678421"=>"20MnSi billet (Hebei Tangshan)",
		"438311"=>"I grade coke (Shanxi Taiyuan)",
		"788311"=>"I grade coke (Hebei Xingtai)",
		"F38310"=>"II grade coke (Shandong Zaozhuang)",
		"S26310"=>"premium coking coal (Shanxi Jiexiu)",
		"T26312"=>"1/3 coking coal (Shanxi Lvliang)",
		"996320"=>"Q5500 thermal coal (Qinhuangdao)",
		"578710"=>"FeSi75-B ferrosilicon (Gansu)",
		"558810"=>"FeMn68C7.0 ferromanganese (Yunnan)",
		"539610"=>"FeCr55C1000 ferrochrome (Sichuan)",
		"B99530"=>"48% Mn ore fines import (Australia)",
		"N99630"=>"42% Mn ore lump import (Turkey)",
		"P99610"=>"42% Mn ore fines import (South Africa)",
		"076210"=>"electrolyzed copper (Shanghai)",
		"076220"=>"aluminum ingot (Shanghai)",
		"076230"=>"lead ingot (Shanghai)",
		"076240"=>"zinc ingot (Shanghai)",
		"076250"=>"tin ingot (Shanghai)",
		"076260"=>"electrolyzed nickel (Shanghai)",
		"076270"=>"gold (Shanghai)",
		"076280"=>"silver (Shanghai)"
	);

  	$daiweis=array(
		"448610"=>"0",
		"458610"=>"0",
		"428620"=>"0",
		"188610"=>"0",
		"188611"=>"0",
		"188619"=>"0",
		"C18630"=>"1",
		"C18690"=>"1",
                "B98670"=>"1",
		"C18620"=>"1",
		"118210"=>"0",
		"E98230"=>"1",
		"D98220"=>"1",
		"168510"=>"0",
		"678411"=>"0",
		"678421"=>"0",
		"438311"=>"0",
		"788311"=>"0",
		"F38310"=>"0",
		"S26310"=>"0",
		"T26312"=>"0",
		"996320"=>"0",
		"578710"=>"0",
		"558810"=>"0",
		"539610"=>"0",
		"B99530"=>"1",
		"N99630"=>"1",
		"P99610"=>"1",
		"076210"=>"0",
		"076220"=>"0",
		"076230"=>"0",
		"076240"=>"0",
		"076250"=>"0",
		"076260"=>"0",
		"076270"=>"0",
		"076280"=>"0"
	);

  	switch ($flag){
  		case 1: $fields_name="price";break;
  	}

	$k=0;
  	foreach ($pzggcz as $news=>$values){

  		if ($news==0){
  			$Data1[0]="";
  		}
  		//$Data2[0]=$arrayen[$values];
		$NewData2[$k][0]=$arrayen[$values];
  		$sql="select * from marketconditions where topicture='".$values."' and (mconmanagedate>='".$stime." 00:00:00' and mconmanagedate<='".$etime." 23:59:59') order by mconmanagedate asc";
  		//echo $sql."<br>";
		$array = $this->connshpi->GetAll($sql);

		$j=1;
		$NewData1[0]="";
		foreach($array as $v){
			$NewData1[]=date("y/m/d",strtotime($v['mconmanagedate']));
			$NewData11[]=date("y-m-d",strtotime($v['mconmanagedate']));

			$day = date("y-m-d",strtotime($v['mconmanagedate']));
			$NewData2temp[$k][$j]=explode("-",$v[$fields_name]);
			$NewData2[$k][$day] =  array_sum($NewData2temp[$k][$j])/count($NewData2temp[$k][$j]);

			$j++;
		}

 		// power由7改成6，甲级会员可以显示均价
		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6 || $_SESSION['memberid']==1 || $_SESSION['enpower']>=6){

			if($daiweis[$values]==0){
				$daiwei="RMB";
			}else{
				$daiwei="USD";
			}

			$NewData2[$k][0]=$arrayen[$values]."   (AVG:".round(array_sum($NewData2[$k])/(count($NewData2[$k])-1))."{$daiwei})";
		}

		$k++;
  	}

	if (is_array($time1)){
		$tstime=date("Y-m-d",strtotime($time1));
		$tetime=date("Y-m-d",strtotime(end($time1)));
	}

	$time1 = array_unique($NewData1);
	$time11 = array_flip(array_unique($NewData11));

	foreach($time11 as $k=>$v){
		//$time[$k] = 0;
		$time[$k] = "";
	}

	foreach($NewData2 as $kk=>$vv){
		$NewData[$kk] = array_merge($time,$vv);
	}

	$chart=array($time1);

	foreach($NewData as $kk=>$vv){
		$m=1;
		foreach($vv as $kkk=>$vvv){
			if($kkk==0){
				$newArray[$kk][0]= $vvv;
			}else{
				$newArray[$kk][$m]= $vvv;
			}
			ksort($newArray[$kk]);
			$m++;
		}
	}


/* 	foreach($newArray as $ky=>$va){
		krsort($va);

		foreach($va as $kyy=>$vaa){

			if($vaa===0){
				$num = $kyy +1;
				if($va[$num]===0){
					$num2 =  $num +1;
					$va[$num] = $va[$num2];
				}
				$LastData[$ky][$kyy] = $va[$num];
			}else{
				$LastData[$ky][$kyy] = $vaa;
			}

		}
	}

	foreach($LastData as $kk=>$vv){
		ksort($vv);
		array_push($chart,array_values($vv));
	}

	*/


	foreach($newArray as $kk=>$vv){
		ksort($vv);
		array_push($chart,array_values($vv));
	}

	// echo "<pre>";
	// print_r($chart);
	// exit;
  	return $chart;

  }



  /*
     ;flash 传值图片走势图形
  */
  function show_flash($Data,$min,$max,$flag=0,$row=0, $isAvg = false ){
    global $SHPIIMAGE,$maminvaluestr,$array_avg;
    //echo $array_avg;
	if ($SHPIIMAGE==""){
	   $SHPIIMAGE="gglg.jpg";
	}
    if ($flag==0){
		$chart['chart_data']=$Data;$skip=5; $yy=50;
	}else{
	    //$chart['chart_data']=$Data;
	     $num=count($Data);
        if( $num >= 2 && $isAvg == true ){
		    if( !empty( $Data[1] ) ){
			   $Data[1][0] = $Data[1][0] . "(平均:" . round( array_sum( $Data[1] ) / ( count( $Data[1] ) - 1 ) ) . ")";
			}
			if( !empty( $Data[2] ) ){
			   $Data[2][0] = $Data[2][0] . "(平均:" . round( array_sum( $Data[2] ) / ( count( $Data[2] ) - 1 ) ) . ")";
			}
			if( !empty( $Data[3] ) ){
			    $Data[3][0] = $Data[3][0] . "(平均:" . round( array_sum( $Data[3] ) / ( count( $Data[3] ) - 1 ) ) . ")";
			}
			if( !empty( $Data[4] ) ){
			    $Data[4][0] = $Data[4][0] . "(平均:" . round( array_sum( $Data[4] ) / ( count( $Data[4] ) - 1 ) ) . ")";
			}
		    if( !empty( $Data[5] ) ){
			    $Data[5][0] = $Data[5][0] . "(平均:" . round( array_sum( $Data[5] ) / ( count( $Data[5] ) - 1 ) ) . ")";
			}
		}


		if ($num==3){$yy=55; $ly=7;}else{$yy=50;
		if ($num==4){$ly=1;}
		if ($num==2){$ly=15;}}

		if ($row==1){
		  $lstr="horizontal";
		  $yy=50;$ly=15;
		}else{
		  $lstr="vertical";
		}
		//echo $lstr;
		//echo $num."num=";
		$num1=count($Data[0]);
		$skip=round($num1/10);
		$rr=""; $wdcs="";
		for ($x=0;$x<$num;$x++){
			foreach ($Data[$x] as $news=>$name1){
			   if ($news==0){$rr.="var r".$x."='".$name1.$maminvaluestr.$array_avg[$x-1];}else{$rr.=";".$name1;}
			}
			$rr.="';\n";
			$wdcs.="window.document.charts.SetVariable('r".$x."', r".$x.");";
		}
	    $script='<SCRIPT>
		function doPassVar(){
		    '.$rr.$wdcs.'
			window.document.charts.SetVariable("rows",'.$num.');
			window.document.charts.SetVariable("cols",'.$num1.');
			}
			//-->
			</SCRIPT> ';
			echo $script;
	}
     $chart[ 'axis_category' ] = array ( 'size'=>14, 'color'=>"000000", 'alpha'=>0, 'font'=>"arial", 'size'=>10, 'bold'=>false, 'skip'=>$skip,'orientation'=>"vertical" );
      //以上指： 左标(X 轴坐标)
	  #orientation:"horizontal", "diagonal_up", "diagonal_down", "vertical_up", and "vertical_down." The default value is "horizontal"
	$chart[ 'axis_value' ] = array (  'font'=>"arial", 'bold'=>true, 'size'=>10, 'color'=>"000000", 'alpha'=>100, 'steps'=>10, 'prefix'=>"", 'suffix'=>"", 'decimals'=>0, 'separator'=>"", 'show_min'=>true, 'min' =>$min+2, 'max'  =>$max+2,  );
	  //以上Y坐标
	  $uurrll="../../images/".$SHPIIMAGE;
	  $chart [ 'canvas_image' ] = array (   'url'  => $uurrll,'alpha'  =>  70);
      $chart[ 'canvas_bg' ] = array ( 'width'=>600, 'height'=>300, 'color'=>"ffe075" );
$chart[ 'chart_bg' ] = array ( 'positive_color'=>"ffffff", 'positive_alpha'=>70, 'negative_color'=>"ff0000",  'negative_alpha'=>10 );
$chart[ 'chart_border' ] =array ('color'=>"5e5e5e",'top_thickness'=>1,'bottom_thickness'=>1,'left_thickness'=>1,'right_thickness'=>1 );
	  //背景
$chart[ 'chart_grid' ] = array ( 'alpha'=>10, 'color'=>"000000", 'horizontal_thickness'=> 1, 'vertical_thickness'=> 1, 'horizontal_skip'=>0, 'vertical_skip'=>0 );
//以上是制图格式
$chart[ 'chart_line' ] = array ( 'line_thickness'=>2, 'point_shape'=>"none", 'fill_shape'=>false );
//以上是线的宽度
$chart[ 'chart_rect' ] = array ( 'x'=>45, 'y'=>$yy, 'width'=>535, 'height'=>200 );
$chart[ 'chart_type' ] = "Line";
	 //以上指数据: 三个数组： 数组1：为下标值 (x 坐标)  //数组2 :坐标值数据
	 $chart [ 'legend_rect' ] = array (
	                                 'x'=>25,
									 'y'=>$ly,
                                     'width'   =>  580,
                                     'height'  =>  5,
                                     'margin'  =>  2
                                 );
 	        //
	/*$chart [ 'legend_bg' ] = array (   'bg_color'          =>  "939393",
									   'bg_alpha'          =>  100,
									   'border_color'      =>  "939393",
									   'border_alpha'      =>  100,
									   'border_thickness'  =>  0
								   );*/
	$chart [ 'legend_label' ] = array (   'layout'  =>  $lstr,
										  'bullet'  =>  "line",
										  'font'    =>  "Arial",
										  'bold'    =>  true,
										  'size'    =>  12,
										  'color'   =>  "5e5e5e",
										  'alpha'   =>  90
									  );
	$chart[ 'series_color' ] = array (  "ff6000","0026ff","00ff68" );
	return $chart;
    //$f_str=DrawChart ( $chart );
  }

  function show_flash_1($Data,$min,$max,$flag=0,$row=0, $isAvg = false ){

// echo "<pre>";
// print_r($Data);

    global $SHPIIMAGE,$maminvaluestr,$array_avg;
	if ($SHPIIMAGE==""){
	   $SHPIIMAGE="gglg.jpg";
	}



    if ($flag==0){
		$chart['chart_data']=$Data;$skip=5; $yy=50;
	}else{
	    $num=count($Data);
        if( $num >= 2 && $isAvg == true ){
		    if( !empty( $Data[1] ) ){
			   $Data[1][0] = $Data[1][0] . "(平均:" . round( array_sum( $Data[1] ) / ( count( $Data[1] ) - 1 ) ) . ")";
			}
			if( !empty( $Data[2] ) ){
			   $Data[2][0] = $Data[2][0] . "(平均:" . round( array_sum( $Data[2] ) / ( count( $Data[2] ) - 1 ) ) . ")";
			}
			if( !empty( $Data[3] ) ){
			    $Data[3][0] = $Data[3][0] . "(平均:" . round( array_sum( $Data[3] ) / ( count( $Data[3] ) - 1 ) ) . ")";
			}
			if( !empty( $Data[4] ) ){
			    $Data[4][0] = $Data[4][0] . "(平均:" . round( array_sum( $Data[4] ) / ( count( $Data[4] ) - 1 ) ) . ")";
			}
		    if( !empty( $Data[5] ) ){
			    $Data[5][0] = $Data[5][0] . "(平均:" . round( array_sum( $Data[5] ) / ( count( $Data[5] ) - 1 ) ) . ")";
			}
		}


		if ($num==3){$yy=55; $ly=7;}else{$yy=50;
		if ($num==4){$ly=1;}
		if ($num==2){$ly=15;}}

		if ($row==1){
		  $lstr="horizontal";
		  $yy=50;$ly=15;
		}else{
		  $lstr="vertical";
		}

		$num1=count($Data[0]);
		$skip=round($num1/10);

// echo $num1."--".$skip."<br>";

		$rr=""; $wdcs="";
		for ($x=0;$x<$num;$x++){
			foreach ($Data[$x] as $news=>$name1){
			   if ($news==0){$rr.="var r".$x."='".$name1.$maminvaluestr.$array_avg[$x-1];}else{$rr.=";".$name1;}
			}
			$rr.="';\n";
			$wdcs.="window.document.charts1.SetVariable('r".$x."', r".$x.");";
		}

	    $script1='<SCRIPT>
		function doPassVar1(){
		    '.$rr.$wdcs.'
			window.document.charts1.SetVariable("rows",'.$num.');
			window.document.charts1.SetVariable("cols",'.$num1.');
			}
			//-->
			</SCRIPT> ';
			echo $script1;

	}
     $chart[ 'axis_category' ] = array ( 'size'=>14, 'color'=>"000000", 'alpha'=>0, 'font'=>"arial", 'size'=>10, 'bold'=>false, 'skip'=>$skip,'orientation'=>"vertical" );
      //以上指： 左标(X 轴坐标)
	  #orientation:"horizontal", "diagonal_up", "diagonal_down", "vertical_up", and "vertical_down." The default value is "horizontal"
	$chart[ 'axis_value' ] = array (  'font'=>"arial", 'bold'=>true, 'size'=>10, 'color'=>"000000", 'alpha'=>100, 'steps'=>10, 'prefix'=>"", 'suffix'=>"", 'decimals'=>0, 'separator'=>"", 'show_min'=>true, 'min' =>$min+2, 'max'  =>$max+2,  );
	  //以上Y坐标
	  $uurrll="../../images/".$SHPIIMAGE;
	  $chart [ 'canvas_image' ] = array (   'url'  => $uurrll,'alpha'  =>  70);
      $chart[ 'canvas_bg' ] = array ( 'width'=>600, 'height'=>300, 'color'=>"ffe075" );
$chart[ 'chart_bg' ] = array ( 'positive_color'=>"ffffff", 'positive_alpha'=>70, 'negative_color'=>"ff0000",  'negative_alpha'=>10 );
$chart[ 'chart_border' ] =array ('color'=>"5e5e5e",'top_thickness'=>1,'bottom_thickness'=>1,'left_thickness'=>1,'right_thickness'=>1 );
	  //背景
$chart[ 'chart_grid' ] = array ( 'alpha'=>10, 'color'=>"000000", 'horizontal_thickness'=> 1, 'vertical_thickness'=> 1, 'horizontal_skip'=>0, 'vertical_skip'=>0 );
//以上是制图格式
$chart[ 'chart_line' ] = array ( 'line_thickness'=>2, 'point_shape'=>"none", 'fill_shape'=>false );
//以上是线的宽度
$chart[ 'chart_rect' ] = array ( 'x'=>45, 'y'=>$yy, 'width'=>535, 'height'=>200 );
$chart[ 'chart_type' ] = "Line";
	 //以上指数据: 三个数组： 数组1：为下标值 (x 坐标)  //数组2 :坐标值数据
	 $chart [ 'legend_rect' ] = array (
	                                 'x'=>25,
									 'y'=>$ly,
                                     'width'   =>  580,
                                     'height'  =>  5,
                                     'margin'  =>  2
                                 );
 	        //
	/*$chart [ 'legend_bg' ] = array (   'bg_color'          =>  "939393",
									   'bg_alpha'          =>  100,
									   'border_color'      =>  "939393",
									   'border_alpha'      =>  100,
									   'border_thickness'  =>  0
								   );*/
	$chart [ 'legend_label' ] = array (   'layout'  =>  $lstr,
										  'bullet'  =>  "line",
										  'font'    =>  "Arial",
										  'bold'    =>  true,
										  'size'    =>  12,
										  'color'   =>  "5e5e5e",
										  'alpha'   =>  90
									  );
	$chart[ 'series_color' ] = array (  "ff6000","0026ff","00ff68" );
	return $chart;

  }

  /*
    ;品种，材质显示
  */
  function show_checkpz($values,$flag=0,$num=7){
     $str=' <table>'; $a=0;
	 $bv="abc,".$values;
	 $avalues = explode(",", $bv);
	 //print_r($avalues);
     if ($flag==0){
	 $td=$num;
	    $sql="select * from shpi_bvw  ";
		$rs=$this->connshpi->Execute($sql);
		while (!$rs->EOF){
		      $ccid=$rs->fields("id");
		     if (array_search($ccid,$avalues)!=NULL){$checked=" checked";}else{$checked=" ";}
			 $bname="<input type='checkbox' name='pzall[]' value='".$ccid."' ".$checked."> ".$rs->fields("bname");
				if ($a==0){$str.='<tr><td height=22> '.$bname.'</td>';}
				   else{
					   if ($a%$td){   $str.='<td><td height=22> '.$bname.'</td>';}
					   else     {   $str.='</tr><tr><td height=22> '.$bname.'</td>';}
				   }
			     $a++;

		  $rs->MoveNext();
		}
	 }else{ $td=$num;
	    $sql="select * from shpi_svw ";
		$rs=$this->connshpi->Execute($sql);
		while (!$rs->EOF){
		   //===得到品种
			 /*$sql="select * from graphvariety where gvid=".$rs->fields("ProductID");
			 $rsp=$this->connshpi->Execute($sql);
			 $pzname=$rsp->fields("varietyname");
			 //===得到材质
			 $sql="select * from graphmaterial where materialid =".$rs->fields("Material");
			 $rsc=$this->connshpi->Execute($sql);
			 $czname=$rsc->fields("materialname");
			 //===得到规格
			 $sql="select * from graphspeci  where sid =".$rs->fields("Specification");
			 $rsg=$this->connshpi->Execute($sql);
			 $ggname=$rsg->fields("specification");
			 $bigpz=$pzname."　".$czname."　".$ggname;*/
			 $bigpz=$rs->fields("vname");
			 $bigpzvalue=$rs->fields("vid");
			 //===========
			 if (array_search($ccid,$avalues)!=NULL){$checked=" checked";}else{$checked=" ";}
		     //if (strstr($values,$bigpzvalue)){$checked=" checked";}else{$checked=" ";}
		  $bname="<input type='checkbox' name='pzall[]' value='".$bigpzvalue."' ".$checked."> ".$bigpz;
		  if ($a==0){$str.='<tr><td height=22> '.$bname.'</td>';}
				   else{
					   if ($a%$td){   $str.='<td><td height=22> '.$bname.'</td>';}
					   else     {   $str.='</tr><tr><td height=22> '.$bname.'</td>';}
				   }
			     $a++;
		  $rs->MoveNext();
		}
	 }
	 $str.="</table>";
	 return $str;
  }
   function show_bpz($values,$flag=0){
     $str=' <table>'; $a=0;
	 $td=7;
	    $sql="select * from shpi_bxw ";
		$rs=$this->connshpi->Execute($sql);
		while (!$rs->EOF){
		      $ccid=$rs->fields("id");
		     if (strstr($values,$ccid)){$checked=" checked";}else{$checked=" ";}
			 $bname="<input type='checkbox' name='pzall[]' value='".$ccid."' ".$checked."> ".$rs->fields("xname");
				if ($a==0){$str.='<tr><td height=22> '.$bname.'</td>';}
				   else{
					   if ($a%$td){   $str.='<td><td height=22> '.$bname.'</td>';}
					   else     {   $str.='</tr><tr><td height=22> '.$bname.'</td>';}
				   }
			     $a++;

		  $rs->MoveNext();
		}
	 $str.="</table>";
	 return $str;
  }
  /*
  ;时间参数
  */
  function date_options($arr_name,$date,$flag=0){
     $year_str="<select type='select' name='".$arr_name["year"]."' >";
 	$i_temp=date("Y")-2000+1;
	 for($i=0;$i<$i_temp;$i++){
	    $y=date("Y")+1;$ys=$y-$i;
		if ($ys==date("Y",strtotime($date))){$sel="selected";}else{$sel="";}
		$year_str.="<option value='".$ys."' ".$sel.">".$ys."年</option>";
	}
	$year_str.="</select>";

	$mon_str="<select type='select' name='".$arr_name["mon"]."' >";
	for($i=1;$i<13;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("m",strtotime($date))){$sel="selected";}else{$sel="";}
		$mon_str.="<option value='".$is."' ".$sel.">".$is."月</option>";
	}
	$mon_str.="</select>";

	$day_str="<select type='select' name='".$arr_name["day"]."' >";
	for($i=1;$i<32;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("d",strtotime($date))){$sel="selected";}else{$sel="";}
		$day_str.="<option value='".$is."' ".$sel.">".$is."日</option>";
	}
	$day_str.="</select>";
    $str=$year_str.$mon_str.$day_str;
	return $str;
	}




function date_options_en($arr_name,$date,$flag=0){
     $year_str="<select type='select' name='".$arr_name["year"]."' >";
	 for($i=0;$i<=2;$i++){
	    $y=date("Y")+1;$ys=$y-$i;
		if ($ys==date("Y",strtotime($date))){$sel="selected";}else{$sel="";}
		$year_str.="<option value='".$ys."' ".$sel.">".$ys."</option>";
	}
	$year_str.="</select>";

	$mon_str="<select type='select' name='".$arr_name["mon"]."' >";
	for($i=1;$i<13;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("m",strtotime($date))){$sel="selected";}else{$sel="";}
		$mon_str.="<option value='".$is."' ".$sel.">".$is."</option>";
	}
	$mon_str.="</select>";

	$day_str="<select type='select' name='".$arr_name["day"]."' >";
	for($i=1;$i<32;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("d",strtotime($date))){$sel="selected";}else{$sel="";}
		$day_str.="<option value='".$is."' ".$sel.">".$is."</option>";
	}
	$day_str.="</select>";
    $str=$year_str.$mon_str.$day_str;
	return $str;
  }
  function date_options_en2($arr_name,$date,$lstime){
	$flag=0;
	if($lstime != 0){

		$z=date('Y')-$lstime;
	}
	else{
		$z=1;
	} //echo $y;
	//$y=1;
	$year_str="<select type='select' name='".$arr_name["year"]."' >";
	for($i=0;$i<=$z+1;$i++){
	   $y=date("Y")+1;$ys=$y-$i;
	   if ($ys==date("Y",strtotime($date))){$sel="selected";}else{$sel="";}
	   $year_str.="<option value='".$ys."' ".$sel.">".$ys."</option>";
   }
   $year_str.="</select>";

   $mon_str="<select type='select' name='".$arr_name["mon"]."' >";
   for($i=1;$i<13;$i++){
	   if ($i<10){$is="0".$i;}else{$is=$i;}
	   if ($flag==1){$is=$i;}
	   if ($is==date("m",strtotime($date))){$sel="selected";}else{$sel="";}
	   $mon_str.="<option value='".$is."' ".$sel.">".$is."</option>";
   }
   $mon_str.="</select>";

   $day_str="<select type='select' name='".$arr_name["day"]."' >";
   for($i=1;$i<32;$i++){
	   if ($i<10){$is="0".$i;}else{$is=$i;}
	   if ($flag==1){$is=$i;}
	   if ($is==date("d",strtotime($date))){$sel="selected";}else{$sel="";}
	   $day_str.="<option value='".$is."' ".$sel.">".$is."</option>";
   }
   $day_str.="</select>";
   $str=$year_str.$mon_str.$day_str;
   return $str;
 }






	// Added for shpi by Zhu Dahua started  2015/08/31
	function date_options_en3($arr_name, $date, $flag = 0)
	{
		$year_str = "<select type='select' name='" . $arr_name ["year"] . "' >";
		$i_temp = Date ( Y ) - 2000 + 1;
		for($i = 0; $i < $i_temp; $i ++)
		{
			$y = date ( "Y" ) + 1;
			$ys = $y - $i;
			if ($ys == date ( "Y", strtotime ( $date ) ))
			{
				$sel = "selected";
			}
			else
			{
				$sel = "";
			}
			$year_str .= "<option value='" . $ys . "' " . $sel . ">" . $ys . "</option>";
		}
		$year_str .= "</select>";

		$mon_str = "<select type='select' name='" . $arr_name ["mon"] . "' >";
		for($i = 1; $i < 13; $i ++)
		{
			if ($i < 10)
			{
				$is = "0" . $i;
			}
			else
			{
				$is = $i;
			}
			if ($flag == 1)
			{
				$is = $i;
			}
			if ($is == date ( "m", strtotime ( $date ) ))
			{
				$sel = "selected";
			}
			else
			{
				$sel = "";
			}
			$mon_str .= "<option value='" . $is . "' " . $sel . ">" . $is . "</option>";
		}
		$mon_str .= "</select>";

		$day_str = "<select type='select' name='" . $arr_name ["day"] . "' >";
		for($i = 1; $i < 32; $i ++)
		{
			if ($i < 10)
			{
				$is = "0" . $i;
			}
			else
			{
				$is = $i;
			}
			if ($flag == 1)
			{
				$is = $i;
			}
			if ($is == date ( "d", strtotime ( $date ) ))
			{
				$sel = "selected";
			}
			else
			{
				$sel = "";
			}
			$day_str .= "<option value='" . $is . "' " . $sel . ">" . $is . "</option>";
		}
		$day_str .= "</select>";
		$str = $year_str . $mon_str . $day_str;
		return $str;
	}
	// Added for shpi by Zhu Dahua ended  2015/08/31
  function times_str($stime,$etime,$flag=0){
    $year_str="<select type='select' name='year1' >";
	for($i=0;$i<5;$i++){
	    $y=date("Y");$ys=$y-$i;
		if ($ys==date("Y",strtotime($stime))){$sel="selected";}else{$sel="";}
		$year_str.="<option value='".$ys."' ".$sel.">".$ys."年</option>";
	}
	$year_str.="</select>";

	$mon_str="<select type='select' name='mon1' >";
	for($i=1;$i<13;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("m",strtotime($stime))){$sel="selected";}else{$sel="";}
		$mon_str.="<option value='".$is."' ".$sel.">".$is."月</option>";
	}
	$mon_str.="</select>";

	$day_str="<select type='select' name='day1' >";
	for($i=1;$i<32;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("d",strtotime($stime))){$sel="selected";}else{$sel="";}
		$day_str.="<option value='".$is."' ".$sel.">".$is."日</option>";
	}
	$day_str.="</select>";

	$year_str1="<select type='select' name='year2' >";
	for($i=0;$i<4;$i++){
	    $y=date("Y");$ys=$y-$i;
		if ($ys==date("Y",strtotime($etime))){$sel="selected";}else{$sel="";}
		$year_str1.="<option value='".$ys."' ".$sel.">".$ys."年</option>";
	}
	$year_str1.="</select>";

	$mon_str1="<select type='select' name='mon2' >";
	for($i=1;$i<13;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("m",strtotime($etime))){$sel="selected";}else{$sel="";}
		$mon_str1.="<option value='".$is."' ".$sel.">".$is."月</option>";
	}
	$mon_str1.="</select>";

	$day_str1="<select type='select' name='day2' >";
	for($i=1;$i<32;$i++){
	    if ($i<10){$is="0".$i;}else{$is=$i;}
		if ($flag==1){$is=$i;}
		if ($is==date("d",strtotime($stime))){$sel="selected";}else{$sel="";}
		$day_str1.="<option value='".$is."' ".$sel.">".$is."日</option>";
	}
	$day_str1.="</select>";
	// $str.="年:<select type='select' name='year1' > ---- ";
	 //$str.="开始时间:<input type='text' name='stime' > ---- ";
	 $str.=$year_str.$mon_str.$day_str."---".$year_str1.$mon_str1.$day_str1;

	//$str.="开始时间:<input type='text' name='stime' > ---- ";
	//$str.="结束时间:<input type='text' name='etime' >";
	 return $str;
  }
  function up_ggcz($flag,$id){

         $sql="select * from shpi_svw where id='".$id."'  ";
	   $rs=$this->connshpi->Execute($sql);
	   if (!$rs->EOF){
	      $arr_value[0]=$rs->fields("vname");	   $arr_value[1]=$rs->fields("vid");
		  $arr_value[2]=$rs->fields("vclass");  $arr_value[3]=$rs->fields("weight");
	   }
       $str='<form action="" name="form2"><table width="98%" align="center" border=1 cellspacing=0 cellpadding=1 bordercolorlight=#000000 bordercolordark=#FFFFFF>';
	   $str.='<tr><td>名称</td><td><input type="" value="'.$arr_value[0].'" name="vname" ></td></tr>';
	    $str.='<tr><td>价格id号</td><td><input type="" value="'.$arr_value[1].'" name="vid" ></td></tr>';
		$str.='<tr><td>类别</td><td><input type="" value="'.$arr_value[2].'" name="vclass" ></td></tr>';
		$str.='<tr><td>权重</td><td><input type="" value="'.$arr_value[3].'" name="weight" ></td></tr>';
	   $str.='<tr><td colspan="2"><input type="submit" value="修改" name="submit"><input type="hidden" value="'.$id.'" name="upid"> </td></tr></table></form>';
    return $str;
  }

 /**
 * verify user & passwrod. if OK return true,else return false
 * 28个城市汇总品种价格
 * @param array  $arr_pzid the variety id
      #$arr_pzid["pzid"]=array("priceid",);
 * @param string $arr_time the times value
      #$arr_time["oneday"]
	  #$arr_time["stime"]
	  #$arr_time["etime"]
 * @return bool
 */
 function  count_28price($arr_pzid,$arr_time){
      #$arr_time["oneday"]
	  #$arr_time["stime"]
	  #$arr_time["etime"]
	  $pzsum=count($arr_pzid);
	  if ($arr_time["oneday"]!=""){
	     $timesql=" and managedate > '".$arr_time["oneday"]." 00:00' and managedate < '".$arr_time["oneday"]." 23:59'";
		 $date=$arr_time["oneday"];
	  }
      #根据品种得到价格

	 foreach ($arr_pzid as $pid=>$arr_pzidv){
		$fg=0;
		foreach ($arr_pzidv as $keys=>$values){
		   if ($keys==0){
		      $pzidsql="('".$values."'";
		   }else{
		      $pzidsql.=",'".$values."'";
		   }
		}
		$pzidsql.=")";
		//==平均价格
		$avgprice=0;
		$avgsql[$pid]="SELECT avg(price) as avgprice,sum( price * weight ) / sum( weight ) AS cwavgprice,sum( weight ) as twei 
			FROM marketconditions AS C
			LEFT JOIN marketrecord AS M ON C.marketrecordid = M.id
			LEFT JOIN city ON RIGHT( M.cityid, 4 ) = city.cityid
			WHERE C.topicture
			IN ".$pzidsql." ".$timesql."";
		$rs=$this->connshpi->Execute($avgsql[$pid]);
		if (!$rs->EOF){
		  $avgprice=$rs->fields("avgprice");
		  $cwavgprice=$rs->fields("cwavgprice"); $twei=$rs->fields("twei");
		   //========插入数据库
			if (($avgprice!=0)&&(($twei>26)&&(($twei<35)))){
				$insertsql="insert into city28pr(pzid,AveragePrice,WeightPrice,`Date`) 
						  values ('".$pid."','".$avgprice."','".$cwavgprice."','".$date."')";
				//echo $insertsql;
				$this->connshpi->Execute($insertsql);
			}
		 }

		//$arr_pzdate[$pid]=array($date,$avgprice,$cwavgprice);
		//========
	  }
	  return true;
	  //print_r($arr_pzdate);
	 /* #查询数据库
	  $cwei=$this->get_weight();
	  foreach ($sql as $ppid=>$sqlv){
	     $rs=$this->connshpi->Execute($sqlv);
		 $total=0;$totalprice=0;$totalcwprice=0;
		 while (!$rs->EOF){
		   $price=$rs->fields("price"); $cityid=$rs->fields("cityid");
		   $cid=substr($cityid,-4);
		   //echo  $rs->fields("cityid")."::".$rs->fields("price")."  ||";
		   //平均价格
		   $totalprice+=$price;
		   $totalcwprice+=$price*$cwei[$cid];
		   $totalcwei+=$cwei[$cid];
		   //=======
		   $total++;
		   $rs->MoveNext();
		 }
	     #得到平均价格
	     $avgprice=round($totalprice/$total,4);
		 echo $total."||";
	     #得到加权价格
	     $cwavgprice=round($totalcwprice/$totalcwei,4);
	     #得到对应日期
		 $date=$arr_time["oneday"];
		 $arr_pzdate[$ppid]=array($date,$avgprice,$cwavgprice);
		 #插入相应数据库
		 //echo "<br><br>";
	  }
	  print_r($arr_pzdate);*/
 }
  /**
 * verify user & passwrod. if OK return true,else return false
 * 28个城市汇总品种价格
 * @param array  $arr_pzid the variety id
      #$arr_pzid["pzid"]=array("priceid",);
 * @param string $arr_time the times value
      #$arr_time["oneday"]
	  #$arr_time["stime"]
	  #$arr_time["etime"]
 * @return bool
 */
 function get_weight(){
   global $city_keys;
	$sql="select `cityid`,`weight` from `city` where `$city_keys`<>''";
	$rs=$this->connshpi->Execute($sql);
	while (!$rs->EOF){
	    $cwei[$rs->fields("cityid")]=$rs->fields("weight");
		$rs->MoveNext();
    }
    return $cwei;
 }
 //======时间的28个城市汇总
  function hzcity28($stime,$etime,$pz,$flag){
     global $min,$max;
	 $min=999999;$max=-1;
     $bigpz=$this->arr_pzbig(1);
	 switch ($flag)
		{
		   case 1: $fields_name=array("AveragePrice");$namev="平均价格走势图";break;
		   case 2: $fields_name=array("WeightPrice");$namev="加权价格走势图";break;
		   case 3: $fields_name=array("AveragePrice","WeightPrice");$namev=array("平均价格走势图","加权价格走势图");break;
		   //case 4: $fields_name="WeightIndex";$namev="加权指数走势图";break;
		}
	//=========
	if ($flag<3){
		 foreach ($pz as $news=>$values){
			 $pz_name= $bigpz[$values]." [$stime] - [$etime] $namev";
			 if ($news==0){	 $Data1[0]=""; }
			 $Data2[0]=$pz_name;
			 $sql="select * from city28pr where pzid =$values and (Date>'".$stime."' and Date<'".$etime."') order by Date asc";
			 $rs=$this->connshpi->Execute($sql);
			 $i=1;
			 while(!$rs->EOF){
				if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("Date"))); }
				//foreach ($fields_name as $news=>$name){
				   $Data2[$i]=$rs->fields($fields_name[0]);
				//}
				//$Data2[$i]=$rs->fields($fields_name);
				if ($min>$Data2[$i]){$min=$Data2[$i];}
				if ($max<$Data2[$i]){$max=$Data2[$i];}
				$i++;
				$rs->MoveNext();
			 }
			 if ($news==0){	 $chart=array($Data1,$Data2);}else{ array_push($chart,$Data2);}
		  }
	  }else{$news=0;
			 $pz_name= $bigpz[$pz[0]]." [$stime] - [$etime] ";
			 if ($news==0){	 $Data1[0]=""; }
			 $Data2[0]=$pz_name.$namev[0];$Data3[0]=$pz_name.$namev[1];
			 $sql="select * from city28pr where pzid =".$pz[0]." and (Date>'".$stime."' and Date<'".$etime."') order by Date asc";
			 $rs=$this->connshpi->Execute($sql);
			 $i=1;
			 while(!$rs->EOF){
				if ($news==0){$Data1[$i]=date("n/j",strtotime($rs->fields("Date"))); }
				/*foreach ($fields_name as $news=>$name){
				   $Data2[$i]=$rs->fields($name);
				}*/
				$Data2[$i]=$rs->fields($fields_name[0]);
				$Data3[$i]=$rs->fields($fields_name[1]);
				if ($Data2[$i]<$Data3[$i]){$mmin=$Data2[$i];$mmax=$Data3[$i];}else{
				$mmin=$Data3[$i];$mmax=$Data2[$i];				}
				if ($min>$mmin){$min=$mmin;}
				if ($max<$mmax){$max=$mmax;}
				$i++;
				$rs->MoveNext();
			 }
			 //$min=min(min($Data2),min($Data3));  $max=max(max($Data2),max($Data3));
			 if ($news==0){	 $chart=array($Data1,$Data2,$Data3);}else{ array_push($chart,$Data2);}
	  }
	 return $chart;
  }
  //=================
   //======多城市某品种汇总
  function manycity($stime,$etime,$pz,$city){
     global $min,$max;
	 $min=999999;$max=-1;
     //$bigpz=$this->arr_pzbig(1);
	 $arr_city=$this->arr_city(1);
	 //print_r($arr_city);
	 //===========
	 if (count($pz)>1){
	    $citya[0]=$city[0];
		$pzf=1;$ctf=0;
		$pzsql=implode(",",$pz);
	 }else{
		$ctf=1;$pzf=0;
		$pzsql=$pz[0];$citya=$city;
	 }
	 $sql="SELECT * FROM `compilesid` where id in (".$pzsql.")";
	 $rs=$this->connshpi->Execute($sql);$x=0;
     while (!$rs->EOF){
	    foreach($citya as $keys=>$cityid){
		  if ($rs->fields('city'.substr($cityid,-2))!=0){
		    $gatherid[$rs->fields('id')][substr($cityid,-2)]=$rs->fields('city'.substr($cityid,-2));
			$pz_name= $rs->fields('varietyname')." ".$arr_city[$cityid]." [$stime] - [$etime] $namev";
			if ($x==0){$Data1[0]="";}
			$Data2[0]=$pz_name;
			$sql="select price,managedate from marketrecord as mr left join marketconditions as mc on
			 mr.id=mc.marketrecordid  where managedate>'".$stime." 00:00' and managedate<'".$etime." 23:59'
			and mc.topicture=".$gatherid[$rs->fields('id')][substr($cityid,-2)]." 
			   order by managedate ";
			 //echo $sql."<br>";
			 $rsc=$this->connshpi->Execute($sql);$i=1;
			 while(!$rsc->EOF){
			    if ($x==0){$Data1[$i]=date("n/j",strtotime($rsc->fields("managedate"))); }
				$pricev=$rsc->fields("price");
				if (strstr($pricev,"-"))
				{
					  $arr_price1= explode("-", $pricev);
					  $pricev=($arr_price1[0]+$arr_price1[1])/2;
				 }
			    $Data2[$i]=$pricev;
				if ($min>$Data2[$i]){$min=$Data2[$i];}
				if ($max<$Data2[$i]){$max=$Data2[$i];}
				$i++;
			    $rsc->MoveNext();
			 }
			 if ($x==0){$chart=array($Data1,$Data2);}else{array_push($chart,$Data2);}
			$x++;
		  }
	   }
	    $rs->MoveNext();
	 }
	 return $chart;
  }
  //=================
	function times_section($stime,$etime){
	  if (date("Y-m-d",strtotime($stime))>date("Y-m-d",strtotime($etime))){
	    echo "error";
	    return false;
	  }
	  //===开始天
	  $i=1;
	  $array_ts[0]=date("Y-m-d",strtotime($stime));
	  while($stime!=$etime){
	     $sday=date("j",strtotime($stime)); $smon=date("n",strtotime($stime)); $syear=date("Y",strtotime($stime));
		 if ($sday==31){
			if ($smon==12){
		       $syear++; $smon=1;
			}else{
			   $smon++;
			}
			$sday=1;
		 }else{
		    $sday++;
		 }
		 $stime=$syear."-".$smon."-".$sday;
		 $array_ts[$i]=date("Y-m-d",strtotime($syear."-".$smon."-".$sday));
		 $i++;
		 if ($i>800){
		    //print_r($array_ts);
		    return false;
		 }
	  }
	  return $array_ts;
	  //print_r($array_ts);
	}
  //========
  #
  //========
function get_date($array_value){
		//foreach ($array_value["pid"] as $nkeys=>$nvalues){

		if($array_value['type']=='2'){
			$nvalues = $array_value["pid"][0];
			$sql="select * from shpi_pp  where bc_id='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
			//echo $sql;
		}else if($array_value['type']=='3'){
			$nvalues = $array_value["pid"][0];
			$sql="select * from shpi_pzp where bvm_id='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
		    //echo $sql;
		}else if($array_value['type']=='4'){
			$nvalues = $array_value["pid"][0];
			$sql="select * from shpi_material where topicture='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
		    //echo $sql;
		}else
		{
			$sql = "select * from shpi_pi where dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
		}

		$rs=$this->connshpi->Execute($sql);$i=1;

		while (!$rs->EOF){
			$dataday=$rs->fields("dateday");
			$rs->MoveNext();
			$arr_date[] = $dataday;
		}

		return $arr_date;
	}

	function xg_shpi($array_value){
		$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3','4'));
		// echo "<pre>";print_r($array_value["pid"]);
		foreach ($array_value["pid"] as $nkeys=>$nvalues){
			switch ($array_value["flag"])
			{
				case 2:
					$fields_name="price";
					$avgfields_name="price";
					break;
				case 4:
					$fields_name="mindex";
					$avgfields_name="index";
					break;
			}
			$fwp = $avgfields_name."_avg_week";
			$fmp = $avgfields_name."_avg_month";
			$fields_name_5 = $avgfields_name."_avg_5";
			$fields_name_10 = $avgfields_name."_avg_10";
			$fields_name_20 = $avgfields_name."_avg_20";
			$fields_name_40 = $avgfields_name."_avg_40";
			$fields_name_60 = $avgfields_name."_avg_60";
			$fields_name_200 = $avgfields_name."_avg_200";

			$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
			$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
			$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
			$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

			$sql="select * from shpi_allarea_index  where typeid='".$nvalues."' AND indextype=4 AND isdel=0 and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select * from shpi_allarea_index where typeid='".$nvalues."' AND indextype=4 AND isdel=0  and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
			// echo $sql."<br>";
			//add by zfy ended 2020/06/19 基准日

			$basedays = $this->connshpi->getrow($sqldate);
			// echo "<pre>";print_r($basedays);exit;
			$rs=$this->connshpi->Execute($sql);
			while (!$rs->EOF){
				$dataday=$rs->fields("dateday");

				if ($array_value['leixing']==2) {
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
					} else {
						$price = $rs->fields($fwp);
					}
				}else if($array_value['leixing']==3){
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
					} else {
						$price = $rs->fields($fmp);
					}
				}else{
					if ($calcBaseIndex==1){
						$price = $this->CalculateBaseDayPoints($rs->fields($fields_name), $basedays[$fields_name]);
					}else {
						$price = $rs->fields($fields_name);
					}
					//add by zfy ended 2020/06/04 基准日
				}
				// echo $nvalues."<br>";
				$array_price["xg".$nvalues][$dataday]=$price;

				$rs->MoveNext();
			}
		}
		// echo "<pre>";print_r($array_price);exit;
		foreach ($array_price as $k => $v) {
			if (array_sum($v) == 0) {
				unset($array_price[$k]);
			}
		}
		return $array_price;
	}

	function zhgc_shpi_one($array_value){
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3', '4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		switch ($array_value["flag"])
		{
			//update by zfy started 2017/7/18
			case 1:
				$fields_name="aveprice";
				$namev="平均价格走势图";
				$fields_name_5="aveprice_avg_5";
				$fields_name_10="aveprice_avg_10";
				$fields_name_20="aveprice_avg_20";
				$fields_name_40="aveprice_avg_40";
				$fields_name_60="aveprice_avg_60";
				$fields_name_200="aveprice_avg_200";
				break;
			case 2:
				$fields_name="weiprice";
				$namev="综合价格走势图";
				$fields_name_5="weiprice_avg_5";
				$fields_name_10="weiprice_avg_10";
				$fields_name_20="weiprice_avg_20";
				$fields_name_40="weiprice_avg_40";
				$fields_name_60="weiprice_avg_60";
				$fields_name_200="weiprice_avg_200";
				break;
			case 3:
				$fields_name="weiindex";
				$namev="价格指数走势图";
				$fields_name_5="wpindex_avg_5";
				$fields_name_10="wpindex_avg_10";
				$fields_name_20="wpindex_avg_20";
				$fields_name_40="wpindex_avg_40";
				$fields_name_60="wpindex_avg_60";
				$fields_name_200="wpindex_avg_200";
				break;
			case 4:
				$fields_name="wpindex";
				$namev="综合指数走势图";
				$fields_name_5="wpindex_avg_5";
				$fields_name_10="wpindex_avg_10";
				$fields_name_20="wpindex_avg_20";
				$fields_name_40="wpindex_avg_40";
				$fields_name_60="wpindex_avg_60";
				$fields_name_200="wpindex_avg_200";
				break;
			//update by zfy ended 2017/7/18
		}
		$fwp="weekindexp";
		$fmp="monthindexp";
		if($fields_name=='weiprice' || $fields_name=='aveprice')
		{
			$fwp="weekp";
			$fmp="monthp";
		}
		if($fields_name=='weiindex' || $fields_name=='wpindex')
		{
			$fwp="weekindexp";
			$fmp="monthindexp";
		}
		//echo $array_value["stime"];
		$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
		$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
		$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
		$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

		//$time2 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
		//add by zfy started 2017/7/19
		$leixing_sum = count($array_value['leixing']);
		//add by zfy ended 2017/7/19
		foreach($array_value['leixing'] as $key=>$value){
			if($value=='2'){
				$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=1 and bigtype='gc' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_week where dateday<='" . $array_value['baseDaydate'] . "' and type=1 and bigtype='gc' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
				//echo $sql;
			}else if($value=='3'){
				$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=1 and bigtype='gc' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_month where dateday<='" . $array_value['baseDaydate'] . "' and type=1 and bigtype='gc' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
			}else
			{
				$table = "shpi_pi";
				if ($array_value['oldshpi'] == "1") {
					$table = "shpi_pi_logs";
				}

				$sql = "select * from $table where dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from $table where dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
			}
			//add by zfy started 2020/06/19 基准日
			$basedays = $this->connshpi->getrow($sqldate);
			//add by zfy ended 2020/06/19 基准日
			$rs=$this->connshpi->Execute($sql);$i=1;
			while (!$rs->EOF){
				$dataday=$rs->fields("dateday");

				if($value==2)
				{
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
					} else {
						$price = $rs->fields($fwp);
					}
					//add by zfy ended 2020/06/04 基准日
				}
				else if($value==3)
				{
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
					} else {
						$price = $rs->fields($fmp);
					}
					//add by zfy ended 2020/06/04 基准日
				}else{
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex==1){
						// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
						$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
					}else {
						$price = $rs->fields($fields_name);
						//add by zfy started 2017/7/18
						$price_5 = $rs->fields($fields_name_5);
						$price_10 = $rs->fields($fields_name_10);
						$price_20 = $rs->fields($fields_name_20);
						$price_40 = $rs->fields($fields_name_40);
						$price_60 = $rs->fields($fields_name_60);
						$price_200 = $rs->fields($fields_name_200);
						//add by zfy ended 2017/7/18
					}
					//add by zfy ended 2020/06/04 基准日
				}
				//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
				$array_price[$key][$dataday]=$price;
				//add by zfy started 2017/7/18
				$i = 1;
				if($leixing_sum == 1&&in_array('1',$array_value['leixing'])){
					if(is_array($array_value["avgprice"]) && in_array('5',$array_value["avgprice"])){
						if($price_5!=""&&$price_5!='0')
						$array_price[$i][$dataday]=$price_5;
						$i++;
					}
					if(is_array($array_value["avgprice"]) && in_array('10',$array_value["avgprice"])){
						if($price_10!=""&&$price_10!='0')
						$array_price[$i][$dataday]=$price_10;
						$i++;
					}
					if(is_array($array_value["avgprice"]) && in_array('20',$array_value["avgprice"])){
						if($price_20!=""&&$price_20!='0')
						$array_price[$i][$dataday]=$price_20;
						$i++;
					}
					if(is_array($array_value["avgprice"]) && in_array('40',$array_value["avgprice"])){
						if($price_40!=""&&$price_40!='0')
						$array_price[$i][$dataday]=$price_40;
						$i++;
					}
					if(is_array($array_value["avgprice"]) && in_array('60',$array_value["avgprice"])){
						if($price_60!=""&&$price_60!='0')
						$array_price[$i][$dataday]=$price_60;
						$i++;
					}
					if(is_array($array_value["avgprice"]) && in_array('200',$array_value["avgprice"])){
						if($price_200!=""&&$price_200!='0')
						$array_price[$i][$dataday]=$price_200;
						$i++;
					}
				}
				//add by zfy ended 2017/7/18
				$rs->MoveNext();
			}
		}
		return $array_price;
	}

	function xg_shpi_one($array_value){
		$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3','4'));
		foreach ($array_value["pid"] as $nkeys=>$nvalues){
			switch ($array_value["flag"])
			{
				case 2:
					$fields_name="price";
					$avgfields_name="price";
					break;
				case 4:
					$fields_name="mindex";
					$avgfields_name="index";
					break;
			}
			$fwp = $avgfields_name."_avg_week";
			$fmp = $avgfields_name."_avg_month";
			$fields_name_5 = $avgfields_name."_avg_5";
			$fields_name_10 = $avgfields_name."_avg_10";
			$fields_name_20 = $avgfields_name."_avg_20";
			$fields_name_40 = $avgfields_name."_avg_40";
			$fields_name_60 = $avgfields_name."_avg_60";
			$fields_name_200 = $avgfields_name."_avg_200";

			$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
			$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
			$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
			$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

			$leixing_sum = count($array_value['leixing']);

			foreach($array_value['leixing'] as $key=>$value){

				$sql="select * from shpi_allarea_index  where typeid='".$nvalues."' AND indextype=4 AND isdel=0 and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select * from shpi_allarea_index where typeid='".$nvalues."' AND indextype=4 AND isdel=0  and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				// echo $sqldate;exit;
				//add by zfy ended 2020/06/19 基准日

				$basedays = $this->connshpi->getrow($sqldate);
				// echo "<pre>";print_r($basedays);exit;
				$rs=$this->connshpi->Execute($sql);
				while (!$rs->EOF){
					$dataday=$rs->fields("dateday");

					if ($value==2) {
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
					}else if($value==3){
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
					}else{
						if ($calcBaseIndex==1){
							$this->getBaseDayPoints2($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name,$avgfields_name);
						}else {
							$price = $rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5 = $rs->fields($fields_name_5);
							$price_10 = $rs->fields($fields_name_10);
							$price_20 = $rs->fields($fields_name_20);
							$price_40 = $rs->fields($fields_name_40);
							$price_60 = $rs->fields($fields_name_60);
							$price_200 = $rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
					}

					$array_price[$key][$dataday]=$price;

					$i = 1;
					if($leixing_sum == 1&&in_array('1',$array_value['leixing'])){
						if(is_array($array_value["avgprice"]) && in_array('5',$array_value["avgprice"])){
							if($price_5!=""&&$price_5!='0')
							$array_price[$i][$dataday]=$price_5;
							$i++;
						}
						if(is_array($array_value["avgprice"]) && in_array('10',$array_value["avgprice"])){
							if($price_10!=""&&$price_10!='0')
							$array_price[$i][$dataday]=$price_10;
							$i++;
						}
						if(is_array($array_value["avgprice"]) && in_array('20',$array_value["avgprice"])){
							if($price_20!=""&&$price_20!='0')
							$array_price[$i][$dataday]=$price_20;
							$i++;
						}
						if(is_array($array_value["avgprice"]) && in_array('40',$array_value["avgprice"])){
							if($price_40!=""&&$price_40!='0')
							$array_price[$i][$dataday]=$price_40;
							$i++;
						}
						if(is_array($array_value["avgprice"]) && in_array('60',$array_value["avgprice"])){
							if($price_60!=""&&$price_60!='0')
							$array_price[$i][$dataday]=$price_60;
							$i++;
						}
						if(is_array($array_value["avgprice"]) && in_array('200',$array_value["avgprice"])){
							if($price_200!=""&&$price_200!='0')
							$array_price[$i][$dataday]=$price_200;
							$i++;
						}
					}
					$rs->MoveNext();
				}
			}
		}
		// echo "<pre>";print_r($array_price);exit;
		foreach ($array_price as $k => $v) {
			if (array_sum($v) == 0) {
				unset($array_price[$k]);
			}
		}
		$array_price = array_values($array_price);
		return $array_price;
	}

	//=========
	#
	//========
	function bpz_shpi_one($array_value){
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3', '4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		//print_r($array_value);
		foreach ($array_value["pid"] as $nkeys=>$nvalues){
			switch ($array_value["flag"])
			{
				//update by zfy started 2017/7/18
			case 1:
				$fields_name="aveprice";
				$namev="平均价格走势图";
				$fields_name_5="aveprice_avg_5";
				$fields_name_10="aveprice_avg_10";
				$fields_name_20="aveprice_avg_20";
				$fields_name_40="aveprice_avg_40";
				$fields_name_60="aveprice_avg_60";
				$fields_name_200="aveprice_avg_200";
				break;
			case 2:
				$fields_name="weiprice";
				$namev="综合价格走势图";
				$fields_name_5="weiprice_avg_5";
				$fields_name_10="weiprice_avg_10";
				$fields_name_20="weiprice_avg_20";
				$fields_name_40="weiprice_avg_40";
				$fields_name_60="weiprice_avg_60";
				$fields_name_200="weiprice_avg_200";
				break;
			case 3:
				$fields_name="weiindex";
				$namev="价格指数走势图";
				$fields_name_5="weiindex_avg_5";
				$fields_name_10="weiindex_avg_10";
				$fields_name_20="weiindex_avg_20";
				$fields_name_40="weiindex_avg_40";
				$fields_name_60="weiindex_avg_60";
				$fields_name_200="weiindex_avg_200";
				break;
			case 4:
				$fields_name="wpindex";
				$namev="综合指数走势图";
				$fields_name_5="wpindex_avg_5";
				$fields_name_10="wpindex_avg_10";
				$fields_name_20="wpindex_avg_20";
				$fields_name_40="wpindex_avg_40";
				$fields_name_60="wpindex_avg_60";
				$fields_name_200="wpindex_avg_200";
				break;
			//update by zfy ended 2017/7/18
			}
			$fwp="weekindexp";
			$fmp="monthindexp";
			if($fields_name=='weiprice' || $fields_name=='aveprice')
			{
				$fwp="weekp";
				$fmp="monthp";
			}
			if($fields_name=='weiindex' || $fields_name=='wpindex')
			{
				$fwp="weekindexp";
				$fmp="monthindexp";
			}

			$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
			$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
			$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
			$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));
			//add by zfy started 2017/7/19
			$leixing_sum = count($array_value['leixing']);
			//add by zfy ended 2017/7/19
			foreach($array_value['leixing'] as $key=>$value){

				if($value=='2'){
					$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=2 and bigtype='gc'and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_week where 1 and type=2 and bigtype='gc'and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日

				}else if($value=='3'){
					$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=2 and bigtype='gc' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_month where 1 and type=2 and bigtype='gc' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
				}else
				{

					$table = "shpi_pp";
					if ($array_value['oldshpi'] == "1") {
						$arr = array(1, 2, 3, 5, 6, 7);
						if (in_array($nvalues, $arr)) {
							$table = "shpi_pp_logs";
						}

					}

			  $sql="select * from $table  where bc_id='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from $table where bc_id='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
				}
				//add by zfy started 2020/06/19 基准日
				$basedays = $this->connshpi->getrow($sqldate);
				//add by zfy ended 2020/06/19 基准日
				$rs=$this->connshpi->Execute($sql);$i=1;
				while (!$rs->EOF){
					$dataday=$rs->fields("dateday");
					if($value==2)
					{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
						//add by zfy ended 2020/06/04 基准日

					}
					else if($value==3)
					{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
						//add by zfy ended 2020/06/04 基准日
					}else{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1){
							// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
							$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
						}else {
							$price = $rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5 = $rs->fields($fields_name_5);
							$price_10 = $rs->fields($fields_name_10);
							$price_20 = $rs->fields($fields_name_20);
							$price_40 = $rs->fields($fields_name_40);
							$price_60 = $rs->fields($fields_name_60);
							$price_200 = $rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
					}
					$array_price[$key][$dataday]=$price;
				//add by zfy started 2017/7/18
				$i = 1;
				if($leixing_sum == 1&&is_array($array_value['leixing'])&&in_array('1',$array_value['leixing'])){
					if(is_array($array_value['avgprice'])&&in_array('5',$array_value["avgprice"])){
						if($price_5!=""&&$price_5!='0')
						$array_price[$i][$dataday]=$price_5;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('10',$array_value["avgprice"])){
						if($price_10!=""&&$price_10!='0')
						$array_price[$i][$dataday]=$price_10;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('20',$array_value["avgprice"])){
						if($price_20!=""&&$price_20!='0')
						$array_price[$i][$dataday]=$price_20;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('40',$array_value["avgprice"])){
						if($price_40!=""&&$price_40!='0')
						$array_price[$i][$dataday]=$price_40;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('60',$array_value["avgprice"])){
						if($price_60!=""&&$price_60!='0')
						$array_price[$i][$dataday]=$price_60;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('200',$array_value["avgprice"])){
						if($price_200!=""&&$price_200!='0')
						$array_price[$i][$dataday]=$price_200;
						$i++;
					}
				}
				//add by zfy ended 2017/7/18
					$rs->MoveNext();
				}
		 }
		}
		return $array_price;
	}

	function bvar_shpi_one($array_value){
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3', '4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		foreach ($array_value["pid"] as $nkeys=>$nvalues){
			switch ($array_value["flag"])
			{
				//update by zfy started 2017/7/18
			case 1:
				$fields_name="aveprice";
				$namev="平均价格走势图";
				$fields_name_5="aveprice_avg_5";
				$fields_name_10="aveprice_avg_10";
				$fields_name_20="aveprice_avg_20";
				$fields_name_40="aveprice_avg_40";
				$fields_name_60="aveprice_avg_60";
				$fields_name_200="aveprice_avg_200";
				break;
			case 2:
				$fields_name="weiprice";
				$namev="综合价格走势图";
				$fields_name_5="weiprice_avg_5";
				$fields_name_10="weiprice_avg_10";
				$fields_name_20="weiprice_avg_20";
				$fields_name_40="weiprice_avg_40";
				$fields_name_60="weiprice_avg_60";
				$fields_name_200="weiprice_avg_200";
				break;
			case 3:
				$fields_name="apindex";
				$namev="价格指数走势图";
				$fields_name_5="apindex_avg_5";
				$fields_name_10="apindex_avg_10";
				$fields_name_20="apindex_avg_20";
				$fields_name_40="apindex_avg_40";
				$fields_name_60="apindex_avg_60";
				$fields_name_200="apindex_avg_200";
				break;
			case 4:
				$fields_name="wpindex";
				$namev="综合指数走势图";
				$fields_name_5="wpindex_avg_5";
				$fields_name_10="wpindex_avg_10";
				$fields_name_20="wpindex_avg_20";
				$fields_name_40="wpindex_avg_40";
				$fields_name_60="wpindex_avg_60";
				$fields_name_200="wpindex_avg_200";
				break;
			//update by zfy ended 2017/7/18
			}
			$fwp="weekindexp";
			$fmp="monthindexp";
			if($fields_name=='weiprice' || $fields_name=='aveprice')
			{
				$fwp="weekp";
				$fmp="monthp";
			}
			if($fields_name=='weiindex' || $fields_name=='wpindex')
			{
				$fwp="weekindexp";
				$fmp="monthindexp";
			}
			$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
			$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
			$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
			$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));
			//add by zfy started 2017/7/19
			$leixing_sum = count($array_value['leixing']);
			//add by zfy ended 2017/7/19
			foreach($array_value['leixing'] as $key=>$value){

				if($value=='2'){
					$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=3 and bigtype='gc' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_week where 1 and type=3 and bigtype='gc' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日

				}else if($value=='3'){
					$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=3 and bigtype='gc' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_month where 1 and type=3 and bigtype='gc' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
				}else
				{

					$table = "shpi_pzp";
					if ($array_value['oldshpi'] == "1") {
						$arr = array(1, 2, 3, 4, 5, 6, 8, 9);
						if (in_array($nvalues, $arr)) {
							$table = "shpi_pzp_logs";
						}

					}

					$sql="select * from $table where bvm_id='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from $table where bvm_id='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
				}
				//add by zfy started 2020/06/19 基准日
				$basedays = $this->connshpi->getrow($sqldate);
				//add by zfy ended 2020/06/19 基准日
				$rs=$this->connshpi->Execute($sql);$i=1;
				while (!$rs->EOF){
					$dataday=$rs->fields("dateday");
					if($value==2)
					{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
						//add by zfy ended 2020/06/04 基准日
					}
					else if($value==3)
					{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
						//add by zfy ended 2020/06/04 基准日
					}else{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1){
							// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
							$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
						}else {
							$price = $rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5 = $rs->fields($fields_name_5);
							$price_10 = $rs->fields($fields_name_10);
							$price_20 = $rs->fields($fields_name_20);
							$price_40 = $rs->fields($fields_name_40);
							$price_60 = $rs->fields($fields_name_60);
							$price_200 = $rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
					}
					$array_price[$key][$dataday]=$price;
				//add by zfy started 2017/7/18
				$i = 1;
				if($leixing_sum == 1&&in_array('1',$array_value['leixing'])){
					if(is_array($array_value['avgprice'])&&in_array('5',$array_value["avgprice"])){
						if($price_5!=""&&$price_5!='0')
						$array_price[$i][$dataday]=$price_5;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('10',$array_value["avgprice"])){
						if($price_10!=""&&$price_10!='0')
						$array_price[$i][$dataday]=$price_10;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('20',$array_value["avgprice"])){
						if($price_20!=""&&$price_20!='0')
						$array_price[$i][$dataday]=$price_20;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('40',$array_value["avgprice"])){
						if($price_40!=""&&$price_40!='0')
						$array_price[$i][$dataday]=$price_40;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('60',$array_value["avgprice"])){
						if($price_60!=""&&$price_60!='0')
						$array_price[$i][$dataday]=$price_60;
						$i++;
					}
					if(is_array($array_value['avgprice'])&&in_array('200',$array_value["avgprice"])){
						if($price_200!=""&&$price_200!='0')
						$array_price[$i][$dataday]=$price_200;
						$i++;
					}
				}
				//add by zfy ended 2017/7/18
					$rs->MoveNext();
				}
			}
		}
		return $array_price;
	}

	function fun_adm_count($array_or_countable,$mode = COUNT_NORMAL){
		if(is_array($array_or_countable) || is_object($array_or_countable)){
			return count($array_or_countable, $mode);
		}else{
			return 0;
		}
	}

	function zhgc_shpi($array_value)
	{
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value, array('3', '4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		switch ($array_value["flag"]) {
			//update by zfy started 2017/7/18
			case 1:
				$fields_name = "aveprice";
				$namev = "平均价格走势图";
				$fields_name_5 = "aveprice_avg_5";
				$fields_name_10 = "aveprice_avg_10";
				$fields_name_20 = "aveprice_avg_20";
				$fields_name_40 = "aveprice_avg_40";
				$fields_name_60 = "aveprice_avg_60";
				$fields_name_200 = "aveprice_avg_200";
				break;
			case 2:
				$fields_name = "weiprice";
				$namev = "综合价格走势图";
				$fields_name_5 = "weiprice_avg_5";
				$fields_name_10 = "weiprice_avg_10";
				$fields_name_20 = "weiprice_avg_20";
				$fields_name_40 = "weiprice_avg_40";
				$fields_name_60 = "weiprice_avg_60";
				$fields_name_200 = "weiprice_avg_200";
				break;
			case 3:
				$fields_name = "apindex";
				$namev = "价格指数走势图";
				$fields_name_5 = "apindex_avg_5";
				$fields_name_10 = "apindex_avg_10";
				$fields_name_20 = "apindex_avg_20";
				$fields_name_40 = "apindex_avg_40";
				$fields_name_60 = "apindex_avg_60";
				$fields_name_200 = "apindex_avg_200";
				break;
			case 4:
				$fields_name = "wpindex";
				$namev = "综合指数走势图";
				$fields_name_5 = "wpindex_avg_5";
				$fields_name_10 = "wpindex_avg_10";
				$fields_name_20 = "wpindex_avg_20";
				$fields_name_40 = "wpindex_avg_40";
				$fields_name_60 = "wpindex_avg_60";
				$fields_name_200 = "wpindex_avg_200";
				break;
			//update by zfy ended 2017/7/18
		}
		//add by zfy started 2017/7/19
		$array_value['leixing']=isset($array_value['leixing'])?$array_value['leixing']:'';
		$leixing_sum = $this->fun_adm_count($array_value['leixing']);
		//add by zfy ended 2017/7/19

		$fwp = "weekindexp";
		$fmp = "monthindexp";
		if ($fields_name == 'weiprice' || $fields_name == 'aveprice') {
			$fwp = "weekp";
			$fmp = "monthp";
		}
		if ($fields_name == 'weiindex' || $fields_name == 'wpindex') {
			$fwp = "weekindexp";
			$fmp = "monthindexp";
		}
		if ($array_value['leixing'] == '2') {
			$sql = "select * from shpi_week where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' and type=1 and bigtype='gc' order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from shpi_week where dateday<='" . $array_value['baseDaydate'] . "' and type=1 and bigtype='gc' order by dateday desc limit 1";
			//add by zfy ended 2020/06/19 基准日

		} else if ($array_value['leixing'] == '3') {
			$sql = "select * from shpi_month where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' and type=1 and bigtype='gc' order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from shpi_month where dateday<='" . $array_value['baseDaydate'] . "' and type=1 and bigtype='gc' order by dateday desc limit 1";
			//add by zfy ended 2020/06/19 基准日
		} else {
			$table = "shpi_pi";
			if (isset($array_value['oldshpi'])&&$array_value['oldshpi'] == "1") {
				$table = "shpi_pi_logs";
			}
			$sql = "select * from $table where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from $table where 1 order by dateday desc limit 1";
			if(isset($array_value['baseDaydate']))
				$sqldate = "select *  from $table where dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
			//add by zfy ended 2020/06/19 基准日
		}

		// echo "<br>"; print_r($this->connshpi);
		//add by zfy started 2020/06/19 基准日
		$basedays = $this->connshpi->getrow($sqldate);
		//add by zfy ended 2020/06/19 基准日
		$rs = $this->connshpi->Execute($sql);
		$i = 1;
		while (!$rs->EOF) {
			$dataday = $rs->fields("dateday");

			if ($array_value['leixing'] == 2) {
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex == 1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fwp), $basedays[$fwp]);
				} else {
					$price = $rs->fields($fwp);
				}
				//add by zfy ended 2020/06/04 基准日
				$_year = $rs->fields('calyear');
				$_week = $rs->fields('calweek');
				$dataday = $_year . "/" . $_week;
			} else if ($array_value['leixing'] == 3) {
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex == 1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fmp), $basedays[$fmp]);
				} else {
					$price = $rs->fields($fmp);
				}
				//add by zfy ended 2020/06/04 基准日
			} else {
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex == 1) {
//				echo "<pre>";print_r($rs);
//				echo "<pre>";print_r($basedays);
					// $this->getBaseDayPoints(&$price, &$price_5, &$price_10, &$price_20, &$price_40, &$price_60, &$price_200, $rs, $basedays, $fields_name);
					$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
				} else {
					$price = $rs->fields($fields_name);
					//add by zfy started 2017/7/18
					$price_5 = $rs->fields($fields_name_5);
					$price_10 = $rs->fields($fields_name_10);
					$price_20 = $rs->fields($fields_name_20);
					$price_40 = $rs->fields($fields_name_40);
					$price_60 = $rs->fields($fields_name_60);
					$price_200 = $rs->fields($fields_name_200);
					//add by zfy ended 2017/7/18
				}
				//add by zfy ended 2020/06/04 基准日
			}
			//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
			$array_price[0][$dataday] = $price;
			//add by zfy started 2017/7/18
			$i = 1;
			if ($leixing_sum == 1 && in_array('1', $array_value['leixing'])) {
				if (in_array('5', $array_value["avgprice"])) {
					if ($price_5 != "" && $price_5 != '0')
						$array_price[$i][$dataday] = $price_5;
					$i++;
				}
				if (in_array('10', $array_value["avgprice"])) {
					if ($price_10 != "" && $price_10 != '0')
						$array_price[$i][$dataday] = $price_10;
					$i++;
				}
				if (in_array('20', $array_value["avgprice"])) {
					if ($price_20 != "" && $price_20 != '0')
						$array_price[$i][$dataday] = $price_20;
					$i++;
				}
				if (in_array('40', $array_value["avgprice"])) {
					if ($price_40 != "" && $price_40 != '0')
						$array_price[$i][$dataday] = $price_40;
					$i++;
				}
				if (in_array('60', $array_value["avgprice"])) {
					if ($price_60 != "" && $price_60 != '0')
						$array_price[$i][$dataday] = $price_60;
					$i++;
				}
				if (in_array('200', $array_value["avgprice"])) {
					if ($price_200 != "" && $price_200 != '0')
						$array_price[$i][$dataday] = $price_200;
					$i++;
				}
			}
			//add by zfy ended 2017/7/18
			$rs->MoveNext();
		}
		return $array_price;
	}
	//=========
	#
	//========
	function bpz_shpi($array_value)
	{
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value, array('3', '4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		foreach ($array_value["pid"] as $nkeys => $nvalues) {
			switch ($array_value["flag"]) {
				case 1:
					$fields_name = "aveprice";
					$namev = "平均价格走势图";
					break;
				case 2:
					$fields_name = "weiprice";
					$namev = "综合价格走势图";
					break;
				case 3:
					$fields_name = "apindex";
					$namev = "价格指数走势图";
					break;
				case 4:
					$fields_name = "wpindex";
					$namev = "综合指数走势图";
					break;
			}
			$fwp = "weekindexp";
			$fmp = "monthindexp";
			if ($fields_name == 'weiprice' || $fields_name == 'aveprice') {
				$fwp = "weekp";
				$fmp = "monthp";
			}
			if ($fields_name == 'weiindex' || $fields_name == 'wpindex') {
				$fwp = "weekindexp";
				$fmp = "monthindexp";
			}
			if (isset($array_value['leixing'])&&$array_value['leixing'] == '2') {
				$sql = "select * from shpi_week where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' and type=2 and bigtype='gc' and smalltype='" . $nvalues . "' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_week where 1 and type=2 and bigtype='gc' and smalltype='" . $nvalues . "' order by dateday desc limit 1";
			if(isset($array_value['baseDaydate']))
				$sqldate = "select *  from shpi_week where 1 and type=2 and bigtype='gc' and smalltype='" . $nvalues . "' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日

			} else if (isset($array_value['leixing'])&&$array_value['leixing'] == '3') {
				$sql = "select * from shpi_month where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' and type=2 and bigtype='gc' and smalltype='" . $nvalues . "' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_month where 1 and type=2 and bigtype='gc' and smalltype='" . $nvalues . "'   order by dateday desc limit 1";
				if(isset($array_value['baseDaydate']))
				$sqldate = "select *  from shpi_month where 1 and type=2 and bigtype='gc' and smalltype='" . $nvalues . "' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
			} else {

				$table = "shpi_pp";
				if (isset($array_value['oldshpi'])&&$array_value['oldshpi'] == "1") {
					$arr = array(1, 2, 3, 5, 6, 7);
					if (in_array($nvalues, $arr)) {
						$table = "shpi_pp_logs";
					}

				}

				$sql = "select * from $table  where bc_id='" . $nvalues . "' and dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from $table where bc_id='" . $nvalues . "'   order by dateday desc limit 1";
				if(isset($array_value['baseDaydate']))
					$sqldate = "select *  from $table where bc_id='" . $nvalues . "' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
			}
			//add by zfy started 2020/06/19 基准日
			$basedays = $this->connshpi->getrow($sqldate);
			//add by zfy ended 2020/06/19 基准日
			$rs = $this->connshpi->Execute($sql);
			$i = 1;
			while (!$rs->EOF) {
				$dataday = $rs->fields("dateday");
				if (isset($array_value['leixing'])&&$array_value['leixing'] == 2) {
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex == 1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fwp), $basedays[$fwp]);
					} else {
						$price = $rs->fields($fwp);
					}
					//add by zfy ended 2020/06/04 基准日
					$_year = $rs->fields('calyear');
					$_week = $rs->fields('calweek');
					$dataday = $_year . "/" . $_week;
				} else if (isset($array_value['leixing'])&&$array_value['leixing'] == 3) {
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex == 1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fmp), $basedays[$fmp]);
					} else {
						$price = $rs->fields($fmp);
					}
					//add by zfy ended 2020/06/04 基准日
				} else {
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex == 1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fields_name), $basedays[$fields_name]);
					} else {
						$price = $rs->fields($fields_name);
					}
					//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				}
				$array_price[$nvalues][$dataday] = $price;

				$rs->MoveNext();
			}
		}
		return $array_price;
	}

	// 查询增值税
	function getShui() {
		$sql = "select rvalue from cntaxrate where rtype='1' and isdel='0' order by rdate desc limit 1";
		$rvaluearr = $this->connshpi->getrow($sql);
		$shui = $rvaluearr['rvalue'] + 1;
		return $shui;
	}

	function bpz_shpi_hs($pricearr) {
		$pricearr_hs = array();
		$shui = $this->getShui();
		foreach ($pricearr as $k => $v) {
			foreach ($v as $date => $price) {
				if (!empty($price)) {
					$pricearr_hs["s".$k][$date] = round($price*$shui, 2);
				} else {
					$pricearr_hs["s".$k][$date] = $price;
				}
			}
		}
		// echo"<pre>";print_r($pricearr);
		return $pricearr_hs;
	}

	function creat_dian_hs($pricearr) {
		$pricearr_hs = array();
		$shui = $this->getShui();
		foreach ($pricearr as $k => $price) {
			if (!empty($price)) {
				$pricearr_hs[$k] = round($price*$shui, 0);
			} else {
				$pricearr_hs[$k] = $price;
			}
		}
		// echo"<pre>";print_r($pricearr);
		return $pricearr_hs;
	}

	//========
	#
	//========
	function bvar_shpi($array_value)
	{
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value, array('3', '4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		foreach ($array_value["pid"] as $nkeys => $nvalues) {
			switch ($array_value["flag"]) {
				case 1:
					$fields_name = "aveprice";
					$namev = "平均价格走势图";
					break;
				case 2:
					$fields_name = "weiprice";
					$namev = "综合价格走势图";
					break;
				case 3:
					$fields_name = "apindex";
					$namev = "价格指数走势图";
					break;
				case 4:
					$fields_name = "wpindex";
					$namev = "综合指数走势图";
					break;
			}
			$fwp = "weekindexp";
			$fmp = "monthindexp";
			if ($fields_name == 'weiprice' || $fields_name == 'aveprice') {
				$fwp = "weekp";
				$fmp = "monthp";
			}
			if ($fields_name == 'weiindex' || $fields_name == 'wpindex') {
				$fwp = "weekindexp";
				$fmp = "monthindexp";
			}
			if ($array_value['leixing'] == '2') {
				$sql = "select * from shpi_week where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' and type=3 and bigtype='gc' and smalltype='" . $nvalues . "' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_week where 1 and type=3 and bigtype='gc' and smalltype='" . $nvalues . "' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日

			} else if ($array_value['leixing'] == '3') {
				$sql = "select * from shpi_month where dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' and type=3 and bigtype='gc' and smalltype='" . $nvalues . "' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_month where 1 and type=3 and bigtype='gc' and smalltype='" . $nvalues . "' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
			} else {
				$table = "shpi_pzp";
				if ($array_value['oldshpi'] == "1") {
					$arr = array(1, 2, 3, 4, 5, 6, 8, 9);
					if (in_array($nvalues, $arr)) {
						$table = "shpi_pzp_logs";
					}

				}

				$sql = "select * from $table where bvm_id='" . $nvalues . "' and dateday>='" . $array_value["stime"] . "' and dateday<='" . $array_value["etime"] . "' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from $table where bvm_id='" . $nvalues . "' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
			}
			//add by zfy started 2020/06/19 基准日
			$basedays = $this->connshpi->getrow($sqldate);
			//add by zfy ended 2020/06/19 基准日
			$rs = $this->connshpi->Execute($sql);
			$i = 1;
			while (!$rs->EOF) {
				$dataday = $rs->fields("dateday");
				if ($array_value['leixing'] == 2) {
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex == 1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fwp), $basedays[$fwp]);
					} else {
						$price = $rs->fields($fwp);
					}
					//add by zfy ended 2020/06/04 基准日
					$_year = $rs->fields('calyear');
					$_week = $rs->fields('calweek');
					$dataday = $_year . "/" . $_week;
				} else if ($array_value['leixing'] == 3) {
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex == 1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fmp), $basedays[$fmp]);
					} else {
						$price = $rs->fields($fmp);
					}
					//add by zfy ended 2020/06/04 基准日
				} else {
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex == 1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fields_name), $basedays[$fields_name]);
					} else {
						$price = $rs->fields($fields_name);
					}
					//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				}
				$array_price["j" . $nvalues][$dataday] = $price;
				$rs->MoveNext();
			}
		}
		return $array_price;
	}

  //========
  //宝钢定制指数
  function baosteel_shpi($array_value,$isone=false){
  	$ARR_SYMBOLS = array(
  		1=>'BAOCNSI-CRP',
  		2=>'BAOCNSI-HDGI',
  		3=>'BAOCNSI-HALGI',
  		4=>'BAOCNSI-EGI',
  		5=>'BAOCNSI-CS',
  		6=>'BAOCNSI-NO',
  		7=>'BAOCNSI-GO',
  		8=>'BAOCNSI-ETP',

  	);
	$is_index = ($array_value['flag'] <= 2) ? false : true;
  	$basedate = $array_value['baseDaydate'] ? $array_value['baseDaydate'] : "2004-09-28";
	foreach ($array_value["pid"] as $nkeys=>$nvalues){
		$symbol = $ARR_SYMBOLS[$nvalues];
		switch ($array_value["flag"]) {
			case 1: $namev="平均价格走势图";break;
			case 2: $namev="综合价格走势图";break;
			case 3: $namev="价格指数走势图";break;
			case 4: $namev="综合指数走势图";break;
		}

		if($is_index) {
			$base_sql = "select dateday,price from shpi_custom where dateday<='$basedate' and symbol='$symbol' order by dateday";
			$base_info = $this->connshpi->getRow($base_sql);
			if(!$base_info){
				$base_sql = "select dateday,price from shpi_custom where dateday>'$basedate' and symbol='".$symbol."' order by dateday desc";
				$base_info = $this->connshpi->getRow($base_sql);
			}
		}
		if(!is_array($array_value['leixing'])){
			$array_value['leixing'] = array($array_value['leixing']);
		}
		$i=0;
		if($array_value['avgprice']) {
			$i=0;
			$sql="select dateday,price from shpi_custom where symbol='".$symbol."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
			$price_info = $this->connshpi->getArray($sql);
			foreach($price_info as $item){
				$price = $item['price'];
				$dateday = $item['dateday'];
				if($is_index){
					$price = round($price*100/$base_info['price'], 2);
				}
				if($isone){
					$array_price[$i][$dateday]=$price;
				}else {
					$array_price["b".$nvalues][$dateday]=$price;
				}
			}

			$i++;
			foreach($array_value['avgprice'] as $period){
				$start_date = date("Y-m-d",strtotime("-7 day",strtotime($array_value["stime"])));
				$avg_type = "avg_".$period;
				$sql="select dateday,price from shpi_custom_average where type='$avg_type' and symbol='".$symbol."' and dateday>='".$start_date."' and dateday<='".$array_value["etime"]."' order by dateday asc";
				$price_info = $this->connshpi->getArray($sql);

				foreach($price_info as $item){
					$price = $item['price'];
					$dateday = $item['dateday'];
					if($is_index){
						$price = round($price*100/$base_info['price'], 2);
					}
					if($isone){
						$array_price[$i][$dateday]=$price;
					}else {
						$array_price["b".$nvalues][$dateday]=$price;
					}
				}
				$i++;
			}
		}else {
			foreach($array_value['leixing'] as $key=>$value){

				switch($value){
					case 2: //周均价
						// 小于7天的取7天前的数据，大于7天的取开始时间的数据
						if(abs(strtotime($array_value["stime"]) - strtotime($array_value["etime"]))/86400<7)
						$start_date = date("Y-m-d",strtotime("-7 day",strtotime($array_value["stime"])));
						else $start_date = $array_value["stime"];
						$sql="select dateday,price from shpi_custom_average where type='week' and symbol='".$symbol."' and dateday>='".$start_date."' and dateday<='".$array_value["etime"]."' order by dateday asc";
						break;
					case 3: //月均价
						// 此处月均价取时间修改，该处逻辑与function.php中bvar_shpi()函数取得时间不一致，导致多选的时候取出来的数据个数不一致，影响均值计算2023-11-09
						// $start_date = date("Y-m-01",strtotime($array_value["stime"]));
						$start_date = $array_value["stime"];
						$sql="select dateday,price from shpi_custom_average where type='month' and symbol='".$symbol."' and dateday>='".$start_date."' and dateday<='".$array_value["etime"]."' order by dateday asc";
						break;
					default:
						$sql="select dateday,price from shpi_custom where symbol='".$symbol."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
				}

				$price_info = $this->connshpi->getArray($sql);

				foreach($price_info as $item){
					$price = $item['price'];
					if($value==2)
					$dateday = date("Y/", strtotime($item['dateday'])).$this->getWeekNumber($item['dateday']);
					// else if($value==3)
					// $dateday = $dateday = date("Y/m", strtotime($item['dateday']));
					else $dateday = $item['dateday'];
					if($is_index){
						$price = round($price*100/$base_info['price'], 2);
					}
					if($isone){
						$array_price[$i][$dateday]=$price;
					}else {
						//$array_price[$i][$dateday]=$price;
						$array_price["b".$nvalues][$dateday]=$price;
					}
				}
				$i++;
			}
		}
	 }
	return $array_price;
  }

    //========
  /*
  *原料指数部分
  *
  */

  function getWeekNumber($dateString) {
    // 创建一个 DateTime 对象
    $date = new DateTime($dateString);

    // 获取日期所在年份
    $year = $date->format('Y');

    // 获取该日期是当年的第几周
    $weekNumber = $date->format('W');

    return $weekNumber;
}

  function bvar_ylshpi($array_value){
	  	if( $_GET['a'] == 'a' ){
	    print_r( $array_value );
		echo "ccd";
	}
     foreach ($array_value["pid"] as $nkeys=>$nvalues){
		  switch ($array_value["flag"])
		  {
			   case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
			   case 2: $fields_name="weiprice";$namev="综合价格走势图";break;
			   case 3: $fields_name="apindex";$namev="价格指数走势图";break;
			   case 4: $fields_name="mindex";$namev="综合指数走势图";break;
			   case 5: $fields_name="price";$namev="综合指数走势图";break;
		  }

		 $sql="select * from shpi_material where topicture='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
    // $sql="select * from shpi_material where topicture='".$nvalues."' and dateday>='2004-09-28' and dateday<='".$array_value["etime"]."' order by dateday asc";
		  $rs=$this->connshpi->Execute($sql);$i=1;
		  while (!$rs->EOF){
			$dataday=$rs->fields("dateday"); $price=$rs->fields($fields_name);
			//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
			$array_price["m".$nvalues][$dataday]=$price;
			$rs->MoveNext();
		  }
	 }

	  return $array_price;
  }
  //===========
  #max
  //============
  function arr_max($array_value){

  // echo "<pre>";
  // print_r($array_value);

      $max=-1;$min=999999999;
	  foreach ($array_value as $news=>$name){
	     if ($name>$max){$max=$name;}
		 if ($name<$min){$min=$name;}
	  }
	  $arr_mma["max"]=$max;$arr_mma["min"]=$min;
	  return $arr_mma;
  }
	  /*周环比*/
	  function weekday($date){
	     //echo $date;
	     $wnum=date("w",strtotime($date));
	     if ($wnum==0){
		    $weekd["be"]=$date;
			$weekd["bs"]=date("Y-m-d",strtotime ("-6 day $date"));
			$weekd["se"]=date("Y-m-d",strtotime ("-7 day $date"));
			$weekd["ss"]=date("Y-m-d",strtotime ("-13 day $date"));
		 }else{
		    $jwn=7-$wnum;
			$gwn=$wnum-1;
		    $weekd["be"]=date("Y-m-d",strtotime ("+$jwn day $date"));
			$weekd["bs"]=date("Y-m-d",strtotime ("-$gwn day $date"));
			//为了处理国庆节期间无数据，将日期强行推前1个星期
			//$sgwn=$wnum+12;
			$sgwn=$wnum+6;
			$weekd["se"]=date("Y-m-d",strtotime ("-$wnum day $date"));
			$weekd["ss"]=date("Y-m-d",strtotime ("-$sgwn day $date"));
		 }
		// if (date("d")>16 && date("d")< 28){
		 	 //$weekd=array("be"=>'2007-03-02',"bs" =>'2007-02-25',"se" =>'2007-02-16',"ss" =>'2007-02-12');
		 //}

		 //added by shizg for 国庆节数据处理 started 2017/10/10
		 require_once('/etc/steelconf/config/isholiday.php');
		$date2 = $weekd["ss"];
		$needckecklastweek = 0;
		while($date2<$weekd["se"]){
			if(! _isholiday($date2)){
				$needckecklastweek = 1;
			}
			$date2 = date("Y-m-d",strtotime($date2."+1 day"));
		}
		 if($needckecklastweek==0){
			$weekd["se"]=date("Y-m-d",strtotime ($weekd["se"]."-7 day "));
			$weekd["ss"]=date("Y-m-d",strtotime ($weekd["ss"]."-7 day "));
		 }
		 //added by shizg for 国庆节数据处理 ended 2017/10/10
	     return $weekd;
	  }
	   /*月环比*/
	  function monday($date){
	     $mnum=date("n",strtotime($date));
		 $y=date("Y",strtotime($date));
	     if ($mnum==1){
		    $weekd["bs"]=$y."-01-01";
		    $weekd["be"]=$y."-01-".date("d",strtotime($date));
		    $weekd["ss"]=($y-1)."-12-01";
		    // $weekd["se"]=$y . '-12' . date("-t", strtotime($y."-12"));
			$weekd["se"]=($y-1) . '-12' . date("-t", strtotime($y."-12"));
		 }else{
		    $weekd["bs"]=$y."-".date("m",strtotime($date))."-01";
		    $weekd["be"]=$y."-".date("m",strtotime($date))."-".date("d",strtotime($date));
			$mnum1=$mnum-1;
			if ($mnum1<10){$mnum1="0".$mnum1;}
		    $weekd["ss"]=$y."-".$mnum1."-01";
		    // $weekd["se"]=$y."-".$mnum1."-31";
			$weekd["se"]=$y."-".$mnum1.date("-t", strtotime($y."-".$mnum1));
		 }
	     return $weekd;
	  }
	  /*季环比*/
	   function season($date){
	     $mnum=date("n",strtotime($date));
		 $y=date("Y",strtotime($date));
		 switch ($mnum){
		    case 1:
			case 2:
			case 3:
			   $weekd["bs"]=$y."-01-01";$weekd["be"]=$y."-03-".date("d",strtotime($date));$weekd["ss"]=($y-1)."-10-01";$weekd["se"]=($y-1) . '-12' . date("-t", strtotime(($y-1)."-12"));break;
			case 4:
			case 5:
			case 6:
			  $weekd["bs"]=$y."-04-01";$weekd["be"]=$y."-06-".date("d",strtotime($date));$weekd["ss"]=$y."-01-01";$weekd["se"]=$y."-03-31";break;
			case 7:
			case 8:
			case 9:
			  $weekd["bs"]=$y."-07-01";$weekd["be"]=$y."-09-".date("d",strtotime($date));$weekd["ss"]=$y."-04-01";$weekd["se"]=$y."-06-30";break;
			case 10:
			case 11:
			case 12:
			  $weekd["bs"]=$y."-10-01";$weekd["be"]=$y."-12-".date("d",strtotime($date));$weekd["ss"]=$y."-07-01";$weekd["se"]=$y."-09-30";break;
		 }
		 if($weekd["be"]==$y."-09-31") {
			$weekd["be"] = $y."-09-30";
		 }
		 if($weekd["be"]==$y."-06-31") {
			$weekd["be"] = $y."-06-30";
		 }
	     return $weekd;
	  }
	    /*年环比*/
	  function yearday($date){
		 $y=date("Y",strtotime($date));
		 $weekd["bs"]=$y."-01-01";
		 $weekd["be"]=$y."-12-".date("d",strtotime($date));
		 $weekd["ss"]=($y-1)."-01-01";
		 $weekd["se"]=($y-1) . '-12' . date("-t", strtotime(($y-1)."-12"));
	     return $weekd;
	  }

function ntb_year($ttdatadayx)
	  {
		  $y=date("Y-m-d");
		  $d=date("Y");
		  $seyy=date("Y-m-d",strtotime('-1 year',strtotime($ttdatadayx)));
		  $ss=date("Y",strtotime('-1 year',strtotime(date("Y"))));

		  $weekd["ss"]=$ss."-01-01";
		  $weekd["bs"]=$d."-01-01";
		  $weekd["be"]=$ttdatadayx;
		  $weekd["se"]=$seyy;

		  return $weekd;

	  }

	  //======
	  function pi_bj($connall,$arr_date){
		 //$weekd=weekday($date);
		 //本平均价
		 $sql="select avg(wpindex) as bawp from shpi_pi where dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."'";
		 //echo $sql."==<br>";
		 $rsb=$connall->Execute($sql);
		 $bavpi=$rsb->fields("bawp");
		 //上平均价
		 $sql="select avg(wpindex) as sawp from shpi_pi where dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."'";
		 $rss=$connall->Execute($sql);
		 //echo $sql."+<br>";
		 $savpi=$rss->fields("sawp");
		 if($savpi != 0)
		 $bp=round((($bavpi-$savpi)/$savpi)*100,2);
		 else
		 $bp="&nbsp;";
		 //echo $bp;
		 return $bp;
	  }
	  function pi_bpzbj($connall,$arr_date,$pid){
		 //$weekd=weekday($date);
		 //本平均价
		 $sql="select avg(wpindex) as bawp from shpi_pp where bc_id='".$pid."' and dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."'";
		 //echo $sql."==<br>";
		 $rsb=$connall->Execute($sql);
		 $bavpi=$rsb->fields("bawp");
		 //上平均价
		 $sql="select avg(wpindex) as sawp from shpi_pp where bc_id='".$pid."' and dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."'";
		 $rss=$connall->Execute($sql);
		 //echo $sql."+<br>";
		 $savpi=$rss->fields("sawp");
		 if($savpi != 0)
		 $bp=round((($bavpi-$savpi)/$savpi)*100,2);
		 else
		 $bp="&nbsp;";
		 return $bp;
	  }
	  //======
	  function pzpi_bpzbj($connall,$arr_date,$pid,$fname="wpindex"){
		 //$weekd=weekday($date);weiprice
		 //本平均价
		 $sql="select avg(".$fname.") as bawp from shpi_pzp  where bvm_id='".$pid."' and dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."'";
		 //echo $sql."==<br>";
		 $rsb=$connall->Execute($sql);
		 $bavpi=$rsb->fields("bawp");
		 //上平均价
		 $sql="select avg(".$fname.") as sawp from shpi_pzp  where bvm_id='".$pid."' and dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."'";
		 $rss=$connall->Execute($sql);
		 //echo $sql."+<br>";
		 $savpi=$rss->fields("sawp");
		 if($savpi != 0)
		 $bp=round((($bavpi-$savpi)/$savpi)*100,2);
		 else
		 $bp="&nbsp;";
		 return $bp;
	  }


/*
*原料比
*关键词：原料
*/

	  function material_week_tb($connall,$arr_date,$tag){
		 //$weekd=weekday($date);
		 //本平均价
		 $sql="select avg(price) as bawp from shpi_material where dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."' and topicture='".$tag."'";
		 //echo $sql."==<br>";
		 $rsb=$connall->Execute($sql);
				// if ( $rsb ) {
			 $bavpi=$rsb->fields("bawp");
		// }
		 //上平均价
		 $sql="select avg(price) as sawp from shpi_material where dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."' and topicture='".$tag."'";
		 $rss=$connall->Execute($sql);
		//  echo $sql."<br>";
		 //print_r($rss);echo "<br>";
		 $savpi=$rss->fields("sawp");
		 //echo $savpi."<br>";
		 if($bavpi != null && $savpi !=null && $savpi != 0)
		 $bp=round((($bavpi-$savpi)/$savpi)*100,2);
		 else
		 $bp="&nbsp;";
		 //echo $bp;
		 return $bp;
	  }
	  function material_week_pp($connall,$arr_date,$tag){
	  	//$weekd=weekday($date);
	  	//本平均价
	  	$sql="select avg(wpindex) as bawp from shpi_pp where dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."' and bc_id='".$tag."'";
	  	//echo $sql."==<br>";
	  	$rsb=$connall->Execute($sql);
	  	$bavpi=$rsb->fields("bawp");
	  	//上平均价
	  	$sql="select avg(wpindex) as sawp from shpi_pp where dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."' and bc_id='".$tag."'";
	  	$rss=$connall->Execute($sql);
	  	// echo $sql."+<br>";
	  	$savpi=$rss->fields("sawp");
	  	if($bavpi != null && $savpi !=null && $savpi != 0)
	  		$bp=round((($bavpi-$savpi)/$savpi)*100,2);
	  	else
	  		$bp="&nbsp;";
	  	//echo $bp;
	  	return $bp;
	  }
	  function shpi_steelscrap_cg_week($connall,$arr_date,$tag){
		//$weekd=weekday($date);
		//本平均价
		$sql="select avg(mindex) as bawp from shpi_steelscrap_cg where ndate>='".$arr_date["bs"]."' and ndate<='".$arr_date["be"]."' and type='".$tag."'";
		//echo $sql."==<br>";
		$rsb=$connall->Execute($sql);
		$bavpi=$rsb->fields("bawp");
		//上平均价
		$sql="select avg(mindex) as sawp from shpi_steelscrap_cg where ndate>='".$arr_date["ss"]."' and ndate<='".$arr_date["se"]."' and type='".$tag."'";
		$rss=$connall->Execute($sql);
		// echo $sql."+<br>";
		$savpi=$rss->fields("sawp");
		if($bavpi != null && $savpi !=null && $savpi != 0)
			$bp=round((($bavpi-$savpi)/$savpi)*100,2);
		else
			$bp="-";
		//echo $bp;
		return $bp;
	}
	function shpi_steelscrap_cg_week_all($connall,$arr_date,$tag){
		//$weekd=weekday($date);
		//本平均价
		$sql="select avg(mindex) as bawp,type from shpi_steelscrap_cg where ndate>='".$arr_date["bs"]."' and ndate<='".$arr_date["be"]."'";
		//echo $sql."==<br>";
		$rsb=$connall->getarray($sql);
		//$bavpi=$rsb->fields("bawp");
		$bavpiarr=array();
		foreach ($rsb as $k => $v) {
			$bavpiarr[$v['type']]=$v['bawp'];
		}

		$sql="select avg(mindex) as sawp,type from shpi_steelscrap_cg where ndate>='".$arr_date["ss"]."' and ndate<='".$arr_date["se"]."'";
		//echo $sql."==<br>";
		$rss=$connall->getarray($sql);
		//$bavpi=$rsb->fields("bawp");
		$savpiarr=array();
		foreach ($rss as $k => $v) {
			$savpiarr[$v['type']]=$v['sawp'];
		}
		//上平均价
		$sql="select avg(mindex) as sawp from shpi_steelscrap_cg where ndate>='".$arr_date["ss"]."' and ndate<='".$arr_date["se"]."'";
		$rss=$connall->getarray($sql);
		$data=array();
		foreach ($bavpiarr as $k => $bavpi) 
		{
			$savpi=$savpiarr[$k];
			if($bavpi != null && $savpi !=null && $savpi != 0)
			$bp=round((($bavpi-$savpi)/$savpi)*100,2);
		    else
			$bp="&nbsp;";
			$data[$k]=$bp;
		}
		return $data;
	}

	  function date_change_day($date,$date_day){
		// echo "<pre>";print_r($date_day);
	  	if (is_array($date)){

	  		$n = count($date);

	  		for($i=0;$i<$n;$i++){
	  			foreach ($date_day as $key=>$values){
	  				$values1 = $values;
	  				$j=0;
	  				while($date[$i][$values1]==""){
	  					$values1 =date("Y-m-d",strtotime($values1)-(1 * 24 * 60 * 60));
	  					$j++;
	  					if($j>31){
	  						break;
	  					}
	  				}
	  				$xxyy[$i][$values]=$date[$i][$values1];
	  			}
	  		}
	  	}
	  	return  $xxyy;
	  }
	  function creat_dd($date_day,$zhgc){
	  	$n = count($zhgc)-1;
	  	$first = array_values($zhgc[$n]);
	  	foreach($date_day as $k=>$v){
	  		if($k==0){
	  			if($zhgc[$n][$v]==""){
	  				$kk = strtotime($v);
	  				$day_mate = date("Y-m",$kk);
	  				$day = $day_mate."-15";
	  				if(strtotime($v)<strtotime($day)){

	  					$creat_array[$v]=intval(round($first[0]));
	  				}
	  			}else{
	  				$creat_array[$v] =intval(round($zhgc[$n][$v]));
	  			}

	  		}else{
	  			if($zhgc[$n][$v]!=""){
	  				$creat_array[$v] = intval(round($zhgc[$n][$v]));
	  			}else{
	  				$creat_array[$v] = "";
	  			}
	  		}
	  	}
	  	$creat_array = array_values($creat_array);
	  	foreach ($creat_array as $key=>$value){
	  		if($value!=""){
	  			$news_key = $key+8;
	  			$creat_array[$key] = "";
	  			$creat_array[$news_key] = $value;
	  		}
	  	}
	  	return $creat_array;
	  }
	  function date_change($date){

	  	$xxyy[0]=$date[0];
	  	if (is_array($date)){
	  		$all_key = array_keys($date[0]);
	  		$n = count($date);
	  	// echo "<pre>";print_r($all_key);exit;
	  		for($i=1;$i<$n;$i++){
	  			foreach ($all_key as $key=>$values){
	  				$values1 = $values;
	  				$j=0;
	  				while($date[$i][$values1]==""){
	  					$values1 =date("Y-m-d",strtotime($values1)-(1 * 24 * 60 * 60));
	  					$j++;
	  					if($j>31){
	  						continue;
	  					}
	  				}
	  				$xxyy[$i][$values]=$date[$i][$values1];
	  			}
	  		}
	  	}

	  	return  $xxyy;
	  }
	  function get_date_new($pz,$stime,$etime,$tableflag){
	  	//foreach ($array_value["pid"] as $nkeys=>$nvalues){


	  	if($tableflag=='1'){
	  		$nvalues = $pz[0];
	  		$sql="select * from shpi_material_pzp  where vid='".$nvalues."' and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
	  	}else if($tableflag=='2'){
	  		$nvalues = $pz[0];
	  		$sql="select * from shpi_mj_pzp  where vid=0 and type='".$nvalues."' and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
	  	}else if($pz[0]>'8'){
			$nvalues = $pz[0];
			$sql="select * from shpi_pp  where bc_id='".$nvalues."' and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
		}
	  	$rs=$this->connshpi->Execute($sql);

	  	while (!$rs->EOF){
	  		$dataday=$rs->fields("dateday");
	  		$rs->MoveNext();
	  		$arr_date[] = $dataday;
	  	}

	  	return $arr_date;
	  }
	  function zhgc_shpi_two($array_value){
		  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('2','5'));
		  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  	foreach ($array_value["pid"] as $nkeys=>$nvalues){
	  		switch ($array_value["flag"])
	  		{
				//update by zfy started 2017/7/18
	  			case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
	  			case 2:
					$fields_name="price";
					$namev="综合价格走势图";
					$fields_name_5="price_avg_5";
					$fields_name_10="price_avg_10";
					$fields_name_20="price_avg_20";
					$fields_name_40="price_avg_40";
					$fields_name_60="price_avg_60";
					$fields_name_200="price_avg_200";
					break;
	  			case 3: $fields_name="weiindex";$namev="价格指数走势图";break;
	  			case 4: $fields_name="wpindex";$namev="综合指数走势图";break;
	  			case 5:
					$fields_name="mindex";
					$namev="综合指数走势图";
					$fields_name_5="mindex_avg_5";
					$fields_name_10="mindex_avg_10";
					$fields_name_20="mindex_avg_20";
					$fields_name_40="mindex_avg_40";
					$fields_name_60="mindex_avg_60";
					$fields_name_200="mindex_avg_200";
					break;
				//update by zfy ended 2017/7/18
	  		}
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  		if($fields_name=='weiprice' || $fields_name=='aveprice'|| $fields_name=='price')
	  		{
	  			$fwp="weekp";
	  			$fmp="monthp";
	  		}
	  		if($fields_name=='weiindex' || $fields_name=='wpindex'||$fields_name=="mindex")
	  		{
	  			$fwp="weekindexp";
	  			$fmp="monthindexp";
			}
	  		//echo $array_value["stime"];
	  		$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
	  		$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
	  		$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

			//add by zfy started 2017/7/19
			$leixing_sum = count($array_value['leixing']);
			//add by zfy ended 2017/7/19

	  		//$time2 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		foreach($array_value['leixing'] as $key=>$value){

	  			if($value=='2'){
	  				$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=4 and bigtype='yl' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_week where 1 and type=4 and bigtype='yl' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  				//echo $sql;
	  			}else if($value=='3'){
	  				$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=4 and bigtype='yl' and smalltype='".$nvalues."'    order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_month where 1 and type=4 and bigtype='yl' and smalltype='".$nvalues."'  and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  			}else
	  			{
	  				$sql = "select * from shpi_material where topicture='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_material where topicture='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  			}
				//add by zfy started 2020/06/19 基准日
				$basedays = $this->connshpi->getrow($sqldate);
				//add by zfy ended 2020/06/19 基准日
	  			//echo "sql1:$sql<br/>";
	  			$rs=$this->connshpi->Execute($sql);$i=1;
	  			while (!$rs->EOF){
	  				$dataday=$rs->fields("dateday");

	  				if($value==2)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
	  				else if($value==3)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}else{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex){
							// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
							$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
						}else {
							$price = $rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5 = $rs->fields($fields_name_5);
							$price_10 = $rs->fields($fields_name_10);
							$price_20 = $rs->fields($fields_name_20);
							$price_40 = $rs->fields($fields_name_40);
							$price_60 = $rs->fields($fields_name_60);
							$price_200 = $rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
					//print_r($price);
	  				//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
	  				$array_price[$key][$dataday]=$price;
				//add by zfy started 2017/7/18
				$i = 1;
				if($leixing_sum == 1&&is_array($array_value['leixing'])&&in_array('1',$array_value['leixing'])){
					if (is_array($array_value['avgprice'])) {
						if(in_array('5',$array_value["avgprice"])){
							if($price_5!=""&&$price_5!='0')
							$array_price[$i][$dataday]=$price_5;
							$i++;
						}
						if(in_array('10',$array_value["avgprice"])){
							if($price_10!=""&&$price_10!='0')
							$array_price[$i][$dataday]=$price_10;
							$i++;
						}
						if(in_array('20',$array_value["avgprice"])){
							if($price_20!=""&&$price_20!='0')
							$array_price[$i][$dataday]=$price_20;
							$i++;
						}
						if(in_array('40',$array_value["avgprice"])){
							if($price_40!=""&&$price_40!='0')
							$array_price[$i][$dataday]=$price_40;
							$i++;
						}
						if(in_array('60',$array_value["avgprice"])){
							if($price_60!=""&&$price_60!='0')
							$array_price[$i][$dataday]=$price_60;
							$i++;
						}
						if(in_array('200',$array_value["avgprice"])){
							if($price_200!=""&&$price_200!='0')
							$array_price[$i][$dataday]=$price_200;
							$i++;
						}
					}
				}
				//add by zfy ended 2017/7/18
	  				$rs->MoveNext();
	  			}
	  		}
	  	}
	  	return $array_price;
	  }
	  function tks_shpi_two($array_value){
		  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('2','3', '4'));
		  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  	foreach ($array_value["pid"] as $nkeys=>$nvalues){
	  		switch ($array_value["flag"])
	  		{
				//update by zfy started 2017/7/19
	  			case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
	  			case 2:
					$fields_name="weiprice";
					$namev="综合价格走势图";
					$fields_name_5="weiprice_avg_5";
					$fields_name_10="weiprice_avg_10";
					$fields_name_20="weiprice_avg_20";
					$fields_name_40="weiprice_avg_40";
					$fields_name_60="weiprice_avg_60";
					$fields_name_200="weiprice_avg_200";
					break;
	  			case 3:
					$fields_name="weiindex";
					$namev="价格指数走势图";
					$fields_name_5="weiindex_avg_5";
					$fields_name_10="weiindex_avg_10";
					$fields_name_20="weiindex_avg_20";
					$fields_name_40="weiindex_avg_40";
					$fields_name_60="weiindex_avg_60";
					$fields_name_200="weiindex_avg_200";
					break;
	  			case 4: $fields_name="wpindex";$namev="综合指数走势图";break;
	  			case 5: $fields_name="mindex";$namev="综合指数走势图";break;
				//update by zfy ended 2017/7/19
	  		}
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  		if($fields_name=='weiprice' || $fields_name=='aveprice'|| $fields_name=='price')
	  		{
	  			$fwp="weekp";
	  			$fmp="monthp";
	  		}
	  		if($fields_name=='weiindex' || $fields_name=='wpindex' )
	  		{
	  			$fwp="weekindexp";
	  			$fmp="monthindexp";
	  		}
	  		//echo $array_value["stime"];
	  		$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
	  		$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
	  		$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

			//add by zfy started 2017/7/19
			$leixing_sum = count($array_value['leixing']);
			//add by zfy ended 2017/7/19

	  		//$time2 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		foreach($array_value['leixing'] as $key=>$value){
	  			if($value=='2'){
	  				$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_week  where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  				//echo $sql;
	  			}else if($value=='3'){
	  				$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_month  where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  			}else
	  			{
	  				$sql="select * from shpi_material_pzp  where vid=".$nvalues." and (dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."') order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_material_pzp  where vid=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日

	  				//$sql = "select * from shpi_material where topicture='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' order by dateday asc";
	  			}
				//add by zfy started 2020/06/19 基准日
				$basedays = $this->connshpi->getrow($sqldate);
				//add by zfy ended 2020/06/19 基准日
	  			$rs=$this->connshpi->Execute($sql);$i=1;
	  			while (!$rs->EOF){
	  				$dataday=$rs->fields("dateday");

	  				if($value==2)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
	  				else if($value==3)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}else{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1){
							// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
							$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
						}else {
							$price = $rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5 = $rs->fields($fields_name_5);
							$price_10 = $rs->fields($fields_name_10);
							$price_20 = $rs->fields($fields_name_20);
							$price_40 = $rs->fields($fields_name_40);
							$price_60 = $rs->fields($fields_name_60);
							$price_200 = $rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
	  				//if ($news==0){$Data1[$i]=date("n/j",strtotime($dataday)); }
	  				$array_price[$key][$dataday]=$price;
				//add by zfy started 2017/7/18
				$i = 1;
				if($leixing_sum == 1&&in_array('1',$array_value['leixing'])){
					if (is_array($array_value['avgprice'])) {
						if (in_array('5', $array_value["avgprice"])) {
							if ($price_5 != "" && $price_5 != '0')
								$array_price[$i][$dataday] = $price_5;
							$i++;
						}
						if (in_array('10', $array_value["avgprice"])) {
							if ($price_10 != "" && $price_10 != '0')
								$array_price[$i][$dataday] = $price_10;
							$i++;
						}
						if (in_array('20', $array_value["avgprice"])) {
							if ($price_20 != "" && $price_20 != '0')
								$array_price[$i][$dataday] = $price_20;
							$i++;
						}
						if (in_array('40', $array_value["avgprice"])) {
							if ($price_40 != "" && $price_40 != '0')
								$array_price[$i][$dataday] = $price_40;
							$i++;
						}
						if (in_array('60', $array_value["avgprice"])) {
							if ($price_60 != "" && $price_60 != '0')
								$array_price[$i][$dataday] = $price_60;
							$i++;
						}
						if (in_array('200', $array_value["avgprice"])) {
							if ($price_200 != "" && $price_200 != '0')
								$array_price[$i][$dataday] = $price_200;
							$i++;
						}
					}
				}
				//add by zfy ended 2017/7/18
	  				$rs->MoveNext();
	  			}
	  		}
	  	}
	  	return $array_price;
	  }
	  function arr_tks_pzp_jkxhk_one($pzggcz,$flag,$stime,$etime,$leixing,$biao="",$avgprice=0,$array_value=array())
	  {
		  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('2','3','6'));
		  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  	global $min,$max,$tabledate,$tstime,$tetime;
	  	foreach ($pzggcz as $nkeys=>$nvalues){
	  		switch ($flag)
	  		{

				//update by zfy started 2017/7/21
	  			case 1:
					$fields_name="aveprice";
					$namev="平均价格走势图";
					$fields_name_5="aveprice_avg_5";
					$fields_name_10="aveprice_avg_10";
					$fields_name_20="aveprice_avg_20";
					$fields_name_40="aveprice_avg_40";
					$fields_name_60="aveprice_avg_60";
					$fields_name_200="aveprice_avg_200";
					break;
	  			case 2:
					$fields_name="weiprice";
					$namev="综合价格走势图";
					$fields_name_5="weiprice_avg_5";
					$fields_name_10="weiprice_avg_10";
					$fields_name_20="weiprice_avg_20";
					$fields_name_40="weiprice_avg_40";
					$fields_name_60="weiprice_avg_60";
					$fields_name_200="weiprice_avg_200";
					break;
	  			case 3:
					$fields_name="weiindex";
					$namev="价格指数走势图";
					$fields_name_5="weiindex_avg_5";
					$fields_name_10="weiindex_avg_10";
					$fields_name_20="weiindex_avg_20";
					$fields_name_40="weiindex_avg_40";
					$fields_name_60="weiindex_avg_60";
					$fields_name_200="weiindex_avg_200";
					break;
	  			case 4:
					$fields_name="wpindex";
					$namev="综合指数走势图";
					$fields_name_5="wpindex_avg_5";
					$fields_name_10="wpindex_avg_10";
					$fields_name_20="wpindex_avg_20";
					$fields_name_40="wpindex_avg_40";
					$fields_name_60="wpindex_avg_60";
					$fields_name_200="wpindex_avg_200";
					break;
	  			case 5:
					$fields_name="weipriceusb";
					$namev="综合指数走势图";
					$fields_name_5="weipriceusb_avg_5";
					$fields_name_10="weipriceusb_avg_10";
					$fields_name_20="weipriceusb_avg_20";
					$fields_name_40="weipriceusb_avg_40";
					$fields_name_60="weipriceusb_avg_60";
					$fields_name_200="weipriceusb_avg_200";
					break;
				//caimm stared for 2018/02/26
				case 6:
					$fields_name="mindex";
					$namev="综合指数走势图";
					$fields_name_5="mindex_avg_5";
					$fields_name_10="mindex_avg_10";
					$fields_name_20="mindex_avg_20";
					$fields_name_40="mindex_avg_40";
					$fields_name_60="mindex_avg_60";
					break;
				case 7:
					$fields_name="price";
					$namev="综合价格走势图";
					$fields_name_5="price_avg_5";
					$fields_name_10="price_avg_10";
					$fields_name_20="price_avg_20";
					$fields_name_40="price_avg_40";
					$fields_name_60="price_avg_60";
					break;
				//caimm ended for 2018/02/26
				//update by zfy ended 2017/7/21

	  		}
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  		if($fields_name=='weiprice' || $fields_name=='aveprice'|| $fields_name=='price')
	  		{
	  			$fwp="weekp";
	  			$fmp="monthp";
	  		}
	  		if($fields_name=='weiindex' || $fields_name=='wpindex'|| $fields_name=='weipriceusb'|| $fields_name=="mindex")
	  		{
	  			$fwp="weekindexp";
	  			$fmp="monthindexp";
	  		}
	  		$time1 =date("Y-m-d",strtotime($stime)-(7 * 24 * 60 * 60));
	  		$time2 =date("Y-m-d",strtotime($etime)+(7 * 24 * 60 * 60));
	  		$time3 =date("Y-m-d",strtotime($stime)-(30 * 24 * 60 * 60));
	  		$time4 =date("Y-m-d",strtotime($etime)+(30 * 24 * 60 * 60));

	  		//data1 日期
	  		$min=1000;
	  		$max=-1;
	  		$namev="平均价格走势图";
	  		foreach($leixing as $key=>$value){
	  			if($value=='2'){

	  				$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_week where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  				if($biao=="mj"){
	  					$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=6 and bigtype='mj' and smalltype='".$nvalues."' order by dateday asc";
						//add by zfy started 2020/06/19 基准日
						$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
						//add by zfy ended 2020/06/19 基准日
						if($nvalues == '1_1')//国产炼焦煤
						{
							$sql = "select * from shpi_week where dateday>='" . $time1 . "' and dateday<='" . $time2 . "' and type=6 and bigtype='mj' and smalltype='4' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='4' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						if($nvalues == '1_2')//进口炼焦煤
						{
							$sql = "select * from shpi_week where dateday>='" . $time1 . "' and dateday<='" . $time2 . "' and type=6 and bigtype='mj' and smalltype='5' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='5' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						//xiangbin add 20180117 start
						if($nvalues == '2_3')//长江港口动力煤
						{
							$sql = "select * from shpi_week where dateday>='" . $time1 . "' and dateday<='" . $time2 . "' and type=6 and bigtype='mj' and smalltype='6' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='6' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						if($nvalues == '3_3')//长江无烟煤 SHPCII-WY-CJ
						{
							$sql = "select * from shpi_week where dateday>='" . $time1 . "' and dateday<='" . $time2 . "' and type=6 and bigtype='mj' and smalltype='7' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='7' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						if($nvalues == '3_4')//长江烟煤 SHPCII-YM-CJ
						{
							$sql = "select * from shpi_week where dateday>='" . $time1 . "' and dateday<='" . $time2 . "' and type=6 and bigtype='mj' and smalltype='8' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='8' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						//xiangbin add 20180117 start
						if($nvalues == 'm3')//焦炭
						{
							$sql = "select * from shpi_week where dateday>='" . $time1 . "' and dateday<='" . $time2 . "' and type=4 and bigtype='yl' and smalltype='3' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=4 and bigtype='yl' and smalltype='3' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						//caimm ended for 2018/02/26
	  				}
	  				//echo $sql;
	  			}else if($value=='3'){
	  				$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_month where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  				if($biao=="mj"){
	  					$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=6 and bigtype='mj' and smalltype='".$nvalues."' order by dateday asc";
						//add by zfy started 2020/06/19 基准日
						$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
						//add by zfy ended 2020/06/19 基准日
						if($nvalues == '1_1')//国产炼焦煤
							{
								$sql = "select * from shpi_month where dateday>='" . $time3 . "' and dateday<='" . $time4 . "' and type=6 and bigtype='mj' and smalltype='4' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='4' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '1_2')//进口炼焦煤
							{
								$sql = "select * from shpi_month where dateday>='" . $time3 . "' and dateday<='" . $time4 . "' and type=6 and bigtype='mj' and smalltype='5' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='5' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '2_3')//长江港口动力煤
							{
								$sql = "select * from shpi_month where dateday>='" . $time3 . "' and dateday<='" . $time4 . "' and type=6 and bigtype='mj' and smalltype='6' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='6' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '3_3')//长江无烟煤 SHPCII-WY-CJ
							{
								$sql = "select * from shpi_month where dateday>='" . $time3 . "' and dateday<='" . $time4 . "' and type=6 and bigtype='mj' and smalltype='7' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='7' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '3_4')//长江烟煤 SHPCII-YM-CJ
							{
								$sql = "select * from shpi_month where dateday>='" . $time3 . "' and dateday<='" . $time4 . "' and type=6 and bigtype='mj' and smalltype='8' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='8' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
								if($nvalues == 'm3')//焦炭
							{
								$sql = "select * from shpi_month where dateday>='" . $time3 . "' and dateday<='" . $time4 . "' and type=4 and bigtype='yl' and smalltype='3' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_month where type=4 and bigtype='yl' and smalltype='3' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							//caimm ended for 2018/02/26

	  				}
	  			}else
	  			{
	  				$sql = "select * from shpi_material_pzp where vid=".$nvalues." and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_material_pzp where vid=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
	  				if($biao=="mj"){
	  					$sql = "select * from shpi_mj_pzp where vid=0 and type=".$nvalues." and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
						//add by zfy started 2020/06/19 基准日
						$sqldate = "select *  from shpi_mj_pzp where vid=0 and type=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
						//add by zfy ended 2020/06/19 基准日
						//add by zhudahua start 20160926
							if($nvalues == '1_1')//国产炼焦煤
							{
								$sql="select * from shpi_mj_pzp  where vid=1 and type=1 and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_mj_pzp where vid=1 and type=1 and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '1_2')//进口炼焦煤
							{
								$sql="select * from shpi_mj_pzp  where vid=2 and type=1 and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_mj_pzp where vid=2 and type=1 and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							//add by zhudahua end 20160926
							if($nvalues == '2_3')//长江港口动力煤
							{
								$sql="select * from shpi_mj_pzp  where vid=3 and type=2 and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_mj_pzp where vid=3 and type=2 and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '3_3')//长江无烟煤 SHPCII-WY-CJ
							{
								$sql="select * from shpi_mj_pzp  where vid=3 and type=3 and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_mj_pzp where vid=3 and type=3 and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == '3_4')//长江烟煤 SHPCII-YM-CJ
							{
								$sql="select * from shpi_mj_pzp  where vid=4 and type=3 and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_mj_pzp where vid=4 and type=3 and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							if($nvalues == 'm3')//焦炭
							{
								$sql = "select * from shpi_material where topicture='3' and dateday>='" . $stime . "' and dateday<='" . $etime . "' order by dateday asc";
								//add by zfy started 2020/06/19 基准日
								$sqldate = "select *  from shpi_material where topicture='3' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
								//add by zfy ended 2020/06/19 基准日
							}
							//caimm ended for 2018/02/26
	  				}
	  			}
				//add by zfy started 2020/06/19 基准日
				$basedays = $this->connshpi->getrow($sqldate);
				//add by zfy ended 2020/06/19 基准日
	  			$rs=$this->connshpi->Execute($sql);
	  			while (!$rs->EOF){
	  				$dataday=$rs->fields("dateday");
	  				if($value==2)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
	  				else if($value==3)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}else{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1){
							// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
							$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
						}else {
							$price=$rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5=$rs->fields($fields_name_5);
							$price_10=$rs->fields($fields_name_10);
							$price_20=$rs->fields($fields_name_20);
							$price_40=$rs->fields($fields_name_40);
							$price_60=$rs->fields($fields_name_60);
							$price_200=$rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
	  				$array_price[$key][$dataday]=$price;
					//add by zfy started 2017/7/18
				$i = 1;
				if(is_array($leixing)&&count($leixing)=='1'&&in_array('1',$leixing)){
					if(is_array($avgprice) && in_array('5',$avgprice)){
						if($price_5!=""&&$price_5!='0')
						$array_price[$i][$dataday]=$price_5;
						$i++;
					}
					if(is_array($avgprice) && in_array('10',$avgprice)){
						if($price_10!=""&&$price_10!='0')
						$array_price[$i][$dataday]=$price_10;
						$i++;
					}
					if(is_array($avgprice) && in_array('20',$avgprice)){
						if($price_20!=""&&$price_20!='0')
						$array_price[$i][$dataday]=$price_20;
						$i++;
					}
					if(is_array($avgprice) && in_array('40',$avgprice)){
						if($price_40!=""&&$price_40!='0')
						$array_price[$i][$dataday]=$price_40;
						$i++;
					}
					if(is_array($avgprice) && in_array('60',$avgprice)){
						if($price_60!=""&&$price_60!='0')
						$array_price[$i][$dataday]=$price_60;
						$i++;
					}
					if(is_array($avgprice) && in_array('200',$avgprice)){
						if($price_200!=""&&$price_200!='0')
						$array_price[$i][$dataday]=$price_200;
						$i++;
					}
				}
				//add by zfy ended 2017/7/18
	  				$rs->MoveNext();
	  			}
	  		}
	  	}

	  	return $array_price;
	  }
	  function arr_tks_pzp_jkxhk_one_jk($pzggcz,$flag,$stime,$etime,$leixing,$biao="",$avgprice="",$array_value=array())
	  {
		  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('5'));
		  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  	 // echo "<pre>";print_r($pzggcz);
	  	global $min,$max,$tabledate,$tstime,$tetime;
	  	foreach ($pzggcz as $nkeys=>$nvalues){
	  		switch ($flag)
	  		{
				//update by zfy started 2017/7/21
	  			case 1:
					$fields_name="aveprice";
					$namev="平均价格走势图";
					$fields_name_5="aveprice_avg_5";
					$fields_name_10="aveprice_avg_10";
					$fields_name_20="aveprice_avg_20";
					$fields_name_40="aveprice_avg_40";
					$fields_name_60="aveprice_avg_60";
					$fields_name_200="aveprice_avg_200";
					break;
	  			case 2:
					$fields_name="weiprice";
					$namev="综合价格走势图";
					$fields_name_5="weiprice_avg_5";
					$fields_name_10="weiprice_avg_10";
					$fields_name_20="weiprice_avg_20";
					$fields_name_40="weiprice_avg_40";
					$fields_name_60="weiprice_avg_60";
					$fields_name_200="weiprice_avg_200";
					break;
	  			case 3:
					$fields_name="weiindex";
					$namev="价格指数走势图";
					$fields_name_5="weiindex_avg_5";
					$fields_name_10="weiindex_avg_10";
					$fields_name_20="weiindex_avg_20";
					$fields_name_40="weiindex_avg_40";
					$fields_name_60="weiindex_avg_60";
					$fields_name_200="weiindex_avg_200";
					break;
	  			case 4:
					$fields_name="wpindex";
					$namev="综合指数走势图";
					$fields_name_5="wpindex_avg_5";
					$fields_name_10="wpindex_avg_10";
					$fields_name_20="wpindex_avg_20";
					$fields_name_40="wpindex_avg_40";
					$fields_name_60="wpindex_avg_60";
					$fields_name_200="wpindex_avg_200";
					break;
	  			case 5:
					$fields_name="weipriceusb";
					$namev="综合指数走势图";
					$fields_name_5="weipriceusb_avg_5";
					$fields_name_10="weipriceusb_avg_10";
					$fields_name_20="weipriceusb_avg_20";
					$fields_name_40="weipriceusb_avg_40";
					$fields_name_60="weipriceusb_avg_60";
					$fields_name_200="weipriceusb_avg_200";
					break;
				//update by zfy ended 2017/7/21
	  		}
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  		if($fields_name=='weiprice' || $fields_name=='aveprice')
	  		{
	  			$fwp="weekp";
	  			$fmp="monthp";
	  		}
	  		if($fields_name=='weiindex' || $fields_name=='wpindex'|| $fields_name=='weipriceusb')
	  		{
	  			$fwp="weekindexp";
	  			$fmp="monthindexp";
	  		}

			if($nvalues>'8'){
				$fields_name='weiprice';
				//add by zfy started 2017/8/9
				$fields_name_5="weiprice_avg_5";
				$fields_name_10="weiprice_avg_10";
				$fields_name_20="weiprice_avg_20";
				$fields_name_40="weiprice_avg_40";
				$fields_name_60="weiprice_avg_60";
				$fields_name_200="weiprice_avg_200";
				//add by zfy ended 2017/8/9
				$fwp="weekp";
	  			$fmp="monthp";
			}
	  		$time1 =date("Y-m-d",strtotime($stime)-(7 * 24 * 60 * 60));
	  		$time2 =date("Y-m-d",strtotime($etime)+(7 * 24 * 60 * 60));
	  		$time3 =date("Y-m-d",strtotime($stime)-(30 * 24 * 60 * 60));
	  		$time4 =date("Y-m-d",strtotime($etime)+(30 * 24 * 60 * 60));

	  		//data1 日期
	  		$min=1000;
	  		$max=-1;
	  		$namev="平均价格走势图";
	  		foreach($leixing as $key=>$value){
			// echo $nvalues;
					if($value=='2'){

						$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=8 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
						//add by zfy started 2020/06/19 基准日
						$sqldate = "select *  from shpi_week where type=8 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
						//add by zfy ended 2020/06/19 基准日
						if($biao=="mj"){
							$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=6 and bigtype='mj' and smalltype='".$nvalues."' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_week where type=6 and bigtype='mj' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
						// echo $sql;
					}else if($value=='3'){
						$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=8 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
						//add by zfy started 2020/06/19 基准日
						$sqldate = "select *  from shpi_month where type=8 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
						//add by zfy ended 2020/06/19 基准日
						if($biao=="mj"){
							$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=6 and bigtype='mj' and smalltype='".$nvalues."' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_month where type=6 and bigtype='mj' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
					}else
					{

						if($biao=="mj"){
							$sql = "select * from shpi_mj_pzp where vid=0 and type=".$nvalues." and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_mj_pzp where vid=0 and type=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}else{
							$sql = "select * from shpi_material_pzp where vid=".$nvalues." and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
							//add by zfy started 2020/06/19 基准日
							$sqldate = "select *  from shpi_material_pzp where vid=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
							//add by zfy ended 2020/06/19 基准日
						}
					}
				if($nvalues>'8'){

				if($value=='2'){
					$sql = "select * from shpi_week where dateday>='".$time1."' and dateday<='".$time2."' and type=2 and bigtype='gc'and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_week where type=2 and bigtype='gc'and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日

				}else if($value=='3'){
					$sql = "select * from shpi_month where dateday>='".$time3."' and dateday<='".$time4."' and type=2 and bigtype='gc' and smalltype='".$nvalues."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_month where type=2 and bigtype='gc' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日

				}else
				{
					$sql="select * from shpi_pp  where bc_id='".$nvalues."' and dateday>='".$stime."' and dateday<='".$etime."' order by dateday asc";
					//add by zfy started 2020/06/19 基准日
					$sqldate = "select *  from shpi_pp where bc_id='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
					//add by zfy ended 2020/06/19 基准日
				}
				}


				//add by zfy started 2020/06/19 基准日
				$basedays = $this->connshpi->getrow($sqldate);
				//add by zfy ended 2020/06/19 基准日
	  			 // echo $fmp."<br />";
	  			$rs=$this->connshpi->Execute($sql);
	  			while (!$rs->EOF){
	  				$dataday=$rs->fields("dateday");
	  				if($value==2)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
						} else {
							$price = $rs->fields($fwp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
	  				else if($value==3)
	  				{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1) {
							$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
						} else {
							$price = $rs->fields($fmp);
						}
						//add by zfy ended 2020/06/04 基准日
	  				}else{
						//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
						if ($calcBaseIndex==1){
							// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
							$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
						}else {
							$price=$rs->fields($fields_name);
							//add by zfy started 2017/7/18
							$price_5=$rs->fields($fields_name_5);
							$price_10=$rs->fields($fields_name_10);
							$price_20=$rs->fields($fields_name_20);
							$price_40=$rs->fields($fields_name_40);
							$price_60=$rs->fields($fields_name_60);
							$price_200=$rs->fields($fields_name_200);
							//add by zfy ended 2017/7/18
						}
						//add by zfy ended 2020/06/04 基准日
	  				}
					if(count($leixing)=='1'){
						$array_price[$nkeys][$dataday]=$price;
					}else{
						$array_price[$key][$dataday]=$price;
					}
					//add by zfy started 2017/7/18
				$i = 1;
				if(is_array($leixing)&&count($leixing)=='1'&&in_array('1',$leixing)){
					if (is_array($avgprice)) {
						if(in_array('5',$avgprice)){
							if($price_5!=""&&$price_5!='0')
							$array_price[$i][$dataday]=$price_5;
							$i++;
						}
						if(in_array('10',$avgprice)){
							if($price_10!=""&&$price_10!='0')
							$array_price[$i][$dataday]=$price_10;
							$i++;
						}
						if(in_array('20',$avgprice)){
							if($price_20!=""&&$price_20!='0')
							$array_price[$i][$dataday]=$price_20;
							$i++;
						}
						if(in_array('40',$avgprice)){
							if($price_40!=""&&$price_40!='0')
							$array_price[$i][$dataday]=$price_40;
							$i++;
						}
						if(in_array('60',$avgprice)){
							if($price_60!=""&&$price_60!='0')
							$array_price[$i][$dataday]=$price_60;
							$i++;
						}
						if(in_array('200',$avgprice)){
							if($price_200!=""&&$price_200!='0')
							$array_price[$i][$dataday]=$price_200;
							$i++;
						}
					}
				}
				//add by zfy ended 2017/7/18
	  				$rs->MoveNext();
	  			}
	  		}
	  	}
	  	//  echo "<pre>";print_r($array_price);
	  	return $array_price;
	  }
	  /*
	   铁矿石加权平均价格
	  */ function arr_tks_pzp_a($pzggcz,$flag,$stime,$etime,$leixing,$avgprice='',$array_value=array()){
	//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('2'));
	//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  foreach ($pzggcz as $nkeys=>$nvalues){
	  	switch ($flag)
	  	{

			//update by zfy started 2017/7/21
	  			case 1:
					$fields_name="aveprice";
					$namev="平均价格走势图";
					$fields_name_5="aveprice_avg_5";
					$fields_name_10="aveprice_avg_10";
					$fields_name_20="aveprice_avg_20";
					$fields_name_40="aveprice_avg_40";
					$fields_name_60="aveprice_avg_60";
					$fields_name_200="aveprice_avg_200";
					break;
	  			case 2:
					$fields_name="weiprice";
					$namev="综合价格走势图";
					$fields_name_5="weiprice_avg_5";
					$fields_name_10="weiprice_avg_10";
					$fields_name_20="weiprice_avg_20";
					$fields_name_40="weiprice_avg_40";
					$fields_name_60="weiprice_avg_60";
					$fields_name_200="weiprice_avg_200";
					break;
	  			case 3:
					$fields_name="apindex";
					$namev="价格指数走势图";
					$fields_name_5="apindex_avg_5";
					$fields_name_10="apindex_avg_10";
					$fields_name_20="apindex_avg_20";
					$fields_name_40="apindex_avg_40";
					$fields_name_60="apindex_avg_60";
					$fields_name_200="apindex_avg_200";
					break;
	  			case 4:
					$fields_name="wpindex";
					$namev="综合指数走势图";
					$fields_name_5="wpindex_avg_5";
					$fields_name_10="wpindex_avg_10";
					$fields_name_20="wpindex_avg_20";
					$fields_name_40="wpindex_avg_40";
					$fields_name_60="wpindex_avg_60";
					$fields_name_200="wpindex_avg_200";
					break;
				//update by zfy ended 2017/7/21
	  	}
	  	$fwp="weekindexp";
	  	$fmp="monthindexp";
	  	if($fields_name=='weiprice' || $fields_name=='aveprice')
	  	{
	  		$fwp="weekp";
	  		$fmp="monthp";
	  	}
	  	if($fields_name=='weiindex' || $fields_name=='wpindex')
	  	{
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  	}
	  	if($leixing=='2'){
	  		$sql = "select * from shpi_week where dateday>='".$stime."' and dateday<='".$etime."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from shpi_week where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
			//add by zfy ended 2020/06/19 基准日

	  	}else if($leixing=='3'){
	  		$sql = "select * from shpi_month where dateday>='".$stime."' and dateday<='".$etime."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from shpi_month where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
			//add by zfy ended 2020/06/19 基准日
	  	}else
	  	{
	  		$sql="select * from shpi_material_pzp  where vid=".$nvalues." and (dateday>='".$stime."' and dateday<='".$etime."') order by dateday asc";
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from shpi_material_pzp where vid=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
			//add by zfy ended 2020/06/19 基准日
	  	}

		  //add by zfy started 2020/06/19 基准日
		  $basedays = $this->connshpi->getrow($sqldate);
		  //add by zfy ended 2020/06/19 基准日
	  	$rs=$this->connshpi->Execute($sql);$i=1;
	  	while (!$rs->EOF){
	  		$dataday=$rs->fields("dateday");
	  		if($leixing==2)
	  		{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex==1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
				} else {
					$price = $rs->fields($fwp);
				}
				//add by zfy ended 2020/06/04 基准日
	  			$_year=$rs->fields('calyear');
	  			$_week=$rs->fields('calweek');
	  			$dataday=$_year."/".$_week;
	  		}
	  		else if($leixing==3)
	  		{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex==1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
				} else {
					$price = $rs->fields($fmp);
				}
				//add by zfy ended 2020/06/04 基准日
	  		}else{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex){
					// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
					$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
				}else {
					$price=$rs->fields($fields_name);
					//add by zfy started 2017/7/18
					$price_5=$rs->fields($fields_name_5);
					$price_10=$rs->fields($fields_name_10);
					$price_20=$rs->fields($fields_name_20);
					$price_40=$rs->fields($fields_name_40);
					$price_60=$rs->fields($fields_name_60);
					$price_200=$rs->fields($fields_name_200);
					//add by zfy ended 2017/7/18
				}
				//add by zfy ended 2020/06/04 基准日

	  		}
	  		$array_price[$nkeys][$dataday]=$price;
			//add by zfy started 2017/7/18
				$i = 1;
				if(is_array($leixing)&&count($leixing)=='1'&&in_array('1',$leixing)){
					if (is_array($avgprice)) {
						if(in_array('5',$avgprice)){
							if($price_5!=""&&$price_5!='0')
							$array_price[$i][$dataday]=$price_5;
							$i++;
						}
						if(in_array('10',$avgprice)){
							if($price_10!=""&&$price_10!='0')
							$array_price[$i][$dataday]=$price_10;
							$i++;
						}
						if(in_array('20',$avgprice)){
							if($price_20!=""&&$price_20!='0')
							$array_price[$i][$dataday]=$price_20;
							$i++;
						}
						if(in_array('40',$avgprice)){
							if($price_40!=""&&$price_40!='0')
							$array_price[$i][$dataday]=$price_40;
							$i++;
						}
						if(in_array('60',$avgprice)){
							if($price_60!=""&&$price_60!='0')
							$array_price[$i][$dataday]=$price_60;
							$i++;
						}
						if(in_array('200',$avgprice)){
							if($price_200!=""&&$price_200!='0')
							$array_price[$i][$dataday]=$price_200;
							$i++;
						}
					}
				}
				//add by zfy ended 2017/7/18
	  		$rs->MoveNext();
	  	}
	  }
	  return $array_price;
	  }

	  function arr_flash_all($arr,$leixing,$avgprice=array()){
	  	global $stime,$etime,$max,$min;
	  	if (!is_array($arr)){
	  		return;
	  	}
	  	// print_r($arr);
	  	$arr_pidcc=array('1'=>'国产铁矿石SHDOPI','2'=>'进口铁矿石SHIOPI','0'=>'铁矿石SHOPI');

	  	$news=0;
	  	foreach ($arr as $nn=>$vv){
	  		$Data1[0]="";$xi=1;
	  		if( $news == 0 ){
	  			$Data2[0]=$arr_pidcc[$nn];
	  		}else{
	  			$Data2[0]=$arr_pidcc[$nn];
	  		}

	  		$xi=1;
	  		foreach ($vv as $ndate=>$nprice){
	  			if ($news==0){
	  				if($leixing=='2'){
	  					$week = '周';
	  					//$week = iconv( "gb2312 ", "utf-8",'周');
	  					$Data1[$xi]=$ndate.$week;
	  				}else if($leixing=='3'){
	  					$Data1[$xi]=date("y/m",strtotime($ndate));
	  				}else{
	  					$Data1[$xi]=date("y/m/d",strtotime($ndate));
	  				}
	  			}
	  			$Data2[$xi]=$nprice;
	  			if ($min>$Data2[$xi]){
	  				$min=$Data2[$xi];
	  			}
	  			if ($max<$Data2[$xi]){
	  				$max=$Data2[$xi];
	  			}
	  			$xi++;
	  		}

	  		// power由7改成6，甲级会员可以显示均价
	  		if($_SESSION['mid']==1 || $_SESSION['maxpower']>=6){
	  			if( $news == 0 ){
	  				$Data2[0]=$arr_pidcc[$nn]."均价".round(array_sum($Data2)/(count($Data2)-1))."元 ";
	  			}else{
	  				$Data2[0]=$arr_pidcc[$nn]."均价".round(array_sum($Data2)/(count($Data2)-1))."元 ";
	  				//$Data2[0]=$var_tks[$values]."(期间均价:".round(array_sum($Dataaa)/count($Dataaa))."元)";
	  			}
	  		}
			//add by zfy started 2017/7/19
			if($avgprice){
				foreach ($avgprice as $k=>$v){
					if($news == 1 && $k == 0){
						$Data2[0]=$v."日均线";break;
					}else if($news == 2 && $k == 1){
						$Data2[0]=$v."日均线";break;
					}else if($news == 3 && $k == 2){
						$Data2[0]=$v."日均线";break;
					}else if($news == 4 && $k == 3){
						$Data2[0]=$v."日均线";break;
					}else if($news == 5 && $k == 4){
						$Data2[0]=$v."日均线";break;
					}else if($news == 6 && $k == 5){
						$Data2[0]=$v."日均线";break;
					}
				}
			}
			//add by zfy ended 2017/7/19
	  		if ($news==0){
	  			$chart=array($Data1,$Data2);
	  		}else{ array_push($chart,$Data2);
	  		}
	  		$news++;
	  	}
	  	return $chart;
	  }
	  function bvar_tksshpi_one($array_value){
		  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3', '4','6'));
		  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  	foreach ($array_value["pid"] as $nkeys=>$nvalues){
	  		switch ($array_value["flag"])
	  		{
	  			case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
	  			case 2: $fields_name="price";$namev="综合价格走势图";break;
	  			case 3: $fields_name="weiindex";$namev="价格指数走势图";break;
	  			case 4: $fields_name="wpindex";$namev="综合指数走势图";break;
	  			case 5: $fields_name="mindex";$namev="综合指数走势图";break;
	  			case 6: $fields_name="weiprice";$namev="综合指数走势图";break;
	  		}
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  		if($fields_name=='weiprice' || $fields_name=='aveprice'|| $fields_name=='price')
	  		{
	  			$fwp="weekp";
	  			$fmp="monthp";
	  		}
	  		if($fields_name=='weiindex' || $fields_name=='wpindex' || $fields_name=='mindex' )
	  		{
	  			$fwp="weekindexp";
	  			$fmp="monthindexp";
	  		}
	  		//echo $array_value["stime"];
	  		$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
	  		$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
	  		$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

	  		//$time2 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		if($array_value['leixing']=='2'){
	  			$sql = "select * from shpi_week where dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_week  where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日

	  		}else if($array_value['leixing']=='3'){
	  			$sql = "select * from shpi_month where dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' and type=5 and bigtype='tks' and smalltype='".$nvalues."' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_month  where type=5 and bigtype='tks' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
	  		}else
	  		{
	  			$sql="select * from shpi_material_pzp  where vid=".$nvalues." and (dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."') order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_material_pzp  where vid=".$nvalues." and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
	  		}
			//add by zfy started 2020/06/19 基准日
			$basedays = $this->connshpi->getrow($sqldate);
			//add by zfy ended 2020/06/19 基准日
	  		$rs=$this->connshpi->Execute($sql);$i=1;
	  		while (!$rs->EOF){
	  			$dataday=$rs->fields("dateday");
	  			if($array_value['leixing']==2)
	  			{
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
					} else {
						$price = $rs->fields($fwp);
					}
					//add by zfy ended 2020/06/04 基准日
	  				$_year=$rs->fields('calyear');
	  				$_week=$rs->fields('calweek');
	  				$dataday=$_year."/".$_week;

	  			}
	  			else if($array_value['leixing']==3)
	  			{
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
					} else {
						$price = $rs->fields($fmp);
					}
					//add by zfy ended 2020/06/04 基准日
	  			}else{
					//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
					if ($calcBaseIndex==1) {
						$price = $this->CalculateBaseDayPoints($rs->fields($fields_name), $basedays[$fields_name]);
					} else {
						$price = $rs->fields($fields_name);
					}
					//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  			}
	  			$array_price["k".$nvalues][$dataday]=$price;
	  			$rs->MoveNext();
	  		}
	  	}

	  	return $array_price;
	  }
	  function bvar_ylshpi_one($array_value){
		  //add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		  $calcBaseIndex = $this->needCalcBaseIndex($array_value,array('5'));
		  //add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
	  	foreach ($array_value["pid"] as $nkeys=>$nvalues){
	  		switch ($array_value["flag"])
	  		{
	  			case 1: $fields_name="aveprice";$namev="平均价格走势图";break;
	  			case 2: $fields_name="price";$namev="综合价格走势图";break;
	  			case 3: $fields_name="weiindex";$namev="价格指数走势图";break;
	  			case 4: $fields_name="wpindex";$namev="综合指数走势图";break;
	  			case 5: $fields_name="mindex";$namev="综合指数走势图";break;
	  		}
	  		$fwp="weekindexp";
	  		$fmp="monthindexp";
	  		if($fields_name=='weiprice' || $fields_name=='aveprice'|| $fields_name=='price')
	  		{
	  			$fwp="weekp";
	  			$fmp="monthp";
	  		}
	  		if($fields_name=='weiindex' || $fields_name=='wpindex' || $fields_name=='mindex' )
	  		{
	  			$fwp="weekindexp";
	  			$fmp="monthindexp";
	  		}
	  		//echo $array_value["stime"];
	  		$time1 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		$time2 =date("Y-m-d",strtotime($array_value["etime"])+(7 * 24 * 60 * 60));
	  		$time3 =date("Y-m-d",strtotime($array_value["stime"])-(30 * 24 * 60 * 60));
	  		$time4 =date("Y-m-d",strtotime($array_value["etime"])+(30 * 24 * 60 * 60));

	  		//$time2 =date("Y-m-d",strtotime($array_value["stime"])-(7 * 24 * 60 * 60));
	  		if($array_value['leixing']=='2'){
	  			$sql = "select * from shpi_week where dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' and type=4 and bigtype='yl' and smalltype='".$nvalues."' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_week where type=4 and bigtype='yl' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日

	  		}else if($array_value['leixing']=='3'){
	  			$sql = "select * from shpi_month where dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' and type=4 and bigtype='yl' and smalltype='".$nvalues."' order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_month where type=4 and bigtype='yl' and smalltype='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
	  		}else
	  		{
	  			$sql = "select * from shpi_material where topicture='".$nvalues."' and dateday>='".$array_value["stime"]."' and dateday<='".$array_value["etime"]."' group by dateday order by dateday asc";
				//add by zfy started 2020/06/19 基准日
				$sqldate = "select *  from shpi_material where topicture='".$nvalues."' and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
				//add by zfy ended 2020/06/19 基准日
	  		}
			//add by zfy started 2020/06/19 基准日
			$basedays = $this->connshpi->getrow($sqldate);
			//add by zfy ended 2020/06/19 基准日
	  		$rs=$this->connshpi->Execute($sql);$i=1;
	  	  while (!$rs->EOF){
			$dataday=$rs->fields("dateday");
			if($array_value['leixing']==2)
			{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex==1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fwp),$basedays[$fwp]);
				} else {
					$price = $rs->fields($fwp);
				}
				//add by zfy ended 2020/06/04 基准日
			  $_year=$rs->fields('calyear');
			  $_week=$rs->fields('calweek');
			  $dataday=$_year."/".$_week;
			}
			else if($array_value['leixing']==3)
			{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex==1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fmp),$basedays[$fmp]);
				} else {
					$price = $rs->fields($fmp);
				}
				//add by zfy ended 2020/06/04 基准日
			}else{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex==1) {
					$price = $this->CalculateBaseDayPoints($rs->fields($fields_name), $basedays[$fields_name]);
				} else {
					$price = $rs->fields($fields_name);
				}
				//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
			}
			$array_price["m".$nvalues][$dataday]=$price;
			$rs->MoveNext();
		  }
	  	}

	  	return $array_price;
	}

	// 国产炼焦煤 、进口炼焦煤
	function bvar_ljmshpi($array_value)
	{
		//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		$calcBaseIndex = $this->needCalcBaseIndex($array_value,array('3','4'));
		//add by zfy ended 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
		foreach ( $array_value ["pid"] as $nkeys => $nvalues )
		{
			switch ($array_value ["flag"])
			{
				//update by zfy started 2017/7/20
				case 1 :
					$fields_name = "aveprice";
					$namev = "平均价格走势图";
					break;
				case 2 :
					$fields_name = "weiprice";
					$namev = "综合价格走势图";
					$fields_name_5 = "weiprice_avg_5";
					$fields_name_10 = "weiprice_avg_10";
					$fields_name_20 = "weiprice_avg_20";
					$fields_name_40 = "weiprice_avg_40";
					$fields_name_60 = "weiprice_avg_60";
					$fields_name_200 = "weiprice_avg_200";
					break;
				case 3 :
					$fields_name = "apindex";
					$namev = "价格指数走势图";
					break;
				case 4 :
					$fields_name = "weiindex";
					$namev = "综合指数走势图";
					$fields_name_5 = "weiindex_avg_5";
					$fields_name_10 = "weiindex_avg_10";
					$fields_name_20 = "weiindex_avg_20";
					$fields_name_40 = "weiindex_avg_40";
					$fields_name_60 = "weiindex_avg_60";
					$fields_name_200 = "weiindex_avg_200";

					break;
				//update by zfy ended 2017/7/20
			}
			//add by zfy started 2020/06/19 基准日
			$sqldate = "select *  from shpi_mj_pzp where vid='".$nvalues."' and  type=1 and dateday<='" . $array_value['baseDaydate'] . "' order by dateday desc limit 1";
			$basedays = $this->connshpi->getrow($sqldate);
			//add by zfy ended 2020/06/19 基准日
			$sql = "select * from shpi_mj_pzp where weiindex!=0 and vid='" . $nvalues . "' and type=1 and dateday>='" . $array_value ["stime"] . "' and dateday<='" . $array_value ["etime"] . "' order by dateday asc";
			$rs = $this->connshpi->Execute ( $sql );//echo "<pre>rs=";print_R($this->connshpi);
			$i = 1;

			if($nvalues=='1'){
				$pzname = "国产炼焦煤";
			} elseif($nvalues=='2') {
				$pzname = "进口炼焦煤";
			}
			$array_price['0'][] = "";
			$array_price['1'][] = $pzname;
			//add by zfy started 2017/7/19
			$count = 2;
			if($array_value["avgprice"]){
				foreach ($array_value["avgprice"] as $k=>$v){
					$array_price[$count][]=$v."日均线";
					$count++;
				}
			}
			//add by zfy ended 2017/7/19
			while ( ! $rs->EOF )
			{
				//add by zfy started 2020/06/04 基准日 如果重新设置了基准日，并且是指数图
				if ($calcBaseIndex==1){
					$dataday=$rs->fields("dateday");
					// $this->getBaseDayPoints(&$price,&$price_5,&$price_10,&$price_20,&$price_40,&$price_60,&$price_200,$rs,$basedays,$fields_name);
					$this->getBaseDayPoints($price,$price_5,$price_10,$price_20,$price_40,$price_60,$price_200,$rs,$basedays,$fields_name);
					$array_price['0'][] = $dataday;
					$array_price['1'][] = $price;
				}else {
					$dataday = $rs->fields ( "dateday" );
					$price = $rs->fields ( $fields_name );
					//add by zfy started 2017/7/18
					$price_5 = $rs->fields($fields_name_5);
					$price_10 = $rs->fields($fields_name_10);
					$price_20 = $rs->fields($fields_name_20);
					$price_40 = $rs->fields($fields_name_40);
					$price_60 = $rs->fields($fields_name_60);
					$price_200 = $rs->fields($fields_name_200);
					//add by zfy ended 2017/7/18
					//$array_price ["o" . $nvalues] [$dataday] = $price;
					$array_price['0'][] = $dataday;
					$array_price['1'][] = $price;
				}
				//add by zfy ended 2020/06/04 基准日

				//add by zfy started 2017/7/18

				$i = 2;
				if (is_array($array_value['avgprice'])) {
					if(in_array('5',$array_value["avgprice"])){
						if($price_5!=""&&$price_5!='0')
						$array_price[$i][]=$price_5;
						$i++;
					}
					if(in_array('10',$array_value["avgprice"])){
						if($price_10!=""&&$price_10!='0')
						$array_price[$i][]=$price_10;
						$i++;
					}
					if(in_array('20',$array_value["avgprice"])){
						if($price_20!=""&&$price_20!='0')
						$array_price[$i][]=$price_20;
						$i++;
					}
					if(in_array('40',$array_value["avgprice"])){
						if($price_40!=""&&$v!='0')
						$array_price[$i][]=$price_40;
						$i++;
					}
					if(in_array('60',$array_value["avgprice"])){
						if($price_60!=""&&$v!='0')
						$array_price[$i][]=$price_60;
						$i++;
					}
					if(in_array('200',$array_value["avgprice"])){
						if($price_200!=""&&$price_200!='0')
						$array_price[$i][]=$price_200;
						$i++;
					}
				}
				//add by zfy ended 2017/7/18
				$rs->MoveNext ();
			}
		}

		return $array_price;
	}

	/**
	 * 计算基准日点数
	 * Created by zfy.
	 * Date:2020/6/4 9:52
	 * @param $data
	 * @param $baseData
	 * @return float|int
	 */
	public function CalculateBaseDayPoints($data, $baseData)
	{
		return ($data / $baseData) * 100;
	}

	public function getBaseDayPoints2(&$price, &$price_5, &$price_10, &$price_20, &$price_40, &$price_60, &$price_200, $rs, $basedays,$fields_name,$avgfields_name)
	{
		$fields = $fields_name;
		$fields_name_5 = $avgfields_name."_avg_5";
		$fields_name_10 = $avgfields_name."_avg_10";
		$fields_name_20 = $avgfields_name."_avg_20";
		$fields_name_40 = $avgfields_name."_avg_40";
		$fields_name_60 = $avgfields_name."_avg_60";
		$fields_name_200 = $avgfields_name."_avg_200";
		$price = $this->CalculateBaseDayPoints($rs->fields($fields_name),$basedays[$fields]);
		$price_5 = $this->CalculateBaseDayPoints($rs->fields($fields_name_5),$basedays[$fields]);
		$price_10 = $this->CalculateBaseDayPoints($rs->fields($fields_name_10),$basedays[$fields]);
		$price_20 = $this->CalculateBaseDayPoints($rs->fields($fields_name_20),$basedays[$fields]);
		$price_40 = $this->CalculateBaseDayPoints($rs->fields($fields_name_40),$basedays[$fields]);
		$price_60 = $this->CalculateBaseDayPoints($rs->fields($fields_name_60),$basedays[$fields]);
		$price_200 = $this->CalculateBaseDayPoints($rs->fields($fields_name_200),$basedays[$fields]);
	}

	/**
	 * 根据自定义的基准日数据计算点位
	 * Created by zfy.
	 * Date:2020/6/4 15:56
	 * @param $price
	 * @param $price_5
	 * @param $price_10
	 * @param $price_20
	 * @param $price_40
	 * @param $price_60
	 * @param $price_200
	 * @param $rs
	 * @param $basedays
	 * @param $fields
	 */
	public function getBaseDayPoints(&$price, &$price_5, &$price_10, &$price_20, &$price_40, &$price_60, &$price_200, $rs, $basedays,$fields)
	{
		$fields_name = $fields;
		$fields_name_5 = $fields."_avg_5";
		$fields_name_10 = $fields."_avg_10";
		$fields_name_20 = $fields."_avg_20";
		$fields_name_40 = $fields."_avg_40";
		$fields_name_60 = $fields."_avg_60";
		$fields_name_200 = $fields."_avg_200";
		$price = $this->CalculateBaseDayPoints($rs->fields($fields_name),$basedays[$fields]);
		$price_5 = $this->CalculateBaseDayPoints($rs->fields($fields_name_5),$basedays[$fields]);
		$price_10 = $this->CalculateBaseDayPoints($rs->fields($fields_name_10),$basedays[$fields]);
		$price_20 = $this->CalculateBaseDayPoints($rs->fields($fields_name_20),$basedays[$fields]);
		$price_40 = $this->CalculateBaseDayPoints($rs->fields($fields_name_40),$basedays[$fields]);
		$price_60 = $this->CalculateBaseDayPoints($rs->fields($fields_name_60),$basedays[$fields]);
		$price_200 = $this->CalculateBaseDayPoints($rs->fields($fields_name_200),$basedays[$fields]);
	}
	/**
	 * 是否需要计算指数
	 * Created by zfy.
	 * Date:2020/6/11 11:20
	 * @param $array_value
	 * @param $array_flag
	 * @return int
	 */
	public function needCalcBaseIndex($array_value,$array_flag){
		$calcBaseIndex = 0;
		if (isset($array_value['baseDaydate'])&&$array_value['baseDaydate'] != '2004-09-28' && $array_value['baseDaydate'] != '' && in_array($array_value["flag"], $array_flag)) {
			$calcBaseIndex = 1;
		}
		return $calcBaseIndex;
	}
	}

define("HOSTIP","sh.steelhome.cn");
//define("HOSTIP","************");
?>
