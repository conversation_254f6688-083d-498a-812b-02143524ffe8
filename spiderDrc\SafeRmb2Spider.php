<?php
//ERROR_REPORTING( E_ALL & ~E_NOTICE );
include_once('/usr/local/www/libs/phpQuery/phpQuery.php');
include_once('BaseSpiderDrc.php');
class SafeRmb2Spider extends BaseSpiderDrc {

	public function __construct($Type){
		parent::__construct($Type);
	}

	public function SaveAndPublish(){

        if ( $this->NeedRun()) {
            $IfList = $this->getDataList();

            $get_data_flag=false;
            if($IfList && count($IfList)>1)
            {
                $get_data_flag=true;
                $this->insertDataTable($IfList);
            }

            $this -> GetData( $get_data_flag );

        }
        echo "over";
	}

    protected function getDataList()
    {
        $html = $this->GetHtml();
        $today = $this->getDate();
        //$last_day = date("Y-m-d",strtotime("-1 day".$today));
        //print_r($html);
        $return_arr = array();
        if ($html) {
            $arr = explode("<br>",$html);
            foreach ($arr as $item){
                //$result = strip_tags($item);
                preg_match('/]*>([^<]+)<\/a>/', $item, $match);
                $textContent = $match[1];
                $textContent = date("Y-m-d",strtotime($textContent));
                //if($today==$textContent){
                    $result = explode("（",$item);
                    if(count($result)>1){
                        $rmb_rate = str_replace("）","",$result[1]);
                        $return_arr[$textContent] = (float)$rmb_rate*100;
                    }
                //}
            }
            //print_r($return_arr);

        }
        return $return_arr;
    }

    protected function insertDataTable($arrays)
    {
        $condrc = $this->GetConn();
        $today = $this->getDate();
        if( $arrays ){
            //echo "SELECT count(1)  FROM `rmbrate` WHERE rdate = '".$today." 00:00:00'";
            $data_num = $condrc->getOne("SELECT count(1)  FROM `rmbrate` WHERE rdate = '".$today." 00:00:00'");
            if($data_num>0 && isset($arrays[$today])){
                $SmallType = $this -> GetSmallType();
                if($SmallType==RMB_RATE_VND){
                    $update_ = " rd51='".$arrays[$today]."' ";
                }else{
                    $update_ = " rd52='".$arrays[$today]."' ";
                }
                //echo "update `rmbrate` set $update_ WHERE rdate = '".$today." 00:00:00'";
                $condrc->execute("update `rmbrate` set $update_ WHERE rdate = '".$today." 00:00:00'");
            }

        }
    }


    protected function NeedRun(){
		$condrc = $this -> GetConn();
		$ret = false;
		$today = date("Y-m-d");
        $SmallType = $this -> GetSmallType();
        if($SmallType==RMB_RATE_VND){
            $where = " and rd51 is NULL ";
        }else{
            $where = " and rd52 is NULL ";
        }
		if( $this -> IsInRuntimeRange() ){
			$data_num = $condrc->getOne("SELECT count(1)  FROM `rmbrate` WHERE rdate = '".$today." 00:00:00' $where ");
			if( $data_num != 0 ){
				$ret = true;
			}
		}
		return $ret;
	}

    protected function getDate()
    {
        $date = date("Y-m-d");
        if(isset($_REQUEST['gen_date']) && $_REQUEST['gen_date']!=""){
            $date = $_REQUEST['gen_date'];
        }
        return $date;
    }
	


}

//$SafeRmbSpider =  new SafeRmb2Spider(RMB_RATE_VND);
//$SafeRmbSpider -> SaveAndPublish();

//$SafeRmbSpider =  new SafeRmb2Spider(RMB_RATE_IDR);
//$SafeRmbSpider -> SaveAndPublish();