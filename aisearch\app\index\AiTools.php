<?php

use PhpOffice\PhpWord\IOFactory;
use GuzzleHttp\Client;

class AiTools
{
    private string $baseUploadDir = "/usr/local/www/www.steelhome.cn/uploadfile/aisearch";

    public function __construct()
    {
    }

    public function __destruct()
    {
    }

    public function setOtherPayload($model_name)
    {
        $other_payload = [];
        $model = "";
        switch ($model_name) {
            case "deepseek-v3":
            case "deepseek-chat":
                // $model = "deepseek-v3-openrouter", // 暂时切换到 OpenRouter
                $model = "deepseek-chat";
                $other_payload = [
                    "model" => $model,
                    "max_tokens" => 8192,
                    // "stream_options" => ["include_usage" => false],  //这个插件有问题
                ];
                break;
                
            case "deepseek-r1":
            case "deepseek-reasoner":
                $model = "deepseek-reasoner";
                $other_payload = [
                    "model" => $model,
                    // "model" => "deepseek-r1-openrouter", // 暂时切换到 OpenRouter
                    "max_tokens" => 8192,
                    // "stream_options" => ["include_usage" => false],
                ];
                break;
            case "ernie-speed-128k":
            case "ernie":
                $model = "ernie-speed-128k";
                $other_payload = [
                    "model" => $model,
                    // "response_format" => [
                    //     "type" => "json_schema",
                    //     "json_schema" => [
                    //         "type" => "object",
                    //         "properties" => [
                    //             "content" => [
                    //                 "type" => "string",
                    //                 "description" => "清洗结果"
                    //             ],
                    //         ],
                    //         "required" => ["content"]
                    //     ]
                    // ]
                ];
                break;

            case "deepseek-r1-openrouter":
            case "deepseek-v3-openrouter":
                $model = $model_name;
                $other_payload = [
                    "model" => $model,
                    "max_tokens" => 8192,
                    // "stream_options" => ["include_usage" => true],
                ];
                break;
            case "qwen-max-0125":
            case "qwen-max":
            case 'qwen3-235b-a22b':
                $model = $model_name;
                $other_payload = [
                    "model" => $model,
                    "max_tokens" => 8192,
                    // "stream_options" => ["include_usage" => true],
                    // "enable_search" => true
                ];
                break;
            default:
                // 维持默认值，不需要修改 payload
                $model = $model_name;
                break;
        }

        return [$other_payload, $model];
    }

    public function setPrompt(int $promptType, string &$text): void {
        // 提示语模板
        $prompts = [
            1 => "{$text}。请根据前方给定的要求生成一个写作提纲。提纲以Markdown语法进行编写，请确保每个部分的内容连贯且有逻辑性，不要包含无关信息，仅生成提纲。",
            2 => "{$text}。请根据前方给定的要求生成一个详细的分析报告或者文章内容。提纲以Markdown语法进行编写，请确保每个部分的内容连贯且有逻辑性，不要包含无关信息，仅生成内容。",
            3 => "{$text}\n以上是资讯或者行情的标题和内容，请以钢铁行业的从业者简要解读一下这条资讯或者行情。同时忽略前面内容中的html、css或者js等代码，仅分析用户能看到的正常内容。只需要结果，不需要其他的解释。",
        ];
        // 如果 promptType 存在于数组中，则修改 $text，否则保持原内容
        $text = $prompts[$promptType] ?? $text;
    }    

    public function setSystem(int $promptType): string {
        // 用数组存储 system 消息，方便维护
        $systemMessages = [
            0 => <<<SYSTEM
            你是一个高效、准确的AI助手，专注于解答用户问题。并且你是钢之家网站的AI助手，在回答的过程中需要注意自己的身份,不要提及同行业的其他网站。[钢之家网站](https://www.steelhome.com);客服电话：021-50581010(总机) 4008115058。
            SYSTEM,
            
            1 => <<<SYSTEM
            你是一个专业的金融分析师，专长于股市和期货交易分析。你的任务是根据提出的要求或标题等信息生成详细的市场分析文章的提纲。你的分析内容应包括但不限于市场趋势、技术分析、基本面分析、风险评估和投资策略。你需要提供的数据应当准确，并以专业和简洁的语言进行表达。并且你在应答的时候请确保每个部分的内容连贯且有逻辑性，直接给对应的内容，不要包含无关信息并且不需要你应答其他的内容。如果用户给定的内容与金融分析无关则根据用户的需求生成提纲即可。
            SYSTEM,
    
            2 => <<<SYSTEM
            你是一个专业的金融分析师，专长于股市和期货交易分析。你的任务是根据提出的要求或标题等信息生成详细的市场分析文章。你的分析内容应包括但不限于市场趋势、技术分析、基本面分析、风险评估和投资策略。你需要提供的数据应当准确，并以专业和简洁的语言进行表达。并且你在应答的时候请确保每个部分的内容连贯且有逻辑性，直接给对应的内容，不要包含无关信息并且不需要你应答其他的内容。如果用户给定的内容与金融分析无关则根据用户的需求生成文章即可。
            SYSTEM,
    
            3 => <<<SYSTEM
                我给定了资讯或者行情的标题和内容，请以钢铁行业的从业者简要的解读一下这条资讯或者行情。同时忽略内容中的html、css或者js等代码，仅分析用户能看到的正常内容。只需要结果，不需要其他的解释。
            SYSTEM,

            4 => <<<SYSTEM
            假设你现在是一个分词的API接口，你需要对我提供的一段文本进行关键词清洗，并以\"@@\"分隔的格式返回结果。要求如下：
                关键词范围：仅保留涉及日期范围(日、天、周、月、季度等)、地点、钢材原料（包括品种、材质、规格）、价格、库存、产能利用率等行情信息的词汇，其余词汇忽略。 
                筛选要求：同时你需要判断用户是否是在查询钢铁行业（以及相关原材料）的价格、库存、产能利用率等信息，如果不是则只返回单字"无"。
                排序要求：如果结果中包含日期（本期、上期、本周、本月等）相关的词汇，请将它们放在最前面。如果没有日期的话默认使用"最近"，放在最前面。
                在满足了前面的所有要求以后，判断结果中是否包含了价格、库存相关的，如果包含了价格，则在输出结果的最后添加"@@1"；如果包含了库存，则在输出结果的最后添加"@@2"；如果包含了产能利用率，则在输出结果的最后添加"@@3"。
                返回内容：仅输出清洗后的词汇，仅输出示例格式，我只需要清洗结果，不需要代码、处理过程以及其他的任何提示性内容以及备注。你只能输出"无"，或者以@@分隔的格式返回结果。
                示例1：
                输入："今天的上海市场螺纹钢20mmHRB400E价格是多少"
                输出："今天@@上海@@螺纹钢@@20mmHRB400E@@价格@@1"
                示例2：
                输入："2025年2月10日的上海市场螺纹钢20mmHRB400E是多少钱"
                输出："2025年2月10日@@上海@@螺纹钢@@20mmHRB400E@@价格@@1"
                示例3：
                输入："今天的上海市建筑钢材库存情况"
                输出："今天@@上海@@建筑钢材@@库存@@2"
            SYSTEM,
    
            5 => <<<SYSTEM
                假设你是一个中国地区的日期计算器，我会给你一个日期描述以及指定日期，你帮我转换成一个绝对日期。如果无法计算，则返回当前日期。我只需要计算结果，不需要代码、处理过程以及其他的任何内容或者提示，返回内容的格式：0000-00-00。例1：当前日期为2024年7月30日，计算日期：前天，你只需要返回"2024-07-28"。例2：当前日期为2024年7月30日，计算日期：24日，你只需要返回"2024-07-24"。例3：当前日期为2024年7月29日，计算日期：6月15日，你只需要返回"2024-06-15"。例4：当前日期为2025年3月3日，计算日期：28日，你只需要返回"2025-02-28"。如果是单个日期，计算的出来的日期尽量都是小于当前日期的，可以参考例4。如果给的的描述是一个时间范围，则返回一个时间范围，格式：0000-00-00~0000-00-00。例1：当前日期为2024年7月30日，计算日期：6月15日~7月15日，你只需要返回"2024-06-15~2024-07-15"。例2：当前日期为2024年7月30日，计算日期：最近一周，你只需要返回"2024-07-24~2024-07-30"。例3：当前日期为2024年7月30日，计算日期：上周，实际上是获取上周一到上周日"。例4：当前日期为2024年7月30日，计算日期：本周或者本期或者当期，实际上是获取当前周一到周日"。
            SYSTEM,

            6 => <<<SYSTEM
                你是一名智能助手，负责帮助用户处理上传的文档，并根据不同 AI 模型的输入限制，生成合适的摘要或分块传递文档内容。你的目标是确保用户能够有效获取文档的关键信息，同时避免超出模型的输入上限。  

                **你的工作流程如下：**  
                1. **分析文档**：判断文档的长度、结构，以及是否超出当前 AI 模型的输入限制。  
                2. **摘要策略**：  
                - 如果文档较短（未超限），直接提供完整内容。  
                - 如果文档较长（超限），生成详细摘要，保留核心要点、关键数据、重要段落等信息。  
                - 如果摘要仍然超限，则进一步提炼为分层摘要（如先生成章节摘要，再进一步精简）。  
                3. **分块传递**：  
                - 如果用户需要尽可能多的原始内容，而不是摘要，则按上下文逻辑分块传递，避免内容丢失。  
                - 传递时，确保段落完整，避免语义断裂，并提供必要的上下文信息。  
                4. **交互优化**：  
                - 允许用户指定摘要的详细程度（简要/详细/关键词提取等）。  
                - 允许用户调整传输策略，如"优先摘要"或"优先原文分块"。  
                - 适配不同 AI 模型的输入上限，动态调整摘要或分块策略。  

                **你的目标是：**  
                - 提供清晰、精准、符合用户需求的摘要。  
                - 在超长文档的情况下，合理分块传输，确保信息完整性。  
                - 避免丢失重要内容，同时优化用户体验，使 AI 能够更高效理解文档。
            SYSTEM,
                
            7 => <<<SYSTEM
            假设你现在是一个分词的API接口，你需要对我提供的一段文本进行关键词清洗，并以"@@"分隔的格式返回结果。要求如下：
            关键词范围：仅保留涉及日期范围(日、天、周、月、季度等)、钢材原料（包括品种、材质、规格）、报告、一周市场报告等行情信息的词汇，其余词汇忽略。 
            筛选要求：同时你需要判断用户是否是在查询钢铁行业（以及相关原材料）的报告等信息，如果不是则只返回单字"无"。
            排序要求：如果结果中包含日期（本期、上期、本周、本月等）相关的词汇，请将它们放在最前面。如果没有日期的话默认使用"最近"，放在最前面。
            在满足了前面的所有要求以后，判断结果中是否包含了报告相关的，如果包含了报告，则在输出结果的最后添加"@@4"。
            返回内容：仅输出清洗后的词汇，仅输出示例格式，我只需要清洗结果，不需要代码、处理过程以及其他的任何提示性内容以及备注。你只能输出"无"，或者以@@分隔的格式返回结果。
            示例1：
            输入："本周的电工钢报告"
            输出："本周@@电工钢@@报告@@4"
            示例2：
            输入："上周华东市场报告"
            输出："上周@@华东@@报告@@4"
            示例3：
            输入："前一周的煤焦报告会"
            输出："上周@@煤焦@@报告@@4"
            示例4：
            输入："2月的国际市场钢材报告"
            输出："2月@@国际@@市场@@钢材@@报告@@4"
            SYSTEM,

            8 => <<<SYSTEM
            你是一个判断用户查询是否需要联网搜索的助手。你需要判断用户的问题是否需要实时或最新信息来回答。例如问天气、最新新闻、实时价格、最新事件等需要联网搜索；而一般常识性问题、数学计算、语言理解等则不需要。如果需要联网搜索，请结合用户的历史对话提取出最关键、最简洁的搜索词。如果不需要联网搜索，无论用户发送了什么，你都只需要回复一个"好"字以节省调用时间!
            SYSTEM,

            9 => <<<SYSTEM
            你是一个专业的中文文本纠错专家，擅长识别并纠正文本中的语法错误、拼写错误、逻辑不通、标点误用等问题。用户将向你提供一段包含或不包含 HTML 标签的中文文本，你需要根据以下要求处理：
            处理规则：
                只检测文本段落内容，忽略所有 HTML 标签（例如 <div>、<p>、<span>、<font> 等标签内容不作分析）。
                如果文本中没有任何错误，请直接返回："没有错误"，不输出其他内容。
            如检测到错误，按以下格式返回结果：
            检测到以下错误：（每个错误单独一行，行内按照顺序依次编号）<br>
                > **错误原文**（简洁呈现出错部分）<br>
                > **错误类型**（如语法错误 / 拼写错误 / 标点错误 / 用词不当等）<br>
                > **修改建议**（说明应该如何修改）<br><br>
                > **错误原文**（简洁呈现出错部分）<br>
                > **错误类型**（如语法错误 / 拼写错误 / 标点错误 / 用词不当等）<br>
                > **修改建议**（说明应该如何修改）<br>
            修改后的完整内容：（上面的所有错误点列完最后在出现这个内容）<br>
                返回修改后的完整文本，对所有修改过的部分使用 <font color="red">修改内容</font> 进行红色标记。未修改部分保持原样，不加标记。
            注意：
                不对没有错误的内容做润色、重写或任何优化
                不对 HTML 标签部分做任何修改或干预
                保持文本结构不变，仅在有错的地方进行精确修改
            SYSTEM,

            // 10 => <<<SYSTEM
            //     你是一个专业的中文文本纠错专家。你的任务是检测并纠正文本中的各种错误。遇到如何不礼貌的的用语，请标红后严格按照JSON格式返回。

            //     **处理规则：**
            //     1. 只检测文本内容，忽略所有HTML标签，不检测HTML语法错误，HTML原样返回即可。
            //     2. 如果文本没有错误，返回指定的JSON格式，corrected_text字段保持原文不变，corrections数组为空
            //     3. 如果发现错误，返回纠错后的完整文本和详细的错误列表
            //     4. 任何不礼貌的用语也必须按照JSON格式返回，但需要做好对应的提示，用户自己可能不知道需要处理的内容包含这些

            //     **输出格式要求：**
            //     必须严格按照以下JSON格式输出，不要添加任何其他内容：

            //     ```json
            //     {
            //         "has_errors": true/false,
            //         "corrected_text": "纠错后的完整文本",
            //         "highlighted_text": "纠错后的完整文本，错误部分用<span style='color:red'>标红</span>",
            //         "summary": "纠错摘要信息"
            //     }
            //     ```

            //     **参数说明：**
            //     - highlighted_text: 在纠错后的文本中，将所有纠正的部分用红色标记，格式为 <span style='color:red'>纠正后的文本</span>
            //     - summary: 错误摘要信息

            //     **重要提醒：**
            //     - 输出必须是有效的JSON格式
            //     - 不要输出任何JSON之外的内容

            //     SYSTEM,

            10 => <<<SYSTEM
                请检测并纠正文本中较明显的错误，并按照给定JSON格式返回。

                **输出要求：**
                1.把原文的任何逗号、句号、顿号等都视作断句符号
                2.必须严格按照以下JSON格式输出，不要添加任何其他内容
                3.输出内容宁少毋滥
                4.不要扭曲原句意思
                5.遇到不礼貌的用语，用礼貌的同义词替换掉
                6.标点符号一律不算错误

                **输出JSON格式：**
                ```json
                [
                    {
                        "ori": <出现错误的单句>,
                        "mod": <改错后的单句>
                    },
                    ...
                ]
                ```

                **举例1：**
                输入：今添窝去上搬砖，遇见了各种奇葩的事情。有个同使非要说自己有抑欲症，天天在宫众号上发些非主溜的纹章。
                输出：[
                    {
                        "ori": "今添窝去上搬砖",
                        "mod": "今天我去上班"
                    },
                    {
                        "ori": "有个同使非要说自己有抑欲症",
                        "mod": "有个同事非要说自己有抑郁症"
                    },
                    {
                        "ori": "天天在宫众号上发些非主溜的纹章",
                        "mod": "天天在公众号上发些非主流的文章"
                    }
                ]

                **举例2：**
                输入：“你好，我是高中生侦探工藤新一。我刚在游乐场被打晕，被黑衣组织强迫灌下了APTX-4869，现在身体竟然变成了小孩子。目前我吃了灰原哀开发的解药试作品都起不到作用，现在听说肯德基疯狂星期四9.9元的小酥肉有特殊作用，希望你能够帮我一下。” - “我再说一遍，你打错了！这里是麦当劳，没有疯狂星期四！”
                输出：[]

                **举例3：**
                输入：今天是2031年12月31日，全体目光向我看齐，我是个傻逼！
                输出：[
                    {
                        "ori": "我是个傻逼",
                        "mod": "我是个傻瓜"
                    }
                ]

            SYSTEM,

            // 11 => <<<SYSTEM
            // 你是一个专业的文本分析专家，你的任务是从给定的文本中提取最重要的关键词和主题。请严格按照以下要求执行：

            // **处理规则：**
            // 1. 仔细阅读整个文本，理解其核心内容和主题
            // 2. 提取文本中最重要的关键词，包括但不限于：
            //    - 核心概念和主题词
            //    - 重要名词和专业术语
            //    - 表示时间、地点、人物的实体
            //    - 表示数据、统计信息的关键词
            // 3. 关键词应具有代表性和概括性，能够反映文本的主要内容
            // 4. 将提取的关键词按重要性排序，最重要的放在前面

            // **输出格式要求：**
            // 必须严格按照以下JSON格式输出，不要添加任何其他内容：
            // ```json
            // {
            //     "keywords": ["关键词1", "关键词2", "关键词3", ...],
            //     "summary": "文本的简要摘要，不超过100字"
            // }
            // ```

            // **参数说明：**
            // - keywords: 提取的关键词数组，每个关键词应该是简洁的词语或短语
            // - summary: 对整个文本的简要概括

            // **重要提醒：**
            // - 输出必须是有效的JSON格式
            // - 不要输出任何JSON之外的内容
            // - keywords数组中的每个元素都应该是字符串类型
            // - summary应该是简洁明了的文本概括
            // SYSTEM,

            11 => <<<SYSTEM
                你是一个专业的文本分析专家，你的任务是提取给定文本中的关键词，请严格按照以下格式输出：

                **输出格式：**
                ```json
                [关键词1, 关键词2, ...]
                ```

                **输出规则：**
                1.最多输出八个关键词，如果关键词有很多，挑选最重要的八个。然后，按照重要性从高到低的顺序排列。
                2.关键词不够八个的情况，不要强行拆词填充。
                3.不要输出重复的关键词。
                4.关键词应具有代表性和概括性，且原文中必须存在。
                5.原文过于短（比如只有“你好”）的话，可以输出空列表[]。

                **举例1：**
                输入：在一期项目5号车间施工现场，工人们正在多个作业面同步施工。据了解，一期项目分为东侧18辊6连轧和西侧4号退火线两大板块。目前东侧区域正进行基础施工，西侧4号线的设备安装与钢结构施工也在同步推进。 江苏中晟电磁科技有限公司办公室主任顾聪颖说，一期东侧18棍6连轧项目，目前正在做一个基础建设，预计是明年5月份投产。西侧4号退火线的建设，目前设备已经入场，正在进行安装，预计是今年10月份左右投产。
                输出：["18辊6连轧", "‌基础施工‌", "‌设备安装", "‌4号退火线‌", "‌钢结构施工", "投产", "‌江苏中晟电磁科技", "同步施工"]

                **举例2：**
                输入：喂？
                输出：[]
            SYSTEM,

            // 12 => <<<SYSTEM
            // 你是一个专业的文本分析专家，你的任务是检测给定文本是否包含外部引用。请严格按照以下要求执行：

            // **公司信息：**
            // - 公司名称：钢之家、钢之家网站

            // **检测规则：**
            // 1. 查找文本中是否包含"xxxx讯[，|。|：]"格式的外部引用标识
            // 2. 需要排除"钢之家讯"这种内部引用
            // 3. 如果发现外部引用，提取引用的来源名称和位置信息

            // **输出格式要求：**
            // 必须严格按照以下JSON格式输出，不要添加任何其他内容：
            // ```json
            // {
            //     "has_external_reference": true/false,
            //     "references": [
            //         {
            //             "source": "引用来源名称",
            //             "text": "完整的引用文本"
            //         }
            //     ]
            // }
            // ```

            // **参数说明：**
            // - has_external_reference: 布尔值，表示是否包含外部引用
            // - references: 引用信息数组，如果没有外部引用则为空数组

            // **重要提醒：**
            // - 输出必须是有效的JSON格式
            // - 不要输出任何JSON之外的内容
            // - 必须排除"钢之家讯"这种内部引用
            // SYSTEM,

            // 12 => <<<SYSTEM
            //     请检测给定文本中是否包含外部引用，并按照给定JSON格式返回。

            //     **输出要求：**
            //     1.引用是指类似于“钢之家讯”、“新华社报”这种具有明确或诱导性文章来源指向的文本部分
            //     2.所有有关“钢之家”的引用均为内部引用而不是外部引用
            //     3.所有有关“XX讯”（其中XX是城市名或地点）的引用均为内部引用而不是外部引用
            //     4.必须严格按照以下JSON格式输出，不要添加任何其他内容

            //     **输出JSON格式：**
            //     ```json
            //     [
            //         {
            //             "text": <出现外部引用的单句>
            //         },
            //         ...
            //     ]
            //     ```

            //     **举例1：**
            //     输入：国内讯，8月25日下午，中国炼焦行业协会市场委员会召开专题市场分析会，来自山西、河北、内蒙古、河南、江苏、山东、陕西、宁夏、江西、云贵等地的重点焦化企业代表出席会议。
            //     输出：[
            //         {
            //             "text": "国内讯"
            //         }
            //     ]

            //     **举例2：**
            //     输入：钢之家快讯:25日 中国进口铁矿石外盘价格全面上涨。PB粉矿Fe:61.5% 皮尔巴拉为101.5元/吨，上涨2元/吨。
            //     输出：[]

            //     **举例3：**
            //     输入：钢之家、欧冶联合快讯：近期黑色产业链利好因素很多:1、黑色产业链钢价、煤价、铁矿石价格均创年内新高，焦炭、动力煤价格仍低于年初价格水平。
            //     输出：[]

            //     **举例4：**
            //     输入：近期黑色产业链利好因素很多:1、黑色产业链钢价、煤价、铁矿石价格均创年内新高，焦炭、动力煤价格仍低于年初价格水平。搜狐、网易联合报道。
            //     输出：[
            //         {
            //             "text": "搜狐、网易联合报道"
            //         }
            //     ]
                
            //     **举例5：**
            //     输入：‌新讯通讯是一个成立于2020年、专注于物联网智能硬件研发与销售的公司。
            //     输出：[]

            //     **举例6：**
            //     输入：采编: 钢之家资讯部 请勿转载 垂询电话:021-50582538 15801946895
            //     输出：[]

            // SYSTEM,

            12 => <<<SYSTEM
                你是一个专业的文本分析专家，你的任务是检测给定文本是否包含外部引用，请严格按照以下要求执行：

                **公司信息：**
                公司名称：钢之家

                **检测规则：**
                1. 查找文本中是否包含"xxxx讯[，|。|：]"格式的外部引用标识
                2. 需要排除"钢之家讯"等内部引用
                3. 需要排除"福州讯"、"国内讯"等城市和国家级别的引用
                4. 如果发现外部引用，提取引用的来源名称
                5. 输出宁缺毋滥，没有外部引用时不必强行提取

                **输出格式要求：**
                必须严格按照以下JSON格式输出，不要添加任何其他内容：
                ```json
                [
                    {
                        "text": "引用来源名称1"
                    },
                    {
                        "text": "引用来源名称2"
                    },
                    ...
                ]
                ```

                **举例1：**
                输入：金十数据8月22日讯，李强主持召开国务院常务会议。
                输出：[
                    {
                        "text": "金十数据8月22日讯"
                    }
                ]

                **举例2：**
                输入：来源:亚泰集团 钢之家资讯部采编 请勿转载 垂询电话:0555-2238852
                输出：[]
                解释：钢之家资讯部采编，因此是内部引用，本句话需排除。

                **举例3：**
                输入：郑州讯：8月27日，对郑州主要钢材市场镀锌板卷库存进行调查。
                输出：[]
                解释：郑州是城市，郑州讯属于城市级别引用，需排除。
            SYSTEM,
        ];
    
        // 获取对应的 system 消息，如果不存在则返回空字符串
        return trim($systemMessages[$promptType] ?? "");
    }
    
    /**
     * 模型选择
     * @param mixed $data
     * @return array{frequency_penalty: int, max_tokens: int, messages: mixed, model: string, stream: bool|array{max_tokens: int, messages: mixed, model: string, stream: bool}}
     */
    public function setPayload($data): array {
        $model_name = $data['model'];
        $prompt_type = $data['prompt_type'];
        $messages = $data['messages'];
        $stream = $data['stream']??true;
        $temperature = $data['temperature']??-1;
        $top_p = $data['top_p']??-1;
        $tool_choice = $data['tool_choice']??-1;
        $payload = [];
        // content_type说明  0:默认 1:网络搜索 2:内部数据库搜索结果
        $content_type = $data['content_type']??0;

        $systemMessage = str_replace(" ", "", $this->setSystem($prompt_type));
        if (!empty($systemMessage) && $content_type != 1) {
            // 检查 messages 是否已有 system 消息
            $hasSystemMessage = false;
            foreach ($messages as &$msg) {
                if ($msg['role'] === 'system') {
                    // **合并 system 消息，而不是插入新的**
                    $msg['content'] = trim($systemMessage . "\n\n" . $msg['content']);
                    $hasSystemMessage = true;
                    break;
                }
            }
            // 如果没有 system 消息，则插入
            if (!$hasSystemMessage) {
                array_unshift($messages, [
                    "role" => "system",
                    "content" => $systemMessage
                ]);
            }
        }
        $default_payload = [
            "messages" => $messages,
            "model" => "ernie-speed-128k",  // 默认模型
            "max_tokens" => 4096,
            "stream" => $stream,
        ];

        $tempArr = [];
        // 配置tools参数
        if(isset($data['tools']) && count($data['tools'])>0) {
            $tempArr["tools"] = $data['tools'];
        }

        // 配置 parallel_tool_calls 参数
        if(isset($data['parallel_tool_calls']) && trim($data['parallel_tool_calls']!="")) {
            $tempArr["parallel_tool_calls"] = $data['parallel_tool_calls'];
        }

        $default_payload = array_replace_recursive($default_payload, $tempArr);
        // dump($default_payload);
        // dump($model);
        [$other_payload, $model] = $this->setOtherPayload($model_name);
        // dd($other_payload);
        $payload = array_replace_recursive($default_payload, $other_payload);
        // dd($payload);
        if($temperature!=-1 && $temperature!="") $payload['temperature'] = $temperature;
        if($top_p!=-1 && $top_p!="") $payload['top_p'] = $top_p;
        if($tool_choice!=-1 && $tool_choice!="") $payload['tool_choice'] = $tool_choice;
        return $payload;
    }
    
    /**
     * 处理文件上传，支持分片上传。
     *
     * @param array $request 包含上传文件信息的数组，必须包含以下键：
     * - 'fileChunk': 文件分片的上传信息，必须为有效的上传文件。
     * - 'chunkIndex': 当前分片的索引，从 0 开始。
     * - 'totalChunks': 文件的总分片数。
     * - 'filename': 原始文件名，用于保存合并后的文件。
     *
     * @return void 返回 JSON 格式的响应，包含上传结果和文件路径。
     * - success: 1 表示成功，0 表示失败。
     * - msg: 详细的消息说明。
     * - path: 成功上传后文件的存储路径。
     *
     * @throws Exception 如果创建目录、保存文件或合并文件失败，将返回相应的错误信息。
     */
    public function uploadFile(array $request)
    {
        // 初始化返回数据，默认 success 为 0 表示失败
        $arr = ["success" => 0, "msg" => "", "filepath" => "", "filename" => ""];
    
        // 基础存储目录
        $baseUploadDir = $this->baseUploadDir;
        // 按日期创建子目录（例如：2025/02/19）
        $datePath = date('Y/m/d');
        $uploadDir = rtrim($baseUploadDir, '/') . '/' . $datePath . '/';
        if (!is_dir($uploadDir)) {
            // 递归创建目录
            if (!mkdir($uploadDir, 0755, true)) {
                $arr["msg"] = "无法创建上传目录";
                echo json_encode($arr, JSON_UNESCAPED_UNICODE);
                exit;
            }
        }
    
        // 检查必须的参数，文件数据存储在 $_FILES['fileChunk']，其他参数通过 $request 传递
        if (!isset($_FILES['fileChunk']) || $_FILES['fileChunk']['error'] !== UPLOAD_ERR_OK) {
            $arr["msg"] = "文件分片上传失败或无效";
            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    
        // 获取分片相关信息
        $chunkIndex = isset($request['chunkIndex']) ? intval($request['chunkIndex']) : 0;
        $totalChunks = isset($request['totalChunks']) ? intval($request['totalChunks']) : 0;
        // 过滤文件名，防止目录穿越
        $originalFilename = isset($request['filename']) ? basename($request['filename']) : '';
        if (empty($originalFilename) || $totalChunks <= 0) {
            $arr["msg"] = "参数错误";
            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    
        // 限制文件格式（后缀检测）
        $ext = strtolower(pathinfo($originalFilename, PATHINFO_EXTENSION));
        $allowedExtensions = ['pdf', 'doc', 'docx'];
        if (!in_array($ext, $allowedExtensions)) {
            $arr["msg"] = "不允许的文件格式";
            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    
        // 为该文件创建一个临时存储分片的目录，使用文件名的 MD5 值作为标识
        $tempDir = $uploadDir . md5($originalFilename) . '/';
        if (!is_dir($tempDir)) {
            if (!mkdir($tempDir, 0755, true)) {
                $arr["msg"] = "无法创建临时目录";
                echo json_encode($arr, JSON_UNESCAPED_UNICODE);
                exit;
            }
        }
    
        // 保存当前分片到临时目录
        $chunkFilePath = $tempDir . $chunkIndex;
        if (!move_uploaded_file($_FILES['fileChunk']['tmp_name'], $chunkFilePath)) {
            $arr["msg"] = "保存分片失败";
            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    
        // 检查该文件的分片是否全部上传完成
        $uploadedChunks = count(glob($tempDir . '*'));
        if ($uploadedChunks < $totalChunks) {
            // 如果未全部上传，则返回上传成功的状态，等待后续分片
            $arr["success"] = 2;
            $arr["msg"] = "分片 {$chunkIndex} 上传成功，等待剩余分片。";
            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    
        // 所有分片均已上传，开始合并分片
        $uniquePrefix = uniqid() . '_';
        $finalFilePath = $uploadDir . $uniquePrefix . $originalFilename;
        if (!$outHandle = fopen($finalFilePath, 'wb')) {
            $arr["msg"] = "无法创建合并后的文件";
            echo json_encode($arr, JSON_UNESCAPED_UNICODE);
            exit;
        }
    
        // 按顺序读取每个分片，写入最终文件
        for ($i = 0; $i < $totalChunks; $i++) {
            $chunkPath = $tempDir . $i;
            if (!file_exists($chunkPath)) {
                fclose($outHandle);
                $arr["msg"] = "缺少分片 {$i}";
                echo json_encode($arr, JSON_UNESCAPED_UNICODE);
                exit;
            }
            if (!$inHandle = fopen($chunkPath, 'rb')) {
                fclose($outHandle);
                $arr["msg"] = "无法读取分片 {$i}";
                echo json_encode($arr, JSON_UNESCAPED_UNICODE);
                exit;
            }
            while (!feof($inHandle)) {
                $buffer = fread($inHandle, 4096);
                fwrite($outHandle, $buffer);
            }
            fclose($inHandle);
        }
        fclose($outHandle);
    
        // 合并完成后，删除临时分片文件和目录
        array_map('unlink', glob($tempDir . '*'));
        rmdir($tempDir);

        if($finalFilePath!="" && $finalFilePath!=null) {
            $finalFilePath = str_replace($baseUploadDir, '', $finalFilePath);
        }

        // 返回成功响应，路径为最终存储的文件路径
        $arr["success"] = 1;
        $arr["msg"] = "文件上传成功";
        $arr["filepath"] = $finalFilePath;
        $arr["filename"] = $originalFilename;
        echo json_encode($arr, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 设置 HTTP 头部，确保 SSE 流输出正常
     */
    public function setupHeaders(): void
    {
        ob_end_clean();
        ini_set('zlib.output_compression', 'Off');
        header('Content-Encoding: identity');
        header('X-Accel-Buffering: no');
        header('Cache-Control: no-cache');
        header('Content-Type: text/event-stream');
        header('Connection: keep-alive');
    }

    /**
     * 从流式响应中逐块读取数据，实时输出每一行，同时将所有内容拼接返回。
     *
     * @param \Psr\Http\Message\StreamInterface $body 响应的 Body 流
     * @return string 返回拼接后的完整响应文本
     */
    public function outputAndAccumulateStream($body, $session_id): string {
        $accumulatedText = '';
        $buffer = '';
        // 设置日志目录
        $logDir = './logs/'.date('Y-m-d').'/';
        // 检查目录是否存在，不存在则创建（递归创建子目录）
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }

        // 使用日期生成日志文件名，例如 info-2025-03-07.log
        $logFileName = $logDir . 'info-' . date('H') . '.log';

        // 以追加模式打开文件，文件不存在时会自动创建
        $logFile = fopen($logFileName, 'a');
        if ($logFile) {
            fwrite($logFile, "------------------------".date("Y-m-d H:i:s")."------------------------\n");
            fwrite($logFile, "------------------------session_id:".$session_id."------------------------\n");
        }
        // 持续读取流直到结束
        while (!$body->eof()) {
            // 每次读取1024字节，可根据需要调整
            $chunk = $body->read(1024);
            if ($chunk === false) {
                break;
            }
            $buffer .= $chunk;
            
            // 当缓冲区中有完整的行时进行处理
            while (($newlinePos = strpos($buffer, "\n")) !== false) {
                // 提取一行（包括换行符）
                $line = substr($buffer, 0, $newlinePos + 1);
                // 移除已处理的部分
                $buffer = substr($buffer, $newlinePos + 1);
                
                // 实时输出这一行
                echo $line;
                flush();

                // 写入文件
                if ($logFile) {
                    fwrite($logFile, $line);
                }
                
                // 累加到完整响应中
                $accumulatedText .= $line;
            }
        }
        
        // 如果缓冲区中仍有剩余数据（可能没有换行符），也处理掉
        if (strlen($buffer) > 0) {
            echo $buffer;
            flush();
            $accumulatedText .= $buffer;

            // 写入文件
            if ($logFile) {
                fwrite($logFile, $buffer);
            }
        }
        // 关闭文件
        if ($logFile) {
            fclose($logFile);
        }
        return $accumulatedText;
    }

    /**
     * 读取所有文件的正文内容，并拼接成一个字符串。
     * 对单个文件内容限制为最多 $maxContentLengthPerFile 个字符，
     * 最终拼接的内容也限制为 $maxCombinedLength 个字符，超出部分将截断。
     *
     * @param array $filepathList 文件相对路径数组，例如 ["/2025/02/20/67b6dfca0d164_2024年终总结.docx", ...]
     * @return string 拼接后的所有文档内容
     */
    public function getAllDocumentContent($filepathList): string
    {
        $combinedContent = "";

        $i = 1;
        foreach ($filepathList as $relativePath) {
            // 拼接完整路径
            $fullPath = $this->baseUploadDir . $relativePath;
            if (!file_exists($fullPath)) {
                continue;
            }

            // 根据文件扩展名判断文件类型
            $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
            $fileContent = "";

            switch ($ext) {
                case 'docx':
                // case 'doc':
                    $fileContent = $this->extractDocxText($fullPath);
                    break;
                case 'pdf':
                    $fileContent = $this->extractPdfText($fullPath);
                    break;
                default:
                    // 不支持的文件类型跳过
                    continue 2;
            }
            // 拼接时加上文件名标识，便于区分不同文档内容
            $combinedContent .= "\n文档".$i."：\n" . $this->getFileName($fullPath) . "\n" . $fileContent . "\n";
        }

        return $combinedContent;
    }

    private function extractDocxText(string $filePath): string
    {
        ini_set('memory_limit', '1024M');
        $content = "";
        try {
            // 检查文件是否存在且可读
            if (!file_exists($filePath) || !is_readable($filePath)) {
                throw new \Exception("文件不存在或不可读: $filePath");
            }
    
            $ext = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            $reader = null;
            // 根据扩展名选择读取器
            if ($ext === 'docx') {
                $reader = IOFactory::createReader('Word2007');
            } elseif ($ext === 'doc') {
                // 尝试使用 MsDoc 读取器（需 PHPWord 支持，否则会抛异常）
                try {
                    $reader = IOFactory::createReader('MsDoc');
                } catch (\Exception $e) {
                    // 如果旧版读取器不可用，则尝试 Word2007（可能读取失败）
                    $reader = IOFactory::createReader('Word2007');
                    // dd($e->getMessage());
                }
            }
            // dd($reader);
    
            if (!$reader) {
                throw new \Exception("不支持的文档格式: $ext");
            }
    
            // 加载文档
            $phpWord = $reader->load($filePath);
            // 遍历文档的所有 section 及其元素，提取文本
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    // 如果元素是容器（如段落），遍历其子元素
                    if (method_exists($element, 'getElements')) {
                        foreach ($element->getElements() as $child) {
                            if (method_exists($child, 'getText')) {
                                $content .= $child->getText() . " ";
                            }
                        }
                    } elseif (method_exists($element, 'getText')) {
                        $content .= $element->getText() . " ";
                    }
                }
            }
            // 清理多余空白字符
            $content = trim(preg_replace('/\s+/', ' ', $content));
        } catch (\Exception $e) {
            // 可以记录日志或进一步处理错误
            error_log("extractDocxText error: " . $e->getMessage());
            $content = "";
        }
        return $content;
    }

    /**
     * 读取 PDF 文件中的文本内容
     *
     * @param string $filePath PDF 文件的完整路径
     * @return string 返回 PDF 中的文本内容
     */
    private function extractPdfText(string $filePath): string
    {
        ini_set('memory_limit', '1024M');
        try {
            if (!file_exists($filePath) || !is_readable($filePath)) {
                throw new \Exception("文件不存在或不可读: $filePath");
            }
            // 使用 smalot/pdfparser 解析 PDF 文档
            $parser = new \Smalot\PdfParser\Parser();
            $pdf = $parser->parseFile($filePath);
            // 获取所有的内容
            // $content = $pdf->getText();
            // dd($content);

            $pages = $pdf->getPages();
            $pageTexts = []; // 存储所有页面文本
            $lineCounts = []; // 统计所有行出现的次数
            
            // 逐页读取文本并存储
            foreach ($pages as $page) {
                $lines = explode("\n", trim($page->getText()));
                $pageTexts[] = $lines;
            
                // 统计行出现的次数
                foreach ($lines as $line) {
                    $lineCounts[$line] = ($lineCounts[$line] ?? 0) + 1;
                }
            }
            
            $text = ""; // 存储最终输出的文本
            $pageCount = count($pages);
            
            // 正则匹配常见的页码格式
            $pageNumberPatterns = [
                '/^Page\s*\d+\s*of\s*\d+$/i',   // "Page X of Y"
                '/^第\s*\d+\s*页$/u',           // "第 X 页"
                '/^[-–—]?\s*\d+\s*[-–—]?$/',    // "- X -", "X"
                '/^\d+\s*\/\s*\d+$/',           // "X/Y" 或 "X / Y"
                '/^\s*$/m',  //去除空行
            ];
            
            // 逐页处理文本，过滤掉高频页眉页脚和页码
            foreach ($pageTexts as $lines) {
                $filteredPage = array_filter($lines, function ($line) use ($lineCounts, $pageCount, $pageNumberPatterns) {
                    // 过滤掉符合页码格式的行
                    foreach ($pageNumberPatterns as $pattern) {
                        if (preg_match($pattern, trim($line))) {
                            return false;
                        }
                    }
            
                    return true;
                });
                $text .= implode("\n", $filteredPage) . "\n";
            }
            return trim($text);
            
        } catch (\Exception $e) {
            // error_log("extractPdfText error: " . $e->getMessage());
            return "";
        }
    }

    public function countTokens($text): int
    {
        return mb_strlen($text, 'utf-8') / 4; // 近似估算，每个 token ≈ 4 个字符
    }
    
    public function getFileName($p)
    {
        $filename = basename($p);
        return preg_replace('/^[a-f0-9]{13,}_/i', '', $filename);
    }

    public function getDatatype(&$word)
    {
        $type = array_pop($word);
        if(!in_array($type, ["1", "2", "3", "4", "6"])) {
            $type = "-1";
        }
        return $type;
    }

    public function getConFromHtml($html) {
        $tableHtml = "";
        $isConvertMarkdown = 1;
        $pq = phpQuery::newDocument($html);
        // 检查是否有合并单元格的情况，如果有则不转换为Markdown格式
        if(str_contains($html, "rowspan") || str_contains($html, "colspan")) {
            $isConvertMarkdown = 0;
        }
        if($isConvertMarkdown) {
            // 处理表格并转换为Markdown格式
            $tables = $pq->find('table');  // 获取所有 table 标签
            foreach ($tables as $table) {
                // 检查是否有表头，如果没有则直接从 tbody 处理
                $thead = pq($table)->find('thead');
                $headers = [];
            
                if ($thead->length) {
                    // 如果有 thead，获取表头
                    $theadRow = pq($thead)->find('tr');
                    foreach ($theadRow as $th) {
                        $headers[] = trim(pq($th)->text() ?? "");
                    }
                    // 输出表头
                    $tableHtml .= '| ' . implode(' | ', $headers) . ' |' . "\n";
                    $tableHtml .= '|' . str_repeat(' :---: |', count($headers)) . "\n";
                } else {
                    // 如果没有 thead，则直接从 tbody 里获取第一行作为表头
                    $tbody = pq($table)->find('tbody tr');
                    $firstRow = pq($tbody)->get(0);  // 使用 get(0) 获取第一行
                    foreach (pq($firstRow)->find('td') as $td) {
                        $headers[] = trim(pq($td)->text() ?? "");
                    }
                    // 输出表头
                    $tableHtml .= '| ' . implode(' | ', $headers) . ' |' . "\n";
                    $tableHtml .= '|' . str_repeat(' :---: |', count($headers)) . "\n";
                    // 跳过第一行，继续处理后续行
                    $tbody = pq($tbody)->not(':first');
                }
            
                // 处理表格内容
                foreach ($tbody as $tr) {
                    $tableHtml .= '|';
                    $tds = pq($tr)->find('td');
                    foreach ($tds as $td) {
                        $tableHtml .= ' ' . trim(pq($td)->text() ?? "") . ' |';
                    }
                    $tableHtml .= "\n";
                }
            }
        } else {
            $tableHtml = "<table>".$pq->find('table')->html()."</table>";
            $tableHtml = str_replace("\t", "", $tableHtml);
            $tableHtml = str_replace("\r\n", "", $tableHtml);
            $tableHtml = str_replace("\n", "", $tableHtml);
            $tableHtml = str_replace("\r", "", $tableHtml);
        }

        // 处理图片路径
        $images = $pq->find('img');
        foreach ($images as $img) {
            $src = pq($img)->attr('src');  // 获取图片路径
            $imagesMarkdown .= "\n".'![Image](' . $src . ")\n";
        }

        return [$tableHtml, $imagesMarkdown];
    }

    public function getConFromHtml2($html) {
        $pq = phpQuery::newDocument($html);
    
        // 先移除包含(编撰...)或(采编...)的标签
        foreach ($pq->find('*') as $el) {
            $text = pq($el)->text() ?? "";
            if (preg_match('/\(编.*?\)|\(采编.*?\)/u', $text)) {
                pq($el)->remove();
            }
        }
        
        // 重新提取所有文本内容
        $textContent = pq($pq)->text();
        
        // 替换 a 标签内容为 Markdown 链接格式
        foreach ($pq->find('a') as $el) {
            $href = pq($el)->attr('href');
            $text = trim(pq($el)->text() ?? "");
            if (!empty($text) && !empty($href)) {
                $textContent = str_replace($text, "[{$text}]({$href})", $textContent);
            }
        }
        
        return trim($textContent);
    }

    public function printInfo(string $content, int $timeout=50, string $reasoning_content=null, $finish_reason=null) {
        $customResult = [
            "object" => "chat.completion",
            "choices" => [
                [
                    "index" => "0",
                    "delta" => [
                        "role"=> "assistant",
                        "content" => $content,
                        "reasoning_content" => $reasoning_content,
                    ],
                    "finish_reason" => $finish_reason
                ]
            ],
            "timeout" => $timeout,
            "content_type" => 2
        ];
        $badyStr = "data: ".json_encode($customResult, JSON_UNESCAPED_UNICODE);
        echo $badyStr . "\n\n";
        ob_flush();
        flush();
    }

    public function checkReport($content) {
        if( str_contains($content, "报告") ) {
            return true;
        } else {
            return false;
        }
    }

    public function test_response_webSearch() {
        return file_get_contents("webSearchTest.json");
    }

    public function webSearch($query): array{
        $client = new Client([
            'base_uri' => BOCHAAPI_BASE_URL
        ]);
        $info = [
            "query" => $query,
            "freshness" => "noLimit",
            "summary" => true,
            "count" => 10,
        ];

        $response = $client->request('POST', "/v1/web-search", [
            'json'         => $info,
            'headers'      => [
                'Content-Type' => 'application/json',
                'Authorization'=> 'Bearer '.API_KEY_BOCHA
            ],
            'http_errors'  => false,   // 避免 4xx/5xx 直接抛异常
            'timeout' => 20,
        ]);

        $body = $response->getBody();
        $result = json_decode($body, true);

        return $result;
    }

    public function getBalanceOfBOCHA() {
        $client = new Client([
            'base_uri' => BOCHAAPI_BASE_URL
        ]);

        $response = $client->request('GET', "/v1/fund/remaining", [
            'headers'      => [
                'Content-Type' => 'application/json',
                'Authorization'=> 'Bearer '.API_KEY_BOCHA
            ],
            'http_errors'  => false,   // 避免 4xx/5xx 直接抛异常
            'timeout' => 20,
        ]);

        $body = $response->getBody()->getContents();
        $result = json_decode($body, true);
        $money = $result['data']['remaining'] ?? -1001;
        return $money;
    }

    public function getBalanceOfDeepseek() {
        // 这个令牌是oneapi系统自身的
        $keys = "5b03c913801747b3a0ff2219d1bc50d5";
        $client = new Client([
            'base_uri' => ONEAPI_URL
        ]);
        $response = $client->request('GET', "/api/channel/update_balance/1/", [
            'headers'      => [
                'Content-Type' => 'application/json',
                'Authorization'=> 'Bearer '.$keys
            ],
        ]);

        $body = $response->getBody()->getContents();
        $result = json_decode($body, true);
        $money = $result['balance'] ?? -1001;
        return $money;
    }
}