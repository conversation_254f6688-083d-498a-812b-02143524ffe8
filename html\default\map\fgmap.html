<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>钢之家钢铁网-<{$pageName}></title>
    <meta name="verify-v1" content="rfRws4Y1mwg6EQn44sY6gX/gxhKUSQteND7cp5jrAoY=" />
    <meta name="Description" content="钢之家网站从事钢铁行业信息服务,钢材交易,网站建设,准确及时的反映各地区钢材市场的钢材价格,钢铁价格以及钢材行情信息,是一个专注于钢铁行业的专业钢铁网站." />
    <meta name="Keywords" content="钢之家,钢材价格,现货报价,钢材市场,钢材行情,钢铁价格走势,钢材价格指数,钢之家钢铁网" />
    <meta name="Copyright" content="钢之家钢铁信息 - 钢材价格 - 钢材市场 - 钢铁价格 - 行情资讯网 - 钢之家资讯" />
    <meta property="wb:webmaster" content="8c9c4eb4c25b011c" />
    <meta http-equiv="Expires" CONTENT= "0">
    <meta http-equiv="Pragma" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-store">
    <meta http-equiv="Cache-Control" CONTENT="must-revalidate">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta content="text/html; charset=utf-8" http-equiv=Content-Type>
    <link href="/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/js/www/layui/css/layui.css">
    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/css/www/base_new.css?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>">
 	<link href="/css/fgmap.css?v=2025021" rel="stylesheet">
    <script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=952ee46c4f5ad535aa544d4390b9918f"></script>
<script type="text/javascript" src="js/ajax.js" ></script>
<script type="text/javascript" src="_v2app/js/topdh.js"></script> 
<script src="_v2app/js/map/jquery.js"></script>
<script src="_v2app/js/map/jquery.autocomplete.js" ></script>
<link rel="Stylesheet"  type="text/css" href="_v2app/js/map/jquery.autocomplete.css" /> 
<script type="text/javascript">
var mapObj=null;

//显示层信息
//var tipOption=new MTipOptions();
var debug = "<{$debug}>";

var arr1=["","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];
var arr2=["","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"];
var area_x = [101.689453,126.977577,114.851074,92.8125,95.844727,112.148438,118.388672];
var area_y = [38.203655,45.562782,40.094882,38.548165,30.334954,28.921631,31.503629];
var area_zoom = [4.5,5.5,6,5,5.5,5,6];

var default_x = '121.525488';
var default_y = '31.226575';
// BD-09 to GCJ-02
function bd09togcj02 (bd_lng, bd_lat)  {
	const x = bd_lng - 0.0065;
	const y = bd_lat - 0.006;
	const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI);
	const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI);
	const gg_lng = z * Math.cos(theta);
	const gg_lat = z * Math.sin(theta);
	return [gg_lng, gg_lat];
}

// GCJ-02 to WGS84
function gcj02towgs84(lng, lat) {
	const a = 6378245.0;
	const ee = 0.00669342162296594323;
	const pi = Math.PI;

	function transformlat(lng, lat) {
		let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(
			lng));
		ret += (20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(lat * pi) + 40.0 * Math.sin(lat / 3.0 * pi)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(lat / 12.0 * pi) + 320 * Math.sin(lat * pi / 30.0)) * 2.0 / 3.0;
		return ret;
	}

	function transformlng(lng, lat) {
		let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
		ret += (20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(lng * pi) + 40.0 * Math.sin(lng / 3.0 * pi)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(lng / 12.0 * pi) + 300.0 * Math.sin(lng / 30.0 * pi)) * 2.0 / 3.0;
		return ret;
	}

	let dlat = transformlat(lng - 105.0, lat - 35.0);
	// console.log(dlat);
	let dlng = transformlng(lng - 105.0, lat - 35.0);
	// console.log(dlng);
	const radlat = lat / 180.0 * pi;
	let magic = Math.sin(radlat);
	magic = 1 - ee * magic * magic;
	const sqrtmagic = Math.sqrt(magic);
	dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * pi);
	dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * pi);
	const mglat = lat + dlat;
	const mglng = lng + dlng;
	// console.log(mglng, mglat);
	return [lng * 2 - mglng, lat * 2 - mglat];
}

// WGS84 to GCJ-02
function wgs84togcj02 (lng, lat) {
	const a = 6378245.0;
	const ee = 0.00669342162296594323;
	const pi = Math.PI;

	function transformlat(lng, lat) {
		let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(
			lng));
		ret += (20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(lat * pi) + 40.0 * Math.sin(lat / 3.0 * pi)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(lat / 12.0 * pi) + 320 * Math.sin(lat * pi / 30.0)) * 2.0 / 3.0;
		return ret;
	}

	function transformlng(lng, lat) {
		let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
		ret += (20.0 * Math.sin(6.0 * lng * pi) + 20.0 * Math.sin(2.0 * lng * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(lng * pi) + 40.0 * Math.sin(lng / 3.0 * pi)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(lng / 12.0 * pi) + 300.0 * Math.sin(lng / 30.0 * pi)) * 2.0 / 3.0;
		return ret;
	}

	let dlat = transformlat(lng - 105.0, lat - 35.0);
	let dlng = transformlng(lng - 105.0, lat - 35.0);
	const radlat = lat / 180.0 * pi;
	let magic = Math.sin(radlat);
	magic = 1 - ee * magic * magic;
	const sqrtmagic = Math.sqrt(magic);
	dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * pi);
	dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * pi);
	const mglat = lat + dlat;
	const mglng = lng + dlng;
	return [mglng, mglat];
}

var map
var zoom = 10;
var pnx='<{$pnx}>';    //pnx，pny是搜索到的钢厂的经纬度坐标
var key='<{$params.keyword}>';   //key是用来标记搜索错误的，出错标为1，否则为0   
var pny='<{$pny}>';
var targetflag=1;

function onLoad() {
	//初始化地图对象
	map = new T.Map("map");
	//设置显示地图的中心点和级别
	if(key!=''&&pnx!=''){	
		// console.log(key);
		const [lng, lat] = [parseFloat(pnx),parseFloat(pny)];//gcj02towgs84(parseFloat(pnx), parseFloat(pny));
		map.centerAndZoom(new T.LngLat(lng, lat), zoom);
	}
	else
	{
		const [lng, lat] = [parseFloat(default_x),parseFloat(default_y)];//gcj02towgs84(parseFloat(default_x), parseFloat(default_y));
		map.centerAndZoom(new T.LngLat(lng, lat), zoom);
	}
	// 假设已经获取了天地图的实例，并将其存储在变量map中  
	map.on('zoomstart', function(event) {  
		// 处理缩放开始事件  
		// console.log('缩放开始');  
	});  
	
	map.on('zoom', function(event) {  
		// 处理缩放过程中事件  
		// console.log('缩放中，当前缩放级别：', event.zoom);  
	});  
	
	map.on('zoomend', function(event) {  
		// 处理缩放结束事件  
		readyMapEnd(0);
		// console.log('缩放结束，最终缩放级别：', event.zoom);  
	});
	// 监听地图移动开始事件  
	map.addEventListener('movestart', function(event) {  
		// console.log('地图移动开始');  
		// 在此处执行初始化操作  
	});  
	
	// 监听地图移动中事件  
	map.addEventListener('moving', function(event) {  
		// console.log('地图正在移动');  
		// 在此处实时更新地图上的元素或信息  
		var center = map.getCenter(); // 获取地图中心点  
		// console.log('当前地图中心点：', center.lng, center.lat);  
	});  
	
	// 监听地图移动结束事件  
	map.addEventListener('moveend', function(event) {  
		// console.log('地图移动结束');  
		// 在此处执行收尾操作  
		readyMapEnd(0);
		// var bounds = map.getBounds(); // 获取地图当前显示范围  
		// console.log('当前地图显示范围：', bounds);  
	});  
	readyMapEnd(0);
	// for(var i=0;i<data_info.length;i++){
	// 	var marker = new T.Marker(new T.LngLat(data_info[i][0],data_info[i][1]));  // 创建标注
	// 	var content = data_info[i][2];
	// 	map.addOverLay(marker);               // 将标注添加到地图中
	// 	addClickHandler(content,marker);
	// }

	function addClickHandler(content,marker){
		marker.addEventListener("click",function(e){
			openInfo(content,e)}
		);
	}
	function openInfo(content,e){
		var point = e.lnglat;
		marker = new T.Marker(point);// 创建标注
		var markerInfoWin = new T.InfoWindow(content,{offset:new T.Point(0,-30)}); // 创建信息窗口对象
		map.openInfoWindow(markerInfoWin,point); //开启信息窗口
	}
}

 

//加载完地图后进行初始化
function readyMapEnd(param){
	addMarkergzj();
	//var bounds=mapObj.getLngLatBounds();//返回MLngLatBounds类对象，该对象表示地图视野范围矩形区域西南和东北角点的经纬度坐标
	var bounds=map.getBounds();//返回MLngLatBounds类对象，该对象表示地图视野范围矩形区域西南和东北角点的经纬度坐标
	//getgc(bounds.southWest.lngX,bounds.southWest.latY,bounds.northEast.lngX,bounds.northEast.latY,page,0);
	var bssw = bounds.getSouthWest();   //可视区域左下角
	var bsne = bounds.getNorthEast();   //可视区域右上角
	// alert("当前地图可视范围是：" + bssw.getLng() + "," + bssw.getLat() + "到" + bsne.getLng() + "," + bsne.getLat());
	getgc(bssw.getLng(),bssw.getLat(),bsne.getLng(),bsne.getLat(),page,0);
}

// function getLngLatBounds() {
// 	var bounds=mapObj.getBounds();//返回MLngLatBounds类对象，该对象表示地图视野范围矩形区域西南和东北角点的经纬度坐标
// 	//getgc(bounds.southWest.lngX,bounds.southWest.latY,bounds.northEast.lngX,bounds.northEast.latY,page,0);
// 	getgc(bounds.getSouthWest().getLng(),bounds.getSouthWest().getLat(),bounds.getNorthEast().getLng(),bounds.getNorthEast().getLat(),page,0);
// }

//var page = "<{$page}>";
//var t_pages = "<{$t_pages}>";
var page = "1";
var t_pages = "0";
var pagenumber = 18;
var pz = "<{$pz}>";
var area = "<{$area}>";
var initing = 0;

function goPage(page_cu){
	var bounds=map.getBounds();//返回MLngLatBounds类对象，该对象表示地图视野范围矩形区域西南和东北角点的经纬度坐标  
	var bssw = bounds.getSouthWest();   //可视区域左下角
	var bsne = bounds.getNorthEast();   //可视区域右上角
	removeMarker();
	getgc(bssw.getLng(),bssw.getLat(),bsne.getLng(),bsne.getLat(),page_cu,1);
}

function getgc(x,y,lx,ly,page_cu,isgopage){
	if(initing==0)
	{
		initing = 1;
		var temps = encryption(); 
		var xy =	wgs84togcj02(parseFloat(x),parseFloat(y));
		var lxy = wgs84togcj02(parseFloat(lx),parseFloat(ly));
		//var param = "temps="+temps+"&view=ajaxgetfg&area="+area+"&page="+page_cu+"&pagenumber="+pagenumber+"&pz="+pz+"&x=" + xy[0] + "&y=" + xy[1] + "&lx=" + lxy[0] + "&ly=" + lxy[1];
		var param = "temps="+temps+"&view=ajaxgetfg&area="+area+"&page="+page_cu+"&pagenumber="+pagenumber+"&pz="+pz+"&x=" + x + "&y=" + y + "&lx=" + lx + "&ly=" + ly;
		if(isgopage==1) param +="&gopage=1";
		if(debug==1)alert(param);
		var ajax = new Ajax( "/_v2app/mapditu.php", setData, param );
	}
}

var encryption = function(){
    var date = new Date();
    var times1970 = date.getTime();
    var times = date.getDate() + "" + date.getHours() + "" + date.getMinutes() + "" + date.getSeconds();
    var encrypt = times * times1970;
    if(arguments.length == 1){
        return arguments[0] + encrypt;
    }else{
        return encrypt;
    }
}

// 删除标记的函数  
function removeMarker() {
	var overlays = map.getOverlays();  
	
	// 遍历覆盖物集合，并删除每个覆盖物  
	for (var i = 0; i < overlays.length; i++) {  
		// 判断覆盖物是否为标记（可以根据需要添加其他判断条件）  
		// 在这里，我们假设所有覆盖物都是标记，或者我们只关心标记  
		map.removeOverLay(overlays[i]);  
	}
}

var current_map = new Array();
//当前地图上的大图
function setData( returnStr )
{	
		//将之前显示的大图变小
		for( var j = 0;j<current_map.length;j++)
		{
			var tt = current_map[j];
			// if(eval("typeof(markerGC"+tt[1]+")") != "undefined")
			// eval("markerGC"+tt[1]+".setMap()");
			// AddTDTMarker(tt,"mapd0.png",0);//全部标记小图
			// console.log(1);
		}

		var tmp = returnStr.split( "|O|" );
		var ii=0;
		for( var i=ii;i<tmp.length ; i++)
		{
			var tt = tmp[i].split("|X|");
			if(tt[0]==0)
			{
				ii = i;
				break;
			}
			if(tt[0]>0)
			{
				// AddTDTMarker(tt,"mapd0.png",0);//全部标记小图
			}
		}

		//var tmp_total = tmp[0].split("|X|");
		var tmp_total = tmp[ii].split("|X|");
		var pagecurrent = tmp_total[1];//当前页
		page = pagecurrent; // 设置保存当前页
		var pagerecords = tmp_total[2];//总记录数
		var pagestotal = tmp_total[3];//总页数
		var ht = "<table>";

		var isLabel = 0;
		//重新标识大图
		current_map = new Array();
		//显示大图
		ii++;
		var j = 1;
		for(var i=ii; i<tmp.length; i++)
		{
			// ht += "<tr>";
			var tt = tmp[i].split("|X|");	
			// ht += "<td><div style='float:left; padding:2px 5px 0 5px;'>"+arr1[j]+"</div><div style='float:left; width:220px'><a href='"+tt[12]+"' target='_blank'>"+tt[4]+"</a></div></td>";
			// ht += "</tr>";


            ht += "<div class='enterprise-item'>";
            ht += "<span class='enterprise-letter'>"+arr1[j]+"</span>";
            ht += "<span class='enterprise-name'>";
			if(tt[12]!=""){
				ht += "<a href='"+tt[12]+"' target='_blank'>"+tt[4]+"</a>";
				//ht += '<div onclick=\'changeGc('+JSON.stringify(tt)+','+j+')\'>'+tt[4]+'</div>';
			}else{
				ht += '<div onclick=\'changeGc('+JSON.stringify(tt)+','+j+')\'>'+tt[4]+'</div>';
			}
			ht += "</span>";

			if(tt[12]!=""){
				ht += "<span class='enterprise-img'><a href='"+tt[12]+"' target='_blank'><img src='images/dznew.png'></a></span>";
			}
            ht += "</div>";

			// if(eval("typeof(markerGC"+tt[1]+")") != "undefined")
			// eval("markerGC"+tt[1]+".setMap()");
			AddTDTMarker(tt,"mapd"+arr2[j]+".png",isLabel);
			current_map[j-1] = tt;
			j++;
		}
		//ht +="<tr><td>";
        var ht2="";
		var pstart = page-3; 
		if(pstart<1)pstart=1;
		var pend = pstart+6;
		if(pend>pagestotal)pend=pagestotal;
		if(pstart>1)
		{
			//起始页不是1,显示页头链接
			var t = "<a href='#' onclick='goPage(1);return false;'><<</a> ";
			ht2 += t;
		}
		for(var k =pstart;k<=pend;k++)
		{//alert(k);
			if(k==page)
			{
				//当前页加粗
				var t = "[<a href='#' onclick='goPage("+k+");return false;'><font color=red><b>"+k+"</b></font></a>] ";
			}else
			{
				var t = "[<a href='#' onclick='goPage("+k+");return false;'>"+k+"</a>] ";
			}
			//alert(t);
			ht2 += t;
		}
		if(pend<pagestotal)
		{
			//结束页不是最后页,显示页尾链接
			var t = "<a href='#' onclick='goPage("+pagestotal+");return false;'>>></a> ";
			ht2 += t;
		}

        if(ht2!="" && pagestotal>1){
            ht += "<div class='enterprise-page'>";
            ht += ht2;
            ht += "</div>";
        }

		//ht +="</td></tr>";

		//ht += "</table>";

		document.getElementById("city2").innerHTML = ht;
		initing = 0;
}

function changeGc(tt,j){
	var lnglat=[];//	gcj02towgs84(parseFloat(tt[6]),parseFloat(tt[7]))
	lnglat[0]=parseFloat(tt[6]);
	lnglat[1]=parseFloat(tt[7]);
    var infoWin1 = new T.InfoWindow();
    var sContent =
        "<div style='margin:0px;'>" +
        "<div>" +
        "<div style='margin:0px;font-size:12px;'><b>"+tt[4]+"</b><br />"+tt[13];
		if(tt[12]!=""){
			sContent +="<a href=\""+tt[12]+"\" target=\"_blank\">更多"+tt[3]+"</a>";
		}else if(tt[13].indexOf("...")!==-1){
			sContent +="<a href=\""+tt[12]+"\" target=\"_blank\">更多"+tt[3]+"</a>";
		}
        sContent +="</div>" +
        "</div>" +
        "</div>";
    infoWin1.setContent(sContent);
	infoWin1.setOffset(new T.Point(15, 5));
	map.openInfoWindow(infoWin1, new T.LngLat(lnglat[0], lnglat[1]));
	
}

//添加事件监听
function endMyZoom(event){
	//显示坐标信息,当缩放后
	getLngLatBounds();
}
 
//2012年7月16日10:04:09 ncc

function addMarkergzj(){
	var gzjinfo=['1', '1', '', '钢之家', '上海钢之家信息科技有限公司', '上海市',default_x, default_y, '15', '上海市东方路818号众城大厦23楼', '', '', 'https://www.steelhome.com/jianjie.php?value=1', '上海市东方路818号众城大厦23楼'];
	AddTDTMarker(gzjinfo,"mapd0.png",1);

}

function AddTDTMarker(GcObj,S_MAP,IS_ShowLabel){
	// console.log(IS_ShowLabel);
	var icon = new T.Icon({
		iconUrl: "/_v2app/images/map/"+S_MAP,
		shadowUrl: '', 
	});

	var lnglat=[];//wgs84togcj02(parseFloat(GcObj[6]),parseFloat(GcObj[7]))
	lnglat[0]=GcObj[6];
	lnglat[1]=GcObj[7];

	// console.log(lnglat);
	var marker = new T.Marker(new T.LngLat(lnglat[0],lnglat[1]));  // 创建标注
	if(IS_ShowLabel==0)marker =new T.Marker(new T.LngLat(lnglat[0],lnglat[1]), {icon: icon});
	map.addOverLay(marker);               // 将标注添加到地图中

	var infoWin1 = new T.InfoWindow();
	var sContent =
		"<div style='margin:0px;'>" +
		"<div>" +
		"<div style='margin:0px;font-size:12px;'><b>"+GcObj[4]+"</b><br />"+GcObj[13];
		if(GcObj[12]!=""){
			sContent +="<a href=\""+GcObj[12]+"\" target=\"_blank\">更多"+GcObj[3]+"</a>";
		}else if(GcObj[13].indexOf("...")!==-1){
			sContent +="<a href=\""+GcObj[12]+"\" target=\"_blank\">更多"+GcObj[3]+"</a>";
		}
		sContent +="</div>" +
		"</div>" +
		"</div>";
	infoWin1.setContent(sContent);
	var pointx=15;
	var pointy=5;
	if(IS_ShowLabel==1){
		pointx=0;
		pointy=0;
		// marker.openInfoWindow(infoWin1,{offset:new T.Point(pointx,pointy)});
	}
	if(key!=""&&pnx!=''&&targetflag==1){
		if(pnx==GcObj[6]){
			marker.openInfoWindow(infoWin1,{offset:new T.Point(pointx,pointy)});
			targetflag=0;
		}
	}
	marker.addEventListener("click", function () {
		marker.openInfoWindow(infoWin1,{offset:new T.Point(pointx,pointy)});
	});// 将标注添加到地图中
			
}

</script>
</head>
<body  onLoad="onLoad();">
    <header>
        <!-- 首页上方banner -->
        <{include file="../components/home_banner.html"}>
        <!-- 首页上方登录条 -->
        <{include file="../components/header.html"}>
    </header>
    <div class="content position_two" style="overflow: hidden;">
        <div class="position_two_title"><span>当前位置</span></div>
        <a href="/" target="_blank">钢之家 &gt; &nbsp;</a>
        <a href="/map.php?type=<{$type}>" target="_blank" id="pageName"><{$pageName}></a>
    </div>
    <!-- 顶部导航栏 -->
    <!-- <header>
        <div class="content container">
            <div class="secondary-nav">
                <div class="nav-links">
                    <a href="#" class="active">钢厂分布图</a>
                    <a href="#">巨都保温管道钢材企业分布图</a>
                    <a href="#">上海林达压力容器有限公司</a>
                </div>
            </div>
        </div>
    </header> -->

    <!-- <div class="map-section" style='width:1000px;height:800px'>
                    <div class="map-container" id="map"  style='width:1000px;height:800px'>
                       
                    </div>
                </div> -->

    <!-- 主内容区 -->
	

    <main>
        <div class="content">
            <div class="content-wrapper">
                <!-- 地图区域 -->
                <div class="map-section" style="overflow: hidden;">
                    <div class="map-container" id="map" style="overflow: hidden;" >
                       
                    </div>
                </div>
                
                <!-- 右侧搜索筛选区 -->
                <div class="filter-section">
                    <div class="section-header">当前视野内的企业</div>
                    <div class="filter-content">
                        <!-- 搜索框 -->
                        <form action="map.php?view=fgmap" method="post" style="display:inline;">
                            <div class="search-box">
								<!-- <select class="search-select">
									<option>全部</option>
								</select> -->
                                <input type="text" name="keyword" value="<{$params.keyword}>" id="search" placeholder="请输入钢厂名称"  class="search-input">
                                <button class="search-btn">搜索</button>
                            </div>
                        </form>
                        <div class="liebiao">
                            <div class="enterprise-list" id="city2">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <section>
        <!-- 首页下方友情链接 -->
        <{include file="../components/friendly_links.html"}>
    </section>
    <{include file="../components/footer.html"}>

    <!-- 交互脚本 -->
    <script>
        // 地图标记点交互
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有地图标记点
            const markers = document.querySelectorAll('.map-marker');
            
            // 为每个标记点添加点击事件
            markers.forEach(marker => {
                marker.addEventListener('click', function() {
                    const companyCode = this.querySelector('.marker-dot').textContent;
                    alert(`您点击了标记 ${companyCode}，这里将显示该企业的详细信息`);
                });
                
                // 添加悬停效果
                marker.addEventListener('mouseenter', function() {
                    this.querySelector('.marker-dot').style.transform = 'scale(1.25)';
                    this.style.zIndex = '10';
                });
                
                marker.addEventListener('mouseleave', function() {
                    this.querySelector('.marker-dot').style.transform = 'scale(1)';
                    this.style.zIndex = '1';
                });
            });
            
            // 筛选条件点击事件
            const filterInputs = document.querySelectorAll('.filter-input');
            filterInputs.forEach(input => {
                input.addEventListener('click', function() {
                    alert(`这里将显示${this.placeholder}的下拉选择列表`);
                });
            });
        });
    </script>
</body>

<script>
var advParams = "";
</script>
<script src="<{$smarty.const.STATIC_URL}>/js/jquery-1.7.2.min.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<script src="<{$smarty.const.STATIC_URL}>/js/www/layui/layui.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<{include file="../components/common_js.html"}>
<script src="/js/search.js" type="text/javascript"></script>
<script language="javascript">
			$(document).ready(function() {
			$("#search").autocomplete(
			"/_v2app/mapditu.php?view=serfg",
			{
				delay:10,
            	minChars:1,
            	matchSubset:1,
            	matchContains:true,
            	cacheLength:10,
            	onItemSelect:selectItem,
            	onFindValue:findValue,
            	formatItem:formatItem,
            	autoFill:false
        	}
    		);
			});
			function findValue(li) {
    			if( li == null ) return alert("No match!");
				if( !!li.extra ) var sValue = li.extra[0];
				else var sValue = li.selectValue;
				
				}
			function selectItem(li) { findValue(li);}
			function formatItem(row) { return row[0];
			}
			function lookupAjax(){
			var oSuggest = $("#search")[0].autocompleter;
			oSuggest.findValue();
			return false;
			}
</script>
</html>
    