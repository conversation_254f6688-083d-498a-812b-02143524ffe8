<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php" );

class marketrecordcheckController extends AbstractController
{

    public function __construct()
    {
        parent:: __construct();
        //测试
        // $this->_action->setDao ( new marketrecordcheckDao ('91W') );
        //正式
         $this->_action->setDao ( new marketrecordcheckDao ("91W",  "91R"));
         $this->_action->oaindao = new marketrecordcheckDao( "OA" ,"OA" );
    }

    public function _dopre()
    {
        $this->_action->checkSession();
    }

   
    public function v_index()
    {
        $this->_action->index($this->_request);
    }
    //对比marketrecord导入check表，完善check
    public function do_import()
    {   
        $this->_action->import($this->_request);
    }

    public function do_mjhqtx()
    {   
        $this->_action->mjhqtx($this->_request);
    }

    public function do_hqtxByCityAndVariety()
    {   
        $this->_action->hqtx_by_city_and_variety($this->_request);
    }
    
    //提醒每日行情未录入check有marketrecord没有的
    public function do_tx_hq()
    {
        $this->_action->tx_hq($this->_request);
    }
    //提醒哪些没有管理员，或者管理员离职，check有mrhq_up_admin没有的，同时检查mrhq_up_admin的管理员有没有离职的
    public function do_tx_admin()
    {
        $this->_action->tx_admin($this->_request);
    }
    public function do_tx_admin2()
    {
        $this->_action->tx_admin2($this->_request);
    }
    //提醒哪些哪些工程报价没有发，check中isgcbj=1的，projiect没有的
    public function do_tx_project()
    {
        $this->_action->tx_project($this->_request);
    }

    public function v_hqtj()
    {
        $this->_action->hqtj($this->_request);
    }

    
    public function do_tx_mark()
    {
        $this->_action->tx_mark($this->_request);
    }


        
    //提醒重复
    public function do_cf_hq()
    {
        $this->_action->cf_hq($this->_request);
    }
    //煤焦汇总提醒
    public function do_tx_mjhuizong()
    {
        $this->_action->tx_mjhuizong($this->_request);
    }
    public function do_checkll()
    {
        $this->_action->checkll($this->_request);
    }

    public function do_checkfbcount()
    {
        $this->_action->checkfbcount($this->_request);
    }

    //行情到点未发布
    public function do_gettoday_hangqing_check(){
        $this->_action->gettoday_hangqing_check( $this->_request );
    } 
    //行情录入 未发布
    public function do_check_marketrecord_noup(){
        $this->_action->check_marketrecord_noup( $this->_request );
    } 

    // 检查行情是否合法
    public function do_check_illegal_data(){
        $this->_action->check_illegal_data( $this->_request );
    } 

    public function do_test(){
        $this->_action->get_wechat_username_by_marketrecordid($this->_request);
    }
}
?>