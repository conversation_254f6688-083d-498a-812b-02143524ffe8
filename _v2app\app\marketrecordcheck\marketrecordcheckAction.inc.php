<?php
//include_once( APP_DIR . "/master/masterAction.inc.php" );
//include_once( '/usr/local/www/libs/util/marketdata/MapDataFunc.php' );
//include_once( '/usr/local/www/libs/util/autorun/runmarketdata.php' );
include_once("/etc/steelconf/config/isholiday.php");

class marketrecordcheckAction extends AbstractAction
{
    public $oaindao;
    public function __construct()
    {
        parent::__construct();
    }


    //检查钢之家后台Session
    public function checkSession()
    {
        // if ($_SESSION['dusername'] == '' || $_SESSION['duserid'] == '') {
        //     goURL("//oa.steelhome.cn/admincp/login.php");
        // }
    }

    public function index($params)
    {

    }

    //选几个时间跑一下完善check表，链接给经理定时跑
    public function import($params)
    {

        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }
        if (empty($params['adminid'])) {
            $admin = '-1';
        } else {
            $admin = $params['adminid'];
        }

        $sql = "select title,isimport,channelid,varietyid,areaid,cityid  from marketrecord where managedate>='$timestart' and managedate<='$timeend'";
        $sql_project = "select id,varietyid,areaid,cityid,isimport   from marketrecord_project where  managedate>='$timestart' and managedate<='$timeend'";
        //echo $sql;
        $MarketRecord = $this->_dao->query($sql);
        $MarketRecord_project = $this->_dao->query($sql_project);
        $m1 = array();
        $m2 = array();
        //拼接数组
        foreach ($MarketRecord as $k => $v) {
            $cityidarr = explode(",", $v['cityid']);
            $m1[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['varietyid'] = $v['varietyid'];
            $m1[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['areaid'] = $cityidarr[0];
            $m1[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['cityid'] = $cityidarr[1];
            $m1[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['isimport'] = $v['isimport'];
            $m1[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['channelid'] = $v['channelid'];
            $m1[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['title'] = $v['title'];
        }
        foreach ($MarketRecord_project as $k => $v) {
            // if(isset( $m2[$v['varietyid'].','.$v['areaid'].','.$v['cityid'].','.$v['isimport']])){
            //     $m5[]=$v['varietyid'].','.$v['cityid'];
            // }
            $m2[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['varietyid'] = $v['varietyid'];
            $m2[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['areaid'] = $v['areaid'];
            $m2[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['cityid'] = $v['cityid'];
            $m2[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['isimport'] = $v['isimport'];
            $m2[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['channelid'] = $v['channelid'];
        }
        // echo  '<pre>';print_R($m5);exit;
        //循环两个数组，MarketRecord能在MarketRecord_project中同步到的添加isgcbj=1，匹配不到的isgcbj=0
        foreach ($m1 as $k1 => $v1) {
            foreach ($m2 as $k2 => $v2) {
                if (isset($m2[$k1])) {
                    $m1[$k1]['isgcbj'] = 1;
                } else {
                    $m1[$k1]['isgcbj'] = 0;
                }
            }
        }

        //查询check的表，去除重复的插入
        $m3 = array();
        $sql_check = "select id,varietyid,areaid,cityid,isimport   from marketrecord_check where isdel=0";
        $MarketRecord_check = $this->_dao->query($sql_check);
        foreach ($MarketRecord_check as $k => $v) {
            $m3[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['varietyid'] = $v['id'];
        }

        foreach ($m1 as $k1 => $v1) {
            foreach ($m3 as $k2 => $v2) {
                if (isset($m3[$k1])) {
                    unset($m1[$k1]);
                }
            }
        }
        //需要添加的增加标题名
        $basesql = 'insert into marketrecord_check(title,isimport,channelid,varietyid,areaid,cityid,isgcbj,createtime,createadminid,actime) values';

        $actime = date("Y-m-d H:i:s");
        require_once("/usr/local/www/oa.steelhome.cn/admin/message/market_title_array.php");
        //print_r("<pre>");
        $chunk_m1 = array_chunk($m1, 100);
        //print_r($chunk_m1);
        foreach ($chunk_m1 as $k => $m1) {
            //print_r($m1);
            $valsql = '';
            foreach ($m1 as $k => $v) {
                $title_key = $v['varietyid'] . '|' . $v['cityid'] . '|' . $v['isimport'];
                if (isset($title_arr[$title_key])) {
                    $v['title'] = $title_arr[$title_key];
                } else {
                    $index = strpos($v['title'], '日');
                    //echo $index;
                    $str = substr($v['title'], $index + 3);
                    $v['title'] = $str;
                }
                $valsql .= "('" . $v['title'] . "','" . $v['isimport'] . "','" . $v['channelid'] . "','" . $v['varietyid'] . "','" . $v['areaid'] . "','" . $v['cityid'] . "','" . $v['isgcbj'] . "','" . $actime . "','" . $admin . "','" . $actime . "'),";
            }
            if ($valsql != "") {
                $ressql = $basesql . substr($valsql, 0, -1);
                //echo  $ressql;
                $this->_dao->execute($ressql);
            }
        }

        //    echo  $valsql;


        $havegcbjsql = "SELECT count(*) FROM `marketrecord_check` WHERE (`title`  LIKE '%大中型材%' or `title`  LIKE '%建筑钢材%' or `title`  LIKE '%H型钢%')AND isgcbj =0";
        $havegcbjcount = $this->_dao->getone($havegcbjsql);
        //    echo $havegcbjcount;
        if ($havegcbjcount > 0) {
            $setgcbjsql = "update `marketrecord_check` set isgcbj=1 WHERE (`title`   LIKE '%大中型材%' or  `title` LIKE '%建筑钢材%' or `title` LIKE '%H型钢%') AND isgcbj =0";
            //    echo  $setgcbjsql;
            $this->_dao->execute($setgcbjsql);
        }
        echo '完成';
    }

    public function checkfbcount($params)
    {

        if (empty($params['date1'])) {
            $timestart1 = date("Y-m-d") . ' 00:00:00';
            $timeend1 = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart1 = $params['date1'] . ' 00:00:00';
            $timeend1 = $params['date1'] . ' 23:59:59';
        }

        if (empty($params['date2'])) {
            $timestart2 = date("Y-m-d") . ' 00:00:00';
            $timeend2 = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart2 = $params['date2'] . ' 00:00:00';
            $timeend2 = $params['date2'] . ' 23:59:59';
        }

        $sql1 = "select title,isimport,channelid,varietyid,areaid,cityid  from marketrecord where managedate>='$timestart1' and managedate<='$timeend1'";
        $sql2 = "select title,isimport,channelid,varietyid,areaid,cityid  from marketrecord where managedate>='$timestart2' and managedate<='$timeend2'";

        //echo $sql;
        $MarketRecord1 = $this->_dao->query($sql1);
        $MarketRecord2 = $this->_dao->query($sql2);

        $m1 = array();
        $m2 = array();
        // 处理第一个结果集
        foreach ($MarketRecord1 as $k => $v) {
            $cityidarr = explode(",", $v['cityid']);
            $key = $v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport'];
            $m1[$key] = [
                'varietyid' => $v['varietyid'],
                'areaid' => $cityidarr[0],
                'cityid' => $cityidarr[1],
                'isimport' => $v['isimport'],
                'channelid' => $v['channelid'],
                'title' => $v['title']
            ];
        }

        // 处理第二个结果集
        foreach ($MarketRecord2 as $k => $v) {
            $cityidarr = explode(",", $v['cityid']);
            $key = $v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport'];
            $m2[$key] = [
                'varietyid' => $v['varietyid'],
                'areaid' => $cityidarr[0],
                'cityid' => $cityidarr[1],
                'isimport' => $v['isimport'],
                'channelid' => $v['channelid'],
                'title' => $v['title']
            ];
        }

        // 比较差异
        $diff = [
            'added' => [],
            'deleted' => [],
            'changed' => []
        ];

        // 检测新增条目
        foreach ($m2 as $key => $item) {
            if (!isset($m1[$key])) {
                $diff['added'][] = $item;
            }
        }

        // 检测删除条目
        foreach ($m1 as $key => $item) {
            if (!isset($m2[$key])) {
                $diff['deleted'][] = $item;
            }
        }

        // // 检测变更条目
        // foreach($m1 as $key => $oldItem){
        //     if(isset($m2[$key])){
        //         $newItem = $m2[$key];
        //         if($oldItem['title'] != $newItem['title']){
        //             $diff['changed'][] = [
        //                 'old' => $oldItem,
        //                 'new' => $newItem
        //             ];
        //         }
        //     }
        // }
        $result = $diff;
        // 输出结果示例
        echo "新增条目：" . count($result['added']) . "个\n<br />";
        echo "未上传条目：" . count($result['deleted']) . "个\n<br />";
        // echo "变更条目：" . count($result['changed']) . "个\n<br />";

        // 详细查看变更内容
        echo "新增条目：\n<br />";
        foreach ($result['added'] as $change) {
            echo "标题：" . $change['title'] . "\n<br />";
        }
        echo "------------------------\n<br />";
        echo "未上传条目：\n<br />";
        // 详细查看变更内容
        foreach ($result['deleted'] as $change) {
            echo "标题：" . $change['title'] . "\n<br />";

        }

        // // 详细查看变更内容
        // foreach($result['changed'] as $change){
        //     echo "标题变更：\n";
        //     echo "原标题：" . $change['old']['title'] . "\n<br />";
        //     echo "新标题：" . $change['new']['title'] . "\n<br />";
        //     echo "------------------------\n";
        // }

    }


    //加入企业微信每日定时提醒
    public function tx_hq($params)
    {
        require_once("/usr/local/www/oa.steelhome.cn/admin/message/market_title_array.php");
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }

        if (empty($params['yesterdayhq'])) {
            $yesterdayhq = 0;
        } else {
            $yesterdayhq = $params['yesterdayhq'];
        }
        $sql = "select title,isimport,channelid,varietyid,areaid,cityid,title  from marketrecord where managedate>='$timestart' and managedate<='$timeend'";
        $sql_check = "select id,varietyid,areaid,cityid,title,isimport   from marketrecord_check where isdel=0 and yesterdayhq=$yesterdayhq";

        $MarketRecord = $this->_dao->query($sql);
        $MarketRecord_check = $this->_dao->query($sql_check);

        $record = [];
        $check = [];
        //拼接数组
        foreach ($MarketRecord as $k => $v) {
            $cityidarr = explode(",", $v['cityid']);
            $record[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['varietyid'] = $v['varietyid'];
            $record[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['areaid'] = $cityidarr[0];
            $record[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['cityid'] = $cityidarr[1];
            $record[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['isimport'] = $v['isimport'];
            $record[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['channelid'] = $v['channelid'];
            $record[$v['varietyid'] . ',' . $cityidarr[1] . ',' . $v['isimport']]['title'] = $v['title'];
        }


        foreach ($MarketRecord_check as $k => $v) {
            $check[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']] = $v;
        }

        $data = [];
        //循环两个数组，匹配不到的提醒未插入
        foreach ($check as $k1 => $v1) {

            foreach ($record as $k2 => $v2) {
                if (!isset($record[$k1])) {
                    $data[$k1] = str_replace("价格行情", "", $v1['title']);
                }
            }

        }
        $sql_admin = "select * from mrhq_up_admin";
        $mrhqadmin = $this->_dao->query($sql_admin);
        foreach ($mrhqadmin as $k => $v) {
            $admin[$v['varietyid'] . ',' . $v['t_city'] . ',' . $v['isimport']] = $v['manageid'];
            $adminids[$v['manageid']] = $v['manageid'];
        }

        $idsstr = implode("','", $adminids);
        $sql_adminuser = "select id,truename  from adminuser where  id in('" . $idsstr . "')";
        $adminuser = $this->_dao->query($sql_adminuser);
        foreach ($adminuser as $k => $v) {
            $adminid_name[$v['id']] = $v['truename'];
        }

        // mrhq_up_admin中缺失，会导致未录入的行情也没有管理员echo  '<pre>';print_R($data);
        //拼接回馈字符串
        $contstr = '';
        if ($yesterdayhq == 1) {
            $daysty = '海外市场';
        } else {
            $daysty = '';
        }
        if (empty($data)) {
            // $contstr='今日'.$daysty.'行情录入完整';
            $contstr = '';
        } else {
            $contstr = '今日' . $daysty . '未录入行情：';
            foreach ($data as $k1 => $v1) {
                if (isset($adminids[$admin[$k1]])) {
                    $contstr .= $v1 . '-' . $adminid_name[$admin[$k1]] . ',';
                } else {
                    $contstr .= $v1 . ',';
                }

            }
            $contstr = substr($contstr, 0, -1);
        }
        if (empty($record))
            $contstr = '今日' . $daysty . '行情尚未录入';
        // $contstr.=$k1.':\n';


        if (empty($record) || empty($data))
            $contstr = '';
        if (count($data) > 80) {
            $contstr = "今日未录入行情过多,详情请看未录入行情列表：" . APP_URL_OA . "/admincpv2/marketrecordcheck.php?view=hqtj";
        }


        $return_arr = array(
            'data' => $this->strconvert($contstr),
        );
        // echo $contstr;
        echo json_encode($return_arr);
        exit;

    }


    public function tx_admin($params)
    {
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }

        if (empty($params['yesterdayhq'])) {
            $yesterdayhq = 0;
        } else {
            $yesterdayhq = $params['yesterdayhq'];
        }
        $sql = "select * from mrhq_up_admin";
        $sql_check = "select id,varietyid,areaid,cityid,title,isimport   from marketrecord_check where isdel=0 and yesterdayhq=$yesterdayhq";

        $mrhqadmin = $this->_dao->query($sql);
        $MarketRecord_check = $this->_dao->query($sql_check);
        $adminids = [];
        $admin = [];
        $check = [];

        foreach ($mrhqadmin as $k => $v) {
            $admin[$v['varietyid'] . ',' . $v['t_city'] . ',' . $v['isimport']]['manageid'] = $v['manageid'];
            $adminids[$v['manageid']] = $v['manageid'];
        }
        foreach ($MarketRecord_check as $k => $v) {
            $check[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']]['title'] = $v['title'];
        }
        $data = [];
        //循环两个数组，匹配不到的没有管理员
        foreach ($check as $k1 => $v1) {
            foreach ($admin as $k2 => $v2) {
                if (!isset($admin[$k1])) {
                    $data[$k1] = $v1['title'];
                }
            }
        }

        if ($yesterdayhq == 1) {
            $daysty = '海外市场';
        } else {
            $daysty = '';
        }
        //查询哪些管理员离职
        // $idsstr=implode("','",$adminids);
        // $sql_admin = "select id,truename  from adminuser where state=0 and id in('".$idsstr."')";
        // $leveadmin = $this->_dao->query($sql_admin);
        $contstr = '';
        if (empty($data)) {
            // $contstr='今日'.$daysty.'0条行情无管理员。';
            $contstr = '';
        } else {
            $contstr = '今日' . $daysty . count($data) . '条行情无绑定管理员：';
            foreach ($data as $k1 => $v1) {
                $contstr .= $v1 . ',';
            }
            $contstr = substr($contstr, 0, -1);
        }
        // //换行
        // $contstr.=$contstr.'\n';
        // if(empty($leveadmin)){
        //     $contstr.='暂无行情管理员离职。';
        // }else{
        //     $contstr.=count($leveadmin).'名现有行情管理员离职：';
        //     foreach ($leveadmin as $k1 => $v1) {
        //         $contstr.=$v1['truename'].',';      
        //     }
        //     $contstr=substr($contstr, 0, -1);
        // }
        if (empty($data))
            $contstr = '';
        $return_arr = array(
            'data' => $this->strconvert($contstr),
        );

        echo json_encode($return_arr);
        exit;
    }
    public function tx_admin2($params)
    {
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }


        $sql = "select * from mrhq_up_admin";
        // $sql_check = "select id,varietyid,areaid,cityid,title   from marketrecord_check where isdel=0";

        $mrhqadmin = $this->_dao->query($sql);
        // $MarketRecord_check = $this->_dao->query($sql_check);
        $adminids = [];
        $admin = [];
        $check = [];

        foreach ($mrhqadmin as $k => $v) {
            $admin[$v['varietyid'] . ',' . $v['t_city'] . ',' . $v['isimport']]['manageid'] = $v['manageid'];
            $adminids[$v['manageid']] = $v['manageid'];

        }
        // foreach($MarketRecord_check as $k=>$v){
        //     $check[$v['varietyid'].','.$v['areaid'].','.$v['cityid']]['title']=$v['title'];
        // }
        // $data='';
        //循环两个数组，匹配不到的没有管理员
        // foreach($check as $k1=>$v1){
        //      foreach($admin as $k2=>$v2){
        //          if(!isset($admin[$k1])){
        //              $data[$k1]=$v1['title'];
        //          }
        //      }
        // }


        //查询哪些管理员离职
        $idsstr = implode("','", $adminids);
        $sql_admin = "select id,truename  from adminuser where state=0 and id in('" . $idsstr . "')";
        $leveadmin = $this->_dao->query($sql_admin);
        $contstr = '';
        // if(empty($data)){
        //     $contstr='今日0条行情无管理员。';
        // }else{
        //     $contstr='今日'.count($data).'条行情无管理员：';
        //     foreach ($data as $k1 => $v1) {
        //         $contstr.=$v1.',';      
        //     }
        //     $contstr=substr($contstr, 0, -1);
        // }
        //换行
        // $contstr.=$contstr.'\n';
        if (empty($leveadmin)) {
            $contstr .= '暂无行情管理员离职。';
        } else {
            $contstr .= count($leveadmin) . '名现有行情管理员离职：';
            foreach ($leveadmin as $k1 => $v1) {
                $contstr .= $v1['truename'] . ',';
            }
            $contstr = substr($contstr, 0, -1);
        }
        if (empty($leveadmin))
            $contstr = '';
        $return_arr = array(
            'data' => $this->strconvert($contstr),
        );

        echo json_encode($return_arr);
        exit;
    }
    public function tx_project($params)
    {
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }

        //$sql_project = "select id,varietyid,areaid,cityid   from marketrecord_project where  managedate>='$timestart' and managedate<='$timeend'";
        $sql_check = "select  id,varietyid,areaid,cityid,title,isimport  from marketrecord_check where isdel=0 and isgcbj=1 ";
        // $MarketRecord_project = $this->_dao->query($sql_project);
        $MarketRecord_check = $this->_dao->query($sql_check);
        $gcbj_sql = "select varietyid,cityid,areaid,isimport from news_gcbj where ndate>='$timestart' and ndate<='$timeend'";
        $news_gcbj = $this->_dao->query($gcbj_sql);
        $project = [];
        $check = [];
        $gcbj = [];
        //拼接数组
        // foreach($MarketRecord_project as $k=>$v){
        //     $project[$v['varietyid'].','.$v['areaid'].','.$v['cityid']]=$v['varietyid'];
        // }
        foreach ($MarketRecord_check as $k => $v) {
            $varietyid = '';
            $varietyid = explode(',', $v['varietyid']);
            $MarketRecord_check[$k]['varietyid'] = $varietyid[2];
        }
        foreach ($MarketRecord_check as $k => $v) {
            $check[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']] = $v;
        }
        foreach ($news_gcbj as $k => $v) {
            $gcbj[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']] = $v;
        }
        // echo '<pre>';print_R($gcbj);exit;

        //     echo count($data);

        //对应管理员的逻辑
        $sql_admin = "select * from mrhq_up_admin";
        $mrhqadmin = $this->_dao->query($sql_admin);
        foreach ($mrhqadmin as $k => $v) {
            $varietyid = [];
            $varietyid = explode(',', $v['varietyid']);
            $admin[$varietyid[2] . ',' . $v['t_city'] . ',' . $v['isimport']] = $v['manageid'];
            $adminids[$v['manageid']] = $v['manageid'];
        }
        $adminid_name = [];
        $idsstr = implode("','", $adminids);
        $sql_adminuser = "select id,truename  from adminuser where  id in('" . $idsstr . "')";
        $adminuser = $this->_dao->query($sql_adminuser);
        foreach ($adminuser as $k => $v) {
            $adminid_name[$v['id']] = $v['truename'];
        }
        //------

        $user_data = array();//没人未发布的工程报价
        //循环两个数组，匹配不到的没有工程报价
        foreach ($check as $k1 => $v1) {
            foreach ($gcbj as $k2 => $v2) {//匹配news_gcbj为准
                if (!isset($gcbj[$k1])) {
                    $data[$k1] = str_replace("价格行情", "", $v1['title']);
                    $user_data[$admin[$k1]][$k1] = str_replace("价格行情", "", $v1['title']);
                }
            }
        }
        //企业微信账号
        $qyuserid_all = $this->oaindao->query("select userid,qyuserid from qywxbindadminuser  where qyuserid!='' order by id desc ");
        $qyuserid_arr = array();
        foreach ($qyuserid_all as $key => $value) {
            if (!isset($qyuserid_arr[$value['userid']])) {
                $qyuserid_arr[$value['userid']] = $value['qyuserid'];
            }
        }

        //拼接回馈字符串
        $contstr = '';
        $remind_str = '';
        if (empty($data)) {
            // $contstr='今日工程报价录入完整';
            $contstr = '';
        } else {
            $contstr = '今日' . count($data) . '条工程报价未录入：';
            foreach ($data as $k1 => $v1) {
                // $contstr.=$v1.'-'.$adminid_name[$admin[$k1]].',';  
                if (isset($adminid_name[$admin[$k1]])) {
                    $contstr .= $v1 . '-' . $adminid_name[$admin[$k1]] . ',';
                } else {
                    $contstr .= $v1 . ',';
                }
            }
            $contstr = substr($contstr, 0, -1);

            //提醒@到个人
            $remind_str = "# **提醒：今日" . count($data) . "条工程报价未录入**\n\n";
            foreach ($user_data as $k1 => $v1) {
                $qyuser = $qyuserid_arr[$k1];
                $remind_str .= "<@" . $qyuser . ">";
                $kk = 1;
                foreach ($v1 as $k2 => $v2) {
                    if ($kk == count($v1)) {
                        $remind_str .= $v2;
                    } else {
                        $remind_str .= $v2 . "、";
                    }
                    $kk++;
                }
                $remind_str .= "\n";
            }
        }
        if (empty($gcbj)) {
            $contstr = '今日工程报价尚未录入';
            if (empty($gcbj))
                $contstr = '';
            $return_arr = array(
                'data' => $this->strconvert($contstr),
            );
        } else {

            $str_constr = explode(',', $contstr);


            if (count($str_constr) < 100) {

                for ($i = 0; $i < 100; $i++) {
                    $str_arr1[$i] = $str_constr[$i];
                }
                $contstr1 = implode(',', array_filter($str_arr1));
            }


            if (100 <= count($str_constr) && count($str_constr) < 200) {

                for ($i = 0; $i < 100; $i++) {
                    $str_arr1[$i] = $str_constr[$i];
                }
                for ($i = 100; $i < 200; $i++) {
                    $str_arr2[$i] = $str_constr[$i];
                }
                $contstr1 = implode(',', array_filter($str_arr1));
                $contstr2 = implode(',', array_filter($str_arr2));
            }

            if (200 <= count($str_constr) && count($str_constr) < 300) {
                for ($i = 0; $i < 100; $i++) {
                    $str_arr1[$i] = $str_constr[$i];
                }
                for ($i = 100; $i < 200; $i++) {
                    $str_arr2[$i] = $str_constr[$i];
                }
                for ($i = 200; $i < 300; $i++) {
                    $str_arr3[$i] = $str_constr[$i];
                }
                $contstr1 = implode(',', array_filter($str_arr1));
                $contstr2 = implode(',', array_filter($str_arr2));
                $contstr3 = implode(',', array_filter($str_arr3));
            }

            if (300 <= count($str_constr) && count($str_constr) < 400) {
                for ($i = 0; $i < 100; $i++) {
                    $str_arr1[$i] = $str_constr[$i];
                }
                for ($i = 100; $i < 200; $i++) {
                    $str_arr2[$i] = $str_constr[$i];
                }
                for ($i = 200; $i < 300; $i++) {
                    $str_arr3[$i] = $str_constr[$i];
                }
                for ($i = 300; $i < 400; $i++) {
                    $str_arr4[$i] = $str_constr[$i];
                }
                $contstr1 = implode(',', array_filter($str_arr1));
                $contstr2 = implode(',', array_filter($str_arr2));
                $contstr3 = implode(',', array_filter($str_arr3));
                $contstr4 = implode(',', array_filter($str_arr4));
            }

            if (400 <= count($str_constr) && count($str_constr) < 500) {
                for ($i = 0; $i < 100; $i++) {
                    $str_arr1[$i] = $str_constr[$i];
                }
                for ($i = 100; $i < 200; $i++) {
                    $str_arr2[$i] = $str_constr[$i];
                }
                for ($i = 200; $i < 300; $i++) {
                    $str_arr3[$i] = $str_constr[$i];
                }
                for ($i = 300; $i < 400; $i++) {
                    $str_arr4[$i] = $str_constr[$i];
                }
                for ($i = 400; $i < 500; $i++) {
                    $str_arr5[$i] = $str_constr[$i];
                }
                $contstr1 = implode(',', array_filter($str_arr1));
                $contstr2 = implode(',', array_filter($str_arr2));
                $contstr3 = implode(',', array_filter($str_arr3));
                $contstr4 = implode(',', array_filter($str_arr4));
                $contstr5 = implode(',', array_filter($str_arr5));
            }
            $return_arr = array(
                'data' => $this->strconvert($contstr1),
                'remind_str' => $this->strconvert($remind_str),
            );
        }
        echo json_encode($return_arr);
        exit;


    }


    public function tx_mark($params)
    {
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }

        $sql_project = "select id,varietyid,isimport,cityid,title   from marketrecord_project where  managedate>='$timestart' and managedate<='$timeend' and marketremark = ''";
        $MarketRecord_project = $this->_dao->query($sql_project);
        $data = [];
        //拼接数组
        foreach ($MarketRecord_project as $k => $v) {
            $data[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']] = $v['title'];
        }

        $admin = [];
        $adminids = [];
        $adminid_name = [];
        //对应管理员的逻辑
        $sql_admin = "select * from mrhq_up_admin";
        $mrhqadmin = $this->_dao->query($sql_admin);
        foreach ($mrhqadmin as $k => $v) {
            $admin[$v['varietyid'] . ',' . $v['t_city'] . ',' . $v['isimport']] = $v['manageid'];
            $adminids[$v['manageid']] = $v['manageid'];
        }

        $idsstr = implode("','", $adminids);
        $sql_adminuser = "select id,truename  from adminuser where  id in('" . $idsstr . "')";
        $adminuser = $this->_dao->query($sql_adminuser);
        foreach ($adminuser as $k => $v) {
            $adminid_name[$v['id']] = $v['truename'];
        }


        //------

        //拼接回馈字符串
        $contstr = '';
        if (empty($data)) {
            // $contstr='今日工程报价录入完整';
            $contstr = '';
        } else {
            $contstr = '今日' . count($data) . '条工程报价未备注：';
            foreach ($data as $k1 => $v1) {
                // $contstr.=$v1.'-'.$adminid_name[$admin[$k1]].',';  
                if (isset($adminid_name[$admin[$k1]])) {
                    $contstr .= $v1 . '-' . $adminid_name[$admin[$k1]] . ',';
                } else {
                    $contstr .= $v1 . ',';
                }
            }
            $contstr = substr($contstr, 0, -1);
        }

        $return_arr = array(
            'data' => $this->strconvert($contstr),
        );
        foreach ($adminuser as $k => $v) {
            $adminid_name[$v['id']] = $v['truename'];
        }


        echo json_encode($return_arr);
        exit;


    }
    function strconvert($data, $code = 'UTF-8')
    {
        if (!empty($data)) {
            $fileType = mb_detect_encoding($data, array('UTF-8', 'GBK', 'LATIN1', 'BIG5'));
            if ($fileType != $code) {
                $data = mb_convert_encoding($data, $code, $fileType);
            }
        }
        return $data;
    }
    function strconvert2($data, $code = 'GBK')
    {
        if (!empty($data)) {
            $fileType = mb_detect_encoding($data, array('UTF-8', 'GBK', 'LATIN1', 'BIG5'));
            if ($fileType != $code) {
                $data = mb_convert_encoding($data, $code, $fileType);
            }
        }
        return $data;
    }



    public function cf_hq($params)
    {
        require_once("/usr/local/www/oa.steelhome.cn/admin/message/market_title_array.php");
        //  $params['date']='2022-05-25';
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }


        $topicturesql = "SELECT marketrecordid, topicture, COUNT( topicture )
                        FROM `marketconditions`
                        WHERE mconmanagedate>='$timestart' and mconmanagedate<='$timeend' AND isview =0
                        AND topicture!= ''
                        GROUP BY topicture
                        HAVING COUNT( topicture ) >1
                        ORDER BY `marketconditions`.`marketrecordid` ASC";
        $mastertopidsql = "SELECT marketrecordid, mastertopid, COUNT( mastertopid )
                        FROM `marketconditions`
                        WHERE mconmanagedate>='$timestart' and mconmanagedate<='$timeend' AND isview =0
                        AND mastertopid!= ''
                        GROUP BY mastertopid 
                        HAVING COUNT( mastertopid ) >1
                        ORDER BY `marketconditions`.`marketrecordid` ASC";
        $cfsql = "SELECT marketrecordid, mastertopid, topicture
                        FROM `marketconditions`
                        WHERE mconmanagedate>='$timestart' and mconmanagedate<='$timeend' AND isview =0
                        AND mastertopid!= ''
                        AND mastertopid = topicture
                        ";

        $topictureRecord = $this->_dao->query($topicturesql);
        $mastertopidRecord = $this->_dao->query($mastertopidsql);
        $cfRecord = $this->_dao->query($cfsql);

        $topicture = '';//topicture
        $mastertopid = '';//mastertopid

        $trecord = '';//topicture的marketrecordid
        $mrecord = '';//mastertopid的marketrecordid
        $crecord = '';//重复
        //查询重复的topicture

        foreach ($topictureRecord as $k => $v) {
            if ($topicture == '') {
                $topicture = "'" . $v['topicture'] . "'";
            } else {
                $topicture = $topicture . ",'" . $v['topicture'] . "'";
            }

        }
        //根据重复的topicture，查询record
        if (!empty($topicture)) {
            $tsql = "SELECT distinct marketrecordid, topicture
            FROM `marketconditions`
            WHERE mconmanagedate>='$timestart' and mconmanagedate<='$timeend' AND isview =0
            AND topicture in(" . $topicture . ")";
            $trecord_Arr = $this->_dao->query($tsql);
        } else {
            $trecord_Arr = array();
        }

        $topicture_Arr = array();
        foreach ($trecord_Arr as $k => $v) {
            if ($trecord == '') {
                $trecord = "'" . $v['marketrecordid'] . "'";
            } else {
                $trecord = $trecord . ",'" . $v['marketrecordid'] . "'";
            }
            $topicture_Arr[$v['topicture']][] = $v['marketrecordid'];
        }

        //查询重复的mastertopid
        foreach ($mastertopidRecord as $k => $v) {
            if ($mastertopid == '') {
                $mastertopid = "'" . $v['mastertopid'] . "'";
            } else {
                $mastertopid = $mastertopid . ",'" . $v['mastertopid'] . "'";
            }

        }
        //根据重复的mastertopid，查询record
        if (!empty($mastertopid)) {
            $msql = "SELECT distinct marketrecordid, mastertopid
            FROM `marketconditions`
            WHERE mconmanagedate>='$timestart' and mconmanagedate<='$timeend' AND isview =0
            AND mastertopid in(" . $mastertopid . ")";
            $mrecord_Arr = $this->_dao->query($msql);
        } else {
            $mrecord_Arr = array();
        }

        $mastertopid_Arr = array();
        foreach ($mrecord_Arr as $k => $v) {
            if ($mrecord == '') {
                $mrecord = "'" . $v['marketrecordid'] . "'";
            } else {
                $mrecord = $mrecord . ",'" . $v['marketrecordid'] . "'";
            }

            $mastertopid_Arr[$v['mastertopid']][] = $v['marketrecordid'];
        }

        $cf_Arr = array();
        foreach ($cfRecord as $k => $v) {
            if ($crecord == '') {
                $crecord = "'" . $v['marketrecordid'] . "'";
            } else {
                $crecord = $crecord . ",'" . $v['marketrecordid'] . "'";
            }

            $cf_Arr[$v['mastertopid']][] = $v['marketrecordid'];
        }
        if (!empty($trecord)) {
            $sql = "SELECT id,manageid,title FROM `marketrecord` WHERE id in(" . $trecord . ")";
            $record_topicture = $this->_dao->query($sql);
        } else {
            $record_topicture = array();
        }
        if (!empty($mrecord)) {
            $sql = "SELECT id,manageid,title FROM `marketrecord` WHERE id in(" . $mrecord . ")";
            $record_mastertopid = $this->_dao->query($sql);
        } else {
            $record_mastertopid = array();
        }
        if (!empty($crecord)) {
            $sql = "SELECT id,manageid,title FROM `marketrecord` WHERE id in(" . $crecord . ")";
            $record_cf = $this->_dao->query($sql);

        } else {
            $record_cf = array();
        }

        $allrecord = array_merge($record_topicture, $record_mastertopid, $record_cf);

        $admin_record = array();
        foreach ($allrecord as $key => $value) {
            $admin_record[$value['id']]['manageid'] = $value['manageid'];
            $admin_record[$value['id']]['title'] = $value['title'];
        }


        $sql_admin = "select * from mrhq_up_admin";
        $mrhqadmin = $this->_dao->query($sql_admin);
        foreach ($mrhqadmin as $k => $v) {
            $adminids[$v['manageid']] = $v['manageid'];
        }

        $idsstr = implode("','", $adminids);
        $sql_adminuser = "select id,truename  from adminuser where  id in('" . $idsstr . "')";
        $adminuser = $this->_dao->query($sql_adminuser);
        foreach ($adminuser as $k => $v) {
            $adminid_name[$v['id']] = $v['truename'];
        }

        // mrhq_up_admin中缺失，会导致未录入的行情也没有管理员echo  '<pre>';print_R($data);
        //拼接回馈字符串
        //用topicture_Arr1键值拼接下面值对应的管理员，相同管理员的只要一个
        if (empty($record_topicture)) {
            $contstr = '';
        } else {

            $contstr = '6位价格id重复：';
            foreach ($topicture_Arr as $k1 => $v1) {
                $contstr .= $k1;
                $str_adminid_cf = array();
                foreach ($v1 as $k2 => $v2) {
                    if (!isset($str_adminid_cf[$admin_record[$v2]['manageid']])) {
                        $str_adminid_cf[$admin_record[$v2]['manageid']] = '';
                        $contstr .= '-' . $adminid_name[$admin_record[$v2]['manageid']];
                    }

                }
                $contstr .= ',';
            }
            $contstr = substr($contstr, 0, -1);
        }
        if (empty($mastertopid_Arr)) {
            $contstr1 = '';
        } else {

            $contstr1 = '7位价格id重复：';
            foreach ($mastertopid_Arr as $k1 => $v1) {
                $contstr1 .= $k1;
                $str_adminid_cf = array();
                foreach ($v1 as $k2 => $v2) {
                    if (!isset($str_adminid_cf[$admin_record[$v2]['manageid']])) {
                        $str_adminid_cf[$admin_record[$v2]['manageid']] = '';
                        $contstr1 .= '-' . $adminid_name[$admin_record[$v2]['manageid']];
                    }

                }
                $contstr1 .= ',';
            }
            $contstr1 = substr($contstr1, 0, -1);
        }
        if (empty($cf_Arr)) {
            $contstr2 = '';
        } else {

            $contstr2 = '6位与7位价格id相同：';
            foreach ($cf_Arr as $k1 => $v1) {
                $contstr2 .= $k1;
                $str_adminid_cf = array();
                foreach ($v1 as $k2 => $v2) {
                    if (!isset($str_adminid_cf[$admin_record[$v2]['manageid']])) {
                        $str_adminid_cf[$admin_record[$v2]['manageid']] = '';
                        $contstr2 .= '-' . $adminid_name[$admin_record[$v2]['manageid']];
                    }

                }
                $contstr2 .= ',';
            }
            $contstr2 = substr($contstr2, 0, -1);
        }



        $sql_6 = "SELECT topicture,marketrecordid
        FROM marketconditions
        WHERE  topicture != ''
        AND isview =0
        AND mconmanagedate >= '$timestart' and mconmanagedate<='$timeend' 
        AND LENGTH( topicture ) !=6
        ";
        $sql_7 = "SELECT mastertopid,marketrecordid
        FROM marketconditions
        WHERE  mastertopid != ''
        AND isview =0
        AND mconmanagedate >= '$timestart' and mconmanagedate<='$timeend' 
        AND LENGTH( mastertopid ) !=7
        ";
        $dy6 = $this->_dao->query($sql_6);
        $dy7 = $this->_dao->query($sql_7);
        $dy6str = '';
        $dy7str = '';
        foreach ($dy6 as $k => $v) {
            if ($dy6str == '') {
                $dy6str = "'" . $v['marketrecordid'] . "'";
            } else {
                $dy6str = $dy6str . ",'" . $v['marketrecordid'] . "'";
            }

            $dy6_Arr[$v['topicture']][] = $v['marketrecordid'];
        }
        if (!empty($dy6str)) {
            $sql = "SELECT id,manageid,title FROM `marketrecord` WHERE id in(" . $dy6str . ")";
            $record_dy6 = $this->_dao->query($sql);
        } else {
            $record_dy6 = array();
        }
        $dy7str = '';
        foreach ($dy7 as $k => $v) {
            if ($dy7str == '') {
                $dy7str = "'" . $v['marketrecordid'] . "'";
            } else {
                $dy7str = $dy7str . ",'" . $v['marketrecordid'] . "'";
            }

            $dy7_Arr[$v['mastertopid']][] = $v['marketrecordid'];
        }

        if (!empty($dy7str)) {
            $sql = "SELECT id,manageid,title FROM `marketrecord` WHERE id in(" . $dy7str . ")";
            $record_dy7 = $this->_dao->query($sql);
        } else {
            $record_dy7 = array();
        }

        $allrecord2 = array_merge($record_dy6, $record_dy7);
        $admin_record2 = array();
        foreach ($allrecord2 as $key => $value) {
            $admin_record2[$value['id']]['manageid'] = $value['manageid'];
            $admin_record2[$value['id']]['title'] = $value['title'];
        }
        if (!empty($dy6_Arr)) {
            foreach ($dy6_Arr as $k1 => $v1) {
                foreach ($v1 as $k2 => $v2) {
                    $data3[$k1][] = $adminid_name[$admin_record2[$v2]['manageid']];
                }
            }
        }
        if (!empty($dy7_Arr)) {
            foreach ($dy7_Arr as $k1 => $v1) {
                foreach ($v1 as $k2 => $v2) {
                    $data4[$k1][] = $adminid_name[$admin_record2[$v2]['manageid']];
                }
            }
        }
        if (empty($data3)) {
            $contstr3 = '';
        } else {

            $contstr3 = '6位价格id不规范：';
            foreach ($data3 as $k1 => $v1) {
                $contstr3 .= $k1 . ":";

                foreach ($v1 as $k2 => $v2) {
                    $contstr3 .= $v2;
                }
                $contstr3 .= ',';
            }
            $contstr3 = substr($contstr3, 0, -1);
        }
        if (empty($data4)) {
            $contstr4 = '';
        } else {

            $contstr4 = '7位价格id不规范：';
            foreach ($data4 as $k1 => $v1) {
                $contstr4 .= $k1;

                foreach ($v1 as $k2 => $v2) {
                    $contstr4 .= "-" . $v2;
                }
                $contstr4 .= ',';
            }
            $contstr4 = substr($contstr4, 0, -1);
        }

        /*
        if(empty($record_topicture)){

            $contstr='';
        }else{
            $contstr='6位价格id重复：';
            foreach ($record_topicture as $k1 => $v1) {
                if(isset($adminids[$v1['manageid']])){
                    $contstr.=$v1['title'].'-'.$adminid_name[$v1['manageid']].',';   
                }else{
                    $contstr.=$v1['title'].',';    
                }

            }
            $contstr=substr($contstr, 0, -1);
        }
        if(empty($record_mastertopid)){

            $contstr1='';
        }else{
            $contstr1='7位价格id重复：';
            foreach ($record_mastertopid as $k1 => $v1) {
                if(isset($adminids[$v1['manageid']])){
                    $contstr1.=$v1['title'].'-'.$adminid_name[$v1['manageid']].',';   
                }else{
                    $contstr1.=$v1['title'].',';    
                }

            }
            $contstr1=substr($contstr1, 0, -1);
        }

        if(empty($record_cf)){

            $contstr2='';
        }else{
            $contstr2='以下行情topicture与mastertopid相同：';
            foreach ($record_topicture as $k1 => $v1) {
                if(isset($adminids[$admin[$v1['manageid']]])){
                    $contstr2.=$v1['title'].'-'.$adminid_name[$v1['manageid']].',';   
                }else{
                    $contstr2.=$v1['title'].',';    
                }

            }
            $contstr2=substr($contstr2, 0, -1);
        }
       */




        $contstr3 = "详情请看重复行情列表：https://oa.steelhome.com/admincpv2/marketrecordcheck.php?view=cf_hq_table";




        $return_arr = array(
            'data' => $this->strconvert($contstr),
            'data1' => $this->strconvert($contstr1),
            'data2' => $this->strconvert($contstr2),
            'data3' => $this->strconvert($contstr3),
            'data4' => $this->strconvert($contstr4),
            'data5' => $this->strconvert($contstr5),
        );

        // echo $contstr;exit;
        echo json_encode($return_arr);
        exit;

    }
    public function tx_mjhuizong($params)
    {

        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }
        $sql = "SELECT ntitle,ischannel FROM news_mrhq WHERE columnid = '056' AND `ischannel` IN ( 11, 17 ) AND ndate>='$timestart' and ndate<='$timeend'";
        $news_mj = $this->_dao->query($sql);
        $title = array(
            '市场焦炭' => 0,
            '市场炼焦煤' => 0,
            '市场煤焦油' => 0,
            '市场粗苯' => 0,
            '市场硫酸铵' => 0,
            '市场喷吹煤' => 0,
            '市场纯苯' => 0,
            '市场（进口）煤炭' => 0,
            '市场焦煤' => 0,
            '市场肥煤' => 0,
            '市场1/3焦煤' => 0,
            '市场瘦、贫瘦煤' => 0,
            '市场气煤' => 0
        );
        foreach ($news_mj as $k => $v) {
            foreach ($title as $k1 => $v1) {
                if (strpos($v['ntitle'], $k1)) {
                    $title[$k1] = 1;
                }
            }

        }
        $wlrarr = array();
        foreach ($title as $k1 => $v1) {
            if ($v1 == 0) {
                $wlrarr[] = str_replace('市场', '', $k1);
            }

        }
        $contstr = '';
        if ($wlrarr) {
            $contstr = implode('、', $wlrarr) . '汇总未录入';
        }
        $return_arr = array(
            'data' => $this->strconvert($contstr),
        );
        echo json_encode($return_arr);
        exit;

    }

    //  https://iwww.steelhome.cn/_v2app/marketrecordcheck.php?action=mjhqtx&date=2023-06-16&type=ll
    public function mjhqtx($params)
    {
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }
        $type = $params['type'];
        if ($type == "") {
            $type = "mj";
        }
        // 炉料
        // 1        内蒙古市场硅铁价格行情        9:30前   狄艳冰
        // 2        宁夏市场硅铁价格行情        9:30前   狄艳冰
        // 3        内蒙古市场硅锰价格行情        9:30前   陈幸安生
        // 4        广西市场硅锰价格行情        9:30前   陈幸安生
        // 5        贵州市场硅锰价格行情        9:30前   陈幸安生
        // 6        华东市场工业硅价格行情        11:00前   张诗曼
        // 7        四川市场工业硅价格行情        11:00前   张诗曼
        // 8        国内市场高碳锰铁价格行情        9:30前   陈幸安生
        // 9        国内市场高碳铬铁价格行情        9:30前     狄艳冰
        // 10        国内市场钒铁价格行情        9:30前   张莹
        // 11        国内市场钼铁价格行情        9:30前   张莹
        // $txArray 中的时间为提前提醒的时间点
        $con = "";
        $txArray = array();
        if ($type == "hjfl") {
            $txArray = [
                "09:00" => [
                    "内蒙古市场硅铁价格行情" => "王文英",
                    "宁夏市场硅铁价格行情" => "王文英",
                    "内蒙古市场硅锰价格行情" => "周思雨",
                    "广西市场硅锰价格行情" => "谢天生",
                    "贵州市场硅锰价格行情" => "狄艳冰",
                    "国内市场高碳锰铁价格行情" => "狄艳冰",
                    "国内市场高碳铬铁价格行情" => "狄艳冰",
                    "国内市场钒铁价格行情" => "王圆圆",
                    "国内市场钼铁价格行情" => "王圆圆"
                ],
                "10:30" => [
                    "华东市场工业硅价格行情" => "",
                    "四川市场工业硅价格行情" => "",
                ],
            ];
        } else if ($type == 'mj') {
            require_once 'mj.php';
        } else if ($type == 'll') {
            require_once 'll.php';
        } else if ($type == 'mj2') {
            require_once 'mj2.php';
        } else {
            echo "参数错误";
            exit;
        }

        // 提前多久提醒
        $minutes = 30;
        if ($type == 'll') {
            // 炉料提前5分钟提醒
            $minutes = 5;
        } else if ($type == 'mj') {
            // 煤焦提前10分钟提醒
            $minutes = 10;
        }
        // dd($con);
        // 煤焦行情11.30检测所有的行情，然后提醒
        $mj_cur_time = date("H:i");
        $mj_is_tx = 0;
        if ($mj_cur_time == "11:30") {
            $mj_is_tx = 1;
            $con = preg_replace("/\d\d:\d\d/", "11:30", $con);
        }

        // 煤焦11.50的时候检测一下9点和9点半的行情，然后提醒
        if ($mj_cur_time == "10:50" && $type == 'mj') {
            $mj_is_tx = 1;
            $con = str_replace("09:30", "10:50", $con);
            $con = str_replace("09:00", "10:50", $con);
        }

        foreach (explode("\n", $con) as $oneOnfo) {
            if (empty($oneOnfo))
                continue;
            $tmp = explode("|", $oneOnfo);
            $time = date("H:i", strtotime($tmp[0] . "-{$minutes} minutes"));
            $name123 = str_replace("\r", "", $tmp[2]);
            $name123 = str_replace("\n", "", $name123);
            $name123 = str_replace("\r\n", "", $name123);
            $txArray[$time][$tmp[1]] = $name123;
        }
        ;

        // dd($txArray);
        $user = array();
        $cur_date = date("j日", strtotime($timestart));

        $cur_time1 = date("H:i", strtotime('-1 minute'));
        $cur_time2 = date("H:i", strtotime('+1 minute'));
        // dd($txArray);exit;
        foreach ($txArray as $t1 => $val) {
            // if(($cur_time1<=$t1 && $cur_time2>=$t1) || (date('H:i', strtotime("+{$minutes} minute ".$t1))>=$cur_time1 && date('H:i', strtotime("+{$minutes} minute ".$t1))<=$cur_time2)){
            foreach ($val as $name) {
                $user[] = $name;
            }
            $user = array_unique($user);
            $names = implode("','", $user);
            $sql = "SELECT truename,mobil FROM adminuser WHERE truename in ('{$names}') and mid=1 and `state`=1";
            $user = $this->_dao->aquery($sql);
            // 手机号码没查出来的直接指定
            if (isset($user['魏俊婷']))
                $user['魏俊婷'] = "18255557049";
            // }
        }
        $contstr = array();
        // print_r($txArray);exit;
        foreach ($txArray as $time => $val) {
            if ($cur_time1 <= $time && $cur_time2 >= $time) {  //提前半小时的提醒
                // 煤焦部第二次需求中的行情不需要提前提醒
                if ($type == "mj2" || ($mj_is_tx && $type == "mj"))
                    break;
                if ($type == 'll' || $type == 'mj') {
                    // 炉料、煤焦的行情，如果在提前提醒的时候发现已经录入过了就不提醒了
                    foreach ($val as $k1 => $v1) {
                        $sql = "SELECT count(*) num FROM marketrecord WHERE managedate>='$timestart' and managedate<='$timeend' and (title='{$cur_date}{$k1}' or title='{$cur_date}{$k1}(新)')";
                        $news_num = $this->_dao->getone($sql);
                        if ($news_num == 0) {
                            $contstr[$user[$v1] . "_tx"][] = $k1 . "({$v1})";
                        }
                    }
                } else {
                    foreach ($val as $k1 => $v1) {
                        if ($v1 == "") {
                            $contstr[$user[$v1] . "_tx"][] = $k1;
                        } else
                            $contstr[$user[$v1] . "_tx"][] = $k1 . "({$v1})";
                    }
                }
            } else if (date('H:i', strtotime("+{$minutes} minute " . $time)) >= $cur_time1 && date('H:i', strtotime("+{$minutes} minute " . $time)) <= $cur_time2) {  //时间到了检测的提醒
                foreach ($val as $k1 => $v1) {
                    $sql = "";
                    if ($type == "mj2") {
                        // mj2的行情，如果是动力煤、朝鲜煤的未录入则不提醒
                        if (!str_contains($k1, "动力煤") && !str_contains($k1, "朝鲜煤")) {
                            $sql = "SELECT count(*) num FROM news_mrhq WHERE ndate>='$timestart' and ndate<='$timeend' and (ntitle='{$cur_date}{$k1}' or ntitle='{$cur_date}{$k1}(新)')";
                        }
                    } else {
                        $sql = "SELECT count(*) num FROM marketrecord WHERE managedate>='$timestart' and managedate<='$timeend' and (title='{$cur_date}{$k1}' or title='{$cur_date}{$k1}(新)')";
                    }
                    if ($sql == "")
                        continue;
                    $news_mj_num = $this->_dao->getone($sql);
                    if ($news_mj_num == 0) {
                        if ($v1 == "") {
                            $contstr[$user[$v1] . "_no"][] = $k1;
                        } else
                            $contstr[$user[$v1] . "_no"][] = $k1 . "({$v1})";
                    }
                }
            }
        }
        echo json_encode($contstr, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function hqtx_by_city_and_variety($params)
    {
        if (empty($params['date'])) {
            $timestart = date("Y-m-d") . ' 00:00:00';
            $timeend = date("Y-m-d") . ' 23:59:59';
        } else {
            $timestart = $params['date'] . ' 00:00:00';
            $timeend = $params['date'] . ' 23:59:59';
        }
        $cur_date = date("j日", strtotime($timestart));
        $sql = "SELECT cityid,cityname FROM city where city_keys!=''";
        $citylist = $this->_dao->aquery($sql);
        $sql = "SELECT mvid,mvname from markvariety";
        $hqlist = $this->_dao->aquery($sql);
        $sql = "select CONCAT(t_city, '|', t_var) AS type, manageid from mrhq_up_admin where isimport=0";
        $userlist = $this->_dao->aquery($sql);
        $type = $params['type'];

        // 提前多久提醒
        $minutes = 5;

        require_once "{$type}.php";
        $txArray = array();
        foreach (explode("\n", $con) as $oneOnfo) {
            if (empty($oneOnfo))
                continue;
            $tmp = explode("|", $oneOnfo);
            $citycode = $varietycode = $sql = "";
            $citycode = array_search($tmp[1], $citylist);
            $varietycode = array_search($tmp[2], $hqlist);
            if (!$varietycode || !$citycode)
                continue;
            $time = date("H:i", strtotime($tmp[0] . "-{$minutes} minutes"));
            $userid = $userlist[$citycode . "|" . $varietycode];
            $txArray[$time][$tmp[1] . "市场" . $tmp[2] . "价格行情"] = $userid;
        }
        ;
        $cur_time1 = date("H:i", strtotime('-2 minute'));
        $cur_time2 = date("H:i", strtotime('+2 minute'));
        foreach ($txArray as $t1 => $val) {
            if (($cur_time1 <= $t1 && $cur_time2 >= $t1) || (date('H:i', strtotime("+{$minutes} minute " . $t1)) >= $cur_time1 && date('H:i', strtotime("+{$minutes} minute " . $t1)) <= $cur_time2)) {
                foreach ($val as $useridv) {
                    $user[] = $useridv;
                }
                $user = array_unique($user);
                $names = implode("','", $user);
                $sql = "SELECT id,mobil,truename FROM adminuser WHERE id in ('{$names}') and mid=1 and `state`=1";
                $usert = $this->_dao->query($sql);
                foreach ($usert as $v) {
                    $user1[$v['id']] = $v['mobil'];
                    $user2[$v['id']] = $v['truename'];
                }
                // 手机号码没查出来的直接指定
                // $user1[''] = "";
            }
        }
        $contstr = array();
        foreach ($txArray as $time => $val) {
            if ($cur_time1 <= $time && $cur_time2 >= $time) {
                foreach ($val as $k1 => $v1) {
                    // $sql="SELECT count(*) num FROM news_mrhq WHERE ndate>='$timestart' and ndate<='$timeend' and ntitle='{$cur_date}{$k1}'";
                    $sql = "SELECT count(*) num FROM marketrecord WHERE managedate>='$timestart' and managedate<='$timeend' and (title='{$cur_date}{$k1}' or title='{$cur_date}{$k1}(新)')";
                    $news_num = $this->_dao->getone($sql);
                    if ($news_num == 0) {
                        $contstr[$user1[$v1] . "_tx"][] = $k1 . "({$user2[$v1]})";
                    }
                }
            } else if (date('H:i', strtotime("+{$minutes} minute " . $time)) >= $cur_time1 && date('H:i', strtotime("+{$minutes} minute " . $time)) <= $cur_time2) {
                foreach ($val as $k1 => $v1) {
                    // $sql="SELECT count(*) num FROM news_mrhq WHERE ndate>='$timestart' and ndate<='$timeend' and ntitle='{$cur_date}{$k1}'";
                    $sql = "SELECT count(*) num FROM marketrecord WHERE managedate>='$timestart' and managedate<='$timeend' and (title='{$cur_date}{$k1}' or title='{$cur_date}{$k1}(新)')";
                    $news_mj_num = $this->_dao->getone($sql);
                    if ($news_mj_num == 0) {
                        $contstr[$user1[$v1] . "_no"][] = $k1 . "({$user2[$v1]})";
                    }
                }
            }
        }
        echo json_encode($contstr);
        exit;
    }
    public function checkll($params)
    {
        require_once 'll.php';
        foreach (explode("\n", $con) as $oneOnfo) {
            if (empty($oneOnfo))
                continue;
            $tmp = explode("|", $oneOnfo);
            $time = date("H:i", strtotime($tmp[0] . "-{$minutes} minutes"));
            $name123 = str_replace("\r", "", $tmp[2]);
            $name123 = str_replace("\n", "", $name123);
            $name123 = str_replace("\r\n", "", $name123);
            $txArray[$tmp[1]] = $name123;
        }
        ;
        $sql = "SELECT id,mobil,truename FROM adminuser WHERE  mid='1' and `state`='1'";
        $usert = $this->_dao->query($sql);
        foreach ($usert as $v) {
            $user2[$v['id']] = $v['truename'];
        }
        $sql = "SELECT manageid,title  FROM marketrecord WHERE managedate>='2024-03-07' and managedate<='2024-03-08' and channelid in ('04','08','15')";
        $marketrecord = $this->_dao->query($sql);
        foreach ($marketrecord as $v) {
            $marketrecordlog[$v['title']] = $user2[$v['manageid']];
        }
        //echo "111";
        foreach ($txArray as $k => $v) {

            if ($v != $marketrecordlog['7日' . $k]) {
                echo '7日' . $k . "-";
                echo $marketrecordlog['7日' . $k] . '<br/>';
            }
        }
        echo "over";

    }

    //到点未发布行情
    public function gettoday_hangqing_check($params)
    {
        //echo time();
        $arr_times = $this->_dao->gettimes_checkhq();

        $arr_times = array_flip($arr_times);
        sort($arr_times);
        $flag = false;
        $nowtime = date('H:i:00');
        $currentTime = time(); // 获取当前时间
        $threeMinutesLater = strtotime('+5 minutes', $currentTime); // 计算三分钟后
        $timeAfterThreeMinutes = date('H:i:00', $threeMinutesLater);
        $check_times = array();
        $check_times_later = array();
        if ($params['type'] == 'all') {
            $check_times = $arr_times;
        } else {
            if (in_array($timeAfterThreeMinutes, $arr_times)) {
                $check_times_later[] = $timeAfterThreeMinutes;
            }
            if (in_array($nowtime, $arr_times)) {
                $check_times[] = $nowtime;
            }
        }
        //$check_times=array_unique($check_times);
        //$check_times_later=array("08:40:00");
        //print_r($check_times);exit;

        //所有未发布资讯
        $arr_mrhq_up_admin = $this->_dao->gettodaymrhq_up_admin();
        $date = $params['date'] != "" ? $params['date'] : date("Y-m-d");
        $arr_record = $this->_dao->gettodayalltjs($date);
        $arr_list = array_udiff($arr_mrhq_up_admin, $arr_record, 'myfunction');

        $record_list = array();
        foreach ($arr_list as $k => $v) {
            $record_list[$v['cityid']][$v['varietyid']][$v['isimport']] = $v['manageid'];
        }

        $qyuserid_all = $this->oaindao->query("select userid,qyuserid from qywxbindadminuser  where qyuserid!='' order by id desc ");
        $qyuserid_arr = array();
        foreach ($qyuserid_all as $key => $value) {
            if (!isset($qyuserid_arr[$value['userid']])) {
                $qyuserid_arr[$value['userid']] = $value['qyuserid'];
            }
        }



        $sql_check = "select  id,varietyid,areaid,cityid,title,isimport,yesterdayhq  from marketrecord_check where isdel=0 ";
        $MarketRecord_check = $this->_dao->query($sql_check);
        $check_title = array();
        foreach ($MarketRecord_check as $k => $v) {
            $check_title[$v['varietyid'] . ',' . $v['cityid'] . ',' . $v['isimport']] = $v;
        }
        $no_hangqing = array();
        $no_hangqing_later = array();
        foreach ($check_times as $check_time_k => $check_time_v) {
            $hangqings_checktime = $this->_dao->gethangqings_checktime($check_time_v);
            foreach ($hangqings_checktime as $hqk => $hqv) {
                $flag = true;
                if ($hqv['mvid2'] == "mg,614,614,c614" && $hqv['mvname'] == "动力煤") {
                    $flag = false;
                }
                if ($hqv['mvid2'] == "mg,615,615,c615" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                if ($hqv['mvid2'] == "hg,617,617,c617" && $hqv['cityid'] == "hd,00C1") {
                    $flag = false;
                }
                if ($hqv['mvid2'] == "ys,062,221,c21" && $hqv['cityid'] == "hd,00C1") {
                    //LME基本金属收盘价格行情 不发布到前台
                    $flag = false;
                }
                if ($hqv['mvid2'] == "mg,913,946,c813" && in_array($hqv['cityid'], array("hd,00D2", "hb,00R2", "db,00B4", "hb,00X3", "hb,0099", "hd,00F2"))) {
                    $flag = false;
                }
                $cityidarr = explode(",", $hqv['cityid']);
                if (isset($check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']]) && $check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']]['yesterdayhq'] == 1) {
                    $flag = false;
                }
                //PA66
                if ($hqv['mvid2'] == "hg,806,874,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //PE
                if ($hqv['mvid2'] == "hg,806,875,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //聚醚多元醇价
                if ($hqv['mvid2'] == "hg,806,872,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //PA6
                if ($hqv['mvid2'] == "hg,806,873,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }

                //钢结构 不提醒
                if ($hqv['mvid2'] == "gc,248,248,c09") {
                    $flag = false;
                }

                //镁锭  北京 天津 济南 无锡 沈阳
                if ($hqv['mvid2'] == "ys,062,226,c21" && ($hqv['cityid'] == "hd,0012" || $hqv['cityid'] == "hd,0017" || $hqv['cityid'] == "hd,0045" || $hqv['cityid'] == "hd,0040" || $hqv['cityid'] == "hd,0039")) {
                    $flag = false;
                }

                //中国进口锰矿石外盘价格
                if ($hqv['mvid2'] == "tj,079,198,C702" && $hqv['cityid'] == "hd,00C1") {
                    $flag = false;
                }

                if (isset($record_list[$hqv['cityid']][$hqv['mvid2']][$hqv['isimport']]) && $flag) {

                    $title = $hqv['cityname'] . $hqv['mvname'];
                    if (isset($check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']])) {
                        $title = $check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']]['title'];
                    }
                    $hqv['title'] = $title;
                    $hqv['qywxid'] = $qyuserid_arr[$record_list[$hqv['cityid']][$hqv['mvid2']][$hqv['isimport']]];
                    if ($hqv['qywxid'] == "") {
                        $hqv['qywxid'] = $record_list[$hqv['cityid']][$hqv['mvid2']][$hqv['isimport']];
                    }
                    $no_hangqing[$check_time_v][$hqv['qywxid']][] = $hqv;
                }
            }
        }

        foreach ($check_times_later as $check_time_k2 => $check_time_v2) {
            $hangqings_checktime = $this->_dao->gethangqings_checktime($check_time_v2);
            foreach ($hangqings_checktime as $hqk => $hqv) {
                $flag = true;
                if ($hqv['mvid2'] == "mg,614,614,c614" && $hqv['mvname'] == "动力煤") {
                    $flag = false;
                }
                if ($hqv['mvid2'] == "mg,615,615,c615" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                if ($hqv['mvid2'] == "hg,617,617,c617" && $hqv['cityid'] == "hd,00C1") {
                    $flag = false;
                }
                if ($hqv['mvid2'] == "ys,062,221,c21" && $hqv['cityid'] == "hd,00C1") {
                    //LME基本金属收盘价格行情 不发布到前台
                    $flag = false;
                }
                if ($hqv['mvid2'] == "mg,913,946,c813" && in_array($hqv['cityid'], array("hd,00D2", "hb,00R2", "db,00B4", "hb,00X3", "hb,0099", "hd,00F2"))) {
                    $flag = false;
                }
                $cityidarr = explode(",", $hqv['cityid']);
                if (isset($check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']]) && $check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']]['yesterdayhq'] == 1) {
                    $flag = false;
                }
                //PA66
                if ($hqv['mvid2'] == "hg,806,874,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //PE
                if ($hqv['mvid2'] == "hg,806,875,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //聚醚多元醇价
                if ($hqv['mvid2'] == "hg,806,872,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //PA6
                if ($hqv['mvid2'] == "hg,806,873,c806" && $hqv['cityid'] == "hd,0007") {
                    $flag = false;
                }
                //钢结构 不提醒
                if ($hqv['mvid2'] == "gc,248,248,c09") {
                    $flag = false;
                }
                //镁锭  北京 天津 济南 无锡 沈阳
                if ($hqv['mvid2'] == "ys,062,226,c21" && ($hqv['cityid'] == "hd,0012" || $hqv['cityid'] == "hd,0017" || $hqv['cityid'] == "hd,0045" || $hqv['cityid'] == "hd,0040" || $hqv['cityid'] == "hd,0039")) {
                    $flag = false;
                }
                //中国进口锰矿石外盘价格
                if ($hqv['mvid2'] == "tj,079,198,C702" && $hqv['cityid'] == "hd,00C1") {
                    $flag = false;
                }

                if (isset($record_list[$hqv['cityid']][$hqv['mvid2']][$hqv['isimport']]) && $flag) {
                    //$cityidarr=explode(",",$hqv['cityid']);
                    $title = $hqv['cityname'] . $hqv['mvname'];
                    if (isset($check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']])) {
                        $title = $check_title[$hqv['mvid2'] . ',' . $cityidarr[1] . ',' . $hqv['isimport']]['title'];
                    }
                    $hqv['title'] = $title;
                    $hqv['qywxid'] = $qyuserid_arr[$record_list[$hqv['cityid']][$hqv['mvid2']][$hqv['isimport']]];
                    if ($hqv['qywxid'] == "") {
                        $hqv['qywxid'] = $record_list[$hqv['cityid']][$hqv['mvid2']][$hqv['isimport']];
                    }
                    $no_hangqing_later[$check_time_v2][$hqv['qywxid']][] = $hqv;
                }
            }
        }

        $content = "";
        foreach ($no_hangqing as $k => $v) {
            $kk = date('H:i', strtotime($k));
            if ($nowtime < $k) {
                $content .= "# **行情提醒：规定（" . $kk . "）上传行情**\n\n";
            } else {
                $content .= "# **行情提醒：规定（" . $kk . "）上传行情未发布**\n\n";
            }
            foreach ($v as $k2 => $v2) {
                $content .= "<@" . $k2 . ">";
                foreach ($v2 as $k3 => $v3) {
                    if ($k3 == count($v2) - 1) {
                        $content .= $v3['title'];
                    } else {
                        $content .= $v3['title'] . "、";
                    }
                }
                $content .= "\n";
            }
            $content .= "\n";
            $content .= "\n";
        }


        $content_later = array();
        foreach ($no_hangqing_later as $k => $v) {
            $kk = date('H:i', strtotime($k));
            $title = "";
            if ($nowtime < $k) {
                $title .= "行情提醒：规定（" . $kk . "）上传行情\n";
            } else {
                $title .= "行情提醒：规定（" . $kk . "）上传行情未发布\n";
            }
            foreach ($v as $k2 => $v2) {
                $content2 = "";
                foreach ($v2 as $k3 => $v3) {
                    if ($k3 == count($v2) - 1) {
                        $content2 .= $v3['title'];
                    } else {
                        $content2 .= $v3['title'] . "、";
                    }
                }
                if ($content2 != "" && $k2 != "") {
                    $content2 = $title . $content2;
                    $content_later[] = array(
                        'touser' => $k2,
                        'content' => $content2,
                    );
                    $this->oaindao->insertSendLog($k2, $title, $content2);
                }
            }
        }

        $return_arr = array(
            'data1' => $this->strconvert($content),
            'data2' => $content_later,
        );
        echo json_encode($return_arr);
        exit;
    }

    public function check_marketrecord_title($nocheck, $title)
    {
        $flag = true;
        foreach ($nocheck as $k => $v) {
            if (strpos($title, $v) !== false) {
                $flag = false;
            }
        }
        return $flag;
    }


    //录入未发布行情提醒    后台行情修改列表 发布状态是否的   11.50 15.50 17.50  检查 
    public function check_marketrecord_noup($params)
    {
        $date = $params['date'] != "" ? $params['date'] : date("Y-m-d");
        //不提醒行情
        $nocheck = array(
            "动力煤价格行情",
            "国际铁矿石运输行情",
            "中国沿海煤炭运价指数",
            "中国沿海金属矿石运价指数",
            "朝鲜煤价格行情",
            "普氏煤炭价格行情CFR",
            "MB铁矿石价格指数",
            "普氏铁矿石价格指数",
            "新加坡交易所铁矿石掉期合约结算价",
            "亚洲粗苯外盘价格行情",
            "国内PA6价格行情",
            "国内PA66价格行情",
            "国内PE价格行情",
            "国内聚醚多元醇价格行情"
        );
        //获取所有录入未发布行情
        $all_noup_marks = $this->_dao->get_noup_marketrecord($date);
        $noupmark_arr = array();
        foreach ($all_noup_marks as $k => $v) {
            $ischeck = $this->check_marketrecord_title($nocheck, $v['title']);
            if ($ischeck) {
                $noupmark_arr[$v['manageid']][] = $v;
            }
        }
        // echo '<pre>';
        // print_r( $noupmark_arr);exit;
        //企业微信账号
        $qyuserid_all = $this->oaindao->query("select userid,qyuserid from qywxbindadminuser  where qyuserid!='' order by id desc ");
        $qyuserid_arr = array();
        foreach ($qyuserid_all as $key => $value) {
            if (!isset($qyuserid_arr[$value['userid']])) {
                $qyuserid_arr[$value['userid']] = $value['qyuserid'];
            }
        }
        //企业微信提醒
        //$content="行情提醒：行情录入未发布\n";
        $content = "";
        foreach ($noupmark_arr as $k => $v) {
            $qyuser = $qyuserid_arr[$k];
            $content .= "<@" . $qyuser . ">";
            foreach ($v as $k2 => $v2) {
                if ($k2 == count($v) - 1) {
                    $content .= $v2['title'];
                } else {
                    $content .= $v2['title'] . "、";
                }
            }
            $content .= "\n";
        }
        if ($content != "") {
            $this->oaindao->insertSendLog("all", "行情提醒：行情已录入未发布", $content);
        }
        $return_arr = array(
            'data1' => $this->strconvert($content),
        );
        echo json_encode($return_arr);
        exit;
    }
    function send_wechat_notice($json_data)
    {
        $notice_request_url = WEIXINBOT_WEBHOOK_AMO_TEST;
        $ch = curl_init($notice_request_url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $json_data,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($json_data)
            ],
            CURLOPT_TIMEOUT => 5
        ]);
        curl_exec($ch);
        curl_close($ch);
    }
    function get_wechat_username_by_marketrecordid($marketrecordid)
    {
        $get_market_record_sql = "SELECT `manageid` FROM `marketrecord` WHERE `id` = ? LIMIT 1";
        $manageid = $this->_dao->getOne($get_market_record_sql, [$marketrecordid]);
        if ($manageid) {
            $get_wechat_name_sql = "SELECT `qyuserid` FROM `steelhome_oa`.`qywxbindadminuser` WHERE `userid` = ? LIMIT 1";
            $wechat_name = $this->oaindao->getOne($get_wechat_name_sql, [$manageid]);
            if ($wechat_name) {
                return $wechat_name;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }
    public function check_illegal_data($params)
    {
        // error_reporting(E_ALL);
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        $marketrecordid = $params['marketrecordid'];
        $get_market_record_sql = "SELECT `price_code` FROM `marketconditions_price_code` WHERE `marketrecord_id` = ? AND `isview` = 0";
        $market_records = $this->_dao->getOnes($get_market_record_sql, [$marketrecordid]);
        $get_all_illegal_data = "SELECT `manufactor_code`,`variety_code`,`material_code`,`specification_code` FROM `market_price_check_Illegal_data` WHERE `status` = 1";
        $all_illegal_data = $this->_dao->query($get_all_illegal_data);
        $result = [];
        foreach ($market_records as $mr) {
            $illegel = false;
            foreach ($all_illegal_data as $illegel_data) {
                if (
                    (!isset($illegel_data['variety_code']) || (substr($mr, 5, 3) == $illegel_data['variety_code'])) &&
                    (!isset($illegel_data['material_code']) || (substr($mr, 8, 3) == $illegel_data['material_code'])) &&
                    (!isset($illegel_data['specification_code']) || (substr($mr, 11, 3) == $illegel_data['specification_code'])) &&
                    (!isset($illegel_data['manufactor_code']) || (substr($mr, 14, 4) == $illegel_data['manufactor_code']))
                ) {
                    $illegel = true;
                    break;
                }
            }
            if ($illegel) {
                $result[] = $mr;
            }
        }
        if (count($result) > 0) {
            $userid = $this->get_wechat_username_by_marketrecordid($marketrecordid);
            if ($userid) {
                $notice_mentioned_list = [];
                $notice_mentioned_list[] = $userid;
                $notice_string = "您好！系统检测到发布的行情中存在不合规价格，请细心审核！\n不合规行情 ID：" . $marketrecordid . "\n不合规价格 20 位 ID：" . join('、', $result);


                
                $notice_mentioned_list[] = "limohan";
                $notice_string .= "\n测试，通知人列表：" . join('、', $notice_mentioned_list);

                $this->send_wechat_notice(json_encode([
                    "msgtype" => "text",
                    "text" => [
                        "content" => $notice_string,
                        "mentioned_list" => $notice_mentioned_list,
                    ],
                ]));
            } else {
                $notice_string = "您好！系统检测到发布的行情中存在不合规价格，请细心审核！\n不合规行情 ID：" . $marketrecordid . "\n不合规价格 20 位 ID：" . join('、', $result);
                $this->send_wechat_notice(json_encode([
                    "msgtype" => "text",
                    "text" => [
                        "content" => $notice_string,
                        "mentioned_list" => ["limohan"],
                    ],
                ]));
            }
        }
    }

}
function myfunction($a, $b)
{
    $a1 = array('isimport' => $a['isimport'], 'varietyid' => $a['varietyid'], 'cityid' => $a['cityid']);
    $b1 = array('isimport' => $b['isimport'], 'varietyid' => $b['varietyid'], 'cityid' => $b['cityid']);
    if ($a1 == $b1) {
        return 0;
    }
    return ($a1 > $b1) ? 1 : -1;
}

?>