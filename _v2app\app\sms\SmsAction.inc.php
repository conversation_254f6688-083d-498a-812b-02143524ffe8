<?php
// ini_set('display_errors',1);
// error_reporting(1);
define( "JG_MOMEY", 0.1 );
define( "SMS_TYPE_PRCIE_SELF", 100);
$GLOBALS['sms_pzs'] = array( 
array( '22', '螺纹钢','1', '', '' ), 
array( '23', '线材','5', '', '' ), 
array( '2', '中厚板','10', '', '' ), 
array( '25', '热轧板卷','15', '', '' ), 
array( '24', '冷轧板卷','20', '', '' ), 
array( '39', '热轧带钢','25', '', '' ), 
array( '40', '冷轧带钢','30', '', '' ), 
array( '44', '中宽带','35', '', '' ), 
array( '31', '热镀锌板','40', '', '' ), 
array( '32', '热轧镀锌板','45', '', '' ), 
array( '33', '电镀锌板','50', '', '' ), 
array( '34', '镀锡板','55', '', '' ), 
array( '35', '热镀铝锌','60', '', '' ), 
array( '36', '彩涂板','65', '', '' ), 
array( '37', '取向硅钢','70', '', '' ), 
array( '38', '无取向硅钢','75', '', '' ), 
array( '26', '角钢','80', '', '' ), 
array( '27', '槽钢','85', '', '' ), 
array( '28', '工字钢','90', '', '' ), 
array( '5', 'H型钢','95', '', '' ), 
array( '45', '球扁钢','100', '', '' ), 
array( '29', '钢轨','105', '', '' ), 
array( '30', '其他型钢','110', '', '' ), 
array( '43', '普碳圆钢','115', '', '' ), 
array( '9', '不锈钢','120', '', '' ), 
array( '42', '焊管','125', '', '' ), 
array( '41', '无缝钢管','130', '', '' ), 
array( '11', '优特钢','135', '', '' ), 
array( '13', '优焊线','140', '', '' ), 
array( '14', '结构钢','145', '', '' ), 
array( '15', '工具钢','150', '', '' ), 
array( '16', '模具钢','155', '', '' ), 
array( '17', '弹簧钢','160', '', '' ), 
array( '18', '轴承钢','165', '', '' ), 
array( '19', '其它特钢','170', '', '' ), 
array( '20', '管坯','175', '', '' ), 
array( '21', '钢坯','180', '', '' ), 
); 
$GLOBALS['STEEL_AREAS'] = array(
    "0001" => "华东地区",
	"0002" => "中南地区",
	"0003" => "华北地区",
	"0004" => "东北地区",
	"0005" => "西南地区",
	"0006" => "西北地区",
);
    $GLOBALS['ACCOUNT_LOG_TYPES'] = array(
           "1"=>"短信充值",
            "2"=>"价格扣费",
           "3"=>"调价扣费",
       ); 
	   //invoiceflag
    $GLOBALS['ORDER_INVOICE_FLAG'] = array(
           "1"=>"不开发票",
            "2"=>"开发票",
       ); 
	    $GLOBALS['USERVALID'] = array(
           "0"=>"试用用户",
            "1"=>"缴费用户",
			"2"=>"无效用户",
       ); 
	    $GLOBALS['SMS_TYPE'] = array(
            "1"=>"推荐短信",
            "2"=>"价格行情",
			"3"=>"调价信息",
			"4"=>"社会库存",
			"5"=>"行情评述",
       ); 
	    $GLOBALS['SMS_TYPE_PRICE'] = array(
            "价格行情"=>"100",
			"市场行情"=>"100",
			"品种行情"=>"100",
			"调价信息"=>"100",
			"市场库存"=>"100",
			"品种库存"=>"100",
			"行情评述"=>"100",
			"行业要闻"=>"100",
			"财经要闻"=>"100",
       ); 
$GLOBALS['ORDER_STATUS'] = array(
    "0"=>"未支付",
    "1"=>"已支付",
	"2"=>"支付失败",
);

/*$GLOBALS['AREA_STEEL']['0001']="'宝钢','宝特','马钢','沙钢','山东钢铁','新钢','永钢','萍钢','南钢','济钢','莱钢','日照','中天','青钢','三钢','宁钢','长达','申特','春冶','长达','济钢闽源','兆顺','兆泰','福达','飞达','石横特钢','莱钢永锋','鑫海','三宝','三益','众达','方大特钢','西城','兴澄','淮钢','杭钢','潍钢','新余特钢','锡钢','宁波宝新','长强','淄博乾能','张浦','鲁丽','广富','巨能','上海克虏伯','宝钢不锈钢'";

	$GLOBALS['AREA_STEEL']['0003']="'河北钢铁','首钢','太钢','首钢长治','天钢','包钢','天铁','国丰','津西','唐山中厚板','沧州中铁','临钢','2672厂','龙海','普阳','邢钢','承德建龙','带钢会议','邢台德龙','唐山德龙','唐山建龙','瑞丰','','荣程','前进','河北新钢','','文丰','河北纵横','','唐山恒通','天津友发','兆博','江天','唐山粤丰','石钢'";

	$GLOBALS['AREA_STEEL']['0004']="'鞍钢','本钢','通钢','凌钢','北台','新抚钢','西钢','东北特钢','吉林建龙','鞍山宝得'";

	$GLOBALS['AREA_STEEL']['0002']="'武钢','鄂钢','涟钢','湘钢','安钢','济源','信钢','柳钢','韶钢','广钢','裕丰','粤钢','万钢','广州联众','新冶钢'";

	$GLOBALS['AREA_STEEL']['0005']="'攀钢','重钢','昆钢','水钢','成钢','达钢','德胜','威钢','成实','新联山','玉溪玉昆','长城特钢','西昌新钢业','鑫丰隆','诚实大港','重庆四钢','贵钢',' 西南不锈'";

	$GLOBALS['AREA_STEEL']['0006']="'酒钢','八钢','龙钢','汉中钢铁','西部钢厂','金特钢铁','西宁特钢'";*/

$GLOBALS['TYPE__VARIETY']['GC']="'c01','c02','c03','c04','c05','c06','c07','c08','c09','c11','c12','c23'";
$GLOBALS['TYPE__VARIETY']['LL']="'c13','c14','c141','c15','c16','c17','c18','c19','c30','c16x'";
$GLOBALS['TYPE__VARIETY']['MJ']="'c614','c615','c617','c618','c620'";
$GLOBALS['TYPE__VARIETY']['YS']='c21';

$GLOBALS['ARRAY_VARIETY']["all"]="全部";
$GLOBALS['ARRAY_VARIETY']["c01"]="建筑钢材";
$GLOBALS['ARRAY_VARIETY']["c02"]="中厚板";
$GLOBALS['ARRAY_VARIETY']["c03"]="冷热板卷";
$GLOBALS['ARRAY_VARIETY']["c04"]="各类型材";
$GLOBALS['ARRAY_VARIETY']["c05"]="涂镀板卷";
$GLOBALS['ARRAY_VARIETY']["c06"]="带钢";
$GLOBALS['ARRAY_VARIETY']["c066"]="钢管";
$GLOBALS['ARRAY_VARIETY']["c07"]="不锈钢";
$GLOBALS['ARRAY_VARIETY']["c08"]="优焊线";
$GLOBALS['ARRAY_VARIETY']["c09"]="结构钢";
$GLOBALS['ARRAY_VARIETY']["c10"]="工模钢";
$GLOBALS['ARRAY_VARIETY']["c11"]="弹簧轴承";
$GLOBALS['ARRAY_VARIETY']["c12"]="其它特钢";
$GLOBALS['ARRAY_VARIETY']["c23"]="电工钢";
$GLOBALS['ARRAY_VARIETY']["c13"]= "矿石";
$GLOBALS['ARRAY_VARIETY']["c14"]= "生铁";
$GLOBALS['ARRAY_VARIETY']["c141"]= "钢坯";
$GLOBALS['ARRAY_VARIETY']["c15"]= "废钢";
$GLOBALS['ARRAY_VARIETY']["c16"]= "铁合金";
$GLOBALS['ARRAY_VARIETY']["c17"]= "煤炭";
$GLOBALS['ARRAY_VARIETY']["c18"]= "耐火材料";
$GLOBALS['ARRAY_VARIETY']["c19"]= "碳素材料";
$GLOBALS['ARRAY_VARIETY']["c30"]= "焦副产品";
$GLOBALS['ARRAY_VARIETY']["c31"]= "冷敦拉丝";
$GLOBALS['ARRAY_VARIETY']["c32"]= "无缝管";
$GLOBALS['ARRAY_VARIETY']["c21"]= "有色金属";
$GLOBALS['ARRAY_VARIETY']["c24"]= "焦炭";
$GLOBALS['ARRAY_VARIETY']["c20"]= "冶金辅料";
$GLOBALS['ARRAY_VARIETY']['c601'] = '甲醇';
$GLOBALS['ARRAY_VARIETY']['c602'] = '工业萘';
$GLOBALS['ARRAY_VARIETY']['c603'] = '二甲苯';
$GLOBALS['ARRAY_VARIETY']['c604'] = '纯苯';
$GLOBALS['ARRAY_VARIETY']['c605'] = '氯化苯';
$GLOBALS['ARRAY_VARIETY']['c606'] = '焦化苯';
$GLOBALS['ARRAY_VARIETY']['c607'] = '顺酐';
$GLOBALS['ARRAY_VARIETY']['c608'] = '加氢苯';
$GLOBALS['ARRAY_VARIETY']['c609'] = '煤沥青';
$GLOBALS['ARRAY_VARIETY']['c610'] = '蒽油';
$GLOBALS['ARRAY_VARIETY']['c611'] = '洗油';
$GLOBALS['ARRAY_VARIETY']['c612'] = '轻油';
$GLOBALS['ARRAY_VARIETY']['c613'] = '粗酚';
$GLOBALS['ARRAY_VARIETY']['c614'] = '动力煤';
$GLOBALS['ARRAY_VARIETY']['c615'] = '炼焦煤';
$GLOBALS['ARRAY_VARIETY']['c616'] = '喷吹煤';
$GLOBALS['ARRAY_VARIETY']['c617'] = '粗苯';
$GLOBALS['ARRAY_VARIETY']['c618'] = '煤焦油';
$GLOBALS['ARRAY_VARIETY']['c619'] = '硫酸铵';
$GLOBALS['ARRAY_VARIETY']['c620'] = '焦炭';
$GLOBALS['ARRAY_VARIETY']['c16x'] = '铁合金';

$GLOBALS['BLACK_NO'] = array("13853815611");


include_once ("global_variety.php");
include_once (BASE_DIR ."/app/sms/HttpClient.class.php");
include_once( "../include/checkpassword.php");
include_once( "../include/check_num_login.php");

class SmsAction extends AbstractAction {
	public $rcdao;
	public $maindao;
	//public $blackdao;
	public function __construct() {
		parent :: __construct();
		$this->sms_type_price=array('价格行情'=>'100元/条/年','调价信息'=>'100元/条/年','行情评述'=>'100元/条/年');
		$this->sms_type_price_real=array('价格行情'=>array('包年'=>'0.1','按条'=>'0.15'),'调价信息'=>array('包年'=>'0.3','按条'=>'0.5'));
		//$this->sms_type_price_real=array('价格行情'=>array('0'=>'0.1','1'=>'0.15'),'调价信息'=>array('0'=>'0.3','1'=>'0.5'));
	}
	//检查钢之家Session
  public function checkSession(){
      if($_SESSION['cookie_user']['usermobile']== '' ){
	      echo "<script>window.top.location.href='sms.php?view=hotsms'</script>";
		  exit;
		  //goURL( "index.php?view=login" );
	  }
  }
	public function gctj($params){
	}
	public function mrhq($params){
		$hqvarlist=$this->maindao->get_hqvar();
		$city_area=$this->maindao->getcitycode_area();
        foreach($city_area as $k=>$v){
        $arrcity_area[substr($v['cityid'],2,4)]=$v['city_keys'];
	    }
		if($params['var']=='')
		{
		$params['var']=30;
		$hqvarlist=$this->maindao->get_hqvar_one($params);
		$params['varid']=3011;
		$hqcitylist=$this->maindao->get_citybyvarid($params);
		}
		else
		{
			$hqcitylist=$this->maindao->get_citybyvarid($params);
			$hqvarlist=$this->maindao->get_hqvar_one($params);
		}

		 foreach($hqcitylist as $k=>$v ){
		$arr_c_a[$GLOBALS['STEEL_AREAS'][$arrcity_area[$v['cityid']]]][]=$v;
	 }
	    $this->assign("hqcitylist",$arr_c_a);
		//$this->assign("hqcitylist",$hqcitylist);
		$this->assign("hqvarlist",$hqvarlist);
	}


	/*hlf change定时修改 */
	public function timing($params){
        $where = "";
		//file_put_contents("/tmp/timing.txt",date("Y-m-d h:i:s")."执行了该程序",FILE_APPEND);
		$date_time=date('Y-m-01');
		$old_time=date('Y-m-01',strtotime("-1 day",strtotime($date_time)));
		/*if($params['ms_id']!=""){

			$where = " and MS_ID='".$params['ms_id']."' ";
		}*/
		//echo "AAAA";
		$sql="SELECT concat(\"'\", MS_ID, \"'\") FROM MESSAGE_DETAIL, SMS_TYPE WHERE 1 $where AND SMS_TYPE.DESCRIBE = MESSAGE_DETAIL.MS_TYPE GROUP BY MS_ID ORDER BY SMS_TYPE.CODE ASC , MS_ID ASC ";
		$alllist1=$this->smssenddao->getOnes($sql);	
		
		$ms_ids = implode(",", $alllist1);
		
		$con = mysql_connect(HOST_NAME_98SMW.":".HOST_PORT_98SMW,USER_NAME_98SMW,USER_PASSWORD_98SMW);
		if (!$con)
		{
			die('Could not connect: ' . mysql_error());
		}

		mysql_select_db(DATABASE_NAME_98SMW, $con);          //选择数据库   
//		mysql_query("SET NAMES 'gbk'");
		
		//foreach($alllist1 as $key=>$tmp){	
			//$ms_id=$tmp['MS_ID'];
			$Sql_update="SELECT reserved3 as ms_id, msgcontent FROM  `sth_sm_mt_send_log` WHERE  `reserved3` in($ms_ids) AND  `requesttime` > str_to_date('$old_time', '%Y-%m-%d')   AND  `requesttime` <   str_to_date('$date_time', '%Y-%m-%d') GROUP BY  `reserved3`";
			//print_r($Sql_update);
		   $result = mysql_query($Sql_update,$con);
			
			
			$sql_dy="UPDATE `MESSAGE_DETAIL` SET canDingYue=0";
			//echo $sql_1."<br>";
			//	file_put_contents("/tmp/a3.txt", $sql_1."\n", FILE_APPEND);
			$this->smssenddao->Execute($sql_dy);
			while($row = mysql_fetch_array($result)) {
				$ms_id = $row["ms_id"];
				$msgcontent = trim($row["msgcontent"]);
				if($msgcontent != "")
				{
					$sql_1="UPDATE `MESSAGE_DETAIL` SET  MS_CONTENT='$msgcontent',canDingYue=1  WHERE MS_ID=$ms_id";
					//echo $sql_1."<br>";
				//	file_put_contents("/tmp/a3.txt", $sql_1."\n", FILE_APPEND);
					$this->smssenddao->Execute($sql_1);
				}else{
					$sql_1="UPDATE `MESSAGE_DETAIL` SET canDingYue=0  WHERE MS_ID=$ms_id";
					$this->smssenddao->Execute($sql_1);
				}
				//file_put_contents("/tmp/a3.txt", "ms_id=$ms_id  msgcontent= $msgcontent\n", FILE_APPEND);
			}
					
		mysql_close($con);
		exit;
	}
/*end */


    //推荐短信套餐
    public function hotsms($params)
    {
        //$paystatus=$this->_dao->query("select paystatus from sms_orderlist where orderid='30'");
        //print_r($_SESSION);
        //print_r($paystatus);

        //hlf2018/1/9 星期二
        $now_url = $_SERVER['PHP_SELF'] . '?' . $_SERVER['QUERY_STRING'];
        //print_r($now_url);
        $now_url = urlencode($now_url);
        $this->assign("now_url", $now_url);
        $gwc_url = urlencode("/_v2app/sms.php?view=mrhq_city_confirm");
        $this->assign("gwc_url", $gwc_url);
        $usermobile = $_SESSION['cookie_user']['usermobile'];
        $this->assign("usermobile", $usermobile);
        //print_r($now_url);exit;
        //hlf/end

        $where_tj = ' where MS_ISTJ=2  ';
        $where = ' where 1  ';
        $par_city = '';
        $par_variety = '';
        $curr_nav = '您已选择';//当前位置
        $citycode = $this->get_citycode();
        $sql_gc = "SELECT st_code,name  FROM research_center.sc_steelcom";
        $gclist = $this->rcdao->query($sql_gc);
        foreach ($gclist as $k => $v) {
            $gccodelist[$v['st_code']] = $v['name'];
        }
        //处理参数
        $url_city = '';
        $url_variety = '';
        if ($params['city'] != '') {
            $cityid = $params['city'];
            $par_city = "and MS_CITYORSTEEL_ID='" . $params['city'] . "'";
            $url_city = "&city=" . $params['city'];
            //$curr_city_nav="  <span style='color:#000000;'>城市|</span>  ".$citycode[substr($params['city'],2,2)];
            //hlf/2017/12/29
            $curr_city_nav = "  <span style='color:#000000;'>城市|</span>  " . $citycode[$params['city']];
            //end
        }
        if ($params['variety'] != '') {
            $varietyid = $params['variety'];
            $par_variety = "and MS_CLASS='" . $params['variety'] . "'";
            $url_variety = "&variety=" . $params['variety'];
            $curr_variety_nav = "  <span style='color:#000000;'>品种|</span>    " . $GLOBALS['ARRAY_VARIETY'][$params['variety']];
            //处理 钢材 炉料 有色 煤焦 总分类
            if ($params['variety'] == 'gc') {
                $par_variety = "and MS_CLASS in (" . $GLOBALS['TYPE__VARIETY']['GC'] . ")";
                $curr_variety_nav = "  <span style='color:#000000;'>品种|</span>   钢材";
            }
            if ($params['variety'] == 'll') {
                $par_variety = "and MS_CLASS in (" . $GLOBALS['TYPE__VARIETY']['LL'] . ")";
                $curr_variety_nav = "  <span style='color:#000000;'>品种|</span>   炉料";
            }
            if ($params['variety'] == 'ys') {
                $par_variety = "and MS_CLASS='c21' ";
                $curr_variety_nav = "  <span style='color:#000000;'>品种|</span>   有色";
            }
            if ($params['variety'] == 'mj') {
                $par_variety = "and MS_CLASS in (" . $GLOBALS['TYPE__VARIETY']['MJ'] . ")";
                $curr_variety_nav = "  <span style='color:#000000;'>品种|</span>   煤焦";
            }
            //处理有色 品种
            if ($params['variety'] == 'c21') {
                if ($params['mvid'] != '') {
                    $par_variety = "and (MS_CLASS='c21' and MS_MVID ='" . $params['mvid'] . "')";
                    $url_variety = "&variety=" . $params['variety'] . "&mvid=" . $params['mvid'];
                }
            }
            //结束
            //$variety_close=" <a href='/_v2app/sms.php?view=hotsms".$url_city."' style='color:#000000;'>取消</a>";
        }
        if ($params['gc_name'] != '') {
            $gcid = $params['gc_name'];
            $par_city = "and MS_CITYORSTEEL_ID='" . $params['gc_name'] . "'";
            $url_city = "&gc_name=" . $params['gc_name'];
            $curr_city_nav = " <span style='color:#000000;'>钢厂 |</span>     " . $gccodelist[$params['gc_name']];
            //$city_close=" <a href='/_v2app/sms.php?view=hotsms".$url_variety."' style='color:#000000;'>取消</a>";
        }
        //分页参数
        $per = 15;
        $page = $params['page'] == '' ? 1 : $params['page'];

        unset($params['page']);
        if ($params['smstype'] != '') {
            $curr_type_nav = " <span style='color:#000000;'>短信类型 |</span>     " . $params['smstype'];
            $type_close = " <a href='/_v2app/sms.php?view=hotsms" . $url_variety . $url_city . "' style='color:#000000;'>取消</a>";
            switch ($params['smstype']) {
                case '价格行情':
                    $where_temp = " and (MS_TYPE ='市场行情' or MS_TYPE ='品种行情')";
                    break;
                case '市场库存':
                    $where_temp = " and (MS_TYPE ='市场库存' or MS_TYPE ='品种库存')";
                    break;
                case '调价信息':
                    $where_temp .= " and MS_TYPE ='" . $params['smstype'] . "'";
                    break;
                case '综合要闻':
                    $where_temp = " and  (MS_TYPE ='财经要闻' or MS_TYPE ='行业要闻')";
                    $url_variety = '';
                    $url_city = '';
                    $curr_nav = '';
                    break;
                default:
                    $where_temp .= " and MS_TYPE ='" . $params['smstype'] . "'";
                    break;
            }
        }
        //组合取消的条件
        if ($params['city'] != '') {
            $curr_nav .= $curr_city_nav . " <a href='/_v2app/sms.php?view=hotsms" . $url_variety . "' style='color:#000000;'><img src='images/xx.png' border=0 style='padding-top:1px;'></a>";
        }
        if ($params['variety'] != '') {
            $curr_nav .= $curr_variety_nav . " <a href='/_v2app/sms.php?view=hotsms" . $url_city . "' style='color:#000000;'><img src='images/xx.png' border=0 style='padding-top:1px;'></a>";
        }
        if ($params['gc_name'] != '') {
            $curr_nav .= $curr_city_nav . " <a href='/_v2app/sms.php?view=hotsms" . $url_variety . "' style='color:#000000;'><img src='images/xx.png' border=0 style='padding-top:1px;'></a>";
        }
        $curr_nav .= $curr_type_nav . $type_close;
        //处理搜索
        if (isset($params) && $params['search'] != '') {
            if (strpos($params['search'], '，') !== false) {
                $params['search'] = str_replace('，', ',', $params['search']);
            }
            $arr_search = explode(",", $params['search']);
            $curr_nav = '搜索关键字|' . $params['search'];
            if ($arr_search[0] == '多个关键字请用逗号分隔') {
                echo "<script>alert('请输入有效的关键字')</script>";
                goURL('sms.php?view=hotsms');

            } else {
                $where .= " and ( MS_FNAME like BINARY( '%" . $arr_search[0] . "%' ) ";
                if (count($arr_search) > 1) {
                    foreach ($arr_search as $k => $val) {
                        if ($k > 0) {
                            $where .= " or MS_FNAME like BINARY( '%" . $arr_search[$k] . "%')";
                        }
                    }

                }
                $where .= ")";
                /*if($arr_search[1]!=''){
                    $where=" where   ( MS_FNAME like BINARY '%".$arr_search[0]."%' or MS_FNAME like BINARY '%".$arr_search[1]."%' ) ";
                }else
                {
                    $where=" where  ( MS_FNAME like BINARY '%".$arr_search[0]."%' ) ";
                }*/
            }

        }
        //组合条件
        $where = $where . $par_city . $par_variety;
        //拼凑类型
        $where .= $where_temp;
        $where_2 .= $where_temp;
        //根据不同条件平凑推荐的条件变量
        if (($cityid != '' && $varietyid != '') || ($gcid != '')) {
            $where_2 = $where_tj . $par_variety;
            if ($gcid != '') {
                $where_2 .= "  and MS_TYPE ='调价信息' ";
            }
            $per = 15;
        }
        //查找列表

        $where_2 .= "  AND SMS_TYPE.DESCRIBE=MESSAGE_DETAIL.MS_TYPE and MESSAGE_DETAIL.MS_CONTENT !='' and canDingYue=1 group by MS_ID order by SMS_TYPE.CODE ASC,MS_ID ASC";
        $where .= "  AND SMS_TYPE.DESCRIBE=MESSAGE_DETAIL.MS_TYPE and MESSAGE_DETAIL.MS_CONTENT !='' and canDingYue=1 group by MS_ID order by SMS_TYPE.CODE ASC,MS_ID ASC";
        //echo $where_2."<br>";
        //echo $where."<br>";
        $alllist = $this->smssenddao->get_message_list($where);
        if (($cityid != '' && $varietyid != '') || ($gcid != '')) {
            $alllist_tj_temp = $this->smssenddao->get_message_list($where_2);
        }
        //推荐相关
        $alllist_tj = $this->array_diff_self($alllist_tj_temp, $alllist);

        if (!empty($alllist_tj)) {
            $alllist_relate = $this->do_type_list($alllist_tj);
        } else {
            $per = 15;
        }
        $start = ($page - 1) * $per;
        $where_per = $where . " limit $start,$per";
        //echo "11111";
        //echo "<pre>";
        foreach ($alllist as $key => &$tmp) {
            $tmp['MS_CONTENT'] = trim($tmp['MS_CONTENT']);
            if ($arr_search) {
                $mark = 1;
                foreach ($arr_search as $k => $val) {
                    $strstr = strstr($tmp['MS_FNAME'], $val);
                    if ($strstr) {
                        $nn = 0;
                        for ($ii = 0; $ii < strlen($strstr); $ii++) {
                            if (ord(substr($strstr, $ii, 1)) > 0xa0) $nn++;
                        }
                        if ($nn % 2 == 0) $mark = 0;
                        //echo $strstr."|".$nn."|"."<br>";
                    }
                }
                if ($mark) {
                    unset($alllist[$key]);
                }
            }
        }
        sort($alllist);
        $alllist_per = $this->smssenddao->get_message_list($where_per);

        $pagebar = pagebar('sms.php', $params, $per, $page, count($alllist));

        $alllist_per = $this->do_type_list($alllist_per);
        foreach ($alllist_per as $key => &$tmp) {
            $tmp['MS_CONTENT'] = trim($tmp['MS_CONTENT']);
            if ($arr_search) {
                $mark = 1;
                foreach ($arr_search as $k => $val) {
                    $strstr = strstr($tmp['MS_FNAME'], $val);
                    if ($strstr) {
                        $nn = 0;
                        for ($ii = 0; $ii < strlen($strstr); $ii++) {
                            if (ord(substr($strstr, $ii, 1)) > 0xa0) $nn++;
                        }
                        if ($nn % 2 == 0) $mark = 0;
                        //echo $strstr."|".$nn."|"."<br>";
                    }
                }
                if ($mark) {
                    unset($alllist_per[$key]);
                }
            }
        }
        sort($alllist_per);
        //print_r($alllist_per);exit;
        $smid_arr = array();
        $ydy_smid = array();
        if ($usermobile != '') {
            $array_smspackage = $this->_dao->getgwc_info($usermobile);


            foreach ($array_smspackage as $key => $val) {
                //print_r($key);
                $smid_arr[] = $array_smspackage[$key]['smsid'];
                //print_r($smid_arr);
            }
            //print_r($params);
            //$date=date("Y-m-d H:m:s");
            //hlf/2018/1/12 星期五----判断是否是已订阅的短信
            $ydy_midarr = $this->_dao->query("select sms_orderdetail.smsid,sms_orderlist.* from sms_orderlist,sms_orderdetail  where sms_orderlist.usermobile='" . $_SESSION['cookie_user']['usermobile'] . "' and sms_orderdetail.orderid=sms_orderlist.orderid");
            //print_r($ydy_midarr);
            $date = date("Y-m-d");
            //$date='2019-01-12';
            foreach ($ydy_midarr as $val) {
                if ($val['orderbtime'] != '0000-00-00' && $date <= $val['orderetime']) {
                    $ydy_smid[] = $val['smsid'];
                } else if ($val['orderbtime'] == '0000-00-00') {
                    $ydy_smid[] = $val['smsid'];
                }
            }
        }

        $this->assign("ydy_smid", $ydy_smid);
        //end
        $this->assign("smid_arr", $smid_arr);
        $this->assign("pagebar", $pagebar);
        $this->assign("url_variety", $url_variety);
        $this->assign("url_city", $url_city);
        $this->assign("curr_nav", $curr_nav);
        $this->assign("alllist", $alllist_per);
        $this->assign("alllist_relate", $alllist_relate);

        // 取区域城市
        $typeurlstr = 'view=hotsms';
        $this->assign("citystr_0001", $this->get_areacity_sms('0001', $url_variety, $typeurlstr));
        $this->assign("citystr_0002", $this->get_areacity_sms('0002', $url_variety, $typeurlstr));
        $this->assign("citystr_0003", $this->get_areacity_sms('0003', $url_variety, $typeurlstr));
        $this->assign("citystr_0004", $this->get_areacity_sms('0004', $url_variety, $typeurlstr));
        $this->assign("citystr_0005", $this->get_areacity_sms('0005', $url_variety, $typeurlstr));
        $this->assign("citystr_0006", $this->get_areacity_sms('0006', $url_variety, $typeurlstr));

        //区域钢厂
        $this->assign("steelstr_0001", $this->get_areasteel_sms('0001', $url_variety, $typeurlstr));
        $this->assign("steelstr_0002", $this->get_areasteel_sms('0002', $url_variety, $typeurlstr));
        $this->assign("steelstr_0003", $this->get_areasteel_sms('0003', $url_variety, $typeurlstr));
        $this->assign("steelstr_0004", $this->get_areasteel_sms('0004', $url_variety, $typeurlstr));
        $this->assign("steelstr_0005", $this->get_areasteel_sms('0005', $url_variety, $typeurlstr));
        $this->assign("steelstr_0006", $this->get_areasteel_sms('0006', $url_variety, $typeurlstr));

    }
	//分类短信套餐
	public function hotsms_type($params){
		//hlf2018/1/9 星期二
		$now_url=$_SERVER['PHP_SELF'].'?'.$_SERVER['QUERY_STRING'];
		//print_r($now_url);
		$now_url=urlencode($now_url);
		$this->assign("now_url",$now_url);
		$gwc_url=urlencode("/_v2app/sms.php?view=mrhq_city_confirm");
		$this->assign("gwc_url",$gwc_url);
		$usermobile=$_SESSION['cookie_user']['usermobile'];
		$this->assign("usermobile",$usermobile);
		//end

	  $where=' where 1 ';
	  $citycode=$this->get_citycode();
	  $sql_gc="SELECT st_code,name  FROM research_center.sc_steelcom"; 
	    $gclist=$this->rcdao->query($sql_gc);
        foreach($gclist as $k=>$v){
		$gccodelist[$v['st_code']]=$v['name'];
		}
      	if($params['smstype']!=''){
			switch($params['smstype']){
				case '价格行情':
				$where.="and (MS_TYPE ='市场行情' or MS_TYPE ='品种行情')";
                 break;
			    case '市场库存':
				$where.="and (MS_TYPE ='市场库存' or MS_TYPE ='品种库存')";
                 break;
				 case '综合要闻':
				$where=" where  (MS_TYPE ='财经要闻' or MS_TYPE ='行业要闻')";
				  break;
				default:
				 $where.="and MS_TYPE ='".$params['smstype']."'";
				 break;
			}
		}
		$smstype="&smstype=".$params['smstype'];
		$typeurlstr='view=hotsms_type'.$smstype;
		$nav=" <a href='".APP_URL_WWW."/_v2app/sms.php?view=hotsms_type&smstype=".$params['smstype']."'>".$params['smstype']."</a>";
		$nav_title=$params['smstype'];
		$par_city='';
		$par_variety='';
	    $curr_nav='您已选择';//当前位置
	   //分页参数
		  $page = $params['page'] == '' ? 1 : $params['page'];
		  $per = 15;

		  unset($params['page'] );
	  //处理参数
		$url_city='';
		$url_variety='';
		if($params['city']!=''){
			$cityid=$params['city'];
			$par_city="and MS_CITYORSTEEL_ID='".$params['city']."'";
			$url_city="&city=".$params['city'];
			//$curr_city_nav="  <span style='color:#000000;'>城市|</span>  ".$citycode[substr($params['city'],2,2)];
			//hlf/2017/12/29
			$curr_city_nav="  <span style='color:#000000;'>城市|</span>  ".$citycode[$params['city']];
			//end
		}
		if($params['variety']!=''){
			$varietyid=$params['variety'];
			$par_variety="and MS_CLASS='".$params['variety']."'";
			$url_variety="&variety=".$params['variety'];
		    $curr_variety_nav="  <span style='color:#000000;'>品种|</span>    ".$GLOBALS['ARRAY_VARIETY'][$params['variety']];
			//处理 钢材 炉料 有色 煤焦 总分类
			if($params['variety']=='gc'){
				$par_variety="and MS_CLASS in (".$GLOBALS['TYPE__VARIETY']['GC'].")";
				$curr_variety_nav="  <span style='color:#000000;'>品种|</span>   钢材";
			}
			if($params['variety']=='ll'){
				$par_variety="and MS_CLASS in (".$GLOBALS['TYPE__VARIETY']['LL'].")";
				$curr_variety_nav="  <span style='color:#000000;'>品种|</span>   炉料";
			}
			if($params['variety']=='ys'){
				$par_variety="and MS_CLASS='c21' ";
				$curr_variety_nav="  <span style='color:#000000;'>品种|</span>   有色";
			}
			if($params['variety']=='mj'){
				$par_variety="and MS_CLASS in (".$GLOBALS['TYPE__VARIETY']['MJ'].")";
				$curr_variety_nav="  <span style='color:#000000;'>品种|</span>   煤焦";
			}
			//处理有色 品种
			if($params['variety']=='c21'){
				if($params['mvid']!=''){
					$par_variety="and (MS_CLASS='c21' and MS_MVID ='".$params['mvid']."')";
					 $url_variety="&variety=".$params['variety']."&mvid=".$params['mvid'];
				}
			}
			//结束
			}
		if($params['gc_name']!=''){
				$gcid=$params['gc_name'];
				$par_city="and MS_CITYORSTEEL_ID='".$params['gc_name']."'";
				$url_city="&gc_name=".$params['gc_name'];
				$curr_city_nav=" <span style='color:#000000;'>钢厂 |</span>     ".$gccodelist[$params['gc_name']];		}
		//组合取消的条件
		if($params['city']!=''){
			$curr_nav.=$curr_city_nav." <a href='/_v2app/sms.php?view=hotsms".$url_variety."' style='color:#000000;'><img src='images/xx.png' border=0 style='padding-top:1px;'></a>";
		}
		if($params['variety']!=''){
			$curr_nav.=$curr_variety_nav." <a href='/_v2app/sms.php?view=hotsms".$url_city."' style='color:#000000;'><img src='images/xx.png' border=0 style='padding-top:1px;'></a>";
		}
		if($params['gc_name']!=''){
			$curr_nav.=$curr_city_nav." <a href='/_v2app/sms.php?view=hotsms".$url_variety."' style='color:#000000;'><img src='images/xx.png' border=0 style='padding-top:1px;'></a>";
		}
		$curr_nav.=$curr_type_nav.$type_close;
        //组合条件
		$where=$where.$par_city.$par_variety;

		//查找列表
		$where.=" and MESSAGE_DETAIL.MS_CONTENT !='' and canDingYue=1 group by MS_ID order by MS_ID asc,MS_ISTJ desc ";
		//echo $page."***".$per;
		$start = ( $page - 1 ) * $per;
	    $where_per=$where." limit $start,$per";
	    $alllist_per=$this->smssenddao->get_message_list($where_per);
		foreach($alllist_per as &$tmp){
			$tmp['MS_CONTENT'] = trim($tmp['MS_CONTENT']);
		}

		$alllist=$this->smssenddao->get_message_list($where);
		$pagebar = pagebar('sms.php', $params, $per, $page, count($alllist));
	    $this->assign( "pagebar", $pagebar);
		$this->assign("url_variety",$url_variety);
		$this->assign("url_city",$url_city);
		$this->assign("smstype",$smstype);
		$this->assign("nav",$nav);
		$this->assign("nav_title",$nav_title);
		$this->assign("curr_nav",$curr_nav);

		$alllist_per_new=$this->do_type_list($alllist_per);
	    $this->assign("alllist",$alllist_per_new);
		//hlf/2018/1/8
		if($usermobile!=''){
			$smid_arr=array();
			$array_smspackage=$this->_dao->getgwc_info($usermobile);
			foreach($array_smspackage as $key=>$val){
				//print_r($key);
				$smid_arr[]=$array_smspackage[$key]['smsid'];
				//print_r($smid_arr);
			}
			//print_r($smid_arr);exit;
			$this->assign("smid_arr",$smid_arr);
		//hlf/2018/1/12 星期五----判断是否是已订阅的短信
			$ydy_midarr=$this->_dao->query("select sms_orderdetail.smsid,sms_orderlist.* from sms_orderlist,sms_orderdetail  where sms_orderlist.usermobile='".$_SESSION['cookie_user']['usermobile']."' and sms_orderdetail.orderid=sms_orderlist.orderid");
			//print_r($ydy_midarr);
			$ydy_smid=array();
			foreach($ydy_midarr as $val){
				if($val['orderbtime'] !='0000-00-00' && date("Y-m-d")<=$val['orderetime']){
					$ydy_smid[]=$val['smsid'];
				}else if($val['orderbtime'] =='0000-00-00'){
					$ydy_smid[]=$val['smsid'];
				}
			}
			$this->assign("ydy_smid",$ydy_smid);
		}
		//hlf/end
		// 取区域城市
		$this->assign("citystr_0001",$this->get_areacity_sms('0001',$url_variety,$typeurlstr));
		$this->assign("citystr_0002",$this->get_areacity_sms('0002',$url_variety,$typeurlstr));
		$this->assign("citystr_0003",$this->get_areacity_sms('0003',$url_variety,$typeurlstr));
		$this->assign("citystr_0004",$this->get_areacity_sms('0004',$url_variety,$typeurlstr));
		$this->assign("citystr_0005",$this->get_areacity_sms('0005',$url_variety,$typeurlstr));
		$this->assign("citystr_0006",$this->get_areacity_sms('0006',$url_variety,$typeurlstr));
		//区域钢厂
		$this->assign("steelstr_0001",$this->get_areasteel_sms('0001',$url_variety,$typeurlstr));
		$this->assign("steelstr_0002",$this->get_areasteel_sms('0002',$url_variety,$typeurlstr));
		$this->assign("steelstr_0003",$this->get_areasteel_sms('0003',$url_variety,$typeurlstr));
		$this->assign("steelstr_0004",$this->get_areasteel_sms('0004',$url_variety,$typeurlstr));
	    $this->assign("steelstr_0005",$this->get_areasteel_sms('0005',$url_variety,$typeurlstr));
		$this->assign("steelstr_0006",$this->get_areasteel_sms('0006',$url_variety,$typeurlstr));
	}
	public function array_diff_self($array1,$array2){
		$arr_last=array();
		foreach($array1 as $k=>$v){
			if(!in_array($v,$array2)){
				$arr_last[]=$v;
			}
		}
         return $arr_last;
	}
	public function  do_type_list($alllist_tj ){
		foreach($alllist_tj as $k=>$v){ 
			switch($v['MS_TYPE']){
					case '品种行情':
					case '市场行情':
					$alllist_tj[$k]['MS_TYPE']='价格行情';
				break;
					case '品种库存':
					case '市场库存':
					$alllist_tj[$k]['MS_TYPE']='市场库存';
				break;
			default:
				$alllist_tj[$k]['MS_TYPE'] =$v['MS_TYPE'];
			}
		}
		return $alllist_tj;
	}
	public function myorderlist($params){
		//hlf/2018/1/10 星期三
		if(strpos($params['orderid'],".php")!=false){

			$temp=explode('.',$params['orderid']);
			$orderid=$temp[0];
			//print_r($params['orderid']);
			if($orderid !=''){
				//start/2018/1/24 星期三/hlf
				$status=$this->_dao->query("select * from sms_orderlist where orderid='".$orderid."'");
				$paystatus=$this->_dao->query("select * from sms_order2fuqianla where orderid='".$orderid."' order by id desc limit 1");
				if($paystatus[0]['paystatus']==-2 && $status[0]['status']==0){
					$this->_dao->execute("update sms_orderlist set status=3 where orderid='".$orderid."'");
					$this->_dao->execute("update sms_order2fuqianla set paystatus=-1 where orderid='".$orderid."' and id='".$status[0]['id']."'");
					//end/hlf
				}
			}
			$params['orderid']=$orderid;
			$params['status']=$status;
		}
			//print_r($params);
		$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
		
		$nowDate=date("Y-m-d",strtotime("- 3 day"));
		//$nowDate='2018-01-12';
		//print_r($nowDate);
		$all_array=$this->_dao->all_orderlist($params);
		foreach($all_array as $val){
			$createtime=date("Y-m-d",strtotime($val['createtime']));
			if($nowDate >= $createtime){
				//echo "0000";
				$this->_dao->upd_ordernum($val['orderid']);
			}
		}
		//end
		
		if($params['usermobile']!=''){
		$myorderlist=$this->_dao->get_myorderlist($params);
		$myorder_detail=$this->_dao->get_myorderdetail_byid($params);
			if($params['orderid']!=''){
				$orderid=$params['orderid'];
				$order_status=$this->_dao->get_orderstatus_byid($params);
				$params['order_status']=$order_status;
				//print_r($order_status);
			}
			else{
				$orderid=$myorder_detail[0]['orderid'];
				$params['orderid']=$orderid;
				$order_status=$this->_dao->get_lastorderstatus_byuser($params);
				$params['order_status']=$order_status;
				
			}
		}
		//hlf/2018/1/10 星期三
		//print_r($params);
		$params['orderid']=$orderid;
		$myorder_detail=$this->_dao->get_myorderdetail_byid($params);
		$this->assign("usermobile",$_SESSION['cookie_user']['usermobile']);
		//hlf/end/2018/1/10 星期三
		
		$this->assign("order_invoice_flag",$GLOBALS['ORDER_INVOICE_FLAG']);
		$this->assign("myorderlist",$myorderlist);
		$this->assign("myorder_detail",$myorder_detail);  
		$this->assign("order_status",$order_status);
		$this->assign("orderid",$orderid);
		$this->assign("orderst",$GLOBALS['ORDER_STATUS']);
		//$this->assign("status",$params['status']);
	}
	public function del_myorderlist($params){
		
		$this->_dao->del_myorderlist($params);
		goURL("sms.php?view=myorderlist");
		
	}
	
	public function do_gatherfee($params){
		
		//echo "<pre>";
		//print_r($params);
		$params['paytype'] = urldecode( $params['paytype'] );
	    //print_r($params);
		$total_fee=0;
		$hqvarcode=$this->get_hqvarcode();
	    $citycode=$this->get_citycode();
		$arr_data=$this->check_sms_common($_SESSION['cookie_user']['usermobile']);
			foreach($arr_data[0] as $k=>$v){
				$hqvarcity_new_1[$k]=array('smstype'=>$_SESSION["cookie_smstype1"][$v]);
			}
			foreach($hqvarcity_new_1 as $kk=>$vv){
			                                 $total_fee+=$this->sms_type_price_real[$vv['smstype']][$params['paytype']];
			}
			echo $total_fee;
			exit;
	}
	public function docancel_sms_byid($params){
		 
		  // 重新计算订单金额
			$arrdetail=$this->_dao->selorderstatus_smsbyid($params);
			if(substr($arrdetail[0]['smstype'],-4)=='套餐'){
				$price=$GLOBALS['SMS_TYPE_PRICE'][substr($arrdetail[0]['smstype'],0,8)];
			 }
			 else{
					$price=SMS_TYPE_PRCIE_SELF;
			 }
			 $par_upd['orderid']=$arrdetail[0]['orderid'];
			 $par_upd['price']=$price;
			 $par_upd['ordernum']=$arrdetail[0]['ordernumber'];
				
			 $this->_dao->upd_howmuch_byorderid($par_upd);
		    // 删除
			$orderlist=$this->_dao->getmyorderlist_byid($par_upd);
			/*if($orderlist[0]['status']==1){
				echo "0";exit;
			}else{*/
				$this->_dao->del_from_smsbyid($params);
				echo "1";exit;
			//}

			 
	}
	public function confirmorder($params){
		//hlf/2017/11/24----订单详情
		//print_r($params['orderid']);exit;
		$this->assign("usermobile",$_SESSION['cookie_user']['usermobile']);
		$usermobile=$_SESSION['cookie_user']['usermobile'];
		$name_sql="select * from message_user where user_mobile	='".$usermobile."'";
		$res=$this->maindao->query($name_sql);
		$username=$res[0]['user_name'];
		//print_r($params);
		$array_ordetail=$this->_dao->query("select * from sms_orderdetail  where  orderid='".$params['orderid']."'");

		$array_smspackage=$this->_dao->query("select * from SmsDingYueCart where  MobileNum='".$usermobile."'");
		
		
		//echo "<pre>";
		$total_array=$this->_dao->getmyorderlist_byid($params);
		//$this->assign("");
		
		//hlf2018/1/16 星期二----支付判断是否有重复短信
		$yzforderidres=$this->_dao->query("select sms_orderdetail.smsid,sms_orderlist.orderid from sms_orderlist,sms_orderdetail where sms_orderlist.status=0 and sms_orderdetail.orderid=sms_orderlist.orderid and sms_orderlist.usermobile=$usermobile");
		$dzhif_smsid = $yzhif_smsid = array();
		foreach($array_smspackage as $val){
			$dzhif_smsid[]=$val['smsid'];
		}
		foreach($yzforderidres as $val){
			$yzhif_smsid[]=$val['smsid'];
		}
		$common_smsid=array_intersect($dzhif_smsid,$yzhif_smsid);
		
		foreach($array_smspackage as $val){
			foreach($common_smsid as $value){
				if($val['smsid']==$value){
					$sms_name[]=$val['smsname'];
				}
			}
			
		}
		if($sms_name!=''){
			$common_strsmsname=implode(',',$sms_name);
			//print_r($common_strsmsname);exit;
		//	echo "<script>alert('您的短信已经支付过，请重新选择'.$common_strsmsname.);</script>";
			//echo "<script>alert('". $common_strsmsname ."!');</script>";
			echo "<script>alert('您已订阅".$common_strsmsname."请不要重复订阅');</script>";
			goURL( 'sms.php?view=myorderlist' );
		}
		//end
		$this->assign("orderid",$params['orderid']);
		$this->assign("total_fee_package",$total_array[0]['ordernumber']);
		$this->assign("totalfee",$total_array[0]['howmuch']);
		$this->assign("array_ordetail",$array_ordetail);
		$this->assign("username",$username);
		$this->assign("usermobile",$_SESSION['cookie_user']['usermobile']);
		
	}
	public function dosms_islogin($params){
			$_SESSION['cookie_user']['usermobile']=$params['usermobile'];
			$myorderlist=$this->_dao->get_myorderlist($params);
			if(empty($myorderlist)){
					alert( "对不起，您还没有订单!请您先订阅." );
					goURL( 'sms.php?view=mrhq_new' );
			}
			else{
				goURL( 'sms.php?view=myorderlist' );
			}			
	}
	// 
	public function check_yanzhm($params){
		if($_SESSION['login_authimg']==$params['yanzhm']){
			echo "1";exit;
		}
		else{
			echo "0";exit;
		}
	}

	//hlf start 2020/9/15
	public function checkPassWord($params){
		$res=checkpassword($params['password']);
		echo $res;exit;
	}
	//hlf end

	//登录 用ajxa 登录可能会好点
	public function dosms_login($params){
		$userinfo=$this->get_userbyusermobile($params);	
		if($userinfo)
		{
				$_SESSION['cookie_user']['username']=$userinfo['username'];
				$_SESSION['cookie_user']['usermobile']=$userinfo['usermobile'];
				goURL( 'sms.php?view=mrhq_new' );
		}
	}
	// 判断是否登陆
	public function doisloginok($params){
		if($_SESSION['cookie_user']['usermobile']!=''){
			echo "1";exit;
		}
		echo "0";exit;
	}
	public function check_username($params){
		$count=0;
		$count=$this->maindao->check_username($params['uname']);
         if($count>0){
		 echo "0";exit;
		 }else{
			 echo "1";exit;
		 }
	}
	public function upd_zhuce($params){
		$this->assign("params",$params);
		//print_r($params['urlstr']);exit;
		 if($_SESSION['cookie_user']['usermobile']=='') { alert("您还没有登陆，请您先登陆！");  goURL( 'sms.php?view=hotsms' );exit;}
				//$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
				//$params['usermobile']='13166436900';
			   // $arruserinfo=$this->_dao->get_userinfo($params);
			   //hlf/2017/1/8
			   $usermobile=$_SESSION['cookie_user']['usermobile'];
				$name_sql="select * from message_user where user_mobile	='".$usermobile."'";
				$arruserinfo=$this->maindao->getRow($name_sql);
				//print_r($this->blackdao);exit;
			   //hlf/end
			  // print_r($arruserinfo);
				// 查找会员信息
				 //$meminfo=$this->maindao->get_meminfo($arruserinfo['member_id']);
			//	 $meminfo=$this->maindao->get_meminfo('156343');
				// 查找会员联系人信息
				//$memadmininfo=$this->maindao->update_admininfo($arruserinfo['user_name']);
				//print_r($memadmininfo);
				//print_r($this->maindao);exit;
				$this->assign("arruserinfo", $arruserinfo);
				//$this->assign("meminfo",$meminfo);
				//$this->assign("memadmininfo", $memadmininfo);
	}
	// 修改资料信息
	public function upd_zhuce_info($params){
		//print_r($params['truename']);exit;
		$params = eval('return '.var_export($params,true).';');
		$truename=$params['truename'];
		$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
		$name_sql="select user_wap_password  from message_user where user_mobile='".$params['usermobile']."'";
		$user_wap_password=$this->maindao->getOnes($name_sql);
		//print_r($user_wap_password);exit;
				if(md5($params['old_passwd']) != $user_wap_password[0]){ 

					//$params['passwd']=$params['old_passwd'];
					echo 1;
				}else{
				//$params['passwordmd5']=md5($params['passwd']);
				  // 会员信息
				//$meminfo=$this->maindao->get_meminfo($params['memid']);
				$upmsql="update member set telnumber='".$params['usermobile']."', admintruename='".$truename."' where mbid='".$params['memid']."'";
				$this->maindao->Execute($upmsql);
				 //  管理员信息
				$upasql="update adminuser set password='".$params['passwd']."', passwordmd5='".md5($params['passwd'])."',truename='".$truename."', mobil='".$params['usermobile']."' where mid='".$params['memid']."' and username='".$params['uname']."'";
				 $this->maindao->Execute($upasql);
				 //短信用户信息
				 $upmes_sql="update message_user set user_mobile='".$params['usermobile']."',user_name='".$truename."', user_wap_password='".md5($params['passwd'])."' where login_name='".$params['uname']."' and user_id='".$params['user_id']."' and member_id='".$params['memid']."'";
				 $this->maindao->Execute($upmes_sql);
				 /* $upsql="update member set ";
				   $i=0;
				   foreach ($params['memberinfo'] as $field=>$value)
				  {
					   if ($i==0){$fields.=$field."='".$value."'";}else {$fields.=",".$field."='".$value."'";}
					   $i++;
				   }
				  $upsql=$upsql.$fields." where mbid=".$params['mbid'];
				  $this->maindao->Execute($upsql);
				  //  管理员信息
				  $upsql_a="update adminuser set ";
				  $j=0;
				   foreach ($params['admininfo'] as $field=>$value)
				   {
					   if ($j==0){$fields_a.=$field."='".$value."'";}else {$fields_a.=",".$field."='".$value."'";}
					   $j++;
				   }
				   $upsql_a=$upsql_a.$fields_a." where id=".$params['id'];

				  $this->maindao->Execute($upsql_a);
				  // 短信用户信息
				  $updsql_sms="update sms_user	set  userpasswd='".$params['admininfo']['password']."', username='".$params['admininfo']['truename']."',email='".$params['admininfo']['email']."'  where memadminid=".$params['id']." and memid=".$params['mbid'];
				  //
				$this->_dao->Execute($updsql_sms);*/
					//alert("信息修改成功！");
				 echo 0; 
				}exit;
	}
	// 注册用户
	public function doregister($params){
		//echo "<pre>";
		//print_r($params);exit;
		 //先转换数据
		// echo "0";exit;
		//hlf add start2019/3/19
		if($params['usermobile']==""||$params['truename']==""||$params['uname']==""||$params['passwd']=="" ||  $params['code']=="" ){
			  echo "2";//不能输入空字符
			  exit;
		 }else{
			 $memcache = new Memcache; 
	 
			$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
			$code=$memcache->get($_REQUEST['PHPSESSID']."_helloweba_math"."_".$params ['usermobile']);
			if($code!=$params['code']){
				echo "4";exit;
			}
			//print_r($code);exit;
			//if($params ['usermobile']!=""){
				//$memcache->set($_REQUEST['PHPSESSID']."_helloweba_math"."_".$params ['usermobile'],$rand,MEMCACHE_COMPRESSED,$expire);
			//}
			$memcache->close();
			$isMob="/^1[34578]{1}\d{9}$/";
			if(!preg_match($isMob,$params['usermobile'])){
				echo '3';exit;//手机号码输入错误
			}
			$params['truename']=mysql_real_escape_string($params['truename']);
			$params['uname']=mysql_real_escape_string($params['uname']);
			$params['passwd']=mysql_real_escape_string($params['passwd']);
		 }
		 //hlf add end
		 //hlf start 2020/9/15
		 $ps_res=checkpassword($params['passwd']);
		 if($ps_res!=0){
			echo "ps".$ps_res;exit;
		 }

		 //hlf end
		$countuname=$this->maindao->check_username($params['uname']);
         if($count>0){
		 echo "0";exit;
		 }
	         foreach($params as $k=>$v){
						if($v!=''){
							$params[$k]=urldecode($v);
						}
				}
				
			    // 判断手机平台有没有这个用户，有则所有注册不成功,否则注册成功
				//hlf/2017/23
				$sql="select count(user_id) as numcount from message_user where user_mobile='".$params['usermobile']."' ";
				$res=$this->maindao->query($sql);
				$user_exists=$res['numcount'];
				//echo $user_exists;
				//$user_exists=$this->_dao->get_user_exists($params['usermobile']);
				if($user_exists==0){ //不存在处理注册信息
				     // 先注册为钢之家会员
				     if($params['compfname']==''){    //会员全称
						 $params['compfname']='短信会员'.$params['usermobile'];
					}
					 if($params['telnumber']==''){    //会员全称
						 $params['telnumber']=$params['usermobile'];
					}
					 // 注册手机所在城市
					 $mobile_str=$this->maindao->get_mobile_db($params);
			         $params['city']=$mobile_str[0]['province']."-".$mobile_str[0]['city'];
					 //再插入会员用户表
					 $memid=$this->maindao->ins_member($params);
					$params['mid']=$memid;
					// 插入会员用户表
					$m_adminid=$this->maindao->ins_member_admin($params); 
					//插入短信用户表
					$params['memid']=$memid;
			        $params['memadminid']=$m_adminid;
					//hlf/2017//11/23

					$sql="INSERT INTO `message_user` (`member_id` ,`user_wap_password` ,`user_name`,user_mobile ,user_sms_status,login_name) VALUES ( '".$params['memid']."', '".md5($params['passwd'])."','".$params['truename']."','".$params['usermobile']."','1','".$params['uname']."')";
					$userinfo=$this->maindao->execute($sql);
			//	print_r($userinfo);
				//end



				  // $userinfo=$this->_dao->ins_sms_user_detail($params);
				   // 注册成功发激活邮件 //2011-10-09
				  // $this->sendemailmessage($params['usermobile'],$params['uname'],$params['username'],$params['email']);
				   //end 2011-10-09
				   //$this->maindao->ins_member_admin($params); 
				    echo "0";
			      exit;
			}
			else{	//已经存在改用户
				 echo "1";
				 exit;
			}	
	}
	public function isexists_mobile($params)
	{
		//print_r($this->_dao);exit;

		//$user_exists=$this->_dao->get_user_exists($params['usermobile']);
		/*if($user_exists>0){
			$user_exists=1;
		}else{
			$user_exists
		}*/

		//hlf/2017/11/23----判断手机号码是否重复
		$sql="select count(user_id) as usercount from message_user where user_mobile='".$params['usermobile']."'";
		$user_exists=$this->maindao->getRow($sql);
		echo $user_exists['usercount'];exit;
	}
	// 获取客户发送的确认短信
	public function autoreply($params){
    // 先从短信发送表找发送过DXDY
	$arr_reply=$this->smssenddao->get_userreply("");
	// 插入前台用户
    foreach($arr_reply as $k=>$v ){
		$this->_dao->ins_smsreply($v);
		// 每插入一条删除一条
		$this->smssenddao->del_userreply($v['SERIALNO']);
	}
    //自动回复发送确认短信的客户
	$this->sms_autoreply_user($params);
	}
	// 自动回复发送确认短信的客户
	public function sms_autoreply_user($params){
	// 从前台用户表找已经注册但是还没激活的用户
	 $arr_user=$this->_dao->get_user_noactive();
     foreach($arr_user as $k=>$v ){
		 // 给客户回复一条短信 回复用户名，密码
		    $param_send['usermobile']=$v['usermobile'];
		    $param_send['smscontent']="您好，感谢您注册钢之家短信频道！你的用户名为".$v['uname']."，密码为:".$v['userpasswd'].".";
		    $param_send['smssendid']=0;
			$param_send['memid']=0;
			$this->smssenddao->ins_sms2send($param_send);
			// 改变用户表的状态
			$this->_dao->upd_sms_userreply($v['id']);
		    // 改变回复表的状态 激活装户
		    $this->_dao->upd_smsuser($v['usermobile']);
	}
	}
	public function doindexhot_login($params){
		//hlf strat 2020/9/15 
		// print_r($this->blackdao);
		 $num=can_do_login($this->maindao,$params['usermobile'],"",false,"2");
		//hlf end
		if($num>0){
			if(preg_match("/^\d{11}$/",$params['usermobile'])){   //是手机号
				//hlf/2017/11/23------根据手机号码验证登录信息
				$mobil_sql="select * from message_user where user_mobile='".$params['usermobile']."' ";
				$userinfo=$this->maindao->getRow($mobil_sql);
				//print_r( $userinfo);exit;
						 // $userinfo=$this->_dao->get_userinfo($params);
						  if(!empty($userinfo))
						{
							//echo $userinfo[0]['user_wap_password'];
							//echo $userinfo[0]['user_mobile'];
		
							if((md5($params['passwd'])==$userinfo['user_wap_password'])&&($userinfo['user_mobile']==$params['usermobile']))
							{
											/*if($userinfo[0]['uservalid']==3)
										{ //说明已经注册，但是还没有激活
											echo "3";exit;
											}*/
											// 登陆成功后，写COOKIE
											$_SESSION['cookie_user']['usermobile']=$userinfo['user_mobile'];
											//$_SESSION['cookie_user']['username']=$userinfo[0]['username'];
											$_SESSION['cookie_user']['uname']=$userinfo['user_name'];
											//hlf start 2020/9/17 清空之前登陆错误的记录
											$ip=getUIP();
											del_check_log($this->maindao,$params['usermobile'],$ip,2);
											//hlf end 2020/9/17
												echo "1";exit;
							}else{//hlf start 2020/9/15
								$ip=getUIP();
								can_do_login($this->maindao,$params['usermobile'],$ip,true,"2");
							 }//hlf end
						 }
				}
		}else{
			echo "2";exit;
		}
		

	echo "0";exit;
	}
    //获取密码
	public function dogetpasswd($params){
		//print_r("11111");exit;
		//hlf/2017/11/23----根据手机号获取用户的详细信息
		$sql="select * from message_user where user_mobile='".$params['usermobile_f']."'";
		$arruserinfo=$this->maindao->query($sql);
		//print_r($arruserinfo[0]['user_name']);exit;
//$arruserinfo=$this->_dao->get_userinfo_bydetail($params);
				if(!empty($arruserinfo))
		       {
                   if(($arruserinfo[0]['user_name']==$params['uname_f'])){
                        // 发送密码到手机
						$param_send['usermobile']=$params['usermobile_f'];
						$param_send['smscontent']="尊敬的用户，您好！您的密码为:".$arruserinfo['userpasswd'];
						$param_send['smssendid']=0;
						$param_send['memid']=$arruserinfo['member_id'];
						$this->smssenddao->ins_sms2send($param_send);
						alert("您的密码已经发送的您的手机，请注意查收！");
					 }else{
							alert("您填写的资料有误，请重新填写或直接联系钢之家管理员！");
							echo "<script>Javascript:history.back();</script>";
					 }
			   }
			   else{
					alert("对不起，该手机号还未注册！");
			   }
				goURL( 'sms.php?view=hotsms' );
	}
	 //修改资料页面
	public function upduserinfo($params){
				$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
			    $arruserinfo=$this->_dao->get_userinfo($params);
				$this->assign("arruserinfo", $arruserinfo);
	}
	//修改资料
	public function doupduserinfo($params){
		$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
		$arruserinfo=$this->_dao->get_userinfo($params);
	
		if(!empty($arruserinfo)){
			if($arruserinfo[0]['userpasswd']!=$params['passwd_upd_old'])
			{
				alert("您输入的旧密码有误，请重新输入!");
					echo "<script>Javascript:history.back();</script>";
			}
			else
			{
					$this->_dao->upd_userinfo($params);
					//同时更新主库的相关信息
					$params['memadminid']=$arruserinfo[0]['memadminid'];
					$params['passwd']=$params['passwd_upd'];
					$memid=$this->maindao->upd_sms_member_admin($params);
					alert("信息修改成功!");
				   goURL( 'sms.php?view=hotsms' );
			}
		}

	}
	//退出
	public function do_loginout($params){
		//注销之后就清空购物车-----hlf/2018/1/17 星期三
				$_SESSION["cookie_hqvarcity"]='';
				$_SESSION["cookie_smstype1"]='';
				$_SESSION['cookie_smspackage']='';
				unset($_SESSION['cookie_smspackage']);
				unset($_SESSION["cookie_smstype1"]);
				unset($_SESSION['cookie_smspackage']);
				//end
				$_SESSION['cookie_user']['username']='';
				$_SESSION['cookie_user']['usermobile']='';
				goURL( 'sms.php?view=hotsms' );
	}
	public function sendrand($params){
		$rand=rand(1000,9999);
		$param_send['usermobile']=$params['usermobile'];
		$param_send['smscontent']="随机验证码为:".$rand;
		$param_send['smssendid']=0;
		$time = date( "Y-m-d H:i:s", time() - 3600 );
		$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$time' " );
		if( $count > 5 ){
		    echo "0";
			exit;
		}
		$this->smssenddao->ins_sms2send($param_send);
		$ip = getfromip();
		$now = date( "Y-m-d H:i:s" );
		$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now'  " );
		//在这里调用短信接口，发送一条短信
		echo $rand;exit;
		
	}
	public function myaccount($params){
		$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
		if($params['usermobile']!=''){
		$myaccount=$this->_dao->get_user_account($params);
		if($params['type']==''){
			$params['type']=1;
		}
		$myaccount_log=$this->_dao->get_user_account_log($params);
		}
		//echo "<pre>";
		//print_r($myaccount_log);
		$this->assign("account_log_types",$GLOBALS['ACCOUNT_LOG_TYPES']);
		$this->assign("uservalid",$GLOBALS['USERVALID']);
		$this->assign("myaccount",$myaccount);
		$this->assign("myaccount_log",$myaccount_log);
	}
	// 2011-04-18
	public function mrhq_city_confirm($params){
		//hlf/2018/1/9 星期二
	$url=urldecode($params['urlstr']);
	//print_r($url);
	$this->assign("url",$url);
	$Str_url=$_SERVER['PHP_SELF'].'?'.$_SERVER['QUERY_STRING'];
	$Str_url=explode('&',$Str_url); 
	//print_r($Str_url[0]);exit;
	$URL_str=urlencode($Str_url[0]);
	//print_r($URL_str);exit;
	$this->assign("URL_str",$URL_str);
	$usermobile=$_SESSION['cookie_user']['usermobile'];
	$this->assign("usermobile",$usermobile);
	//print_r($url);exit;
	//end
	$userinfo=$_SESSION["cookie_user"];
	$hqvarcity=$_SESSION["cookie_hqvarcity"];
	$hqvarcode=$this->get_hqvarcode();
	$citycode=$this->get_citycode();

    $gccode = $this->getgccode();//钢厂名称
	$gcvarcode = $this->getgcvarcode();//钢材名称
//print_r($_SESSION["cookie_hqvarcity"]);exit;
	foreach ($_SESSION["cookie_hqvarcity"] as $name => $value)
    {
		$smstype=$_SESSION["cookie_smstype1"][$name];
		//2011-05-05 
		if( $smstype == '调价信息' ){
		     $array_havarcity[]=array('id'=>$value,'var'=>$gcvarcode[substr($value,4,4)],'city'=>$gccode[substr($value,0,4)],'price'=>$this->sms_type_price[$smstype],'smstype'=>'调价信息');
		}
		if( $smstype == "价格行情" ){
		    $array_havarcity[]=array('id'=>$value,'var'=>$hqvarcode[substr($value,2,4)],'city'=>$citycode[substr($value,0,2)],'price'=>$this->sms_type_price[$smstype],'smstype'=>'价格行情');
		}
    }
	//获取短信套餐的订阅信息
	//hlf/2018/1/17 星期三
	$array_smspackage=$this->_dao->getgwc_info($usermobile);
	//end

	// print_r($array_smspackage);exit;

	$this->assign("citycode",$citycode);
	$this->assign("hqvarcode",$hqvarcode);
	$this->assign("userinfo",$userinfo);
	$this->assign("array_havarcity",$array_havarcity);
	$this->assign("array_smspackage",$array_smspackage);
	}
	public function get_hqvarcode(){
	 $hqvarlist=$this->maindao->get_hqvar();
	 foreach($hqvarlist as $k=>$v){
			$array_hqcode[$v['varid']]=$v['varname'];
		}
		return $array_hqcode;
	}
	public function get_citycode(){
	 $citylist=$this->maindao->getcitycode();
		foreach($citylist as $k=>$v){
			//$array_citycode[substr($v['cityid'],-2)]=$v['cityname'];
			//hlf/2017/12/29
			$array_citycode[$v['cityid']]=$v['cityname'];
			//end
		}
		return $array_citycode;
	}
	public function getgccode(){
	    $gcs = $this->rcdao->query( "SELECT st_code, name FROM research_center.sc_steelcom order by st_type  asc,a_od asc" );
		$ngcs = array();
		foreach( $gcs as $g ){
		    if( strlen( $g['st_code'] ) == 3 ) $g['st_code'] = '0' . $g['st_code'];
			$ngcs[$g['st_code']] = $g['name'];
		}
		return $ngcs;
	}
	public function getgcvarcode(){
	    $vars = $this->_dao->query( "SELECT code, gcpzname FROM steelhome_smsmessage.sms_gc_pz ORDER BY od" );
		$c = array();
		foreach( $vars as $v ){
		    $c[$v['code']] = $v['gcpzname'];
		}
		return $c;
	}
  //  2011-04-18 
	public function dosms_mrhqins($params){
		//先插入session
		foreach ($params['city'] as $k=>$v){
		$_SESSION['cookie_hqvarcity'][$v.$params['hqvar']]= $v.$params['hqvar'];
		$_SESSION['cookie_smstype1'][$v.$params['hqvar']]='价格行情';
		}
		alert( "您的订阅已进入购物车，请在购物车内查阅" );
		//goURL( 'sms.php?view=mrhq_new&var='.substr($params['hqvar'],0,2));
		goURL( 'sms.php?view=mrhq_city_confirm' );
	}
    public function check_data($params){
}
	public function dosms_mrhqins_confirm_before($params){
	$_SESSION['cookie_user']['username']=$params['username'];
	$_SESSION['cookie_user']['usermobile']=$params['usermobile'];
			goURL( 'sms.php?view=confirmorder');
}
	public function yanzhm($params){
			$type=($_GET['t'])?($_GET['t']):'png';
			$width=($_GET['w'])?($_GET['w']):54;
			$height=($_GET['h'])?($_GET['h']):21;
			session_start();
			Header("Content-type: image/".$type);
			srand((double)microtime()*1000000);
			$randval = sprintf("%04d", rand(0,9999));
			session_register('login_authimg');
			if ( is_array($_SESSION) ) {
				$_SESSION['login_authimg'] = $randval;
				$session_verify=$_SESSION['login_authimg'];
			}
			else {
				$session_verify = $randval;
			}
			if ( $type!='gif' && function_exists('imagecreatetruecolor')) {
				$im = @imagecreatetruecolor($width,$height);
				}else {
					$im = @imagecreate($width,$height);
				}

			$r = Array(225,255,255,223);
			$g = Array(225,236,237,255);
			$b = Array(225,236,166,125);

			$key = rand(0,3);

			$backColor = ImageColorAllocate($im, $r[$key],$g[$key],$b[$key]);//背景色（随机）
			$borderColor = ImageColorAllocate($im, 0, 0, 0);//边框色
			$pointColor = ImageColorAllocate($im, 0, 255, 255);//点颜色

			@imagefilledrectangle($im, 0, 0, $width - 1, $height - 1, $backColor);
			@imagerectangle($im, 0, 0, $width-1, $height-1, $borderColor);
			$stringColor = ImageColorAllocate($im, 255,51,153);
			for($i=0;$i<=10;$i++){
				$pointX = rand(2,$width-2);
				$pointY = rand(2,$height-2);
				@imagesetpixel($im, $pointX, $pointY, $pointColor);
			}
			@imagestring($im, 5, 8, 3, $randval, $stringColor);
			$ImageFun='Image'.$type;
			$ImageFun($im);
			@ImageDestroy($im);
}
	public function dosms_mrhqins_confirm($params){
		   //先处理重复信息 计算总计费
			$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
			$hqvarcity=$_SESSION["cookie_hqvarcity"];
			$arr_data=$this->check_sms_common($_SESSION['cookie_user']['usermobile']);
			$hqvarcity_new_1 = [];
			foreach($arr_data[0] as $k=>$v){
				$hqvarcity_new_1[$k]=$_SESSION["cookie_smstype1"][$v];
			}
			
			//计算自助短信的费用
			//$total_fee=SMS_TYPE_PRCIE_SELF*count($hqvarcity_new_1);
			//处理短信套餐订阅重复信息
			/*$arr_data_p=$this->check_sms_common_p($_SESSION['cookie_user']['usermobile']);
			
            foreach($arr_data_p[0] as $k=>$v){
				$array_smspackage[$v]=$temp_smspackage[$v];
			}
			//print_r($array_smspackage_common);*/
			//计算套餐短信的费用
			$array_smspackage=$this->_dao->getgwc_info($_SESSION['cookie_user']['usermobile']);
			$total_fee_package=0;
			foreach($array_smspackage as $k=>$v){
			                                 $total_fee_package+=$GLOBALS['SMS_TYPE_PRICE'][$v['smstype']];
			}
		//print_r($total_fee_package);exit;
        $params['howmuch']=$total_fee_package;
		$hqvarcode=$this->get_hqvarcode();
			$citycode=$this->get_citycode();
			$gccode = $this->getgccode();
	        $gcvarcode = $this->getgcvarcode();
		//生成订单
		$array_smspackage=$this->_dao->getgwc_info($_SESSION['cookie_user']['usermobile']);
		$params['ordernumber']=count($hqvarcity_new_1)+count($array_smspackage);
		if($params['ordernumber']<1){
			 alert( "您还没有有效的订阅信息,请您继续订阅!" );
		    goURL( 'sms.php?view=hotsms' );
			exit;
		  }
		$params['createtime']=date("Y-m-d H:i:s");
		$params['orderbtime']=date("Y-m-d");
		$params['orderetime']=date('Y-m-d',strtotime('+1 year',strtotime(date("Y-m-d"))));

		//用户表插入一份  因为采用了注册用户的方式，这里暂时不需要
		/* $user_exists=$this->_dao->get_user_exists($params['usermobile']);
		if(!$user_exists){
			$params_user['usermobile']=$params['usermobile'];
			$params_user['username']=$params['username'];
			$this->_dao->ins_sms_user($params_user);
		}*/
		$msg=$this->maindao->query("select * from message_user where user_mobile='".$_SESSION['cookie_user']['usermobile']."'");
		//print_r($msg);exit;
		$params['user_id']=$msg[0]['user_id'];
		$params['mid']=$msg[0]['member_id'];
		$insid=$this->_dao->ins_sms_orderlist($params);
		$payOrderNum_arr=array(
			'orderid'=>$insid
			);
		$this->payOrderNum($payOrderNum_arr);
		$params_one_detail=array();
		//插入订阅表，		订单明细表
		$params_one_detail['orderid']=$insid;
		$params_one_detail['usermobile']=$_SESSION['cookie_user']['usermobile'];//2011-10-08
		//处理分类订阅
		foreach ($hqvarcity_new_1 as $v=>$vtype){
	      $params_one['variety']=$v;
		  // 根据短信类型处理,短信的名称 截取 2011-05-05
		  if( $vtype == '价格行情' ){
		      $params_one['smsname']=$citycode[substr($v,0,2)].$hqvarcode[substr($v,2,4)];
		  }
		  if( $vtype == '调价信息' ){
		      $params_one['smsname']=$gccode[substr($v,0,4)].$gcvarcode[substr($v,4,4)];
		  }
		  $params_one['type']=$vtype;
		  $params_one_detail['smstype']=$vtype;
		    //判断如果已经存在就不入sms_messagetype库
			$array_smsid_exists=$this->_dao->get_exists($params_one);
            if(empty($arr_smsid_exists))
			{
		       $smsid=$this->_dao->ins_sms_messagetype($params_one);
			}
			else
			{
				$smsid=$arr_smsid_exists['smsid'];
			}
		  $params_one_detail['smsid']=$smsid;
		  $params_one_detail['smsname']=$params_one['smsname'];
		  $params_one_detail['smscode']=$v;
		  $this->_dao->ins_sms_orderdetail($params_one_detail);
		}
		// 短信套餐形式订阅
		//print_r($array_smspackage);exit;
		foreach($array_smspackage as $k=>$v){
			$params_one_detail['smsid']=$v['smsid'];
			$params_one_detail['smsname']=$v['smsname'];
			$params_one_detail['smscode']='';
			//$params_one_detail['smstype']=$v['smstype'].'套餐';
			//hlf/satrt/2018/5/30
			$params_one_detail['smstype']=$v['smstype'];
			//hlf /end
			$params_one_detail['smscontent']=$v['smsdesc'];
			$this->_dao->ins_sms_orderdetail($params_one_detail);
			//hlf/2017/11/24---清空购物车
			$this->_dao->execute("delete from SmsDingYueCart where smsid='".$v['smsid']."' and MobileNum='".$params_one_detail['usermobile']."'");
			//end
		}
		//订阅完session 置空
		//print_r($params_one_detail['orderid']);exit;// ($array_smspackage['orderid']);exit;
		/*$_SESSION["cookie_hqvarcity"]='';
		$_SESSION["cookie_smstype1"]='';
		$_SESSION['cookie_smspackage']='';
		unset($_SESSION['cookie_smspackage']);
		unset($_SESSION["cookie_smstype1"]);
		unset($_SESSION['cookie_smspackage']);*/
		
		
		$this->assign("orderid",$params_one_detail['orderid']);
		
		
		//goURL( "sms.php?view=confirmorder&orderid='".$params_one_detail['orderid']."'");
		echo "<script>window.location='sms.php?view=confirmorder&orderid=".$params_one_detail['orderid']."'</script>";
	}
	//生成订单时,若输入的验证码正确判断本次的订阅信息和上次的订阅信息是否有重复，有的话列出重复的信息
    public function check_sms_common($params){
		//取手机号的有效订单信息
		$my_valid_orderlist=$this->_dao->get_usersmslist($params);
		$hqvarcity_old = array();
        foreach($my_valid_orderlist as $k=>$v)
		{
			$hqvarcity_old[$v['smscode']]=$v['smsname'];
		}
		//取当前用户的订单信息
		  $hqvarcity=$_SESSION["cookie_hqvarcity"];
		  if($hqvarcity=='')
		  {
			  $hqvarcity=array();
		  }
		  if(!empty($hqvarcity_old)){
           $order_diff=array_diff_key($hqvarcity,$hqvarcity_old);
		   $order_intersect=array_intersect_key($hqvarcity,$hqvarcity_old);
		  }
		  else{
			  $order_diff=$hqvarcity;
			  $order_intersect=array();
		}
			$array_last[0]= $order_diff;
			$array_last[1]= $order_intersect;
            return $array_last;
	}
	 public function check_sms_common_p($params){
		//取手机号的有效订单信息
		$my_valid_orderlist=$this->_dao->get_usersmslist_p($params);
        foreach($my_valid_orderlist as $k=>$v)
		 {
			$hqvarcity_old[]=$v['smsid'];
		}
		//取当前用户的订单信息
		  $hqvarcity=array_keys($_SESSION['cookie_smspackage']);
		  if(!empty($hqvarcity_old)){
           $order_diff=array_diff($hqvarcity,$hqvarcity_old);
		   $order_intersect=array_intersect($hqvarcity,$hqvarcity_old);
		  }
		  else{
			  $order_diff=$hqvarcity;
			  $order_intersect=array();
		}
		$array_last[0]= $order_diff;
		$array_last[1]= $order_intersect;
		//先不要删除
		foreach($order_intersect as $k=>$v){
			unset($_SESSION['cookie_smspackage'][$v]);
		}
        return $array_last;
	}
	public function domrhq_city_confirm_del($params){
		//从session删除
		$_SESSION["cookie_hqvarcity"][$params['flag']]='';
		UNSET($_SESSION["cookie_hqvarcity"][$params['flag']]);
		//goURL( 'sms.php?view=mrhq_city_confirm' );
	}
	//套餐短信发布
	public function  dosend_package_type_count($params){

		$sql_sms_p="SELECT sms_user.usermobile,sms_user.uservalid,sms_user.id FROM sms_orderdetail, sms_user WHERE sms_user.usermobile = sms_orderdetail.usermobile
         AND (sms_user.uservalid =1 OR (sms_user.uservalid =0 AND sms_user.trysendcount >0)) AND right(sms_orderdetail.smstype,4) = '套餐'  AND sms_orderdetail.smsid = '$params[smsid]'  ORDER BY sms_orderdetail.orderid ASC";
		// echo $sql_sms_p;
		 $arr_p_users=$this->_dao->query($sql_sms_p);
          foreach( $arr_p_users as $k=>$v){
							//插入短信日志 
							$params['usermobile']=$v['usermobile'];
							 $p_inserid=$this->_dao->ins_sms_sendmessage($params);
							 //插入短信
							 $params['smssendid']=$params['smsid'];
							 $this->smssenddao->ins_sms2send($params);
							 //扣用户的钱或次数
							 if( $user['uservalid'] == 0 ){

							    $this->_dao->execute( "UPDATE sms_user SET trysendcount = trysendcount - 1 WHERE usermobile=$v[usermobile]" );
							 }
							 if( $user['uservalid'] == 1 ){
								$this->_dao->execute( "UPDATE sms_user SET useraccount = useraccount - " . JG_MOMEY . " WHERE usermobile=$v[usermobile]" );
							     //插入财务日志
								$this->_dao->execute( "INSERT INTO sms_user_account_log SET usermobile='$v[usermobile]', actiontime=NOW(), `type`=3, howmuch = '" .JG_MOMEY. "', accountid =  $p_inserid" );
							 }
		  }
	}
	//套餐短信发布 包年方式的发布 2011-06-23 修改
	public function  dosend_package($params){
		/* $sql_sms_p="SELECT sms_orderdetail. usermobile 
		FROM sms_orderlist, sms_orderdetail  WHERE sms_orderdetail.orderid = sms_orderlist.orderid   and sms_orderdetail.smsid= '$params[smsid]'  AND ((sms_orderlist.status =1 AND sms_orderlist.orderetime > DATE_FORMAT( NOW( ) , '%Y-%m-%d' ) ) OR ((sms_orderlist.status !=1 AND sms_orderlist.status !=2) AND (DATEDIFF( now( ) , sms_orderlist.createtime) <3))) GROUP BY  sms_orderdetail.usermobile"; */
		// $sql_sms_p="SELECT sms_orderdetail. usermobile
		//FROM sms_orderlist, sms_orderdetail  WHERE sms_orderdetail.orderid = sms_orderlist.orderid   and sms_orderdetail.smsid= '$params[smsid]'   AND right(sms_orderdetail.smstype,4) = '套餐'   AND ((sms_orderlist.status =1 AND sms_orderlist.orderetime > DATE_FORMAT( NOW( ) , '%Y-%m-%d' ) ) OR (sms_orderlist.status =0 AND sms_orderlist.tryendtime > now())) GROUP BY  sms_orderdetail.usermobile";
		  $sql_sms_p="SELECT sms_orderdetail. usermobile
		FROM sms_orderlist, sms_orderdetail  WHERE sms_orderdetail.orderid = sms_orderlist.orderid   and sms_orderdetail.smsid= '$params[smsid]'  AND ((sms_orderlist.status =1 AND sms_orderlist.orderetime > DATE_FORMAT( NOW( ) , '%Y-%m-%d' ) ) OR (sms_orderlist.status =0 AND sms_orderlist.tryendtime > now())) GROUP BY  sms_orderdetail.usermobile";

		$arr_p_users=$this->_dao->query($sql_sms_p);
				//短信用户
		$sql_0="SELECT *  FROM  steelhome_sms.`MESSAGE_USER_LIST` WHERE `MS_ID` ='".$params['smsid']."'";
		$MESSAGE_USER_LIST=$this->smssenddao->query($sql_0);
		foreach($MESSAGE_USER_LIST as $userKey=>$userValue)
		{
			$MESSAGE_USER_LIST_Arr[$userValue['MUL_USER_MOBILE']]=$userValue;
		}
		
		//print_R($this->smssenddao);
		//print_R($MESSAGE_USER_LIST);
		$sql="SELECT mid ,UserId,MobileNumber,OpenId   FROM steelhome_mapp.`wx_openIdInfo` WHERE Status=1 and  token='gh_ca60cff19200' ";
		$wx_openIdInfoarr=$this->smssenddao->query("$sql");
		
		foreach($wx_openIdInfoarr as $userKey=>$userValue)
		{
			$WxSendlist[$userValue['MobileNumber']]=$userValue;
		}

				foreach( $arr_p_users as $k=>$v)
			   {
				  
				   if(empty($MESSAGE_USER_LIST_Arr[$v['usermobile']]))//验证是否已发送相同手机号
				   {
					   //插入短信日志
					$params['usermobile']=$v['usermobile'];
					$params['sendtype']=$params['sendtype'].'套餐';
					// $p_inserid=$this->_dao->ins_sms_sendmessage($params);
					    if(empty($WxSendlist[$v['usermobile']]))//验证是否绑定微信
						{
								//插入短信
							 $params['smssendid']=$params['smsid'];
		
							 $this->smssenddao->ins_sms2send($params);
							
						}
						else
						{
							//file_put_contents("/tmp/xiangbin1.txt", "arr_android:".print_R($WxSendlist[$mul_user_mobile]['OpenId'],true)."\n",FILE_APPEND);
							// $url = WEIXIN_WEB_URL.'/gzjbindwx/SendMessage.php';
							// $post_data['openid'] = $WxSendlist[$v['usermobile']]['OpenId'];
							// $post_data['content'] = trim(urldecode($params['smscontent']));
							// $post_data1 = isset($post_data)? http_build_query($post_data) : '';
							//$res =$this->request_post($url, $post_data1);


							$insertsql="INSERT INTO steelhome_sms.`WX_SEND_LOG` ( `openid`, `content`, `mobile`, `msid`, `post_id`, `createdate`) VALUES ( '".$post_data['openid']."', '".$post_data['content']."', '".$v["usermobile"]."', '".$params['smsid'] ."', '-1', now())";
					        
							// $this->smssenddao->execute("set character  set 'utf8';");
		                    // $this->smssenddao->execute("set names 'utf8';");
							$this->smssenddao->execute($insertsql);
							// $this->smssenddao->execute("set character  set 'latin1';");
		                    // $this->smssenddao->execute("set names 'latin1';");
						}
					   
					
					 
				   }
				   else
				   {
					     echo "已发过";
				   }
				  
		     }
	}
	
	public function request_post($url = '', $param = '') {
        if (empty($url) || empty($param)) {
            return false;
        }
        $postUrl = $url;
        $curlPost = $param;
        $ch = curl_init();//初始化curl
        curl_setopt($ch, CURLOPT_URL,$postUrl);//抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0);//设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
        curl_setopt($ch, CURLOPT_POST, 1);//post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        $data = curl_exec($ch);//运行curl
        curl_close($ch);
        
        return $data;
    }
	
	//生成短信
	public function docreate_sms_mrhq_type_count(){
		$hqvarcode=$this->get_hqvarcode();
	    $citycode=$this->get_citycode();
		$upnosend_data=$this->_dao->get_upnosenddata();
		foreach($upnosend_data as $k=>$v){
			$arr_upnosend[$v['usermobile']][substr($v['smscode'],2,4)][$v['id']]=array('content'=>$v['content'],'city'=>substr($v['smscode'],0,2));
		}
		foreach($arr_upnosend as $k1=>$v1){
			$uservalid=$this->_dao->get_uservalid($k1);
			/*			//试用用户判断
			if($uservalid==0){
			$user_sendcount=$this->_dao->get_usersendcount($k1);
			if($user_sendcount>51){
				continue;
			 }
			}
			//试用用户判断 结束
			*/
			foreach($v1 as $kk=>$vv){//循环手机号
			$nosend_count=$this->_dao->get_nosendcount($k1,$kk);
			//echo $nosend_count."***";
			$j=0;
			$content=date("d")."日".$hqvarcode[$kk]."价格:";
			$sms_name_log_tmp=" ".$hqvarcode[$kk]."价格 ";
			$sms_name_log='';
			$detail_ids='';
			$splice=3;
			//循环品种
			foreach($vv as $kk1=>$vv1){
				$j++;
				$content.= $citycode[$vv1['city']].$vv1['content'];
				$sms_name_log.=$citycode[$vv1['city']];
				$detail_ids.=$kk1.",";
				$param_send['usermobile']=$k1;
				$param_send['smscontent']=$content;
				$param_send['sendtype']='价格行情';
				$param_user['sms_price_one']=JG_MOMEY;
				$param_user['usermobile']=$k1;
				$param_user['type']=2;
				if($j%$splice==0){
				//在这里判断当前账户有无费用没有,没有就不处理下面的操作。
				$curr_user_account=$this->_dao->get_useraccount($k1);
				if($curr_user_account>0 || $uservalid==0){
				//形成一条短信，插入短信发送表，同时更改订阅表的相应状态。
				//$content=$content."[钢之家]";
				$param_send['smscontent']=$content;
				$param_user['sms_name_log']=$sms_name_log.$sms_name_log_tmp;
				//内容超过60字符要处理下
				$smssendid=$this->_dao->ins_sms_sendmessage($param_send);
				//发送到正式短信发送表
				$param_send['smssendid']=$smssendid;
				$this->smssenddao->ins_sms2send($param_send);
				// 改变账号的当前金额
				if($uservalid==1){
				$this->_dao->upd_userbyusermobile($param_user);
				//记录消费日志
				$param_user['accountid']=$smssendid;
				$this->_dao->ins_user_account_log($param_user);
				}
				//试用会员减少试用条数
				 if($uservalid==0){
					 $this->_dao->upd_usertrycount($param_user);
				 }
				//改变订单表的发送标志
				$detail_ids=substr($detail_ids,0,-1);
                $this->_dao->upd_sms_orderdetail($detail_ids);
			   	echo $content."<br>";
			   	echo $sms_name_log.$sms_name_log_tmp."<br>";
				$content=date("d")."日".$hqvarcode[$kk]."价格:";
				}
			  }
			}
			if($j%$splice!=0 && $nosend_count<1){
				$curr_user_account=$this->_dao->get_useraccount($k1);
				//echo "www".$uservalid;
				if($curr_user_account>0  || $uservalid==0){
				//若不够拼成一条短信，但是其他已经全部发送，该条也发送。
				//$content=$content."[钢之家]";
				$param_send['smscontent']=$content;
				$param_user['sms_name_log']=$sms_name_log.$sms_name_log_tmp;
				//内容超过60字符要处理下
				$smssendid=$this->_dao->ins_sms_sendmessage($param_send);
				//发送到正式短信发送表
				$this->smssenddao->ins_sms2send($param_send);

				$param_send['smssendid']=$smssendid;
				//  缴费会员改变账号的当前金额
				if($uservalid==1){
				$this->_dao->upd_userbyusermobile($param_user);
				//记录消费日志
				$param_user['accountid']=$smssendid;
				$this->_dao->ins_user_account_log($param_user);
				}
				//试用会员减少次数
				 if($uservalid==0){
					 $this->_dao->upd_usertrycount($param_user);
				 }
				//改变订单表的发送标志
				$detail_ids=substr($detail_ids,0,-1);
                $this->_dao->upd_sms_orderdetail($detail_ids);
			    echo $sms_name_log.$sms_name_log_tmp."<br>";
			   	echo $content."<br>";
				}
			}
		}
	  }
	}
	//生成短信 结束
	// 按年付费的自助短信订阅开始
	public function docreate_sms_mrhq(){
		$hqvarcode=$this->get_hqvarcode();
	    $citycode=$this->get_citycode();
		$upnosend_data=$this->_dao->get_upnosenddata_typeyear();
		foreach($upnosend_data as $k=>$v){
			$arr_upnosend[$v['usermobile']][substr($v['smscode'],2,4)][$v['id']]=array('content'=>$v['content'],'city'=>substr($v['smscode'],0,2));
		}
		foreach($arr_upnosend as $k1=>$v1){
			$uservalid=$this->_dao->get_uservalid($k1);
			foreach($v1 as $kk=>$vv){//循环手机号
			$nosend_count=$this->_dao->get_nosendcount($k1,$kk);
			echo $nosend_count."***";
			$j=0;
			$content=date("d")."日".$hqvarcode[$kk]."价格:";
			$sms_name_log_tmp=" ".$hqvarcode[$kk]."价格 ";
			$sms_name_log='';
			$detail_ids='';
			$splice=3;
			//循环品种
			foreach($vv as $kk1=>$vv1){
				$j++;
				$content.= $citycode[$vv1['city']].$vv1['content'];
				$sms_name_log.=$citycode[$vv1['city']];
				$detail_ids.=$kk1.",";
				$param_send['usermobile']=$k1;
				$param_send['smscontent']=$content;
				$param_send['sendtype']='价格行情';
				$param_user['usermobile']=$k1;
				$param_user['type']=2;
				if($j%$splice==0){
				//形成一条短信，插入短信发送表，同时更改订阅表的相应状态。
				//$content=$content."[钢之家]";
				$param_send['smscontent']=$content;
				$param_user['sms_name_log']=$sms_name_log.$sms_name_log_tmp;
				//内容超过60字符要处理下
				$smssendid=$this->_dao->ins_sms_sendmessage($param_send);
				//发送到正式短信发送表
				if($smssendid==''){$smssendid=0;}
				$param_send['smssendid']=$smssendid;
				$this->smssenddao->ins_sms2send($param_send);
				//改变订单表的发送标志
				$detail_ids=substr($detail_ids,0,-1);
                $this->_dao->upd_sms_orderdetail($detail_ids);
			   	echo $content."<br>";
			   	echo $sms_name_log.$sms_name_log_tmp."<br>";
				$content=date("d")."日".$hqvarcode[$kk]."价格:";
			  }
			}
			if($j%$splice!=0 && $nosend_count<1){
				//若不够拼成一条短信，但是其他已经全部发送，该条也发送。
				$param_send['smscontent']=$content;
				$param_user['sms_name_log']=$sms_name_log.$sms_name_log_tmp;
				//内容超过60字符要处理下
				$smssendid=$this->_dao->ins_sms_sendmessage($param_send);
				//发送到正式短信发送表
				if($smssendid==''){$smssendid=0;}
				$param_send['smssendid']=$smssendid;
				$this->smssenddao->ins_sms2send($param_send);
				//改变订单表的发送标志
				$detail_ids=substr($detail_ids,0,-1);
               $this->_dao->upd_sms_orderdetail($detail_ids);
			    echo $sms_name_log.$sms_name_log_tmp."<br>";
			   	echo $content."<br>";
			}
		}
	  }
	}
	//每天晚上自动改变订阅表和订单明细表的状态
	//付款提醒页面
	public function myfeeremind($params){
}
//hlf
public function order_tip($params){
	//file_put_contents("/tmp/a.txt","--- gobackurl: url=http://".$_SERVER['SERVER_NAME']."/_v2app/Sms_".$params[orderid].".html\n",FILE_APPEND);
	//print_r($_SERVER['SERVER_NAME']);
	include('/etc/steelconf/config/config.php') ;
	$this->assign("APPID", APPID);
	$this->assign("MERCHID", MERCHID);
	//$this->assign("SUBJECT",$params['']);//商品标题
	$this->assign("HUIDIAO_URL",HUIDIAO_URL);
	$arr=$this->_dao->query("select * from sms_orderlist where orderid=".$params['orderid']);
	$params['howmuch']=$arr[0]['howmuch'];
	$this->assign("howmuch",$params['howmuch']);
	//print_r($params['howmuch']);exit;
	$this->assign("params", $params);
	$this->assign("return_url",$_SERVER['SERVER_NAME']);
	$this->assign("usermobile",$_SESSION['cookie_user']['usermobile']);
}
//hlf/2017/11/23-----短信订单支付
public function payOrderNum($params){
	//$this->assign('orderid',$params['orderid']);

	 //echo $params['howmuch'];
	$Dateday = date("YmdHis");
	$OrderNo1 ="30".sprintf("%s%s",$TestOrComfirm,$Dateday);
	$params['FuQianLaOrderid']=$OrderNo1;
	$OrderNumber=$this->_dao->getOrderNO2($params);
	$OrderNo2=$OrderNumber[0]['max(FuQianLaOrderidNo2)'];
	if($OrderNo2==''){
		$OrderNo2='0000';
	}else{
		$OrderNo2=sprintf("%04d",$OrderNo2+1);
	}
	$this->_dao->ins_orderNUm($OrderNo1,$OrderNo2,$params['orderid']);
	//print_r($OrderNumber);
	$Fuqianlaorderid=$OrderNo1.$OrderNo2;
	$this->assign("params",$params);
	//echo $Fuqianlaorderid;exit;

}
public function SpayOrderNum($params){
	$params['usermobile']=$_SESSION['cookie_user']['usermobile'];
	$status=$this->_dao->get_lastorderstatus_byuser($params);
	//print_r($status);exit;
	if($status!=0){
		echo 0;
		//echo "<script>alert('该订单已支付，请不要重复支付')</script>";
		//goURL("/_v2app/sms.php?view=order_tip");
		exit;
	}else{
		$this->payOrderNum($params);
		$arr=$this->_dao->get_orderNUm($params['orderid']);
		$Fuqianlaorderid=$arr[0]['FuQianLaOrderid'].$arr[0]['FuQianLaOrderidNo2'];
		$this->assign("params",$params);
		echo $Fuqianlaorderid;exit; 
	}
	
}
//付款完成
public function paysuccess($params){
	print_r($params);
}
//付款提醒动作
 public function domyfeeremind($params){
	  $params['operator']=$_SESSION['cookie_user']['username'];
	  $params['operatetime']=DATE('Y-m-d H:i:s');
	  $params['operatortype']=1;
	 $this->_dao->execute("INSERT INTO `sms_user_payreacord` (`usermobile` ,`username` ,`paymuch` ,`paytime` ,`operator` ,`operatetime` ,`operatortype` )VALUES ( '" . $params['usermobile'] . "', '" . $params['username'] . "', '" . $params['paytime'] . "', '" . $params['paytime'] . "', '" . $params['operator'] . "','" . $params['operatetime'] . "', '" . $params['operatortype'] . "')");
		//alert( "操作成功!" );
	   goURL("/_v2app/sms.php?view=myfeeremind_tip");
	 }
	public function docronupd_sms_orderdetail_all(){
	$this->_dao->cronupd_sms_orderdetail_all();
	$this->_dao->cronupd_sms_messagetype();
	}
	public function mrhq_city($params){
	$city_area=$this->maindao->getcitycode_area();
     foreach($city_area as $k=>$v){
    $arrcity_area[substr($v['cityid'],2)]=$v['city_keys'];
	 }
	$hqcitylist=$this->maindao->get_citybyvarid($params);

	 foreach($hqcitylist as $k=>$v ){
		$arr_c_a[$arrcity_area[$v['cityid']]][]=array('cityid'=>$v['cityid'],'cityname'=>$v['cityname']);
	 }
	 //print_r($arr_c_a);exit;
	$hqcitystr='<TABLE width="98%" border=0 cellPadding=0   cellSpacing=1 borderColorLight=#000000 borderColorDark=#ffffff style="font-size:12px;">';
	foreach($arr_c_a as $k1=>$v1 ){
	  if($k1!=''){
		$hqcitystr.='	<tr style=" line-height:26px; font-size:12px;"><td height="25" colspan=8><div style="width:730px;padding-left:2px;border:0px solid #CCCCCC;font-weight:bold;color:#474747;">'.$GLOBALS['STEEL_AREAS'][$k1].'</div></td></tr><tr><div  style="border:1px solid #CCCCCC;">';
	    $i=0;
		foreach($v1 as $k=>$v){
	if ($i%8==0){$hqcitystr.='</div></tr><tr><div style="border:1px solid #CCCCCC;">';}
	$hqcitystr.='<td height="25"><input name="city[]" type="checkbox" value='. $v['cityid'].' >&nbsp;'.$v['cityname']."</td>";
	 $i++;
			}
	}
	}
	$hqcitystr.='</div></tr></table>';
	echo $hqcitystr;exit;
	}
	//取得具体的小品种
	public function mrhq_littlevar($params){
	$hqvarlist=$this->maindao->get_hqvar_one($params);
	$hqcitystr='<table width="98%"  border="0" align="center" cellpadding="0" cellspacing="0" bgcolor="#FFFFF"><tr bgcolor="#FFFFFF">';
	$i=0;
	foreach($hqvarlist as $k=>$v){
	if ($i%3==0){$hqcitystr.="</tr>  <tr bgcolor='#FFFFFF'>";	}
	$hqcitystr.='<td height="25" width="300px;"><input  onclick="show_citylist('.$v['varid'].')" name="hqvar"  id="hqvar"  type="radio" value='.$v['varid'].'>'.$v['varname'].' </td>';
	 $i++;
	}
	$hqcitystr.='</tr></table>';
	//echo $hqcitystr;
	echo $hqcitystr;
	exit;
	}
  public function smsgcmanage( $params ){
        $params['gcpzid'] =  $params['gcpzid'] == '' ? 22 : $params['gcpzid'];
		$pzgcs = $this->rcdao->query( "SELECT DISTINCT sc_steelcom.* FROM research_center.sc_steelpz, research_center.sc_steelcom, research_center.sc_steelcom_pz WHERE sc_steelpz.sc_id = sc_steelcom.id AND sc_steelcom_pz.id = sc_steelpz.pz_id AND sc_steelcom_pz.id = $params[gcpzid] ORDER BY st_type asc, a_od" );
        $npzgcs = array();
		foreach( $pzgcs as $pzgc ){
			if( $pzgc['st_code'] != '' )
		    $npzgcs[$pzgc['area']][] = $pzgc;
		}
		$npzgcs2 = array();
		foreach( $GLOBALS['STEEL_AREAS'] as $v ){  //用于排序
		    $npzgc2[$v] = $npzgcs[$v];
		}
		foreach( $npzgc2 as &$areagc ){
		    $areagc = array_chunk( $areagc, 8 );
		}
        $this->assign( "pzgcs", $npzgc2 );
		$selected[$params['gcpzid']] = true;
		$pz = $this->_dao->getRow( "SELECT * FROM steelhome_smsmessage.sms_gc_pz WHERE gcpzid=$params[gcpzid]" );
		$this->assign( "spz", $pz );
		$this->assign( "selected", $selected );
		

	    $pzs = $this->_dao->query( "SELECT * FROM steelhome_smsmessage.sms_gc_pz WHERE status=1 ORDER BY od" );
		$pzs = array_chunk( $pzs, 3 );
		$this->assign( "pzs", $pzs );
		/*$biggcs = $this->rcdao->query( "SELECT * FROM sc_steelcom WHERE st_type = 5" );
		$biggcs = array_chunk( $biggcs, 9 );
		$this->assign( "biggcs", $biggcs );*/
	}

	public function dingyue( $params ){
	    $pz = $params['pzcode'];
		foreach( $params['gcs'] as $gc ){
			if( strlen( $gc ) == 3 ) $gc = "0" . $gc; 
		    $_SESSION['cookie_hqvarcity'][$gc . $pz] = $gc . $pz;
		    $_SESSION['cookie_smstype1'][$gc . $pz] = '钢厂调价';
		}
		goURL( "sms.php?view=mrhq_city_confirm" );
	}
	public function ajaxtogwc( $params ){
		if( $params['type'] == 1 ){
            $params['city'] = explode( ",", $params['tocity'] );
		    $params['hqvar'] = $params['pz'];
		    foreach ($params['city'] as $k=>$v){
		        $_SESSION['cookie_hqvarcity'][$v.$params['hqvar']]= $v.$params['hqvar'];
		        $_SESSION['cookie_smstype1'][$v.$params['hqvar']]='价格行情';
		    }
	        echo "1";
		}
		if( $params['type'] == 2 ){
	        $pz = $params['pzcode'];
			$params['gcs'] = explode( ",", $params['togc'] );
		    foreach( $params['gcs'] as $gc ){
			    if( strlen( $gc ) == 3 ) $gc = "0" . $gc; 
		          $_SESSION['cookie_hqvarcity'][$gc . $pz] = $gc . $pz;
		          $_SESSION['cookie_smstype1'][$gc . $pz] = '钢厂调价';
		     }
	         echo "1";
		}
		exit;
	}
 // 短信套餐订阅存储session
   public function ajaxaddcar($params){
		//$num=count($_SESSION['cookie_smspackage']);
		
		//echo "1#".$params['idstr']."#".$num;exit;
		//存购物车表---hlf/2018/1/17 星期三
		//print_r($params);exit;
		$smsdesc=(urldecode($params['contentstr']));
		$type=(urldecode($params['typestr']));
		$smaname=(urldecode($params['namestr']));
		$MobileNum=$_SESSION['cookie_user']['usermobile'];
		//$d1=date("Y-m-d H:m:s");
		//echo "d1----".$d1;
		$this->_dao->execute("insert into SmsDingYueCart(MobileNum,Type,smsid,smsname,smstype,smsdesc,smspricedesc) values('".$MobileNum."','1','".$params['idstr']."','".$smaname."','".$type."','".$smsdesc."','100元/条/年')");
		//$d2=date("Y-m-d H:m:s");
		//echo "d2----".$d2;

		$array=$this->_dao->getgwc_info($MobileNum);
		//$d3=date("Y-m-d H:m:s");
		//echo "d3----".$d3;
		$num=count($array);
		echo "1#".$params['idstr']."#".$num;exit;
		
	}
	public function ajaxdel( $params ){
		
		$this->domrhq_city_confirm_del( $params );
	    $this->mrhq_city_confirm( $params );
	}
    public function ajaxdel_from_car( $params ){
		
		$MobileNum=$_SESSION['cookie_user']['usermobile'];
		$this->_dao->execute("delete from SmsDingYueCart where smsid='".$params['id']."' and MobileNum='".$MobileNum."'");
		unset($_SESSION['cookie_smspackage'][$params['id']]);
	   goURL( "sms.php?view=mrhq_city_confirm");
	}
	public function doareacity($params){
			$citylist=$this->maindao->getcitycode_bykeys($params['city_keys']);
			foreach($citylist as $k=>$v){
			$str.='<a href="/_v2app/sms.php?view=hotsms&city='.$v['cityid'].$url_variety.'"> '.($v['cityname'])."</a> ";
		}
	echo $str;exit;
}
public function doareasteel($params){
	include_once ("./sms/global_areasteel.php");
	global $arr_hd;
	//钢厂使用固定的顺序排
	$sql="SELECT st_code,name  FROM research_center.sc_steelcom where binary name  in (".$GLOBALS['AREA_STEEL'][$params['area_keys']].") order by st_type  asc,a_od asc"; 
	$citylist=$this->rcdao->query($sql);
		foreach($citylist as $k=>$v){
			$str.='<a href="/_v2app/sms.php?view=hotsms&gc_name='.$v['st_code'].$url_variety.'"> '.$v['name']."</a> ";
		}
	echo $str;exit;
}
public function get_areasteel_sms($keys,$url_variety,$typeurlstr){
	//钢厂使用固定的顺序排
	$str = "";
	$sql="SELECT st_code,name  FROM research_center.sc_steelcom where binary name  in (".$GLOBALS['AREA_STEEL'][$keys].") order by st_type  asc,a_od asc"; 
	$citylist = [];
	if($GLOBALS['AREA_STEEL'][$keys])
	$citylist=$this->rcdao->query($sql);
	foreach($citylist as $k=>$v){
		$str.='<a href="/_v2app/sms.php?'.$typeurlstr.'&gc_name='.$v['st_code'].$url_variety.'"> '.$v['name']."</a> ";
	}
	return $str;
}
public function get_areacity_sms($keys,$url_variety,$typeurlstr){
	$citylist=$this->maindao->getcitycode_bykeys($keys);
			foreach($citylist as $k=>$v){
			if($v['cityid']!=='00C1' && $v['cityid']!=='9999'){
			$citystr.='<a href="/_v2app/sms.php?'.$typeurlstr.'&city='.$v['cityid'].$url_variety.'"> '.$v['cityname']."</a> ";
			}
		}
	return $citystr;
}
//发邮件给注册者
 function sendemailmessage($m,$u,$un,$email)
{
include_once('./public/Class.Smtp.php');
$d=date('Y-m-d H:i:s');
/*$m='13166436900';
$u='liuyl';
$un='刘亚利';
*/
$str=$m."#".$u."#".$un."#".$d;
$a=$this->encrypt('MCRYPT_3DES', $str) ;
$add="尊敬的".$un."，您好! 感谢您注册钢之家短信频道! 您的手机号为".$m.",用户名为".$u."。请复制下面的激活链接到浏览器进行访问，以激活你的账户。
激活链接:".APP_URL_WWW."/_v2app/sms.php?action=checkmail&par=".$a;
$message1=$add.$message1;
$smtpserver = "mail.steelhome.cn";//SMTP服务器 
$smtpserverport =25;//SMTP服务器端口 
$smtpusermail = "<EMAIL>";//SMTP服务器的用户邮箱 
//$smtpemailto = "<EMAIL>";//发送给谁 
$smtpemailto = $email;//发送给谁
$smtpuser = "job";//SMTP服务器的用户帐号 
$smtppass = "50581010";//SMTP服务器的用户密码 
$smtp = new smtp($smtpserver,$smtpserverport,true,$smtpuser,$smtppass);//这里面的一个true是表示使用身份验证,否则不使用身份验证. 
$smtp->debug = TRUE;//是否显示发送的调试信息 
$smtp->sendmail($smtpemailto, $smtpusermail,"钢之家短信订阅服务账户激活邮件",$message1, $mailtype); 
}
function checkmail($params)
{
 // echo "<pre>";
  //print_r($params);
  $par=$this->decrypt('MCRYPT_3DES',$params['par']);
  $arr_par=explode('#',$par);
  $d1=strtotime($arr_par[3]);
  $d2=strtotime(date('Y-m-d H:i:s'));
  $dd=round(($d2-$d1)/3600/24);
 /* if($dd>1){
   alert('您的验证邮件已失效.');
    //goURL( "sms.php?view=hotsms");
  }
  */
  // 不判断验证邮件的有效期,永远有效,激活邮件一天之内有效 /**/

  //核对账户信息
  $parnew['usermobile']=$arr_par[0];
  $arruserinfo=$this->_dao->get_userinfo_byuname($parnew);
 if(($arruserinfo[0]['uname']=$arr_par[1])&&($arruserinfo[0]['username']=$arr_par[2])){
  // 激活账户
			 $this->_dao->upd_smsuser($arruserinfo[0]['usermobile']);
			   alert('您的账户已经激活,可以使用！');
               //goURL( "sms.php?view=hotsms");
  }

}
//加密字符串函数
function encrypt($key, $plain_text) {
$plain_text = trim($plain_text);
$iv = substr(md5($key), 0,mcrypt_get_iv_size(MCRYPT_CAST_256,MCRYPT_MODE_CFB));
$c_t = mcrypt_cfb (MCRYPT_CAST_256, $key, $plain_text, MCRYPT_ENCRYPT, $iv);
return trim(chop(base64_encode($c_t)));
}
//解密字符串函数
function decrypt($key, $c_t) {
$c_t = trim(chop(base64_decode($c_t)));
$iv = substr(md5($key), 0,mcrypt_get_iv_size (MCRYPT_CAST_256,MCRYPT_MODE_CFB));
$p_t = mcrypt_cfb (MCRYPT_CAST_256, $key, $c_t, MCRYPT_DECRYPT, $iv);
return trim(chop($p_t));
}
public function sendrand_zc($params){
	//取消发送短信接口
	//echo "error";
	//exit;
	//取消发送短信接口 如恢复请删除以上代码
	//exit;
	$rand=rand(100000,999999);
	//$rand = $params['rand'];
	 $otime = date( "Y-m-d H:i:s", time() - 1200 );	
    $oldrand=$this->_dao->getOne( "SELECT randvalue FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$otime' " );
	if($oldrand !=""){
	   $rand=$oldrand;
	}
	$_SESSION["EN_VALIDATE"]=$rand;
    if (!$this->isValidChineseMobileNumber($params['usermobile'])){
        echo "b";
        exit;
    }
	$param_send['usermobile']=$params['usermobile'];
		//hlf start 2018/9/6------为百家诚信短信验证添加memcache
		$expire = 120; 
		//$_SESSION['helloweba_math'] = $opvalue;

		$memcache = new Memcache; 
		$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
			if($params ['usermobile']!=""){
				$memcache->set($_REQUEST['PHPSESSID']."_helloweba_math"."_".$params ['usermobile'],$rand,MEMCACHE_COMPRESSED,$expire);
			}
		$memcache->close();
		//hlf end
		$param_send ['smscontent'] = "尊敬的用户,您好!注册验证码为" . $rand . ",如有操作疑问,请联系钢之家,电话021-50581010.";
		file_put_contents("/tmp/code.txt",print_r($rand,true)."\n",FILE_APPEND);
		$param_send ['smssendid'] = 0;
		//hlf start 2018/9/19--- 酒店预订修改
		if($params['type']=="hotel"){
			$param_send ['smscontent'] = "尊敬的用户,您好!手机验证码为" . $rand . ",如有操作疑问,请联系钢之家,电话021-50581010.";
			$time = date ( "Y-m-d H:i:s", time () - 3600 * 24 * 1 );
			$max_num=20;//发送次数最多为多少次；
		}else{
			$time = date ( "Y-m-d H:i:s", time () - 3600 * 24 * 7 );
			$max_num=5;
		}
		
		//hlf end
		if (trim ( $params ['usermobile'] ) == "")
		{
			echo "1";
			exit ();
		}

	if(in_array($params['usermobile'],$GLOBALS['BLACK_NO'])){
		echo "b";
		exit;
	}

	$ip = getfromip();
	if($ip!="**************" && $ip!="**************" && $ip!="*************")
	{
		$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
		if( $count >$max_num && $params['usermobile']!='18017891461'){
			echo "1";
			exit;
		}
	}
	$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
	if( $count >$max_num && $params['usermobile']!='18017891461' ){
		echo "1";
		exit;
	}
 

/*	if($params['type']==2){
 	 
		$AppKey = SMS_APPKEY;
		$AppSecret = SMS_APPSECRTE;
		$MobileNumber = $params['usermobile'];
		$MobileMess = $param_send['smscontent'];
		$CuDate = date("Y-m-d");   
			//签名
		$Sign = md5($AppKey.$AppSecret.$CuDate);
		
		//发送请求
		$arr=array('action' => 'SendSms', 'AppKey' => $AppKey, 'Sign' => $Sign, 'MobileNumber' => $MobileNumber, 'MobileMess' => $MobileMess);
		$client = new HttpClient(SMS_SITE);
		$client->setDebug(false);
		$client->setMaxRedirects(20);
		if (!$client->post(SMS_URL, $arr)) {
		  return array("resp_code" => "S000001", "resp_msg" => "网络连接错误");
		  echo "1";
		  exit;
		}else{
		  //echo $client->getContent();
		}

	}

	else{*/
		$this->smssenddao->ins_sms2send($param_send);
	//}
	$now = date( "Y-m-d H:i:s" );
	$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now'  " );
	//在这里调用短信接口，发送一条短信
	//echo $rand;exit;
	echo "0"; exit;
	
}

    /**
     * 验证中国大陆手机号码，排除国外号码和非法手机号
     * Created by zfy.
     * Date:2025/3/13 10:29
     * @param $phoneNumber
     * @return bool
     */
    function isValidChineseMobileNumber($phoneNumber) {
        // 移除所有非数字字符
        $phoneNumber = preg_replace('/\D/', '', $phoneNumber);

        // 如果以国际区号+86开头，去掉区号
        if (strpos($phoneNumber, '86') === 0 && strlen($phoneNumber) > 10) {
            $phoneNumber = substr($phoneNumber, 2);
        }

        // 验证手机号是否为11位
        if (strlen($phoneNumber) != 11) {
            return false;
        }

        // 验证手机号是否以1开头
        if (substr($phoneNumber, 0, 1) != '1') {
            return false;
        }

        // 中国大陆手机号第二位规则验证
        $secondDigit = substr($phoneNumber, 1, 1);
        if (!in_array($secondDigit, ['3', '4', '5', '6', '7', '8', '9'])) {
            return false;
        }

        // 更严格的验证：使用正则表达式匹配运营商号段
        // 移动：134-139, 147-148, 150-152, 157-159, 165, 172, 178, 182-184, 187-188, 195, 197-198
        // 联通：130-132, 145-146, 155-156, 166, 175-176, 185-186, 196
        // 电信：133, 149, 153, 173-174, 177, 180-181, 189, 191, 193, 199
        // 广电：192
        // 虚拟运营商：170-171
//		$pattern = '/^1(3[0-9]|4[5-9]|5[0-3,5-9]|6[5-6]|7[0-8]|8[0-9]|9[1,3,5-9])\d{8}$/';

//		return preg_match($pattern, $phoneNumber) === 1;
        return true;
    }
/**用于会议抽奖在11月1日停止发送短信**/
//add by zk for 投票验证
public function sendrand_cjyzm($params){
	print_r($_SESSION['EN_VALIDATE']);
	
}
public function sendrand_cj($params){
	$rand=rand(1000,9999);
	$params['usermobile']=$params['mobile'];
	 //if(strtotime($otime)>strtotime('2016-5-2 00:00:00'))//时间超过2号就不再发送了
	 //{
		 //echo "time is over";//"尊敬的会员,您好!投票时间已过,如有操作疑问,请联系钢之家,电话021-50581010.";
		 //exit;
	 //}
	//$rand = $params['rand'];
	 /*$otime = date( "Y-m-d H:i:s", time() - 1200 ); 
	 if(strtotime($otime)>strtotime('2015-11-2 00:00:00'))//时间超过2号就不再发送了
	 {
		 echo "400";//"尊敬的会员,您好!投票时间已过,如有操作疑问,请联系钢之家,电话021-50581010.";
		 exit;
	 }
    $oldrand=$this->_dao->getOne( "SELECT randvalue FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$otime' " );
	if($oldrand !=""){
	   $rand=$oldrand;
	}
	*/
	$_SESSION["EN_VALIDATE"]=$rand;
	$param_send['usermobile']=$params['usermobile'];
	$param_send['smscontent']="尊敬的会员,您好!投票验证码为".$rand.",如有操作疑问,请联系钢之家会务组.";
	$param_send['smssendid']=0;
	$time = date( "Y-m-d H:i:s", time() - 3600*24*7 );

	if(trim($params['usermobile'])=="")
	{
		echo "mobile empty";
		exit;
	}

	if(in_array($params['usermobile'],$GLOBALS['BLACK_NO'])){
		echo "black list";
		exit;
	}
	
	$ip = getfromip();
	if($params['usermobile']!='13085554576'){
		/*if($ip!="**************" && $ip!="**************" && $ip!="*************")
		{
			$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
			if( $count >= 5 && $params[usermobile]!='18017891461'){
				echo "same ip send more";
				exit;
			}
		}*/
		
		$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
		if( $count >5 ){
			echo "this mobile is send more";
			exit;
		}
	}
	
	$this->smssenddao->ins_sms2send($param_send);
	 
	$now = date( "Y-m-d H:i:s" );
 
	$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='".$params['usermobile']."', randvalue='$rand', fromip='$ip', createtime='$now'  " );
	//在这里调用短信接口，发送一条短信
	echo "1";
	exit;	
}
		// add by zk for 投票验证
		
// added by zhudahua start 2016/01/19
	public function sendrand_wxcard($params)
	{
		$rand = rand ( 1000, 9999 );
		$params ['usermobile'] = $params ['mobile'];
		// if(strtotime($otime)>strtotime('2015-11-2 00:00:00'))//时间超过2号就不再发送了
		// {
		// echo "time is over";//"尊敬的会员,您好!投票时间已过,如有操作疑问,请联系钢之家,电话021-50581010.";
		// exit;
		// }
		// $rand = $params['rand'];
		/*
		 * $otime = date( "Y-m-d H:i:s", time() - 1200 );
		 * if(strtotime($otime)>strtotime('2015-11-2 00:00:00'))//时间超过2号就不再发送了
		 * {
		 * echo "400";//"尊敬的会员,您好!投票时间已过,如有操作疑问,请联系钢之家,电话021-50581010.";
		 * exit;
		 * }
		 * $oldrand=$this->_dao->getOne( "SELECT randvalue FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$otime' " );
		 * if($oldrand !=""){
		 * $rand=$oldrand;
		 * }
		 */
		$_SESSION ["EN_VALIDATE"] = $rand;
		$param_send ['usermobile'] = $params ['usermobile'];
		$param_send ['smscontent'] = $rand . "为您此次的登陆验证码，欢迎您制作个性化的节日贺卡。";
		$param_send ['smssendid'] = 0;
		$time = date ( "Y-m-d H:i:s", time () - 3600 * 24 );
		
		if (trim ( $params ['usermobile'] ) == "")
		{
			echo "mobile empty";
			exit ();
		}
		if (in_array ( $params ['usermobile'], $GLOBALS ['BLACK_NO'] ))
		{
			echo "black list";
			exit ();
		}
		$ip = getfromip ();
		if ($params ['usermobile'] != '13085554576')
		{
			/*
			 * if($ip!="**************" && $ip!="**************" && $ip!="*************")
			 * {
			 * $count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
			 * if( $count >= 5 && $params[usermobile]!='18017891461'){
			 * echo "same ip send more";
			 * exit;
			 * }
			 * }
			 */
			
			$count = $this->_dao->getOne ( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$time' " );
			if ($count > 5)
			{    
     			// $arr[0]="this mobile is send more";
				  // json_encode($arr);
				// echo json_encode($arr);
				echo "this mobile is send more";
				
				exit ();
			}
		}
		
		$this->smssenddao->ins_sms2send($param_send);
		
		$now = date ( "Y-m-d H:i:s" );
		
		$this->_dao->execute ( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now'  " );
		// 在这里调用短信接口，发送一条短信
		echo "1";
		exit ();
	}
//added by zhudahua end 2016/01/19

//获取代表证验证码
public function sendrand_dbz($params){
	//if(isset($_GET['callback'])) {echo 1;exit;}
	$rand=rand(100000,999999);
	$params ['usermobile'] = $params ['mobile'];
	$_SESSION ["EN_VALIDATE"] = $rand;
	$param_send ['usermobile'] = $params ['usermobile'];
	$param_send ['smscontent'] = "您的签到验证码为".$rand . "。";
	$param_send ['smssendid'] = 0; //exit;
	$time = date ( "Y-m-d H:i:s", time () - 120 );
	
	if (trim ( $params ['usermobile'] ) == "")
	{
		echo "mobile empty";
		exit ();
	}
	if (in_array ( $params ['usermobile'], $GLOBALS ['BLACK_NO'] ))
	{
		echo "blacklist";
		exit ();
	}
	$ip = getfromip ();
	
	$count = $this->_dao->getOne ( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$time' " );
	if ($count > 2)
	{
		echo "maxsendtimes";
		exit ();
	}
	
	$this->smssenddao->ins_sms2send($param_send);
	
	$now = date ( "Y-m-d H:i:s" );
	
	$this->_dao->execute ( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now' " );
	// 在这里调用短信接口，发送一条短信
	echo "rand:".$rand;
	exit ();
}

//获取手机绑定微信验证码
public function sendrand_bindwx($params){
	//if(isset($_GET['callback'])) {echo 1;exit;}
	$rand=rand(100000,999999);
	$params ['usermobile'] = $params ['mobile'];
	$_SESSION ["EN_VALIDATE"] = $rand;
	$param_send ['usermobile'] = $params ['usermobile'];
	$param_send ['smscontent'] = "欢迎绑定钢之家微信公众号，您的绑定验证码为".$rand . "。";
	$param_send ['smssendid'] = 0; //exit;
	$time = date ( "Y-m-d H:i:s", time () - 120 );
	
	if (trim ( $params ['usermobile'] ) == "")
	{
		echo "mobile empty";
		exit ();
	}
	if (in_array ( $params ['usermobile'], $GLOBALS ['BLACK_NO'] ))
	{
		echo "blacklist";
		exit ();
	}
	$ip = getfromip ();
	
	$count = $this->_dao->getOne ( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$time' " );
	if ($count > 2)
	{
		echo "maxsendtimes";
		exit ();
	}
	
	$this->smssenddao->ins_sms2send($param_send);
	
	$now = date ( "Y-m-d H:i:s" );
	
	$this->_dao->execute ( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now' " );
	// 在这里调用短信接口，发送一条短信
	echo "rand:".$rand;
	exit ();
}

//获取知一网手机验证码
public function sendrand_zyw($params){
	//if(isset($_GET['callback'])) {echo 1;exit;}
	$rand=rand(100000,999999);
	$params ['usermobile'] = $params ['mobile'];
	$_SESSION ["EN_VALIDATE"] = $rand;
	$param_send ['usermobile'] = $params ['usermobile'];
	$param_send ['smscontent'] = $params ['message'].$rand . "。";
	$param_send ['smssendid'] = 0; //exit;
	$time = date ( "Y-m-d H:i:s", time () - 120 );
	
	if (trim ( $params ['usermobile'] ) == "")
	{
		echo "mobile empty";
		exit ();
	}
	if (in_array ( $params ['usermobile'], $GLOBALS ['BLACK_NO'] ))
	{
		echo "blacklist";
		exit ();
	}
	$ip = getfromip ();
	
	$count = $this->_dao->getOne ( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$time' " );
	if ($count > 2)
	{
		echo "maxsendtimes";
		exit ();
	}
	
	$this->smssenddao->ins_sms2send($param_send);
	
	$now = date ( "Y-m-d H:i:s" );
	
	$this->_dao->execute ( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now' " );
	// 在这里调用短信接口，发送一条短信
	echo "rand:".$rand;
	exit ();
}

//获取知一网手机验证码
public function sendsms_general($params){ 
	if(empty($params ['time'])) $time = date ( "Y-m-d H:i:s", time () - 180 );
	else $time = date ( "Y-m-d H:i:s", $params ['time'] );
	$mobiles = explode(',', $params ['mobile']);
	$count=0;
	$snum=0;
	$ret="";
	$ip = getfromip ();
	foreach($mobiles as $id=>$mob){
		$param_send ['usermobile'] = $mob;
		$param_send ['smscontent'] = $params ['content'];
		$param_send ['smssendid'] = 0;
		if (trim ( $mob ) == "")
		{
			$ret.=" fail:".$mob."- mobile empty";
			$count++;
			continue;
		}
		if (in_array ( $mob, $GLOBALS ['BLACK_NO'] ))
		{
			$ret.=" fail:".$mob."- blacklist ";
			$count++;
			continue;
		}
		$count = $this->_dao->getOne ( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$mob' AND createtime > '$time' " );
		if ($count > 3)
		{
			$ret.=" fail:".$mob."- maxsendtimes";
			$count++;
			continue;
		}
		$this->smssenddao->ins_sms2send($param_send);
		$now = date ( "Y-m-d H:i:s",time() );
		$this->_dao->execute ( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now' ");
		// 在这里调用短信接口，发送一条短信
		$snum++;
	}
	
	if(empty($ret)) $ret=$snum.($snum>1?" messages":" message")." send success!";
	else $ret="Total failed:".$count.".".$ret;
	echo $ret;
	exit ();
}
//hlf---2017/12/12
public function login($params){
	// print_r($params);
	$urlstr= urldecode($params['urlstr']);
	$this->assign("urlstr",$urlstr);
}

public function sendrand_bd($params){
	//exit;
	$rand=rand(100000,999999);
	//$rand = $params['rand'];
	$_SESSION["EN_VALIDATE"]=$rand;
	$param_send['usermobile']=$params['usermobile'];
	$param_send['smscontent']="尊敬的用户,您好!验证码为".$rand.",如有操作疑问,请联系钢之家,电话021-50581010.";
	$param_send['smssendid']=0;
	$time = date( "Y-m-d H:i:s", time() - 3600*24 );

	if(trim($params['usermobile'])=="")
	{
		echo "1";
		exit;
	}

	if(in_array($params['usermobile'],$GLOBALS['BLACK_NO'])){
		echo "b";
		exit;
	}

	$ip = getfromip();
	if($ip!="**************" && $ip!="**************" && $ip!="*************")
	{
		$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
		if( $count >= 3 ){
			echo "1";
			exit;
		}
	}
	$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$time' " );
	if( $count > 3 ){
		echo "1";
		exit;
	}
	$this->smssenddao->ins_sms2send($param_send);
	$now = date( "Y-m-d H:i:s" );
	$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now'  " );
	//在这里调用短信接口，发送一条短信
	echo $rand;exit;
}

    public function oyregister($params){
        //exit;
        $rand=rand(100000,999999);
        //$rand = $params['rand'];
        $_SESSION["OYREGISTER_CODE"]=$rand;
        $param_send['usermobile']=$params['usermobile'];
        $param_send['smscontent']="尊敬的用户,您好!验证码为".$rand.",如有操作疑问,请联系钢之家,电话021-50581010.";
        $param_send['smssendid']=0;
        $time = date( "Y-m-d H:i:s", time() - 3600*12 );

        if(trim($params['usermobile'])=="")
        {
            echo "1";
            exit;
        }

        $ip = getfromip();
        if($ip!="**************" && $ip!="**************" && $ip!="*************")
        {
            $count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
            if( $count >= 5 ){
                echo "1";
                exit;
            }
        }
        $count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
        if( $count > 5 ){
            echo "1";
            exit;
        }
        $this->smssenddao->ins_sms2send($param_send);
        $now = date( "Y-m-d H:i:s" );
        $this->_dao->execute( "INSERT INTO sms_randkey SET mobile='".$params['usermobile']."', randvalue='$rand', fromip='$ip', createtime='$now'  " );
        //在这里调用短信接口，发送一条短信
        echo $rand;
        exit;
    }

    public function meetingregister($params){
        //exit;

        $rand=rand(100000,999999);
        //$rand = $params['rand'];
        $_SESSION["MEETINGREGISTER_CODE"]=$rand;
        $param_send['usermobile']=$params['usermobile'];
        $param_send['smscontent']="尊敬的用户,您好!验证码为".$rand.",如有操作疑问,请联系钢之家,电话021-50581010.";
        $param_send['smssendid']=0;
        $time = date( "Y-m-d H:i:s", time() - 3600*12 );

        if(trim($params['usermobile'])=="")
        {
            echo "1";
            exit;
        }

        $sql = "select mid from `adminuser`,`member` where `adminuser`.mid=`member`.mbid and `adminuser`.mobil='".$params['usermobile']."' and `adminuser`.state=1  limit 1 ";
        $mid = $this->maindao->getOne($sql);
        if($mid!=null){
            echo "2";
            exit;
        }

        $ip = getfromip();
        if($ip!="**************" && $ip!="**************" && $ip!="*************")
        {
            $count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
            if( $count >= 6 ){
                echo "1";
                exit;
            }
        }
        $count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
        if( $count > 6 ){
            echo "1";
            exit;
        }

        $this->smssenddao->ins_sms2send($param_send);
        $now = date( "Y-m-d H:i:s" );
        $this->_dao->execute( "INSERT INTO sms_randkey SET mobile='".$params['usermobile']."', randvalue='$rand', fromip='$ip', createtime='$now'  " );
        //在这里调用短信接口，发送一条短信
        echo $rand;
        exit;
    }

public function  sendsjcglwgsms($params)
{
	$ip = getfromip();
	$handle_1=fopen("/usr/local/www/oa.steelhome.cn/admincp/lanipnew.txt","rb");
		 $lanip_1=fread($handle_1,8192);
		 $iplist=explode('#', $lanip_1);
		$iplist[]="127.0.0.1";
		 //print_R($iplist);
		 if(!in_array($ip,$iplist))
		 {
			 echo "您不在局域网内，您不能发送微信消息，您的IP地址为：".$ip;exit;
		 }

      //add by zfy started 2018/2/1 for 短信推送
	require_once ('/etc/steelconf/config/config.php');
	require_once ('/usr/local/www/libs/xgpush/src/XingeApp.php');
	//add by zfy started 2018/2/1 for 短信推送

	        $data_date2=date("Y-m-d");
		 //$data_date2=date("Y-m-d",strtotime('2019-09-20'));
		$dates_a2=$this->get_xhdate($data_date2);
		$data_date2=$dates_a2['today'];
		$data_date=date("Y-m-d",strtotime("-1 day",strtotime($data_date2)));
		$dates_a=$this->get_xhdate($data_date);
		$data_date=$dates_a['today'];
        $data_date=date("Y-m-d",strtotime($data_date));
		$day=date("j",strtotime($data_date2));
	   $type=$params['type']==''?'1':$params['type'];
	   $leixing=$params['leixing']==''?'2':$params['leixing'];
	  //短信id；

	   //print_r($this->smssenddao);exit;

	  if($leixing==1)
	  {
		$ms_id=5750;
		 $list = $this->maindao->query("SELECT * FROM `SJCGIndex` where DType=0 and CityName='西部综合' and Date='".$data_date."' or Date='".$data_date2."' ");
		foreach($list as $k=>$v){
		   $list[$v['Date']]=$v;
	      }
		$Monday =$this->firstOfWeek($data_date2);
		$lastMonday =date("Y-m-d",strtotime('-7 day', strtotime($Monday)));

		$lastweekindex = $this->maindao->getrow("SELECT `index`  FROM `SJCGIndex` where DType=1 and CityName='西部综合' and Date='".$lastMonday."' ");//上周均值（DType=1 CityName=西部综合  Date=上周开始日期）
		$weekindex = $this->maindao->getrow("SELECT `index` FROM `SJCGIndex` where DType=1 and CityName='西部综合' and Date='".$Monday."'");//本周均值

		$content= $day.'日陕晋川甘论坛西部螺纹钢综合指数'.round($list[$data_date2]['index']).'，与昨日'.$this->zhangdie1(round($list[$data_date2]['index'])-round($list[$data_date]['index'])).'；本周指数均值为'.round($weekindex['index']).'，较上周'.$this->zhangdie1(round($weekindex['index']-$lastweekindex['index'])).'。详情咨询15800777960';
		}
	  else if($leixing==2)
	  {
		$ms_id=5751;
		$list = $this->maindao->query("SELECT * FROM `SJCGIndex` where DType=0 and type='".$type."' and CityName in('西部综合','成都','重庆','西安','郑州','兰州','绵阳','宝鸡') and Date='".$data_date2."' ");
		foreach($list as $k=>$v){
			$list[$v['CityName']]=round($v['index']);
		}
		$list1 = $this->maindao->query("SELECT * FROM `SJCGIndex` where DType=0  and type='".$type."' and CityName in('西部综合','成都','重庆','西安','郑州','兰州','绵阳','宝鸡') and Date='".$data_date."' ");
		foreach($list1 as $k=>$v){
			$list1[$v['CityName']]=round($v['index']);
		}
		$jszgarr=array(
			'1'=>'螺纹钢',
			'2'=>'盘螺',
			'3'=>'高线'
		);

		$content= $day.'日西部(陕晋川甘论坛)'.$jszgarr[$type].'价格指数：西部综合'.round($list['西部综合']).$this->zhangdie1(round($list['西部综合']-$list1['西部综合'])).'，成都'.round($list['成都']).$this->zhangdie1(round($list['成都']-$list1['成都'])).'，重庆'.round($list['重庆']).$this->zhangdie1(round($list['重庆']-$list1['重庆'])).'，西安'.round($list['西安']).$this->zhangdie1(round($list['西安']-$list1['西安'])).'，郑州'.round($list['郑州']).$this->zhangdie1(round($list['郑州']-$list1['郑州'])).'，兰州'.round($list['兰州']).$this->zhangdie1(round($list['兰州']-$list1['兰州'])).'，绵阳'.round($list['绵阳']).$this->zhangdie1(round($list['绵阳']-$list1['绵阳'])).'，宝鸡'.round($list['宝鸡']).$this->zhangdie1(round($list['宝鸡']-$list1['宝鸡'])).'';
	  }
	  
	  if(empty($ms_id))
	  {
		ob_clean();
		$arr['Status']="0";
		$arr['Success']="0";
		$arr['Message']="您发送的参数有误，请检查";
	    echo json_encode($arr);exit;
	  }
      				//短信状态取消  及  到期会员  及  还没有日期者
				$time =date("Y-m-d",time());
				
				$sql_id="SELECT user_id  FROM `message_user` WHERE `user_sms_status` = 0 or `user_sms_status2` != 1 OR  `user_sms_end_date` < '$time' OR `user_sms_end_date` IS NULL  ";
				
				$arr_no_user_id=$this->maindao->query("$sql_id");
				$not_in_id="0";
				$not_effect_users = array();
				for($i=0; $i<count($arr_no_user_id); $i++){
					//$not_in_id=$not_in_id.",".$arr_no_user_id[$i]["user_id"];
					$not_effect_users[$arr_no_user_id[$i]["user_id"]] = 1;
				}
				//短信用户
				$sql_0="SELECT *  FROM `MESSAGE_USER_LIST` WHERE `MS_ID` = $ms_id and deliver='1'";
				
				$array_user_temp=$this->smssenddao->query($sql_0);
				
				$array_user=array();
				$jtemp=0;
				for($i=0;$i<count($array_user_temp);$i++){
					
					$mul_id=$array_user_temp[$i]["MUL_ID"];
					$mul_mid=$array_user_temp[$i]["MUL_MID"];
					$mul_userid=$array_user_temp[$i]["MUL_USERID"];
					$mul_user_mobile=$array_user_temp[$i]["MUL_USER_MOBILE"];	
					if(!isset($not_effect_users[$mul_userid])){
						$array_user[$jtemp]["MUL_ID"]=$mul_id;
						$array_user[$jtemp]["MUL_MID"]=$mul_mid;
						$array_user[$jtemp]["MUL_USERID"]=$mul_userid;
						$array_user[$jtemp]["MUL_USER_MOBILE"]=$mul_user_mobile;
						$jtemp++;
					}																	
				}			
			    //print_r($array_user);
				//日志
				$mpl_post_count=count($array_user);
				//$mpl_post_count=0;
				
				//判断是否推送
				//echo  $content;exit;
				
				$sql_m="SELECT * FROM `MESSAGE` WHERE `MS_ID` = $ms_id";
				$arr_m=$this->smssenddao->getrow($sql_m);
				$ms_name=$arr_m["MS_NAME"];
				$ms_type=$arr_m["MS_TYPE"];
				$str_ms_dept=$arr_m["MS_DEPT"];
				$str_ms_dept_name=$arr_m["MS_DEPT_NAME"];
				$str_ms_aid=$arr_m["MS_AID"];
				$str_ms_admin_name=$arr_m["MS_ADMIN_NAME"];
				$sql_2="INSERT INTO MESSAGE_POST_LOG(`MS_ID`, `MS_NAME`, `MS_TYPE`, `MS_CONTENT`, `MS_AID`, `MS_ADMIN_NAME`,`MS_DEPT`,`MS_DEPT_NAME`, `MPL_DATE`, `USER_COUNT`,`MPL_POST_COUNT`) VALUES ('$ms_id', '$ms_name', '$ms_type', '$content', '$str_ms_aid', '$str_ms_admin_name', '$str_ms_dept', '$str_ms_dept_name', NOW(), '$mpl_post_count','0' );";			
				$this->smssenddao->Execute($sql_2);
		
			
				$smstitle = base64_encode($ms_name);
				$contentts = base64_encode($content);
	$android_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=1");

	$android_hd_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=2");

	$ios_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=3");

	$ios_hd_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=4");

				/*$arr_android = XingeApp::PushTagAndroid($APPID_FOR_ANDROID, $SECRETKEY_FOR_ANDROID, $smstitle, $contentts, $ms_id);
				$arr_android_hd = XingeApp::PushTagAndroid($APPID_FOR_ANDROID_HD, $SECRETKEY_FOR_ANDROID_HD, $smstitle, $contentts, $ms_id);

				$arr_ios_pad = XingeApp::PushTagIos($APPID_FOR_IOS_PAD,$SECRETKEY_FOR_IOS_PAD, $contentts,$ms_id,$smstitle, XingeApp::IOSENV_PROD);

				$arr_ios = XingeApp::PushTagIos($APPID_FOR_IOS,$SECRETKEY_FOR_IOS, $contentts,$ms_id,$smstitle, XingeApp::IOSENV_PROD);*/
			
				//add by zfy ended 2018/1/8 for 短信推送
				
				$sql_log="INSERT INTO `MESSAGE_ADMIN_POST_LOG` (`MAPL_ADMIN_ID`, `MAPL_ADMIN_NAME`, `MAPL_MS_CONTENT`, `MAPL_POST_COUNT`, `MAPL_TYPE`, `MAPL_MS_ID`, `MAPL_DATE`) VALUES ('$str_ms_aid', '$str_ms_admin_name', '$content', '$mpl_post_count', '订阅发布', '$ms_id', NOW() )";
				$this->smssenddao->Execute($sql_log);			
						
				
				//更新
				$sql_update="UPDATE `MESSAGE` SET MS_AID='$str_ms_aid', MS_ADMIN_NAME='$str_ms_admin_name' , MS_CONTENT='$content', `MS_LAST_DATE` = NOW() WHERE `MESSAGE`.`MS_ID` = $ms_id LIMIT 1 " ;
				//echo $sql_update."<br>";								
				$this->smssenddao->Execute($sql_update);
				
				$sql_md="SELECT MS_CONTENT FROM `MESSAGE_DETAIL` WHERE `MS_ID` = $ms_id group by `MS_ID` ";				
				$arr_temp=$this->smssenddao->query($sql_md);
				$MS_CONTENT_MD=$arr_temp[0]["MS_CONTENT"];
				if($MS_CONTENT_MD==""){
					$sql_update="UPDATE `MESSAGE_DETAIL` SET  MS_CONTENT='$content'  WHERE `MS_ID` = $ms_id  " ;
					//echo $sql_update."<br>";								
					$this->smssenddao->Execute($sql_update);
				}
					
				
				file_get_contents(APP_URL_WWW."/_v2app/sms.php?action=send_package&smsid=$ms_id&smscontent=$content&sendtype=$ms_type");
			//	file_get_contents("http://iwww.steelhome.cn/_v2app/sms.php?action=send_package&smsid=$ms_id&smscontent=".urlencode($content)."&sendtype=$ms_type");
			   //2011-08-02 给内容编码
			   ob_clean();
			   $arr['Status']="1";
			   $arr['Success']="1";
			   $arr['Message']="添加到发布列表成功";
			 echo json_encode($arr);exit;

}
//xiangbin add 20191206 start
public function  sendsmsapi($params)
{
	$ip = getfromip();
	$handle_1=fopen("/usr/local/www/oa.steelhome.cn/admincp/lanipnew.txt","rb");
		 $lanip_1=fread($handle_1,8192);
		 $iplist=explode('#', $lanip_1);
		$iplist[]="127.0.0.1";
		 //print_R($iplist);
		 if(!in_array($ip,$iplist) && $params['noip']!=1)
		 {
			 echo "您不在局域网内，您不能发送微信消息，您的IP地址为：".$ip;exit;
		 }

      //add by zfy started 2018/2/1 for 短信推送
	require_once ('/etc/steelconf/config/config.php');
	require_once ('/usr/local/www/libs/xgpush/src/XingeApp.php');
	//add by zfy started 2018/2/1 for 短信推送
	   $ms_id=$params['smsid']==''?'':$params['smsid'];
	  //短信id；

	   //print_r($this->smssenddao);exit;

	 
	  
	  if(empty($ms_id))
	  {
		ob_clean();
		$arr['Status']="0";
		$arr['Success']="0";
		$arr['Message']="您发送的参数有误，请检查";
	    echo json_encode($arr);exit;
	  }
	  $content = urldecode($params['smscontent']);
      				//短信状态取消  及  到期会员  及  还没有日期者
				$time =date("Y-m-d",time());
				
				$sql_id="SELECT user_id  FROM `message_user` WHERE `user_sms_status` = 0 or `user_sms_status2` != 1 OR  `user_sms_end_date` < '$time' OR `user_sms_end_date` IS NULL  ";
				
				$arr_no_user_id=$this->maindao->query("$sql_id");
				$not_in_id="0";
				$not_effect_users = array();
				for($i=0; $i<count($arr_no_user_id); $i++){
					//$not_in_id=$not_in_id.",".$arr_no_user_id[$i]["user_id"];
					$not_effect_users[$arr_no_user_id[$i]["user_id"]] = 1;
				}
				//短信用户
				$sql_0="SELECT *  FROM `MESSAGE_USER_LIST` WHERE `MS_ID` = $ms_id and deliver='1'";
				
				$array_user_temp=$this->smssenddao->query($sql_0);
				
				$array_user=array();
				$jtemp=0;
				for($i=0;$i<count($array_user_temp);$i++){
					
					$mul_id=$array_user_temp[$i]["MUL_ID"];
					$mul_mid=$array_user_temp[$i]["MUL_MID"];
					$mul_userid=$array_user_temp[$i]["MUL_USERID"];
					$mul_user_mobile=$array_user_temp[$i]["MUL_USER_MOBILE"];	
					if(!isset($not_effect_users[$mul_userid])){
						$array_user[$jtemp]["MUL_ID"]=$mul_id;
						$array_user[$jtemp]["MUL_MID"]=$mul_mid;
						$array_user[$jtemp]["MUL_USERID"]=$mul_userid;
						$array_user[$jtemp]["MUL_USER_MOBILE"]=$mul_user_mobile;
						$jtemp++;
					}																	
				}			
			    //print_r($array_user);
				//日志
				$mpl_post_count=count($array_user);
				//$mpl_post_count=0;
				
				//判断是否推送
				//echo  $content;exit;
				
				$sql_m="SELECT * FROM `MESSAGE` WHERE `MS_ID` = $ms_id";
				$arr_m=$this->smssenddao->getrow($sql_m);
				$ms_name=$arr_m["MS_NAME"];
				$ms_type=$arr_m["MS_TYPE"];
				$str_ms_dept=$arr_m["MS_DEPT"];
				$str_ms_dept_name=$arr_m["MS_DEPT_NAME"];
				$str_ms_aid=$arr_m["MS_AID"];
				$str_ms_admin_name=$arr_m["MS_ADMIN_NAME"];
				$sql_2="INSERT INTO MESSAGE_POST_LOG(`MS_ID`, `MS_NAME`, `MS_TYPE`, `MS_CONTENT`, `MS_AID`, `MS_ADMIN_NAME`,`MS_DEPT`,`MS_DEPT_NAME`, `MPL_DATE`, `USER_COUNT`,`MPL_POST_COUNT`) VALUES ('$ms_id', '$ms_name', '$ms_type', '$content', '$str_ms_aid', '$str_ms_admin_name', '$str_ms_dept', '$str_ms_dept_name', NOW(), '$mpl_post_count','0' );";			
				$this->smssenddao->Execute($sql_2);
		
			
				$smstitle = base64_encode($ms_name);
				$contentts = base64_encode($content);

	$android_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=1");

	$android_hd_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=2");

	$ios_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=3");

	$ios_hd_ret = file_get_contents(APP_URL_WWW."/cron_97_system/xingePush/xingePush.php?title=".$smstitle."&content=".$contentts."&tag=".$ms_id."&pushType=push&deviceType=4");

				/*$arr_android = XingeApp::PushTagAndroid($APPID_FOR_ANDROID, $SECRETKEY_FOR_ANDROID, $smstitle, $contentts, $ms_id);

				$arr_android_hd = XingeApp::PushTagAndroid($APPID_FOR_ANDROID_HD, $SECRETKEY_FOR_ANDROID_HD, $smstitle, $contentts, $ms_id);
  
				$arr_ios_pad = XingeApp::PushTagIos($APPID_FOR_IOS_PAD,$SECRETKEY_FOR_IOS_PAD, $contentts,$ms_id,$smstitle, XingeApp::IOSENV_PROD);
				
				$arr_ios = XingeApp::PushTagIos($APPID_FOR_IOS,$SECRETKEY_FOR_IOS, $contentts,$ms_id,$smstitle, XingeApp::IOSENV_PROD);*/
				
				//add by zfy ended 2018/1/8 for 短信推送
				
				$sql_log="INSERT INTO `MESSAGE_ADMIN_POST_LOG` (`MAPL_ADMIN_ID`, `MAPL_ADMIN_NAME`, `MAPL_MS_CONTENT`, `MAPL_POST_COUNT`, `MAPL_TYPE`, `MAPL_MS_ID`, `MAPL_DATE`) VALUES ('$str_ms_aid', '$str_ms_admin_name', '$content', '$mpl_post_count', '订阅发布', '$ms_id', NOW() )";
				$this->smssenddao->Execute($sql_log);			
						
				
				//更新
				$sql_update="UPDATE `MESSAGE` SET MS_AID='$str_ms_aid', MS_ADMIN_NAME='$str_ms_admin_name' , MS_CONTENT='$content', `MS_LAST_DATE` = NOW() WHERE `MESSAGE`.`MS_ID` = $ms_id LIMIT 1 " ;
				//echo $sql_update."<br>";								
				$this->smssenddao->Execute($sql_update);
				
				$sql_md="SELECT MS_CONTENT FROM `MESSAGE_DETAIL` WHERE `MS_ID` = $ms_id group by `MS_ID` ";				
				$arr_temp=$this->smssenddao->query($sql_md);
				$MS_CONTENT_MD=$arr_temp[0]["MS_CONTENT"];
				if($MS_CONTENT_MD==""){
					$sql_update="UPDATE `MESSAGE_DETAIL` SET  MS_CONTENT='$content'  WHERE `MS_ID` = $ms_id  " ;
					//echo $sql_update."<br>";								
					$this->smssenddao->Execute($sql_update);
				}
					
				
				file_get_contents(APP_URL_WWW."/_v2app/sms.php?action=send_package&smsid=$ms_id&smscontent=$content&sendtype=$ms_type");
			//	file_get_contents("http://iwww.steelhome.cn/_v2app/sms.php?action=send_package&smsid=$ms_id&smscontent=".urlencode($content)."&sendtype=$ms_type");
			   //2011-08-02 给内容编码
			   ob_clean();
			   $arr['Status']="1";
			   $arr['Success']="1";
			   $arr['Message']="添加到发布列表成功";
			 echo json_encode($arr);exit;

}
//xiangbin add 20191206 end
//shitaodi add 2023/11/20
public function sendrand_survey($params){
	
	$rand=rand(100000,999999);
	$otime = date( "Y-m-d H:i:s", time() - 1200 );	
    $oldrand=$this->_dao->getOne( "SELECT randvalue FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$otime' " );
	if($oldrand !=""){
	   $rand=$oldrand;
	}
	$_SESSION["EN_VALIDATE"]=$rand;
	$param_send['usermobile']=$params['usermobile'];
	
	
	//为调查问卷短信验证添加memcache
	$expire = 120; 
	$memcache = new Memcache; 
	$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
	
	if($params ['usermobile']!=""){
		$memcache->set($params['PHPSESSID']."_hellosurvey_math"."_".$params ['usermobile'],$rand,MEMCACHE_COMPRESSED,$expire);
	}
	$memcache->close();
	$param_send ['smscontent'] = "尊敬的用户,您好!您的验证码为" . $rand . ",如有操作疑问,请联系钢之家,电话021-50581010.";
	file_put_contents("/tmp/code.txt",print_r($rand,true)."\n",FILE_APPEND);
	$param_send ['smssendid'] = 0;
	$time = date ( "Y-m-d H:i:s", time () - 3600 * 24 );
	$max_num=5;
	if (trim ( $params ['usermobile'] ) == "")
	{
		echo "1";
		exit ();
	}
	if(in_array($params['usermobile'],$GLOBALS['BLACK_NO'])){
		echo "b";
		exit;
	}
	
	$ip = getfromip();
	if($ip!="**************" && $ip!="**************" && $ip!="*************")
	{
		$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
		if( $count >$max_num && $params['usermobile']!='18017891461'){
			echo "1";
			exit;
		}
	}
	$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
	if( $count >$max_num && $params['usermobile']!='18017891461' ){
		echo "1";
		exit;
	}
	$this->smssenddao->ins_sms2send($param_send);
	$now = date( "Y-m-d H:i:s" );
	$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now'  " );
	//在这里调用短信接口，发送一条短信
	//echo $rand;exit;
	echo "0"; exit;
	
}
public function sendrand_wxchat($params){
	
	$rand=rand(1000,9999);
	$otime = date( "Y-m-d H:i:s", time() - 300 );	
    $oldrand=$this->_dao->getOne( "SELECT randvalue FROM sms_randkey WHERE mobile='$params[usermobile]' AND createtime > '$otime' " );
	if($oldrand !=""){
	   $rand=$oldrand;
	}
	$_SESSION["EN_VALIDATE"]=$rand;
	$param_send['usermobile']=$params['usermobile'];
	
	
	//为调查问卷短信验证添加memcache
	$expire = 120; 
	$memcache = new Memcache; 
	$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
	
	if($params ['usermobile']!=""){
		$memcache->set($params['openid']."_wechat"."_".$params ['usermobile'],$rand,MEMCACHE_COMPRESSED,$expire);
	}
	$memcache->close();
	$param_send ['smscontent'] = "尊敬的用户,您好!您的验证码为" . $rand . ",如有操作疑问,请联系钢之家,电话021-50581010.";
	//file_put_contents("/tmp/code.txt",print_r($rand,true)."\n",FILE_APPEND);
	$param_send ['smssendid'] = 0;
	$time = date ( "Y-m-d H:i:s", time () - 3600 * 24 );
	$max_num=5;
	if (trim ( $params ['usermobile'] ) == "")
	{
		echo "1";
		exit ();
	}
	if(in_array($params['usermobile'],$GLOBALS['BLACK_NO'])){
		echo "b";
		exit;
	}
	$ip = getfromip();
	$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
	if( $count >$max_num && $params['usermobile']!='18156590749' ){
		echo "1";
		exit;
	}
	$this->smssenddao->ins_sms2send($param_send);
	$now = date( "Y-m-d H:i:s" );
	$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='$params[usermobile]', randvalue='$rand', fromip='$ip', createtime='$now'  " );
	//在这里调用短信接口，发送一条短信
	//echo $rand;exit;
	echo "0"; exit;
	
}

// 春煦验证码
public function cx_yzm($params){
	$rand=rand(100000,999999);
	// $rand = 111111;
	$type = $params['type'];
	$issend = $params['issend']; //只有0、1，1为短信发送，0为比对验证码是否正确
	$ucode = $params['code'];
	$time = date( "Y-m-d H:i:s", time() - 3600*1 );
	$expire = 60*5;
	$expire2 = 60;  //1分钟内禁止重复发送
	$memcache = new Memcache;
	$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
	
	if(trim($params['mobile'])=="") {
		$memcache->close();
		echo "1";
		exit;
	}

	// 判断手机号是否合法
	if(!preg_match('/^1[3456789]\d{9}$/', $params['mobile'])) {
		$memcache->close();
		echo "1";
		exit;
	}

	$code = $memcache->get("CX_CODE_".trim($params['mobile']));
	if($issend=="1") {
		$param_send['usermobile'] = $params['mobile'];
		$param_send['smscontent'] = "尊敬的用户,您好!验证码：".$rand.",验证码5分钟有效，请勿向他人泄露。";
		$param_send['smssendid'] = 0;
		if($memcache->get("CX_CODE_".trim($params['mobile'])."2")) {
			$memcache->close();
			echo "1";
			exit;
		}
		$memcache->set("CX_CODE_".trim($params['mobile']), $rand, MEMCACHE_COMPRESSED, $expire);
		$memcache->set("CX_CODE_".trim($params['mobile'])."2", $rand, MEMCACHE_COMPRESSED, $expire2);
		$memcache->close();
		$ip = getfromip();
		if($ip!="**************" && $ip!="**************" && $ip!="*************") {
			$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE fromip='$ip' AND createtime > '$time' " );
			if( $count > 10 ) {
				echo "2";
				exit;
			}
		}
		$count = $this->_dao->getOne( "SELECT COUNT( id ) as c FROM sms_randkey WHERE mobile='".$params['usermobile']."' AND createtime > '$time' " );
		if( $count > 10 ){
			echo "2";
			exit;
		}
		$this->smssenddao->ins_sms2send($param_send);
		$now = date( "Y-m-d H:i:s" );
		$this->_dao->execute( "INSERT INTO sms_randkey SET mobile='".$params['usermobile']."', randvalue='$rand', fromip='$ip', createtime='$now'  " );
		echo "0";
		exit;
	} else if ($issend=="0") {
		$memcache->close();
		if($code != false && $code != null && $code != "" && $ucode == $code) {
			echo "0";
			exit;
		} else {
			echo "1";
			exit;
		}
	}
	
}
function gettoday_sms_check($params)
{
	$arr_times=$this->maindao->gettimes_sms();
	$arr_times=array_flip($arr_times);
	sort($arr_times);
	$flag=false;
	$nowtime=date('H:i:00');
	$currentTime = time(); // 获取当前时间
	$HourLater = strtotime('-1 hour', $currentTime); // 1小时候后
	$smstimeLater = date('H:i:00', $HourLater);
	$date=date('Y-m-d');
	$check_times=array();
	$check_times_later=array();
	//$nowtime='09:40:00';
	//$smstimeLater='08:40:00';
	$arr=array();
	$arr['Success']="0";
	$content='';
	$content_now=array();
	$content_later=array();
	if(in_array($nowtime,$arr_times)){
		$check_times[]=$nowtime;
	}
	if(time()<=strtotime($date." 14:00:00")&&in_array($smstimeLater,$arr_times)){
		$check_times_later[]=$smstimeLater;
	}
	if(empty($check_times)&&empty($check_times_later)&&$nowtime!='11:30:00')
	{
		$content="未到检测时间";
	}
	else
	{

		if(!empty($check_times_later))
	   {
			$data=$this->getcontent($smstimeLater,$date);
			foreach($data['content_now'] as $k=>$v)
			{
				$content_now[$k]=$v;
			}
			$content.=$data['content'];
			$content_later=array_merge($content_later,$data['content_later']);
	   }
	   $data=$this->getcontent($nowtime,$date);
	   foreach($data['content_now'] as $k=>$v)
		{
			$content_now[$k]=isset($content_now[$k])?$content_now[$k].$v:$v;
		}
	   $content.=$data['content'];
	   $content_later=array_merge($content_later,$data['content_later']);
	   
	   if($nowtime=='11:30:00')//华东
	   {
		    $content2="";
			$content2_now=array();
			$content2_later=array();
			$gettodayam_checktime = $this->maindao->gettodayam_checktime($nowtime,1);
			foreach($gettodayam_checktime as $k=>$v)
			{
				$data=$this->getcontent($k,$date,1);
				foreach($data['content_now'] as $k1=>$v1)
				{
					$content2_now[$k1]=isset($content2_now[$k1])?$content2_now[$k1].$v1:$v1;
				}
				$content2.=$data['content'];
				$content2_later=array_merge($content2_later,$data['content_later']);
			}
			$arr['content2']=$content2;
			$arr['content2_now']=$content2_now;
			$arr['content_later2']=$content2_later;
	   }
	   if($nowtime=='11:45:00')//中西部
	   {
		    $content3="";
			$content3_now=array();
			$content3_later=array();
			$gettodayam_checktime = $this->maindao->gettodayam_checktime($nowtime,3);
			foreach($gettodayam_checktime as $k=>$v)
			{
				$data=$this->getcontent($k,$date,3);
				foreach($data['content_now'] as $k1=>$v1)
				{
					$content3_now[$k1]=isset($content3_now[$k1])?$content3_now[$k1].$v1:$v1;
				}
				$content3.=$data['content'];
				$content3_later=array_merge($content3_later,$data['content_later']);
			}
			$arr['content3']=$content3;
			$arr['content3_now']=$content3_now;
			$arr['content_later3']=$content3_later;
	   }
	   
	   $arr['Success']="1";
	}
	ob_clean();
	
	$arr['content']=$content;
	$arr['content_now']=$content_now;
	$arr['content_later']=$content_later;
	echo json_encode($arr);
	exit;
}
function getcontent($nowtime,$date,$depttype=0)
{
     $hangqings_checktime = $this->maindao->getsms_checktime($nowtime,$depttype);
      $smsidarr=array();
	  $checksms=array();
	  foreach($hangqings_checktime as $k=>$v)
	  {
		 if(!in_array($v['smsid'],$smsidarr))
		 {
			$smsidarr[]=$v['smsid'];
		 }
		 $checksms[$v['type']][]=$v;
	  }
	  //print_r($checksms);
	  $sql="select MS_ID,MS_NAME,MS_CONTENT,MS_AID,MS_ADMIN_NAME,MPL_DATE from MESSAGE_POST_LOG where MS_ID in ('".implode("','",$smsidarr)."')  and MPL_DATE>='".$date." 00:00:00' and MPL_DATE<='".$date." 23:59:59' order by MPL_DATE ";
	  //echo $sql;
	  $smsinfolist = $this->smssenddao->query($sql);
	  $sendsmsarr=array();
	  foreach($smsinfolist as $k=>$v)
	  {
	     $sendsmsarr[]=$v['MS_ID'];
	  }
	  $nosendsms=array();
	   foreach($checksms as $k=>$v)
	   {
		 if($k==1)//工作日
		 {
			if($this->_isholiday($date))
			{
				continue;
			}
			foreach($v as $k1=>$v1)
			{
				if(!in_array($v1['smsid'],$sendsmsarr))
				{
					$nosendsms[]=$v1;
				}
			}
		 }
		 else if($k==2)//周几
		 {
			$weekday=date('w');
			foreach($v as $k1=>$v1)
			{
				if($weekday==$v1['num']&&!in_array($v1['smsid'],$sendsmsarr))
				{
					$nosendsms[]=$v1;
				}
			}
			//echo $weekday;
		 }
		 else if($k==3)
		 {
			$day=date('j');
			$dayt=date('t');
			foreach($v as $k1=>$v1)
			{
				if($v1['num']==30)
				{
					if($dayt==$day&&!in_array($v1['smsid'],$sendsmsarr))
					{
						$nosendsms[]=$v1;
					}
				}
				else{
					if($day==$v1['num']&&!in_array($v1['smsid'],$sendsmsarr))
					{
						$nosendsms[]=$v1;
					}
				}
				
			}

		 }
	   }
	   $no_sms=array();
	    foreach($nosendsms as $k=>$v)
		{
			$no_sms[$v['depttype']][$v['qyuserid']][]=$v;
		}
	   $content="";
	   $content2="";
	   $content_now=array();
	   $content_later=array();
	   if(!empty($no_sms))
	   {
		   $kk= date('H:i', strtotime($nowtime));
		   
		   foreach($no_sms as $k=>$v)
		   {
			   $content="# **短信提醒：规定（".$kk."）上传短信**\n\n";
			   foreach($v as $k1=>$v1)
				{
					$content.="<@".$k1.">短信：";
					$content2="**短信提醒：规定（".$kk."）上传短信**\n短信：";
					foreach($v1 as $k3=>$v3){
							if($k3==count($v1)-1){
								$content.=$v3['smsname']."(".$v3['smsid'].")";
								$content2.=$v3['smsname']."(".$v3['smsid'].")";
							}else{
								$content.=$v3['smsname']."(".$v3['smsid'].")、";
								$content2.=$v3['smsname']."(".$v3['smsid'].")、";
							}
						}
						if($content2!="" && $k1!=""){
							$content_later[] = array(
								'touser' => $k1,
								'content' => $content2,
							);
						}
						$content.="\n";
					
				}
				if($content!="" && $k!=""){
				  $content_now[$k] =$content;
				}
			}

	   }
	   $arr=array();
	   $arr['content']=$content;
	   $arr['content_now']=$content_now;
	   $arr['content_later']=$content_later;
	   return $arr;
}
function gettoday_sms_checkready($params)
{
     $date=date('Y-m-d');
	 $arr['Success']="0";
     if($this->_isholiday($date))
	{
		$content= "今日非工作日";
		
	}
	else
	{
		$gettodayam_checktime = $this->maindao->getdept_sms(2);
		$smsidarr=array();
		foreach($gettodayam_checktime as $k=>$v)
		{
			if(!in_array($k,$smsidarr))
			{
				$smsidarr[]=$v['smsid'];
			}
		}
		if(empty($smsidarr))
		{
			$content="已全部发送完成";
		}
		else
		{
			$sql="select MS_ID,PRICE_ID,state  from MESSAGE_PRICE_LIST where MS_ID in ('".implode("','",$smsidarr)."') ";
			$pricelist = $this->smssenddao->query($sql);
			$ready=array();
			foreach($pricelist as $k=>$v)
			{
				$ready[$v['MS_ID']][$v['state']]=1;
			}

			$sql="select MS_ID,MS_NAME,MS_CONTENT,MS_AID,MS_ADMIN_NAME,MPL_DATE from MESSAGE_POST_LOG where MS_ID in ('".implode("','",$smsidarr)."')  and MPL_DATE>='".$date." 00:00:00' and MPL_DATE<='".$date." 23:59:59' order by MPL_DATE ";
			$smsinfolist = $this->smssenddao->query($sql);
			$sendsmsarr=array();
			foreach($smsinfolist as $k=>$v)
			{
				$sendsmsarr[]=$v['MS_ID'];
			}
			$smsready=array();
			$updateid=array();
			foreach($gettodayam_checktime as $k=>$v)
			{
				if(in_array($v['smsid'],$sendsmsarr))//短信已发送或者短信未准备好，不提醒
				{
					//echo $v['smsid']."_1<br>";
				}
				// else if((in_array($v['smsid'],$ready)))//未准备好
				// {
				// 	//echo $v['smsid']."_2<br>";
				// }
				else if(isset($ready[$v['smsid']])&&!isset($ready[$v['smsid']][0]))//未准备好
				{
					$smsready[]=$v;
					$updateid[]=$v['id'];
				}
				else{

				}

			}
			$ready_sms=array();
			foreach($smsready as $k=>$v)
			{
				$ready_sms[$v['qyuserid']][]=$v;
			}
			$content="";
			$content_later=array();
			if(!empty($ready_sms))
			{
				$content="# **短信发送提醒：价格已全，请及时发送**\n\n";
			foreach($ready_sms as $k=>$v)
				{
					$content.="<@".$k.">短信：";
					$content2="**短信发送提醒：价格已全，请及时发送**\n短信：";
					foreach($v as $k3=>$v3){
							if($k3==count($v)-1){
								$content.=$v3['smsname']."(".$v3['smsid'].")";
								$content2.=$v3['smsname']."(".$v3['smsid'].")";
							}else{
								$content.=$v3['smsname']."(".$v3['smsid'].")、";
								$content2.=$v3['smsname']."(".$v3['smsid'].")、";
							}
						}
						if($content2!="" && $k!=""){
							$content_later[] = array(
								'touser' => $k,
								'content' => $content2,
							);
						}
						$content.="\n";
						
					
				}
			}
			if(!empty($updateid))
			{
				$this->maindao->execute("update SmsStandardTime set lasttime=Now() where id in ('".implode("','",$updateid)."')");
			}
			$arr['Success']="1";
		}

		
	}
	ob_clean();
	
	$arr['content']=$content;
	$arr['content_later']=$content_later;
	echo json_encode($arr);
	exit;
}

function _isholiday($date)
	{
		$holiday = $this->maindao->getRow("select * from holiday where date='$date'");
		$isholiday = 0;
		if($holiday){
			$isholiday = $holiday['isholiday'];
		}else {
			if (date('D', strtotime($date)) == "Sat" or date('D', strtotime($date)) == 'Sun') {
				$isholiday = 1;
			}
		}
		return $isholiday;
	}
	function get_xhdate($today)
	{
		if(!$this->isValidDate($today))
		{
		$today=date('Y-m-d');
		}
		$now=date('H:i');
		// $now='9:20';
		if(strtotime($now)<strtotime('8:30'))
		{
		$today=date('Y-m-d',strtotime('-1 day',strtotime($today)));
		}
		else
		{
		$today=$today;
		}
	
		if(!$this->_isholiday($today))//不是
		{ 
		$flag=1;
		$lastday=$today;
		while(true)
		{
			$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
			if(!_isholiday($lastday))
			{ 
			break;
			} 
		}
		$today_s=$today;
		$lastday_s=$lastday;
		}
		else// 今天是节假日
		{
		//取不是节假日的当天时间
		$todayflag=1;
		while(true)
		{
		$today=date('Y-m-d',strtotime('-'.$todayflag.' day',strtotime($today)));
		if(!$this->_isholiday($today))
		{ 
		break;
		} 
		}
		//取昨天的日期
		$lastflag=1;
		while(true)
		{
		$lastday=date('Y-m-d',strtotime('-'.$lastflag.' day',strtotime($today)));
		if(!$this->_isholiday($lastday))
		{ 
			break;
		} 
		}
		$today_s=$today;
		$lastday_s=$lastday;
		}   
	
		$dates['today']=$today_s;
		$dates['lastday']=$lastday_s;
		return $dates;
	}
	// 判断是否是日期的函数
		function  isValidDate($date) 
		{		$year=date('Y',strtotime($date));
				$month=date('n',strtotime($date));
				$day=date('j',strtotime($date));
				return   checkdate($month,   $day,   $year); 
		}


// 返回执行日期所在周的第一天(周一)日期
function firstOfWeek($date)
{
	$now = strtotime($date);    //当时的时间戳
	$number = date("w",$now);  //当时是周几
	$number = $number == 0 ? 7 : $number; //如遇周末,将0换成7
	$diff_day = $number - 1; //求到周一差几天
	return date("Y-m-d",$now - ($diff_day * 60 * 60 * 24));
}
function zhangdie($sjc)
{
 $sjc=number_format($sjc,2);
	if($sjc>0){
		$sjc = "升".abs($sjc); 
	}else if($sjc<0){
		$sjc = "降".abs($sjc);
	}else{
		$sjc ="持平";
	}
	return $sjc;
}
function zhangdie1($sjc)
{
 $sjc=number_format($sjc,2);
	if($sjc>0){
		$sjc = "涨".abs($sjc); 
	}else if($sjc<0){
		$sjc = "跌".abs($sjc);
	}else{
		$sjc ="稳";
	}
	return $sjc;
}



}
?>