<?php
// PayPal IPN监听器
$raw_post_data = file_get_contents('php://input');
file_put_contents('ipn.log', date('[Y-m-d H:i:s]').print_r($raw_post_data,true), FILE_APPEND);
$raw_post_array = explode('&', $raw_post_data);
$myPost = array();
foreach ($raw_post_array as $keyval) {
    $keyval = explode('=', $keyval);
    if (count($keyval) == 2)
        $myPost[$keyval[0]] = urldecode($keyval[1]);
}

// 读取IPN消息并验证
$req = 'cmd=_notify-validate';
foreach ($myPost as $key => $value) {
    $value = urlencode($value);
    $req .= "&$key=$value";
}
file_put_contents('ipn.log', date('[Y-m-d H:i:s]')." REQUEST: $req\n", FILE_APPEND);
// 向PayPal验证IPN
$ch = curl_init('https://www.sandbox.paypal.com/cgi-bin/webscr');
curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $req);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
$res = curl_exec($ch);
curl_close($ch);

// 处理验证结果
if (strcmp($res, "VERIFIED") == 0) {
    // IPN验证成功，处理业务逻辑
    $payment_status = $_POST['payment_status'];
    $txn_id = $_POST['txn_id'];
    $receiver_email = $_POST['receiver_email'];
    $payment_amount = $_POST['mc_gross'];
    
    // 在此处添加您的业务处理代码
    file_put_contents('ipn.log', date('[Y-m-d H:i:s]')." Verified IPN: $txn_id\n", FILE_APPEND);
    require_once('../english/include/config.php');
    $OrderNo=$_POST['invoice'];
    $OrderNo1 = substr($OrderNo, 0, 16);
	$OrderNo2 = substr($OrderNo, 16, 4);
    $sql = "SELECT * FROM  PurchasedNewsInfo where OrderNo1='" . $OrderNo1 . "' and OrderNo2='" . $OrderNo2 . "' ";
    $Orhavepay=$conn->Execute($sql);
    $result = $Orhavepay->FetchRow();
    if($result)
    {
        if ($result['status'] != 0) {
            $newsType = $result['Type'];
			$newsId = $result['newsid'];
			$manageId = 0;
            $sql = "UPDATE PurchasedNewsInfo SET status='0',txn_id='" . $txn_id . "',statusStr='" . $payment_status . "',receiver_email='" . $receiver_email . "',completeTime=NOW(),manageid='" . $manageId . "' where OrderNo1='" . $OrderNo1 . "' and OrderNo2='" . $OrderNo2 . "' ";
		   $conn->Execute($sql);
        }
    }
    
} else if (strcmp($res, "INVALID") == 0) {
    // IPN验证失败
    file_put_contents('ipn.log', date('[Y-m-d H:i:s]')." Invalid IPN\n", FILE_APPEND);
}
?>
