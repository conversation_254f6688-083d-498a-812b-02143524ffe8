<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );
class indexController extends AbstractController {

    public function __construct() {
        parent::__construct();
        $this->_action->setDao( new indexDao( '91R' ) );
        $this->_action->logs = new indexDao( 'STLOGSW' );
    }

    public function v_index(){
        $this->_action->index($this->_request);
    }

    public function do_uploadFile(){
        $this->_action->uploadFile($this->_request);
    }

    public function v_dialog(){
        $this->_action->dialog($this->_request);
    }

    public function do_chat(){
        $this->_action->chat($this->_request);
    }

    public function do_getChatDetail(){
        $this->_action->getChatDetail($this->_request);
    }

    public function do_getChatList() {
        $this->_action->getChatListApi($this->_request);
    }

    public function do_getChatAbove() {
        $this->_action->getChatAbove($this->_request);
    }

    public function do_saveChat() {
        $this->_action->saveChat($this->_request);
    }

    public function do_deleteChat() {
        $this->_action->deleteChat($this->_request);
    }
    
    public function do_getAllBalance(){
        $this->_action->getAllBalance($this->_request);
    }

    public function do_textCorrection() {
        // $this->_action->textCorrection($this->_request);
        $this->_action->mcpTools($this->_request);
    }

    public function do_getTextKeywords() {
        $this->_action->mcpTools($this->_request);
    }

    public function do_checkTextReference() {
        $this->_action->mcpTools($this->_request);
    }
}