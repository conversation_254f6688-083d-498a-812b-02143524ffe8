<?php
ignore_user_abort(true);// 忽略客户端断开
set_time_limit(0);    // 设置执行不超时
// error_reporting(0);
ini_set('display_errors',1);
error_reporting(1);
ini_set("max_execution_time", "0");
require_once("/etc/steelconf/env/www_env.php");
require_once("/usr/local/www/libs/vendor/autoload.php");

$tdate = date("Y-m-01");
$lastDayOfLastMonth = date("Y-m-t", strtotime($tdate." first day of last month"));
$firstDayOfLastMonth = date("Y-m-01", strtotime($lastDayOfLastMonth));
$firstDayOfLastMonth = getLastWorkDay(date("Y-m-d", strtotime($firstDayOfLastMonth." -1 day")));
$date_ym = date("Y-m", strtotime($lastDayOfLastMonth));

if(!isFirstWorkDayOfCurrentMonth(date("Y-m-d"))) {
    echo "不是当月的第一个工作日，不执行";
    exit;
}

$db = ADONewConnection('mysqli');
$db->Connect(HOST_NAME_91R.":".HOST_PORT_91R, USER_NAME_91R,USER_PASSWORD_91R,DATABASE_NAME_91R);

$dbw = ADONewConnection('mysqli');
$dbw->Connect(HOST_NAME_91W.":".HOST_PORT_91W, USER_NAME_91W,USER_PASSWORD_91W,DATABASE_NAME_91W);

// 不需要检查的id
$noCheckIds = [];
$noCheckTitles = [];

$lastWorkDay = getLastWorkDay($lastDayOfLastMonth);
if($lastWorkDay=="") {echo "error";exit;}
// 查全部、不分频道
// $sql = "SELECT mastertopid,topicture FROM marketconditions where mconmanagedate>='$lastWorkDay 00:00:00'  and mconmanagedate<='$lastWorkDay 23:59:59' and (mastertopid!='' or topicture!='')";
// 查询钢材频道所有的非屏蔽的价格ID
// 铁合金15、新能源25、有色08，化工17、水泥19
$sql = "SELECT mastertopid,topicture,marketrecord.id,marketrecord.channelid FROM marketconditions left join marketrecord on marketrecord.id=marketconditions.marketrecordid where mconmanagedate>='$lastWorkDay 00:00:00'  and mconmanagedate<='$lastWorkDay 23:59:59' and (mastertopid!='' or topicture!='') and marketrecord.channelid in ('19','25','15','08','17') and isview=0";
// $lastWorkDay = "2025-05-30";
// $sql = "SELECT mastertopid,topicture,marketrecord.id,marketrecord.channelid FROM marketconditions left join marketrecord on marketrecord.id=marketconditions.marketrecordid where mconmanagedate>='$lastWorkDay 00:00:00'  and mconmanagedate<='$lastWorkDay 23:59:59' and (mastertopid!='' or topicture!='') and marketrecord.channelid in ('19','25','15','08','17') and isview=0";
$d = $db->getArray($sql);
// dd($d);
$hq_id_mapping = [];
$ids6 = [];$ids7 = [];
foreach($d as $val) {
    if($val['mastertopid']=="" && $val['topicture']!="") {
        if(in_array($val['topicture'], $noCheckIds)) continue;
        $ids6[] = $val['topicture'];
        $hq_id_mapping[$val['topicture']] = $val['id'];
    } else {
        if(in_array($val['mastertopid'], $noCheckIds)) continue;
        $ids7[] = $val['mastertopid'];
        $hq_id_mapping[$val['mastertopid']] = $val['id'];
    }

}
$hqs = [];
$tmphq_title = [];
getTitles(array_unique($ids6), 'topicture', $hqs);
getTitles(array_unique($ids7), 'mastertopid', $hqs);
$titles = array_values( array_filter($hqs, fn(array $v)  => !in_array($v['title'], $noCheckTitles)) );
$temp_hq_title = [];
$tmp_num = 500;
$total_num = count($titles);
$all_tmp_num = ceil($total_num/$tmp_num);
$updateHq = [];
for($ii=0;$ii<$all_tmp_num;$ii++) {
    $insert_sql = "insert into hq_check (title,date_ym,manager_id,pid,channelid) values ";
    $insert_sql_values = [];
    $tmpdata = array_slice($titles, $ii*$tmp_num, $tmp_num);
    foreach($tmpdata as $tval) {
        if(in_array($tval['title'], $temp_hq_title)) {
            $total_num--;
            $updateHq[$tval['title']] = $tval['pid'];
            continue;
        }
        $temp_hq_title[] = $tval['title']; 
        $insert_sql_values[$tval['title']] = "('{$tval['title']}','{$date_ym}', {$tval['manager_id']}, '{$tval['pid']}', '{$tval['channelid']}')";
    }
    if(count($insert_sql_values) == 0) continue;
    $insert_sql .= implode(",", $insert_sql_values);
    if($ii == 0) $dbw->execute( "delete from hq_check where date_ym='{$date_ym}' and channelid in ('19','25','15','08','17')" );
    $dbw->execute( $insert_sql );
}
// 6、7位ID的行情分离检测，导致同一条行情可能会被检测两次，这里直接将第二次检测的ID补充进去
foreach($updateHq as $utitle => $upid) {
    $dbw->execute("UPDATE hq_check SET pid = CONCAT(pid, ',', '{$upid}') WHERE title = '{$utitle}' AND date_ym = '{$date_ym}' and channelid in ('19','25','15','08','17')");
}
sendM("铁合金、有色、化工、水泥、新能源上月有价格未变动的行情共：".$total_num."条！详情请查看列表：".APP_URL_OA."/admincpv2/hqcheck.php");
$db->close();
$dbw->close();
echo "hq_check_hj.php\n";
echo "ok\n";
echo "铁合金、有色、化工、水泥、新能源上月有价格未变动的行情共：".$total_num."条！详情请查看列表：".APP_URL_OA."/admincpv2/hqcheck.php\n";
exit;

function getLastWorkDay($day){
    $isholiday = file_get_contents(APP_URL_WWW."/isholiday.php?date=".$day);
    if($isholiday == 1) $day = getLastWorkDay(date( "Y-m-d", strtotime($day."-1 day")));
    return $day;
}

function isFirstWorkDayOfCurrentMonth($day){
    $first = date( "Y-m-01", strtotime($day));
    $isholiday = file_get_contents(APP_URL_WWW."/isholiday.php?date=".$first);
    while($isholiday) {
        $first = date( "Y-m-d", strtotime($first."+1 day"));
        $isholiday = file_get_contents(APP_URL_WWW."/isholiday.php?date=".$first);
    }
    return $day == $first;
}

function getTitles($ids, $field, &$hqs) {
    global $db, $firstDayOfLastMonth, $lastDayOfLastMonth, $lastWorkDay, $hq_id_mapping, $tmphq_title;
    $noChangeIdList = [];
    foreach($ids as $v_id) {
        $sql = <<<EOF
            SELECT 
            AVG(
                CASE 
                    WHEN INSTR(price, '-') > 0 THEN
                        (CAST(SUBSTRING_INDEX(price, '-', 1) AS DECIMAL(10,2)) + 
                        CAST(SUBSTRING_INDEX(price, '-', -1) AS DECIMAL(10,2))) / 2
                    ELSE 
                        CAST(price AS DECIMAL(10,2))
                END
            ) AS avgprice,
            CASE 
                WHEN INSTR(price, '-') > 0 THEN
                    (CAST(SUBSTRING_INDEX(price, '-', 1) AS DECIMAL(10,2)) + 
                    CAST(SUBSTRING_INDEX(price, '-', -1) AS DECIMAL(10,2))) / 2
                ELSE 
                    CAST(price AS DECIMAL(10,2))
            END AS price,
            MAX(
                CASE 
                    WHEN INSTR(price, '-') > 0 THEN 
                        (CAST(SUBSTRING_INDEX(price, '-', 1) AS DECIMAL(10,2)) + 
                        CAST(SUBSTRING_INDEX(price, '-', -1) AS DECIMAL(10,2))) / 2
                    ELSE 
                        CAST(price AS DECIMAL(10,2))
                END
            ) AS maxprice
        FROM marketconditions WHERE 
                mconmanagedate >= '{$firstDayOfLastMonth} 00:00:00' AND mconmanagedate <= '{$lastDayOfLastMonth} 23:59:59' AND $field = '{$v_id}' AND isview=0
        EOF;
        $a = $db->GetRow( $sql );
        $avg = number_format((float)$a['avgprice'], 5, '.', '');
        $price = number_format((float)$a['price'], 5, '.', '');
        $max = number_format((float)$a['maxprice'], 5, '.', '');
        if( $avg === $price && $avg === $max && $price === $max && !in_array($v_id, $noChangeIdList) ) {
            $noChangeIdList[] = $v_id;
        } else {
            unset($hq_id_mapping[$v_id]);
        }
    }
    $hq_id = [];
    foreach($hq_id_mapping as $t_id => $t_hq_id) {
        if($field == 'mastertopid' && strlen($t_id) == 7 ) {
            $hq_id[$t_hq_id][] = $t_id;
        } else if($field =='topicture' && strlen($t_id) == 6) {
            $hq_id[$t_hq_id][] = $t_id;
        }
    }
    // 每次处理多少个id
    $num = 200;
    $all_n = ceil(count($noChangeIdList)/$num);
    for($i=0; $i<$all_n;$i++) {
        $id_str = implode("','", array_slice($noChangeIdList, $i*$num, $num));
        $sql = <<<AABBCCDD
            SELECT title,manageid,mid,channelid FROM 
                marketrecord 
            RIGHT JOIN (
                SELECT 
                    marketrecordid AS mid
                FROM 
                    marketconditions  
                WHERE 
                    {$field} IN ('{$id_str}') 
                    AND mconmanagedate >= '{$lastWorkDay} 00:00:00' 
                    AND mconmanagedate <= '{$lastWorkDay} 23:59:59' AND isview=0
                GROUP BY 
                    marketrecordid
            ) d 
            ON d.mid = marketrecord.id 
            WHERE 
                managedate >= '{$lastWorkDay} 00:00:00' 
                AND managedate <= '{$lastWorkDay} 23:59:59'
                AND channelid in ('19','25','15','08','17')
        AABBCCDD;
        $t_info = $db->GetAll($sql);
        foreach($t_info as $val) {
            $title11 = preg_replace( '/(\d+日)/', '', $val['title'] );
            if($hq_id[$val['mid']]==null) {
                continue;
            }
            $pid = implode(",", $hq_id[$val['mid']]);
            if(!in_array($title11, $tmphq_title)) {
                $tmphq_title[] = $title11;
                $hqs[] = ["title"=>$title11, "manager_id"=>$val['manageid'], "pid"=>$pid, "channelid"=>$val['channelid']];
            }
        }
    }
}

function sendM($content) {
    $webhook = WEBHOOK1;
    // $webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d64c7c51-68f9-4ceb-bb9f-57de65e6058d";
    $template = <<<ABCDEF
        {
            "msgtype": "text",
            "text": {
                "content": "{$content}"
            }
        }
        ABCDEF;
        // "mentioned_mobile_list":["13085554576"]
    $dataRes1 = request_post( $webhook, $template );
    $dataRes=json_decode($dataRes1);
    // print_r( $dataRes );
}

function request_post($url = '', $param = '') {
    if (empty($url) || empty($param))
    {
        return false;
    }
    $curl = curl_init(); // 启动一个CURL会话
    curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
    curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
    curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
    curl_setopt($curl, CURLOPT_POSTFIELDS, $param); // Post提交的数据包
    curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
    curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
    $data = curl_exec($curl); // 执行操作
    if (curl_errno($curl)) {
            echo 'Errno'.curl_error($curl);//捕抓异常
    }
    curl_close($curl); // 关闭CURL会话
    return $data; // 返回数据，json格式
}