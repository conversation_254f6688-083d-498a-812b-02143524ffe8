<?php
    $pathdir="/usr/local/www/www.steelhome.cn";
    require_once($pathdir."/include/functionshpi.php");
    require_once($pathdir.'/tks_index_function.php');
    require_once($pathdir.'/include/config.php');
    require_once ('./_v2config/adodb.drc.inc.php');

	$conn=$conn_w;

$datetime =  isset( $_REQUEST['datetimeb'] ) && !empty($_REQUEST['datetimeb']) ? date("Y-m-d",strtotime($_REQUEST['datetimeb'])) : date("Y-m-d");
$url = "https://oa.steelhome.com/admincpv2/shpi_manage.php?action=cal_tks_index&date=$datetime";
$data = file_get_contents($url);
$data = json_decode($data, true);

//基准数据
$base_arr = [
	'gcxhk'=>703, //国产现货矿
	'jkxhk'=>586.59, //进口现货矿
	//'jkcxk'=>0, //进口长协矿
	'tks_zh'=>614, //铁矿石综合
];

$type_arr = [
	'gcxhk'=>1, //国产现货矿
	'jkxhk'=>3, //进口现货矿
	'jkcxk'=>4, //进口长协矿
	'tks_zh'=>0, //铁矿石综合
];

if($data['status'] == 0){
	$sql = "select vid,weiprice from shpi_material_pzp where dateday='2004-09-28'";
	$info = $conn->getArray($sql);
	
	$base_info = [];
	foreach($info as $row) {
		$base_info[$row['vid']] = $row['weiprice'];
	}
	
	foreach($type_arr as $type => $vid){
		$weiprice = $data[$type]['price_rmb'];
		$weiprice_wet = $data[$type]['price_wet'] ?? $weiprice;
		$weipriceusb = $data[$type]['price_usa'];
		$weiindex = 0;
		if(isset($base_arr[$type])){
			$weiindex = round($weiprice *100 / $base_arr[$type], 4);
		}
		$weiindex_new = round($weiprice *100 / $base_info[$vid], 4);
		$conn->execute("delete from shpi_material_pzp where vid=$vid and dateday='$datetime'");
		$sql = "insert into shpi_material_pzp (vid,weiprice,weiprice_wet,weipriceusb,weiindex,weiindex_new,dateday) 
		        values ($vid,$weiprice,$weiprice_wet,$weipriceusb,$weiindex,$weiindex_new,'$datetime') ";
		echo $sql."<br />";
		$conn->execute($sql);
	}
	
	// 国产现货矿
	
}

echo "计算完成";
exit;


// 以下是旧算法，现在用了。		

	$datetime=$_REQUEST['datetimeb'];

	if($datetime=='')
	{
		$datetime=date("Y-m-d");
	}

	include_once('include/huilv_func.php');		

	$datetime=$_REQUEST['datetimeb'];

	if($datetime=='')
	{
		$datetime=date("Y-m-d");
	}

	// $sql_day = "select distinct dateday from shpi_pi where dateday>='".$datetime."' and dateday<='".$datetime."'";

	// //$sql_day = "select dateday from shpi_pi where dateday>='2012-01-01' and dateday<='2013-01-07'";

	// $time_array = $conn->getArray($sql_day);
	// //$time_array=array("0"=>array("2013-01-06"));	
	// foreach($time_array as $tkey=>$tvalue){
	// 	$time_arr[$tkey] = $tvalue['0'];
	// 	// $huilv[$tvalue['0']] ='6.2814' ;
	// 	$huilv[$tvalue['0']] =get_huilv($tvalue['0'])/100 ;
		
	// }
	//修改直接通过datetime获取汇率
	$curdate_huilv=get_huilv($datetime);
	if($curdate_huilv!=0){
		$time_arr[0] = $datetime;
		$huilv[$datetime] = $curdate_huilv/100;
	}
	
	//65%价格指数计算

	$price_id_65 =array(
		'448610','458610','438610','228610','208610','248610','368610','538610'
	 );

	$type_65='1';//方法第几个计算方法
	$tks_type_65='1';//等于1表示原始数据为人民币，2表示美元
	$small_type_65='13';//shpi_city_weight表 对应权重
	$reduced_65='14';//shpi_city_weight表 对应折算
	$rmb_index_65 = price_index($time_arr,$price_id_65,$type_65,$tks_type_65,$huilv,$small_type_65,$reduced_65);

	
	//钢厂自有矿
	$price_id_zy = array(
		'448610'
	);
	$type_zy='2';//方法第几个计算方法
	$tks_type_zy='1';//等于1表示原始数据为人民币，2表示美元
	$small_type_zy='36';//shpi_city_weight表 对应权重
	$reduced_zy='37';//shpi_city_weight表 对应价格
	$rmb_index_zy = price_index($time_arr,$price_id_zy,$type_zy,$tks_type_zy,$huilv,$small_type_zy,$reduced_zy);

	
	//进口现货矿
	
	//--港口现货矿
	
	//$price_id_gkxh = array(
	//	'188610','408610','D28610','1886102','4086102','D286102','1886103','4086103','D286103'
	//);
	$price_id_gkxh = array(
		'188611','408611','D28611','1886121','4086121','D286121',
	);
	$type_gkxh='5';//方法第几个计算方法
	$tks_type_gkxh='1';//等于1表示原始数据为人民币，2表示美元
	$small_type_gkxh='46';//shpi_city_weight表 对应折算系数
	$reduced_gkxh='47';//shpi_city_weight表 对应权重
	$rmb_index_gkxh = price_index($time_arr,$price_id_gkxh,$type_gkxh,$tks_type_gkxh,$huilv,$small_type_gkxh,$reduced_gkxh);
	
	
	//--进口铁矿石外盘报价
	$price_id_wpbj = array(
		'B98650','C18690',
	);
	$type_wpbj='6';//方法第几个计算方法
	$tks_type_wpbj='2';//等于1表示原始数据为人民币，2表示美元
	$small_type_wpbj='46';//shpi_city_weight表 对应折算系数
	$reduced_wpbj='47';//shpi_city_weight表 对应权重
	$rmb_index_wpbj = price_index($time_arr,$price_id_wpbj,$type_wpbj,$tks_type_wpbj,$huilv,$small_type_wpbj,$reduced_wpbj);
	
	$reduced_jkxhk='45';
	$rmb_index_jkxhk = tks_sum($time_arr,$reduced_jkxhk,array($rmb_index_gkxh,$rmb_index_wpbj));

	//计算综合指数时要转换成干基计算
	$rmb_index_wpbj_tmp[0] = $rmb_index_wpbj[2];
	$rmb_index_wpbj_tmp[1] = $rmb_index_wpbj[1];
	$rmb_index_jkxhk_ganji = tks_sum($time_arr,$reduced_jkxhk,array($rmb_index_gkxh,$rmb_index_wpbj_tmp));

	// echo "<pre>";print_r($rmb_index_jkxhk);
	
	//进口长协矿
	/*
	$price_id_jkcxk = array(
		'010109','010108'
	);
	$type_jkcxk='4';//方法第几个计算方法
	$tks_type_jkcxk='2';//等于1表示原始数据为人民币，2表示美元
	$small_type_jkcxk='42';//shpi_city_weight表 对应折算系数
	$reduced_jkcxk='43';//shpi_city_weight表 对应权重
	$rmb_index_jkcxk = price_index($time_arr,$price_id_jkcxk,$type_jkcxk,$tks_type_jkcxk,$huilv,$small_type_jkcxk,$reduced_jkcxk);
	*/
	$rmb_index_jkcxk = jkcxk_index($time_arr,$huilv);
	
	// echo "<pre>";print_r($rmb_index_jkcxk);
	
	$reduced_tks='44';
	$rmb_index_tks = tks_sum($time_arr,$reduced_tks,array($rmb_index_65,$rmb_index_zy,$rmb_index_jkxhk,$rmb_index_jkcxk));
	//$rmb_index_tks = tks_sum($time_arr,$reduced_tks,array($rmb_index_65,$rmb_index_zy,$rmb_index_jkxhk_ganji,$rmb_index_jkcxk));//先不用干基价格
	
	// echo "<pre>";print_r($time_arr);
	// echo "<pre>";print_r($rmb_index_tks);
	
	//基准数据
	$gcxhk_pricebase=703;//65%国产现货矿
	$jkxhk_pricebase=721.66;//进口现货矿
	$gkxhk_pricebase=742.85;
	$jkwp_pricebase=76.19;//进口外盘
	$jktks_pricebase=586.59;//进口铁矿石 3
	$zonghe_pricebase=614;//综合
	

	foreach($time_arr as $key=>$value){
		if(!empty($rmb_index_tks['0'][$value]) && $rmb_index_tks['0'][$value]!='') {
			
			$int_rmb_index_tks = round($rmb_index_tks['0'][$value]);
			$zonghe_tks = round($int_rmb_index_tks/$zonghe_pricebase*100,2);
			
			echo $sql_tks = "insert into shpi_material_pzp (vid,dateday,weiprice,weipriceusb,weiindex,isbase) values ('0','".$value."','".round($rmb_index_tks['0'][$value])."','".round($rmb_index_tks['1'][$value],2)."','".$zonghe_tks."','0')";
			$conn->Execute($sql_tks);
		}
		
		if(!empty($rmb_index_65['0'][$value]) && $rmb_index_65['0'][$value]!='') {
		
			$int_rmb_index_65 = round($rmb_index_65['0'][$value]);
			$zonghe_65 = round($int_rmb_index_65/$gcxhk_pricebase*100,2);
			
			$sql_65 = "insert into shpi_material_pzp (vid,dateday,weiprice,weipriceusb,weiindex,isbase) values ('1','".$value."','".round($rmb_index_65['0'][$value])."','".round($rmb_index_65['1'][$value],2)."','".$zonghe_65."','0')";
			$conn->Execute($sql_65);
		}
		
		if(!empty($rmb_index_jkxhk['0'][$value]) && $rmb_index_jkxhk['0'][$value]!='') {
		
			$int_rmb_index_jkxhk = round($rmb_index_jkxhk['0'][$value]);
			$zonghe_jkxhk = round($int_rmb_index_jkxhk/$jktks_pricebase*100,2);
			
			$sql_jkxhk = "insert into shpi_material_pzp (vid,dateday,weiprice,weipriceusb,weiindex,isbase) values ('3','".$value."','".round($rmb_index_jkxhk['0'][$value])."','".round($rmb_index_jkxhk['1'][$value],2)."','".$zonghe_jkxhk."','0')";
			$conn->Execute($sql_jkxhk);
		}
		
		if(!empty($rmb_index_jkcxk['0'][$value]) && $rmb_index_jkcxk['0'][$value]!='') {
			$sql_jkcxk = "insert into shpi_material_pzp (vid,dateday,weiprice,weipriceusb,weiindex,isbase) values ('4','".$value."','".round($rmb_index_jkcxk['0'][$value])."','".round($rmb_index_jkcxk['1'][$value],2)."','0','0')";
			$conn->Execute($sql_jkcxk);
		}
	}
	
	

?>