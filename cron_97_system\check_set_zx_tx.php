<?php
ini_set('display_errors',1);
error_reporting(1);
include_once ("/etc/steelconf/config/isholiday.php");
//include_once( '../include/configall.php');
include('/etc/steelconf/config/config.php');
require_once("/etc/steelconf/env/www_env.php");
//require("../include/adodb/adodb.inc.php");
require_once("/usr/local/www/libs/vendor/autoload.php");

include_once('/usr/local/www/libs/phpQuery/phpQuery.php');

include('/etc/steelconf/sthframe/steelhome_db_config.php');

//定时提醒任务  提醒到各个群   
$connall = ADONewConnection('mysql');  # create a connection
$connall->Connect($hostname_steelhome_r, $username_steelhome_r, $password_steelhome_r, $database_steelhome_r);

$conn_oa = ADONewConnection('mysql');
$conn_oa->Connect(HOST_NAME_OA.":".HOST_PORT_OA, USER_NAME_OA, USER_PASSWORD_OA, DATABASE_NAME_OA);

$WEBHOOK1=WEBHOOK1;
$WEBHOOK7=WEBHOOK7;
$WEBHOOK9=WEBHOOK9;
$WEBHOOK11=WEBHOOK11;
$WEBHOOK15=WEBHOOK15;
$WEBHOOK16=WEBHOOK16;
$WEBHOOK17=WEBHOOK17;
$WEBHOOK23=WEBHOOK23;
$WEBHOOK24=WEBHOOK24;
$WEBHOOK25=WEBHOOK25;
$WEBHOOK26=WEBHOOK26;
$WEBHOOK27=WEBHOOK27;
$WEBHOOK28=WEBHOOK28;
$WEBHOOK29=WEBHOOK29;
$WEBHOOK30=WEBHOOK30;
$WEBHOOK35=WEBHOOK35;
$WEBHOOK36=WEBHOOK36;
$WEBHOOK37=WEBHOOK37;



// $hook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80310365-89bc-4adc-a967-2eaa1ab67bce";
// $WEBHOOK1=$hook;
// $WEBHOOK7=$hook;
// $WEBHOOK9=$hook;
// $WEBHOOK11=$hook;
// $WEBHOOK15=$hook;
// $WEBHOOK16=$hook;
// $WEBHOOK17=$hook;
// $WEBHOOK23=$hook;
// $WEBHOOK24=$hook;
// $WEBHOOK25=$hook;
// $WEBHOOK26=$hook;
// $WEBHOOK27=$hook;
// $WEBHOOK28=$hook;
// $WEBHOOK29=$hook;
// $WEBHOOK30=$hook;
// $WEBHOOK35=$hook;
// $WEBHOOK36=$hook;
// $WEBHOOK37=$hook;



$depts=array(
	"WEBHOOK1"=>"技术交流群",
	"WEBHOOK7"=>"中西部",
	"WEBHOOK9"=>"北方组",
	"WEBHOOK11"=>"华东组",
	"WEBHOOK15"=>"煤焦",
	"WEBHOOK16"=>"合金辅料部",
	"WEBHOOK17"=>"炉料部",
	"WEBHOOK23"=>"特钢",
	"WEBHOOK24"=>"研究院",
	"WEBHOOK25"=>"国际部",
	"WEBHOOK26"=>"建材报告群",
	"WEBHOOK27"=>"中板报告群",
	"WEBHOOK28"=>"热轧报告群",
	"WEBHOOK29"=>"冷轧报告群 涂镀报告群",
	"WEBHOOK30"=>"晨会在线编辑群&市场报告会",
	"WEBHOOK35"=>"型材小组",
    "WEBHOOK36"=>"检修汇总群",
    "WEBHOOK37"=>"开工率、成交量、库存汇总群",
);

echo "<h2>******定时提醒任务*******</h2>";


// 测试
// $cronExpression = '0 30 12 ? * 1,3,5 *';
// if (checkYmdCronMatch($cronExpression)) {
//     echo '当前时间匹配 CRON 表达式';
// } else {
//     echo '当前时间不匹配 CRON 表达式';
// } 
// exit;
/*******获取所有提醒任务*******/
//当前时分
$nowtime=date('H:i');
if(isset($_REQUEST['time'])){
    $nowtime = $_REQUEST['time'];
}

$today = date("Y-m-d");
if(isset($_REQUEST['date'])){
	$today = $_REQUEST['date'];
}

$nowday=$today." ".$nowtime.":00"; //当前时间
$last_date = date('Y-m-d', strtotime($today . ' -1 day'));//昨天
$last_work_date=ish($last_date);//上一个工作日

$isholiday=_isholiday($today);//今天是否节假日
$isholiday_last=_isholiday($last_date);//昨天是否节假日
$qyuser_list= get_qyuserid($conn_oa);//所有企业微信用户id


$debug =$_REQUEST['debug'];
//给任务设置提醒时间
$task_sql_1="select * from `remind_task` where `status`=1 and isdel=0 and optype=1 and `cron`!=''";
$task_result_1=$connall->getArray($task_sql_1);

foreach($task_result_1 as $k=>$v){
    $cron=$v['cron'];
    $task_id=$v['id'];
    $remind_arr_crowd_1=array();
    $remind_arr_user_1=array();
    //节假日不执行
    if($isholiday && $v['ish']==0){
        continue;
    }
    $istxc=false;//是否设置提醒词
    //到点提醒
    if(checkCronMatch($cron,$nowday)){
        $sql="select * from `remind_task_set` where `taskid`='$task_id' and isdel=0 ";
        $task_set=$connall->getArray($sql);
        $allnews=array();
        //提醒到群
        if($v['txtarget']==1 && $v['crowd']!=""){
            $crowd_arr=explode(",",$v['crowd']);//群
            foreach($task_set as $k2=>$v2){
                //未发布
                $sdate=getSdate($today,$v2['date_type']);
                $is_report=check_news($connall,$v2['zxtitle'],$sdate,$today);
                if(!$is_report){
                    $arr=array(
                        "title"=>$v2['txtitle']!=""?$v2['txtitle']:$v2['zxtitle'],
                        "user"=>$v2['txuser']!=""?explode(",",$v2['txuser']):array(),//@个人
                    );
                    $allnews[]=$arr;
                    if($v2['txtitle']!=""){
                        $istxc=true;//是否设置提醒词
                    }
                }
            }

            if(!empty($allnews)){
                foreach($crowd_arr as $k1=>$v1){
                    $remind_arr_crowd_1[$v1]['news']=$allnews;
                    $remind_arr_crowd_1[$v1]['isall']=$v['isall'];
                }
            }
            
        }else if($v['txtarget']==2){
            //提醒到个人 云办公
            foreach($task_set as $k1=>$v1){
                if($v1['txuser']!="" && $v1['zxtitle']!=""){
                    $sdate=getSdate($today,$v1['date_type']);
                    $is_report=check_news($connall,$v1['zxtitle'],$sdate,$today);
                    if(!$is_report){
                    	$user_arr=explode(",",$v1['txuser']);//个人
                        foreach($user_arr as $k2=>$v2){
                            $remind_arr_user_1[$v2]['news'][]= $v1['txtitle']!=""?$v1['txtitle']:$v1['zxtitle'];
                        }
                        if($v1['txtitle']!=""){
                            $istxc=true;//是否设置提醒词
                        }
                    }
                }
            } 
        }
        // echo "<pre>";
        // print_r($remind_arr_crowd_1);
        // print_r($remind_arr_user_1);
        //发送通知
        if(!empty($remind_arr_crowd_1)){
            foreach($remind_arr_crowd_1 as $kk=>$vv){
                $dept=$depts[$kk]; //获取部门名称
                $hook=getVariableByName($kk); //获取机器人webhook   
                if($vv['isall']==1){
                    //@所有人
                    $content="";
                    $str="【".$v['title'].'('.count($vv['news']).")】\r\n";
                    echo "<font style='color:red;'>到点提醒到群@所有人</font><br>";
                    echo $dept."**********".$hook."<br>";
                    echo $str."<br>";
                    //是否设置提醒词
                    if($istxc){
                        //数组转字符串
                        foreach($vv['news'] as $kk2=>$vv2){
                            if($kk2==count($vv['news'])-1){
                                $content.=$vv2['title'];
                            }else{
                                $content.=$vv2['title']."，";
                            }
                        }
                        echo $content."<br>";
                    }else{
                        foreach($vv['news'] as $kk2=>$vv2){
                            $content.=$vv2['title']."\r\n";
                            echo $vv2['title']."<br>";
                        }
                    }
                    echo "<br>";
                    //echo $str.$content;
                    //发送通知到群并@所有人
                    if( $content!=""  && $debug!=1 ){
                        $ncontent=$str.$content;
                        $template = '{
                            "msgtype": "text",
                            "text": {
                                "content": "'.$ncontent.'",
                                "mentioned_list":["@all"]
                            }
                        }';
                        $dataRes =  request_post ( $hook, $template );
                        send_log($conn_oa,$dept,$content,$hook,$task_id);
                    }
                }else{
                    //@个人
                    $content="";
                    $str="# **".$v['title'].'('.count($vv['news']).")**\n\n";
                    echo "<font style='color:red;'>到点提醒到群@个人</font><br>";
                    echo $dept."**********".$hook."<br>";
                    echo $str."<br>";
                    foreach($vv['news'] as $kk2=>$vv2){
                        if(!empty($vv2['user'])){
                            foreach($vv2['user'] as $kk3=>$vv3){
                                $content.="<@".$qyuser_list[$vv3].">";
                                echo "<@".$qyuser_list[$vv3].">";
                            }
                        }
                        $content.=$vv2['title'];
                        $content.="\n";
                        echo $vv2['title']."<br>";
                    }
                    
                    echo "<br>";
                    //echo $str.$content;
                    //发送通知到群并@个人
                    if( $content!=""  && $debug!=1 ){
                        $ncontent=$str.$content;
                        $template = '{
                            "msgtype": "markdown",
                            "markdown": {
                                "content": "'.$ncontent.'"
                            }
                        }';
                        $dataRes1 = request_post( $hook, $template );
                        send_log($conn_oa,$dept,$content,$hook,$task_id);
                    }
                }
            }
        }else if(!empty($remind_arr_user_1)){

            foreach($remind_arr_user_1 as $kk=>$vv){
                $content="";
                $senduser=$kk;
                $str="【".$v['title']."】<br>";
                echo "<font style='color:red;'>到点提醒到个人</font><br>";
                //是否设置提醒词
                if($istxc){
                    foreach($vv['news'] as $kk2=>$vv2){
                        if($kk2==count($vv['news'])-1){
                            $content.=$vv2['title'];
                        }else{
                            $content.=$vv2['title']."，";
                        }
                    }
                }else{
                    foreach($vv['news'] as $kk2=>$vv2){
                        $content.=$vv2."<br>";
                    }
                }
                echo $qyuser_list[$senduser]."<br>";
                echo $str.$content;
                if($content!="" && $debug!=1){
                    //$senduser="452460";
                    $sendcontent=$str.$content;
                    send_qyuser($sendcontent,$senduser,$conn_oa,$task_id);
                }
            }
        }
    }
}


// 给资讯单独设置提醒时间
$task_sql_2="select * from `remind_task` where `status`=1 and isdel=0 and optype=2 ";
$task_result_2=$connall->getArray($task_sql_2);

foreach($task_result_2 as $k=>$v){
    //节假日不执行
    if($isholiday && $v['ish']==0){
        continue;
    }
    $task_id=$v['id'];
    //到点提醒资讯
    $sql="select * from `remind_task_set` where `taskid`='".$v['id']."' and isdel=0 ";
    $task_set=$connall->getArray($sql);

    $remind_arr_crowd_2=array();
    $remind_arr_user_2=array();

    $istxc=false;//是否设置提醒词
    //群
    $crowd_arr=explode(",",$v['crowd']);
    $allnews=array();
    foreach($task_set as $k1=>$v1){
        $cron=$v1['cron'];
        if(checkCronMatch($cron,$nowday)){
            $sdate=getSdate($today,$v1['date_type']);
            //提醒到群
            if($v['txtarget']==1 && $v['crowd']!=""){
                $is_report=check_news($connall,$v1['zxtitle'],$sdate,$today);
                if(!$is_report){
                    $arr=array(
                        "title"=>$v1['txtitle']!=""?$v1['txtitle']:$v1['zxtitle'],
                        "user"=>$v1['txuser']!=""?explode(",",$v1['txuser']):array(),//@个人
                    );
                    $allnews[]=$arr;
                    if($v1['txtitle']!=""){
                        $istxc=true;//是否设置提醒词	
                    }
                    
                }
            }else if($v['txtarget']==2 && $v1['txuser']!=""){
                //提醒到个人 云办公
                $is_report=check_news($connall,$v1['zxtitle'],$sdate,$today);
                if(!$is_report){
                    $user_arr=explode(",",$v1['txuser']);//个人
                    foreach($user_arr as $k2=>$v2){
                        $remind_arr_user_2[$v2]['news'][]=$v1['txtitle']!=""?$v1['txtitle']:$v1['zxtitle'];
                    } 
                    if($v1['txtitle']!=""){
                        $istxc=true;//是否设置提醒词	
                    }
                }
            }
        }
    }

    

    if(!empty($allnews)){
        foreach($crowd_arr as $k1=>$v1){
            $remind_arr_crowd_2[$v1]['news']=$allnews;
        }
    }

    // echo "<pre>";
    // print_r($remind_arr_crowd_2);
    // print_r($remind_arr_user_2);

    if(!empty($remind_arr_crowd_2)){
        if($v['isall']==1){
            //群@所有人
            $content="";
            foreach($remind_arr_crowd_2 as $kk=>$vv){
                $dept=$depts[$kk]; //获取部门名称
                $hook=getVariableByName($kk); //获取机器人webhook   
                $content="";
                $str="【".$v['title'].'('.count($vv['news']).")】\r\n";
                echo "<font style='color:red;'>#到点提醒到群@所有人</font><br>";
                echo $dept."**********".$hook."<br>";
                echo $str."<br>";
                if($istxc){
                    foreach($vv['news'] as $kk2=>$vv2){
                        if($kk2==count($vv['news'])-1){
                            $content.=$vv2['title'];
                        }else{
                            $content.=$vv2['title']."，";
                        }
                    }
                    echo $content."<br>";
                }else{
                    foreach($vv['news'] as $kk2=>$vv2){
                        $content.=$vv2['title']."\r\n";
                        echo $vv2['title']."<br>";
                    }
                }
                
                //echo $str.$content;
                //发送通知到群并@所有人
                if( $content!=""  && $debug!=1 ){
                    $ncontent=$str.$content;
                    $template = '{
                        "msgtype": "text",
                        "text": {
                            "content": "'.$ncontent.'",
                            "mentioned_list":["@all"]
                        }
                    }';
                    $dataRes =  request_post ( $hook, $template );
                    send_log($conn_oa,$dept,$content,$hook,$task_id);
                }
            }
        }else{
            //@个人
            $content="";
            foreach($remind_arr_crowd_2 as $kk=>$vv){
                $dept=$depts[$kk]; //获取部门名称
                $hook=getVariableByName($kk); //获取机器人webhook
                $str="# **".$v['title'].'('.count($vv['news']).")**\n\n";
                echo "<font style='color:red;'>#到点提醒到群@个人</font><br>";
                echo $dept."**********".$hook."<br>";
                echo $str."<br>";
                foreach($vv['news'] as $kk2=>$vv2){
                    if(!empty($vv2['user'])){
                        foreach($vv2['user'] as $kk3=>$vv3){
                            $content.="<@".$qyuser_list[$vv3].">";
                            echo "<@".$qyuser_list[$vv3].">";
                        }
                    }
                    $content.=$vv2['title'];
                    $content.="\n";
                    echo $vv2['title']."<br>";
                }
                echo "<br>";
                //echo $str.$content;
                //发送通知到群并@个人
                if( $content!=""  && $debug!=1 ){
                    $ncontent=$str.$content;
                    $template = '{
                        "msgtype": "markdown",
                        "markdown": {
                            "content": "'.$ncontent.'"
                        }
                    }';
                    $dataRes1 = request_post( $hook, $template );
                    send_log($conn_oa,$dept,$content,$hook,$task_id);
                }
            }
           
        }
    }else if(!empty($remind_arr_user_2)){
    	//群@个人
        $content="";
        $str="【".$v['title']."】<br>";
        foreach($remind_arr_user_2 as $kk=>$vv){
            $content="";
            $senduser=$kk;	
            echo "<font style='color:red;'>#到点提醒到个人</font><br>";
            if($istxc){
                foreach($vv['news'] as $kk2=>$vv2){
                    if($kk2==count($vv['news'])-1){
                        $content.=$vv2['title'];
                    }else{
                        $content.=$vv2['title']."，";
                    }
                }
            }else{
                foreach($vv['news'] as $kk2=>$vv2){
                    $content.=$vv2."<br>";
                }
            }
            
            echo $qyuser_list[$senduser]."<br>";
            echo $str.$content;
            if($content!="" && $debug!=1){
                //$senduser="452460";
                $sendcontent=$str.$content;
                send_qyuser($sendcontent,$senduser,$conn_oa,$task_id);
            }
        }
    }
}



// exit;


//次日提醒 
$lastday=$last_date." ".$nowtime.":00";
$lastworkday=$last_work_date." ".$nowtime.":00";

//给任务设置时间
$task_sql_3="select * from `remind_task` where `status`=1 and isdel=0 and optype=1 and nexttime!='' and `cron`!='' ";
$task_result_3=$connall->getArray($task_sql_3);

foreach($task_result_3 as $k=>$v){
    $cronExpression = $v['cron'];
    $nexttime=$v['nexttime'];
    $date=$last_date;
    $task_id=$v['id'];
    //节假日不执行
    // if($isholiday && $v['ish']==0){
    //     continue;
    // }
    //昨天是节假日 且节假日不提醒
    if($isholiday_last && $v['ish']==0){
        continue;
    }
    
    if($v['ish']==1 && checkYmdCronMatch($cronExpression,$lastday) && $nexttime==$nowtime){
        $date=$last_date;
    }else if($v['ish']==0 && checkYmdCronMatch($cronExpression,$lastworkday)  && $nexttime==$nowtime){
        $date=$last_work_date;
    }else{
        continue;
    }
    $istxc=false;//是否设置提醒词
    $remind_arr_crowd_3=array();
    $remind_arr_user_3=array();
    $sql="select * from `remind_task_set` where `taskid`='".$v['id']."' and isdel=0 ";
    $task_set=$connall->getArray($sql);
    //提醒到群
    if($v['txtarget']==1 && $v['crowd']!=""){
        $crowd_arr=explode(",",$v['crowd']);//群
        $allnews=array();
        foreach($task_set as $k2=>$v2){
            $sdate=getSdate($date,$v2['date_type']);
            $is_report=check_news($connall,$v2['zxtitle'],$sdate,$date);
            if(!$is_report){
            	$arr=array(
                    "title"=>$v2['txtitle']!=""?$v2['txtitle']:$v2['zxtitle'],
                    "user"=>$v2['txuser']!=""?explode(",",$v2['txuser']):array(),//@个人	
                );
                $allnews[]=$arr;
                if($v2['txtitle']!=""){
                    $istxc=true;//是否设置提醒词	
                }
            }
        }
        if(!empty($allnews)){
            foreach($crowd_arr as $k1=>$v1){
                $remind_arr_crowd_3[$v1]['news']=$allnews;
                $remind_arr_crowd_3[$v1]['isall']=$v['isall'];
            }
        }
    }else if($v['txtarget']==2){
        //提醒到个人 云办公
        foreach($task_set as $k1=>$v1){
            if($v1['txuser']!="" && $v1['zxtitle']!=""){
                $sdate=getSdate($date,$v1['date_type']);
                $is_report=check_news($connall,$v1['zxtitle'],$sdate,$date);
                if(!$is_report){
                    $user_arr=explode(",",$v1['txuser']);//个人
                    foreach($user_arr as $k2=>$v2){
                        $remind_arr_user_3[$v2]['news'][]=$v1['txtitle']!=""?$v1['txtitle']:$v1['zxtitle'];
                    }
                    if($v1['txtitle']!=""){
                        $istxc=true;//是否设置提醒词	
                    }
                }	
            } 	
        }	
    }

    //提醒标题
    $txtitle=$v['nexttitle']!=""?$v['nexttitle']:$v['title'];
    //发送通知
    if(!empty($remind_arr_crowd_3)){
        foreach($remind_arr_crowd_3 as $kk=>$vv){
            $dept=$depts[$kk]; //获取部门名称
            $hook=getVariableByName($kk); //获取机器人webhook   
            if($vv['isall']==1){
                //@所有人
                $content="";
                $str="【".date("n月j日",strtotime($date)).$txtitle.'('.count($vv['news']).")】\r\n";
                echo "<font style='color:red;'>次日到点提醒到群@所有人</font><br>";
                echo $dept."**********".$hook."<br>";
                echo $str."<br>";
                if($istxc){
                    foreach($vv['news'] as $kk2=>$vv2){
                        if($kk2==count($vv['news'])-1){
                            $content.=$vv2['title'];
                        }else{
                            $content.=$vv2['title']."，";
                        }
                    }
                    echo $content."<br>";	
                }else{
                    foreach($vv['news'] as $kk2=>$vv2){
                        $content.=$vv2['title']."\r\n";
                        echo $vv2['title']."<br>";
                    }
                }
                echo "<br>";
                //echo $str.$content;
                //发送通知到群并@所有人
                if( $content!=""  && $debug!=1 ){
                    $ncontent=$str.$content;
                    $template = '{
                        "msgtype": "text",
                        "text": {
                            "content": "'.$ncontent.'",
                            "mentioned_list":["@all"]
                        }
                    }';
                    $dataRes =  request_post ( $hook, $template );
                    send_log($conn_oa,$dept,$content,$hook,$task_id);
                }
            }else{
                //@个人
                $content="";
                $str="# **".date("n月j日",strtotime($date)).$txtitle.'('.count($vv['news']).")**\n\n";
                echo "<font style='color:red;'>次日到点提醒到群@所有人</font><br>";
                echo $dept."**********".$hook."<br>";
                echo $str."<br>";
                foreach($vv['news'] as $kk2=>$vv2){
                    if(!empty($vv2['user'])){
                        foreach($vv2['user'] as $kk3=>$vv3){
                            $content.="<@".$qyuser_list[$vv3].">";
                            echo "<@".$qyuser_list[$vv3].">";
                        }
                    }
                    $content.=$vv2['title'];
                    $content.="\n";
                    echo $vv2['title']."<br>";
                }
                echo "<br>";
                //echo $str.$content;
                //发送通知到群并@个人
                if( $content!=""  && $debug!=1 ){
                    $ncontent=$str.$content;
                    $template = '{
                        "msgtype": "markdown",
                        "markdown": {
                            "content": "'.$ncontent.'"
                        }
                    }';
                    $dataRes1 = request_post( $hook, $template );
                    send_log($conn_oa,$dept,$content,$hook,$task_id);
                }
            }
        }
    }else if(!empty($remind_arr_user_3)){
        foreach($remind_arr_user_3 as $kk=>$vv){
            $content="";
            $senduser=$kk;
            $str="【".date("n月j日",strtotime($date)).$txtitle.'('.count($vv['news']).")】<br>";
            echo "<font style='color:red;'>次日到点提醒到个人</font><br>";
            if($istxc){
                foreach($vv['news'] as $kk2=>$vv2){
                    if($kk2==count($vv['news'])-1){
                        $content.=$vv2['title'];
                    }else{
                        $content.=$vv2['title']."，";
                    }
                }
            }else{
                foreach($vv['news'] as $kk2=>$vv2){
                    $content.=$vv2."<br>";
                }
            }
            
            echo $qyuser_list[$senduser]."<br>";
            echo $str.$content;
            if($content!="" && $debug!=1){
                //$senduser="452460";
                $sendcontent=$str.$content;
                send_qyuser($sendcontent,$senduser,$conn_oa,$task_id);
            }
        }
    }

}

//给资讯标题单独设置时间
$task_sql_4="select * from `remind_task` where `status`=1 and isdel=0 and optype=2 and nexttime!=''  ";
$task_result_4=$connall->getArray($task_sql_4);
foreach($task_result_4 as $k=>$v){
    //节假日不执行
    
    // if($isholiday && $v['ish']==0){
    //     continue;
    // }
    //昨天是节假日 且节假日不提醒
    if($isholiday_last && $v['ish']==0){
        continue;
    }
    $task_id=$v['id'];
    $nexttime=$v['nexttime'];//次日提醒时间
    $sql="select * from `remind_task_set` where `taskid`='".$v['id']."' and isdel=0 ";
    $task_set=$connall->getArray($sql);
    $remind_arr_crowd_4=array();
    $remind_arr_user_4=array();
    //获取上一次日期
    $date=$last_date;
    if($v['ish']!=1){
        $date=$last_work_date;
    }
    $istxc=false;//是否设置提醒词
    //提醒到群
    if($v['txtarget']==1 && $v['crowd']!=""){
        $crowd_arr=explode(",",$v['crowd']);//群
        $allnews=array();
        foreach($task_set as $k2=>$v2){
            $cronExpression=$v2['cron'];
            if(($v['ish']==1 && checkYmdCronMatch($cronExpression,$lastday) && $nexttime==$nowtime)||($v['ish']==0 && checkYmdCronMatch($cronExpression,$lastworkday)  && $nexttime==$nowtime)){
                $sdate=getSdate($date,$v2['date_type']);
                $is_report=check_news($connall,$v2['zxtitle'],$sdate,$date);
                if(!$is_report){
                    $arr=array(
                        "title"=>$v2['txtitle']!=""?$v2['txtitle']:$v2['zxtitle'],
                        "user"=>$v2['txuser']!=""?explode(",",$v2['txuser']):array(),//@个人	
                    );
                    $allnews[]=$arr;
                    if($v2['txtitle']!=""){
                        $istxc=true;//是否设置提醒词	
                    }
                }	
            }
        } 	
        if(!empty($allnews)){
            foreach($crowd_arr as $k1=>$v1){
                $remind_arr_crowd_4[$v1]['news']=$allnews;
                $remind_arr_crowd_4[$v1]['isall']=$v['isall'];
            }	
        }
    }else if($v['txtarget']==2){
        //提醒到个人 云办公
        foreach($task_set as $k1=>$v1){
            $cronExpression=$v1['cron'];
            if(($v['ish']==1 && checkYmdCronMatch($cronExpression,$lastday) && $nexttime==$nowtime)||($v['ish']==0 && checkYmdCronMatch($cronExpression,$lastworkday)  && $nexttime==$nowtime)){
                $sdate=getSdate($date,$v1['date_type']);
                $is_report=check_news($connall,$v1['zxtitle'],$sdate,$date);
                if(!$is_report){
                    $user_arr=explode(",",$v1['txuser']);//个人
                    foreach($user_arr as $k2=>$v2){
                        $remind_arr_user_4[$v2]['news'][]=$v1['txtitle']!=""?$v1['txtitle']:$v1['zxtitle'];	
                    }	
                    if($v1['txtitle']!=""){
                        $istxc=true;//是否设置提醒词	
                    }
                }	
            }
        }	
    }

    // echo "<pre>";
    // print_r($remind_arr_crowd_4);
    // print_r($remind_arr_user_4);
    //提醒标题
    $txtitle=$v['nexttitle']!=""?$v['nexttitle']:$v['title'];
    if(!empty($remind_arr_crowd_4)){
        if($v['isall']==1){
            //群@所有人
            $content="";
            foreach($remind_arr_crowd_4 as $kk=>$vv){
                $dept=$depts[$kk]; //获取部门名称
                $hook=getVariableByName($kk); //获取机器人webhook   
                $content="";
                $str="【".date("n月j日",strtotime($date)).$txtitle.'('.count($vv['news']).")】\r\n";
                echo "<font style='color:red;'>次日到点提醒到群@所有人</font><br>";
                echo $dept."**********".$hook."<br>";
                echo $str."<br>";
                if($istxc){
                    foreach($vv['news'] as $kk2=>$vv2){
                        if($kk2==count($vv['news'])-1){
                            $content.=$vv2['title'];
                        }else{
                            $content.=$vv2['title']."，";
                        }
                    }
                    echo $content."<br>";	
                }else{
                    foreach($vv['news'] as $kk2=>$vv2){
                        $content.=$vv2['title']."\r\n";
                        echo $vv2['title']."<br>";
                    }
                }
                
                //echo $str.$content;
                echo "<br>";
                //发送通知到群并@所有人
                if( $content!=""  && $debug!=1 ){
                    $ncontent=$str.$content;
                    $template = '{
                        "msgtype": "text",
                        "text": {
                            "content": "'.$ncontent.'",
                            "mentioned_list":["@all"]
                        }
                    }';
                    $dataRes =  request_post ( $hook, $template );
                    send_log($conn_oa,$dept,$content,$hook,$task_id);
                }
            }
        }else{
            //群通知@个人
            $content="";
            echo "<font style='color:red;'>次日到点提醒到群@所有人</font><br>";
            foreach($remind_arr_crowd_4 as $kk=>$vv){
                $dept=$depts[$kk]; //获取部门名称
                $hook=getVariableByName($kk); //获取机器人webhook
                $content="";
                $str="# **".date("n月j日",strtotime($date)).$txtitle.'('.count($vv['news']).")**\n\n";
                echo $dept."**********".$hook."<br>";
                echo $str."<br>";
                foreach($vv['news'] as $kk2=>$vv2){
                    if(!empty($vv2['user'])){
                        foreach($vv2['user'] as $kk3=>$vv3){
                            $content.="<@".$qyuser_list[$vv3].">";
                            echo "<@".$qyuser_list[$vv3].">";
                        }
                    }
                    $content.=$vv2['title']."\r\n";
                    echo $vv2['title']."<br>";
                }
                //echo $str.$content;
                echo '<br>';
                if($content!="" && $debug!=1){
                    $ncontent=$str.$content;
                    $template = '{
                        "msgtype": "markdown",
                        "markdown": {
                            "content": "'.$ncontent.'"
                        }
                    }';
                    $dataRes1 = request_post( $hook, $template );
                    send_log($conn_oa,$dept,$content,$hook,$task_id);
                }
            }

        }
    }else if(!empty($remind_arr_user_4)){
        foreach($remind_arr_user_4 as $kk=>$vv){
            $content="";
            $senduser=$kk;
            $str="【".date("n月j日",strtotime($date)).$txtitle.'('.count($vv['news']).")】<br>";
            echo "<font style='color:red;'>次日到点提醒到个人</font><br>";
            if($istxc){
                foreach($vv['news'] as $kk2=>$vv2){
                    if($kk2==count($vv['news'])-1){
                        $content.=$vv2['title'];
                    }else{
                        $content.=$vv2['title']."，";
                    }
                }
            }else{
                foreach($vv['news'] as $kk2=>$vv2){
                    $content.=$vv2."<br>";
                }
            }
            echo $qyuser_list[$senduser]."<br>";
            echo $str.$content;
            if($content!="" && $debug!=1){
                //$senduser="452460";
                $sendcontent=$str.$content;
                send_qyuser($sendcontent,$senduser,$conn_oa,$task_id);	
            }	
        }	
    }

}




function send_qyuser($sendcontent,$senduser,$conn_oa,$task_id){
    $domea = APP_URL_WORK;
    //$domea = "https://work.steelhome.com";
    $post = array(
        'adminid' => $senduser,
        'msgtype' => 'text',
        'content' => $sendcontent,
    );
    $post_content = http_build_query($post);
    $options = array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type:application/x-www-form-urlencoded',
            'content' => $post_content,
        )
    );
    $url = $domea."/qiye/SendMessage.php";
    // echo $url."<br>";
    // echo '<pre>';
    // print_r($options);
    // exit;
    file_get_contents($url, false, stream_context_create($options));
    send_log($conn_oa,"云办公",$sendcontent,$senduser,$task_id);
}



function getSdate($date,$type){
    if($type==1){
        $sdate=date('Y-m-d', strtotime($date.'-3 day'));//周
    }else if($type==2){
        $sdate=date('Y-m-d', strtotime($date.'-15 day'));	//月
    }else if($type==3){
        $sdate=date('Y-m-d', strtotime($date.'-5 day'));	//旬
    }else{
        $sdate=$date;//日
    }
    return $sdate;
}

//提醒记录
function send_log($conn_oa,$dept,$content,$senduser,$task_id=''){
	if($content!=""){
		$sql="insert into qywx_qr_sp set logid='$task_id',qjtaskid=0,type=32,createtime=NOW(),title='".$dept."未上传资讯提醒',description='$content',senduser='$senduser',msgtype='textcard'";
		$conn_oa->execute($sql);
	}
}

function request_post($url = '', $param = '')  {
	if (empty($url) || empty($param))
	{
		return false;
	}
	$curl = curl_init(); // 启动一个CURL会话
	curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
	curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0); // 对认证证书来源的检查
	curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1); // 从证书中检查SSL加密算法是否存在
	curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']); // 模拟用户使用的浏览器
	curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); // 使用自动跳转
	curl_setopt($curl, CURLOPT_AUTOREFERER, 1); // 自动设置Referer
	curl_setopt($curl, CURLOPT_POST, 1); // 发送一个常规的Post请求
	curl_setopt($curl, CURLOPT_POSTFIELDS, $param); // Post提交的数据包
	curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
	curl_setopt($curl, CURLOPT_HEADER, 0); // 显示返回的Header区域内容
	curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回
	$data = curl_exec($curl); // 执行操作
	if (curl_errno($curl)) {
		echo 'Errno'.curl_error($curl);//捕抓异常
	}
	curl_close($curl); // 关闭CURL会话
	return $data; // 返回数据，json格式
}

function check_news($connall,$title,$sdate,$date){
    //title字符串转数组
    $title=htmlspecialchars_decode($title);
    $title_all=explode("&&",$title);
    $where="";
    if(!empty($title_all[0])){
        $title_arr=explode(",",$title_all[0]);
        if(count($title_arr)==1){
            $where.=" and ntitle like '%".$title_arr[0]."%'";
        }else{
            $where.=" and (";
            $kk=0;
            foreach($title_arr as $key=>$value){
                if($kk==0){
                    $where.=" ntitle like '%".$value."%'";
                }else{
                    $where.=" or ntitle like '%".$value."%'";
                }
                $kk++;
            }
            $where.=" )";
        }
    }
    
    if(!empty($title_all[1])){
    	$title_arr2=explode(",",$title_all[1]);
        foreach($title_arr2 as $key=>$value){
            $where.=" and ntitle like '%".$value."%'";
        }
    }


	$sql="select nid from news where ndate>='".$sdate." 00:00:00' and ndate<='".$date." 23:59:59' $where order by nid asc";
	echo $sql.'<br>';
	$newsList=$connall->getArray($sql);
	if(!empty($newsList)){
		return true;
	}else{
		return false;
	}	
}

function getVariableByName($name) {
    global $$name;
    return $$name;
}

function checkYmdCronMatch($cronExpression,$nowday) {
    $parts = explode(' ', $cronExpression);
    // 支持 5 位和 7 位 Cron 表达式
    if (count($parts) != 5 && count($parts) != 7) {
        return false;
    }

    if (count($parts) == 5) {
        // 5 位格式补全秒和年份部分
        array_unshift($parts, '0'); // 补秒
        $parts[] = '*';             // 补年
    }

    list($second, $minute, $hour, $dayOfMonth, $month, $dayOfWeek, $year) = $parts;

    // 当前时间，测试用固定时间
    $now = new DateTime($nowday);

    // 只匹配年月日和星期，忽略时分秒
    // 日期字段：日
    if (!matchCronPartYmd($dayOfMonth, $now->format('d')) && $dayOfMonth !== '?') {
        return false;
    }
    // 月份
    if (!matchCronPartYmd($month, $now->format('m'))) {
        return false;
    }
    // 检查星期
    if (!matchCronPart($dayOfWeek, $now->format('N'))) {
        return false;
    }
    // 年份
    if (!matchCronPartYmd($year, $now->format('Y'))) {
        return false;
    }
    return true;
}
function matchCronPartYmd($part, $value) {
    if ($part === '*') return true;

    foreach (explode(',', $part) as $section) {
        if (strpos($section, '/') !== false) {
            list($range, $step) = explode('/', $section);
            $step = intval($step);
            if ($range === '*') {
                if ($value % $step === 0) return true;
            } else if (strpos($range, '-') !== false) {
                list($start, $end) = explode('-', $range);
                if ($value >= $start && $value <= $end && ($value - $start) % $step === 0) return true;
            }
        } else if (strpos($section, '-') !== false) {
            list($start, $end) = explode('-', $section);
            if ($value >= $start && $value <= $end) return true;
        } else if ($section == $value) {
            return true;
        }
    }

    return false;
}
//解析cron表达式
function checkCronMatch($cronExpression,$nowday) {
    $parts = explode(' ', $cronExpression);
    // 支持 5 位和 7 位 Cron 表达式
    if (count($parts) != 5 && count($parts) != 7) {
        return false;
    }

    if (count($parts) == 5) {
        // 5 位格式补全秒和年份部分
        array_unshift($parts, '0');
        $parts[] = '*';
    }

    list($second, $minute, $hour, $dayOfMonth, $month, $dayOfWeek, $year) = $parts;
    $now = new DateTime();
    $now = new DateTime($nowday);

    // 检查秒
    if (!matchCronPart($second, $now->format('s'))) {
        return false;
    }

    // 检查分钟
    if (!matchCronPart($minute, $now->format('i'))) {
        return false;
    }

    // 检查小时
    if (!matchCronPart($hour, $now->format('H'))) {
        return false;
    }

    // 检查日期
    if (!matchCronPart($dayOfMonth, $now->format('d'))) {
        return false;
    }

    // 检查月份
    if (!matchCronPart($month, $now->format('m'))) {
        return false;
    }

    // 检查星期
    if (!matchCronPart($dayOfWeek, $now->format('N'))) {
        return false;
    }

    // 检查年份
    if (!matchCronPart($year, $now->format('Y'))) {
        return false;
    }

    return true;
}

function matchCronPart($cronPart, $timeValue) {
    if ($cronPart === '*' || $cronPart === '?') {
        return true;
    }

    $ranges = explode(',', $cronPart);
    foreach ($ranges as $range) {
        if (strpos($range, '-') !== false) {
            list($start, $end) = explode('-', $range);
            if ($timeValue >= $start && $timeValue <= $end) {
                return true;
            }
        } elseif ($range == $timeValue) {
            return true;
        }
    }

    return false;
}

function ish($date){
    if ( _isholiday($date) ) {
        for ($x=1; $x<=15; $x++) {
            $a="-".$x;
            $date2=date("Y-m-d",strtotime("$a day",strtotime("$date")));
            if ( _isholiday($date2) ) {	
            }else{
                $date=date("Y-m-d",strtotime("$a day",strtotime("$date")));
            break;
                }
        }
    }else{
        $date=$date;
    }
    return $date;
}
function get_qyuserid($conn_oa){
    $qyuserid_all=$conn_oa->query("select userid,qyuserid from qywxbindadminuser  where qyuserid!='' and truename!='' order by id desc ");
    $qyuserid_arr=array();
    foreach($qyuserid_all as $key=>$value){
        if(!isset($qyuserid_arr[$value['userid']])){
            $qyuserid_arr[$value['userid']]=$value['qyuserid'];
        }
    }
    return $qyuserid_arr;
}