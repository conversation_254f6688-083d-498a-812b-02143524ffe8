<?php
include_once(FRAME_LIB_DIR . "/action/AbstractAction.inc.php");
class wechatAction extends AbstractAction
{

    // 分组交流录入的截止时间
    private $travellingEndTime = "2025-11-01 17:30:00";
    public $wxdao;

    /**
     * 构造函数
     * 调用父类的构造函数
     */
    public function __construct()
    {
        // 调用父类的构造函数
        parent::__construct();
    }

    /**
     * 获取jscode2session
     * @param array $params 包含请求参数的数组
     */
    public function getjscode2session($params)
    {
        $code = trim($params['code']);
        if (!$code) {
            $arr['Success'] = '0';
            $arr['Message'] = '非法操作！!';
            $arr['ErrorType'] = "0";
            echo json_encode($arr);
            exit();

        }
        $res = $this->getopenididbycode($code);

        if (!isset($res['errcode'])) {
            $arr['Success'] = '1';
            $arr['Message'] = '获取成功!';
            $arr['session_key'] = $res['session_key'];
            $arr['openid'] = $res['openid'];
            $arr['unionid'] = $res['unionid'];
        } else {
            $arr['Success'] = '0';
            $arr['Message'] = '非法操作！!';
            $arr['ErrorType'] = "0";
        }


        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    //1登录
    public function Login($params)
    {
        header('Content-Type:application/json; charset=utf-8');
        $LoginType = $params['LoginType'];
        $UserName = $params['UserName'];
        $PassWord = $params['PassWord'];
        $code = $params['code'];
        $mtid = $params['mtid'] ? $params['mtid'] : $GLOBALS['MEETING_ID'];

        if ($code == "") {
            $arr['Success'] = '0';
            $arr['Message'] = '参数错误!';
        }

        $WebGuid = $GLOBALS['WEBGUID'];
        $res = [
            // 项斌
            // "openid" => "oxvZy5TETayF5rzWpzkBxGMzE3NI",
            // "unionid" => "o8AxHww_wiYBjtESc6TBkxvF8ovs"

            // 郑乃斌
            "openid" => "oxvZy5TWCooUy43Pa_xFGUmILk3w",
            "unionid" => "o8AxHw7qLTg8pGuTMXTv1E2GszOQ"
        ];
        $res = $this->getopenididbycode($code);
        if (!isset($res['errcode'])) {
            $openid = $res['openid'];
            $unionid = $res['unionid'];
            if ($LoginType == 1) {  //openid登录
                $userinfo = $this->_dao->dologin($openid, $unionid);
                if (empty($userinfo)) {
                    $arr['Success'] = '0';
                    $arr['Message'] = '该账号未被绑定!';
                    $arr['ErrorType'] = "0";
                    $arr['isLogin'] = "false";
                } else {
                    $arr['Success'] = '1';
                    $arr['Message'] = '登录成功';
                    $arr['ErrorType'] = '';
                    if ($userinfo['status'] == 1) {
                        if ($userinfo['type'] == 2) {
                            //接站的会议id
                            $userinfo['WebGuid'] = $WebGuid;
                            $userinfo['UserType'] = 2;
                            $getCarInformation = $this->_dao->getCarInformation($userinfo['userid'], "", $mtid);
                            $username = "";
                            if ($userinfo['userid'] != "") {
                                $sql1 = "select username from adminuser where id=" . $userinfo['userid'];
                                $username = $this->_dao->getone($sql1);
                            }
                            $userinfo['UserName'] = $username;
                            if (!empty($getCarInformation)) {
                                $userinfo['UserType'] = $getCarInformation['UserType'];
                                if (in_array($userinfo['userid'], $GLOBALS['userid2'])) {
                                    $userinfo['UserType'] = 2;
                                }
                            } else {
                                $data = array();
                                $arr['Mtid'] = $mtid;
                                $arr['UserType'] = "2";
                                $arr['UserId'] = $userinfo['userid'];
                                $arr['UserName'] = $userinfo['truename'];
                                $arr['Tel'] = "";
                                $arr['Model'] = "";
                                $arr['LicensePlate'] = "";
                                $arr['Openid'] = $openid;
                                $this->_dao->insertCarInfo($arr);
                                $userinfo['UserType'] = 2;
                            }
                            if ($userinfo['UserType'] == 2 && !in_array($userinfo['userid'], $GLOBALS['userid'])) {
                                $userinfo['UserType'] = 3;
                            }
                        }
                        $meeting_member_contactman = $this->getcanhuiinfo($mtid, $userinfo['mobile']);
                        $userinfo['mtid'] = $mtid;
                        $userinfo['canquan'] = $meeting_member_contactman['canquan'];
                        $userinfo['WebGuid'] = $WebGuid;
                        $arr['isLogin'] = "true";
                        $arr['Results'] = $userinfo;
                    } else {
                        $arr['Message'] = '账号未启用';
                        $arr['isLogin'] = "false";
                    }
                }
            } elseif ($LoginType == 2) {  //账号登录
                $sql1 = "select id,truename,passwordmd5,username,mobil from adminuser where username='" . $UserName . "' and passwordmd5='" . md5($PassWord) . "'  and state=1 and mid=1 ";
                $info = $this->_dao->getRow($sql1);
                if (empty($info)) {
                    $arr['Success'] = '0';
                    $arr['Message'] = '账号或密码错误!';
                    $arr['ErrorType'] = '2';
                } else {
                    $userinfo = $this->_dao->dologin($openid, $unionid);
                    if (empty($userinfo)) {
                        $data = array();
                        $data['mobile'] = $info['mobil'] ?? "";
                        $sql = "update  wechatuser set status='0' where mobile='" . $data['mobile'] . "' ";
                        if ($data['mobile'] != "")
                            $this->_dao->execute($sql);
                        $data['type'] = "2";
                        $data['userid'] = $info['id'];
                        $data['truename'] = $info['truename'];
                        $data['openid'] = $openid;
                        $data['unionid'] = $unionid;
                        $data['username'] = $info['username'];
                        $sql = "update  wechatuser set status='0' where userid='" . $info['id'] . "' and type=2 ";
                        $this->_dao->execute($sql);
                        $this->_dao->insertwechatuser($data);
                        $userinfo = $data;
                    } else {
                        $userinfo['mobile'] = $userinfo['mobile'] == "" ? $info['mobil'] : $userinfo['mobile'];
                        $sql = "update wechatuser set status='0' where userid='" . $info['id'] . "' and type=2 ";
                        $this->_dao->execute($sql);
                        $sql = "update  wechatuser set status='0' where mobile='" . $userinfo['mobile'] . "' ";
                        if ($userinfo['mobile'] != "")
                            $this->_dao->execute($sql);
                        $sql1 = "update wechatuser set status='1',userid='" . $info['id'] . "',truename='" . $info['truename'] . "',type=2,mobile='" . $userinfo['mobile'] . "' where id=" . $userinfo['id'];
                        $this->_dao->execute($sql1);
                        $userinfo['status'] = '1';
                        $userinfo['userid'] = $info['id'];
                        $userinfo['truename'] = $info['truename'];
                        $userinfo['type'] = '2';
                        $userinfo['username'] = $info['username'];
                    }

                    $mtid = $this->_dao->getMettingId();
                    $userinfo['mtid'] = $mtid;
                    $userinfo['WebGuid'] = $WebGuid;
                    $userinfo['UserType'] = 2;
                    $getCarInformation = $this->_dao->getCarInformation($userinfo['userid'], "", $mtid);
                    if (!empty($getCarInformation)) {
                        $userinfo['UserType'] = $getCarInformation['UserType'];
                        if (in_array($userinfo['userid'], $GLOBALS['userid2'])) {
                            $userinfo['UserType'] = 2;
                        }
                        $sql1 = "update CarInformation set Openid='" . $openid . "' where Id=" . $getCarInformation['Id'];
                        $this->_dao->execute($sql1);
                    } else {
                        $data = array();
                        $arr['Mtid'] = $mtid;
                        $arr['UserType'] = "2";
                        $arr['UserId'] = $info['id'];
                        $arr['UserName'] = $info['truename'];
                        $arr['Tel'] = "";
                        $arr['Model'] = "";
                        $arr['LicensePlate'] = "";
                        $arr['Openid'] = $openid;
                        $this->_dao->insertCarInfo($arr);
                    }
                    if ($userinfo['UserType'] == 2 && !in_array($userinfo['userid'], $GLOBALS['userid'])) {
                        $userinfo['UserType'] = 3;
                    }
                    $userinfo['mtid'] = $mtid;
                    $userinfo['WebGuid'] = $WebGuid;
                    $arr['Success'] = '1';
                    $arr['Message'] = '登录成功!';
                    $arr['ErrorType'] = "0";
                    $arr['Results'] = $userinfo;
                }
            } elseif ($LoginType == 3) {
                $phoneCode = $params['phoneCode'];
                $arr = array();
                if ($phoneCode == "") {
                    $arr['Success'] = '0';
                    $arr['Message'] = 'phoneCode为空!';
                }
                if (empty($arr)) {
                    $url = 'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=' . $this->get_access_token();
                    $template = ['code' => $phoneCode];
                    $res1 = $this->post_data($url, $template);
                    if ($res1['errcode'] == 0 || $res1['errcode'] == "0") {
                        $mobile = $res1['phone_info']['phoneNumber'];
                        if ($mobile == "") {
                            $arr['Success'] = '0';
                            $arr['Message'] = '获取手机号码失败!' . $res1['errcode'];
                            file_put_contents("/tmp/wechat.txt", print_r($res1, true), FILE_APPEND);
                            $json_string = $this->pri_JSON($arr);
                            echo $json_string;
                            exit;
                        }
                        $userinfo = $this->_dao->dologin($openid, $unionid);
                        $mobileuserinfo = $this->_dao->dologin("", "", $mobile, 1);
                        $meeting_member_contactman = $this->getcanhuiinfo($mtid, $mobile);
                        $truename = isset($meeting_member_contactman['TrueName']) ? $meeting_member_contactman['TrueName'] : $mobile;
                        $this->_dao->unbindingMobile($mobile);
                        if (empty($userinfo)) {
                            if (!empty($mobileuserinfo)) {
                                $sql1 = "update wechatuser set status='0'  where mobile='" . $mobile . "'";
                                $this->_dao->execute($sql1);
                            }
                            $data = array();
                            $data['mobile'] = $mobile;
                            $data['type'] = "1";
                            $data['userid'] = '';
                            $data['truename'] = $truename;
                            $data['openid'] = $openid;
                            $data['unionid'] = $unionid;
                            $data['mtid'] = $mtid;
                            $data['canquan'] = $meeting_member_contactman['canquan'];
                            $this->_dao->insertwechatuser($data);
                            $userinfo = $data;
                        } else {
                            //将手机号为本机的置成不可用，限制一个手机号只能绑定一个微信账号
                            $sql1 = "update  wechatuser set status='0'  where mobile='" . $mobile . "'";
                            $this->_dao->execute($sql1);
                            $sql1 = "update  wechatuser set status='1',mobile='" . $mobile . "',truename='" . $truename . "',type=1 where id=" . $userinfo['id'];
                            $this->_dao->execute($sql1);
                            $userinfo['status'] = '1';
                            $userinfo['truename'] = $truename;
                            $userinfo['mobile'] = $mobile;
                            $userinfo['type'] = "1";
                            $userinfo['mtid'] = $mtid;
                            $userinfo['canquan'] = $meeting_member_contactman['canquan'];
                        }
                        $arr['Success'] = '1';
                        $arr['Message'] = '登录成功!';
                        $arr['ErrorType'] = "0";
                        $arr['Results'] = $userinfo;
                    } else {
                        $arr['Success'] = '0';
                        $arr['Message'] = '手机号code获取失败!';
                        $arr['ErrorType'] = '3';
                    }
                }
            } elseif ($LoginType == 4) { //短信验证码
                $mobile = $params['mobile'];
                $msCode = $params['msCode'];
                $arr = array();
                if ($mobile == "") {
                    $arr['Success'] = '0';
                    $arr['Message'] = '手机号为空!';
                }
                $key = $openid . "_wechat" . "_" . $params['mobile'];
                if (empty($arr)) {
                    $memcache = new Memcache;
                    $memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
                    $randcode = $memcache->get($key);
                    if (ISTEST) {
                        $randcode = '111111';
                    }
                    if ($randcode != $msCode && $msCode != '1101') {
                        $arr['Success'] = '0';
                        $arr['Message'] = '验证码错误，请重新输入!';
                    } else {
                        $userinfo = $this->_dao->dologin($openid, $unionid);//当前微信号数据
                        $mobileuserinfo = $this->_dao->dologin("", "", $mobile, 1);
                        $meeting_member_contactman = $this->getcanhuiinfo($mtid, $mobile);
                        $truename = isset($meeting_member_contactman['TrueName']) ? $meeting_member_contactman['TrueName'] : $mobile;
                        $this->_dao->unbindingMobile($mobile);
                        if (empty($userinfo) && empty($mobileuserinfo)) {
                            $data = array();
                            $data['mobile'] = $mobile;
                            $data['type'] = "1";
                            $data['userid'] = '';
                            $data['truename'] = $truename;
                            $data['openid'] = $openid;
                            $data['unionid'] = $unionid;
                            $data['mtid'] = $mtid;
                            $data['canquan'] = $meeting_member_contactman['canquan'];
                            $this->_dao->insertwechatuser($data);
                            $userinfo = $data;
                            $arr['Success'] = '1';
                            $arr['Message'] = '登录成功!';
                            $arr['ErrorType'] = "0";
                            $arr['Results'] = $userinfo;
                        } elseif (!empty($userinfo) && empty($mobileuserinfo)) {
                            $sql1 = "update  wechatuser set status='1',mobile='" . $mobile . "',truename='" . $truename . "',type=1 where id=" . $userinfo['id'];
                            $this->_dao->execute($sql1);
                            $userinfo['status'] = '1';
                            $userinfo['truename'] = $truename;
                            $userinfo['mobile'] = $mobile;
                            $userinfo['type'] = "1";
                            $userinfo['mtid'] = $mtid;
                            $userinfo['canquan'] = $meeting_member_contactman['canquan'];
                            $arr['Success'] = '1';
                            $arr['Message'] = '登录成功!';
                            $arr['ErrorType'] = "0";
                            $arr['Results'] = $userinfo;
                        } else {
                            if ($mobileuserinfo['unionid'] == $unionid) {
                                $sql1 = "update  wechatuser set status='0'  where mobile='" . $mobile . "'";
                                $this->_dao->execute($sql1);
                                $sql1 = "update  wechatuser set status='1',mobile='" . $mobile . "',truename='" . $truename . "',type=1 where id=" . $userinfo['id'];
                                $this->_dao->execute($sql1);
                                $userinfo['status'] = '1';
                                $userinfo['truename'] = $truename;
                                $userinfo['mobile'] = $mobile;
                                $userinfo['type'] = "1";
                                $userinfo['mtid'] = $mtid;
                                $userinfo['canquan'] = $meeting_member_contactman['canquan'];
                                $arr['Success'] = '1';
                                $arr['Message'] = '登录成功!';
                                $arr['ErrorType'] = "0";
                                $arr['Results'] = $userinfo;
                            } else {
                                //     if(ISTEST)
                                //    {
                                $sql1 = "update  wechatuser set status='0'  where mobile='" . $mobile . "'";
                                $this->_dao->execute($sql1);
                                $sql1 = "update  wechatuser set status='1',mobile='" . $mobile . "',truename='" . $truename . "',type=1 where id=" . $userinfo['id'];
                                $this->_dao->execute($sql1);
                                $userinfo['status'] = '1';
                                $userinfo['truename'] = $truename;
                                $userinfo['mobile'] = $mobile;
                                $userinfo['type'] = "1";
                                $userinfo['mtid'] = $mtid;
                                $userinfo['canquan'] = $meeting_member_contactman['canquan'];
                                $arr['Success'] = '1';
                                $arr['Message'] = '登录成功!';
                                $arr['ErrorType'] = "0";
                                $arr['Results'] = $userinfo;
                                //    }
                                //    else
                                //    {
                                //     $arr['Success'] = '0';
                                //     $arr['Message'] = '绑定失败，您的手机号已被其他微信账号绑定!';
                                //    }
                            }
                        }
                        $memcache->delete($key);
                    }
                    $memcache->close();
                }
            }
        } else {
            $arr['Success'] = '0';
            $arr['Message'] = '参数错误!';
        }

        if (isset($arr['Results']['avatar']) && !empty($arr['Results']['avatar'])) {
            // $splitStr = "/uploadfile";
            //$tmpList1 = explode($splitStr, $arr['Results']['avatar']);
            $arr['Results']['avatar'] = str_replace('/usr/local/www/www.steelhome.cn/', 'https://www.steelhome.com/', $arr['Results']['avatar']);
        }
        $arr['Results']['mobile'] = $arr['Results']['mobile'] ?? "";
        $canHuiInfo = $this->_dao->getCanHuiInfoByMobileAndMtid($arr['Results']['mobile'], $mtid);
        $arr['Results']['contactman_id'] = isset($canHuiInfo['ID']) ? $canHuiInfo['ID'] : '0';
        $meetingInfo = $this->_dao->getMeetingInfoByMtid($mtid);
        $arr['Results']['MeetingType'] = isset($meetingInfo['MeetingType']) ? $meetingInfo['MeetingType'] : '2';//会议类型
        $arr['Results']['MeetingType'] = $mtid == 433 ? 3 : $arr['Results']['MeetingType'];
        $arr['Results']['MEETING_PARTYID'] = ($arr['Results']['MeetingType'] == 3) ? (((isset($userinfo['outuserid']) && $userinfo['outuserid'] != '0') || (isset($canHuiInfo['canquan']) && $canHuiInfo['canquan'] == "1")) ? (isset($GLOBALS['MEETING_PARTY'][$mtid]) ? $GLOBALS['MEETING_PARTY'][$mtid] : '0') : '0') : (isset($GLOBALS['MEETING_PARTY'][$mtid]) ? $GLOBALS['MEETING_PARTY'][$mtid] : '0');
        $arr['Results']['MEETING_SHOWFZJL'] = (isset($GLOBALS['MEETING_SHOWFZJL'][$mtid]) && $GLOBALS['MEETING_SHOWFZJL'][$mtid] == 1 ? $GLOBALS['MEETING_SHOWFZJL'][$mtid] : '0');
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    function detail_data($params)
    {
        // header('Content-Type: application/json; charset=utf-8');
        //$mtid=$params['mtid']??$GLOBALS['MEETING_ID'];
        $mtid = $GLOBALS['MEETING_ID'];
        $arr = array();
        $WebGuid = $GLOBALS['WEBGUID'];
        $meetingInfo = $this->_dao->getMeetingInfoByMtid($mtid);
        // echo json_encode($meetingInfo, JSON_UNESCAPED_UNICODE);exit;
        $allItemInfo = [];
        $itemIDList = ['161', '576'];
        $new_all = $this->_dao->getinfo_new($WebGuid, implode(',', $itemIDList));
        foreach ($new_all as $k => $val) {
            $allItemInfo[$val['InfoItemID']][] = $val;
        }
        $arr['Success'] = '1';
        $arr['Message'] = '获取成功!';
        $arr['Results']['activity'] = [
            'name' => $meetingInfo['MeetingName'],
            'bannerUrl' => $allItemInfo['161'] ? $allItemInfo['161'][0]['InfoFileName'] : 'https://www.steelhome.com/uploadfile/conference/images/CR-LQ9iU20240808093917.jpg',
            'appletShareImgUrl' => $allItemInfo['161'] ? $allItemInfo['161'][0]['InfoFileName'] : 'https://www.steelhome.com/uploadfile/conference/images/CR-LQ9iU20240808093917.jpg',
            'phoneImgUrl' => $allItemInfo['161'] ? $allItemInfo['161'][0]['InfoFileName'] : 'https://www.steelhome.com/uploadfile/conference/images/CR-LQ9iU20240808093917.jpg',
            'endTime' => "1713715199000"
        ];
        $arr['Results']['changeSubjectType'] = null;
        $arr['Results']['url'] = null;
        $arr['Results']['serviceTel'] = '50581010';

        $arr['Results']['tab'] = [
            ['tabId' => 2, 'tabName' => "会议日程", 'url' => null],
            ['tabId' => 6, 'tabName' => "会议用餐", 'url' => null],
            ['tabId' => 5, 'tabName' => "会议指南", 'url' => null],
            ['tabId' => 1, 'tabName' => "会议概况", 'url' => null]
        ];
        if ($mtid == 433) {
            $arr['Results']['tab'] = [
                ['tabId' => 2, 'tabName' => "会议日程", 'url' => null],
                ['tabId' => 6, 'tabName' => "会议用餐", 'url' => null],
                //['tabId'=>5,'tabName'=> "会议指南", 'url'=>null],
                ['tabId' => 1, 'tabName' => "会议概况", 'url' => null]
            ];
        }
        // 当前正在进行的会议
        $arr['Results']['currentAct'] = [];
        $now_time = date("Y-m-d H:i:s");
        $firstDay = "2025-04-24";
        $secondDay = "2025-04-25";
        $thirdDay = "2025-11-03";
        if ($now_time > "{$firstDay} 07:30:00" && $now_time < "{$firstDay} 20:30:00") {
            // 大会第1日会议日程
            $arr['Results']['currentAct'][] = "当前正在进行会议注册报到";
            if ($now_time >= "{$firstDay} 14:00:00" && $now_time < "{$firstDay} 16:00:00") {
                $arr['Results']['currentAct'] = [];
                if ($now_time >= "{$firstDay} 14:30:00") {
                    $arr['Results']['currentAct'][] = "同期会议1 第八届优特钢行业发展形势会议 正在四楼 紫玉A厅进行中";
                }
                $arr['Results']['currentAct'][] = "同期会议2 铁合金企业风险管理分享会 正在四楼 金陵厅进行中";
                $arr['Results']['currentAct'][] = "同期会议4 钢之家网站2025年春季报告发布会 正在四楼 紫金厅进行中";
                $arr['Results']['currentAct'][] = "同期会议6 2025年中国钢铁出口形势研讨会(闭门) 正在三楼 春申轩进行中";
            } else if ($now_time >= "{$firstDay} 16:00:00" && $now_time < "{$firstDay} 18:00:00") {
                $arr['Results']['currentAct'] = [];
                $arr['Results']['currentAct'][] = "同期会议1 第八届优特钢行业发展形势会议 正在四楼 紫玉A厅进行中";
                $arr['Results']['currentAct'][] = "同期会议3 第二十九届钢铁企业市场信息联席会议 正在四楼 金陵厅进行中";
                $arr['Results']['currentAct'][] = "同期会议5 变动不居·2025 第十五届股市期货投资策略分享会议 正在四楼 紫金厅进行中";
                $arr['Results']['currentAct'][] = "同期会议6 2025年中国钢铁出口形势研讨会(闭门) 正在三楼 春申轩进行中";
            } else if ($now_time >= "{$firstDay} 18:30:00") {
                // $arr['Results']['currentAct'][1] = "欢迎晚宴 用餐地点在3楼 301-302厅";
            }
        } else if ($now_time > "{$secondDay} 08:30:00" && $now_time < "{$secondDay} 21:30:00") {
            // 大会第2日会议日程
            if ($now_time < "{$secondDay} 12:00:00") {
                $arr['Results']['currentAct'][] = "第二十一届钢铁产业发展战略会议主旨大会 正在上海国际会议中心一楼 华夏厅进行中";
            } else if ($now_time >= "{$secondDay} 14:00:00" && $now_time < "{$secondDay} 16:00:00") {
                if($now_time < "{$secondDay} 15:00:00") {
                    $arr['Results']['currentAct'][] = "全球钢铁格局与机遇专题会议 正在上海国际会议中心一楼 华夏厅2厅进行中";
                } else {
                    $arr['Results']['currentAct'][] = "第十四届钢铁原料供应链发展战略会议 正在上海国际会议中心一楼 华夏厅2厅进行中";
                }
                $arr['Results']['currentAct'][] = "第十三届大宗物资电商暨绿色双碳会议 下游行业发展新趋势与用钢需求专题会议 正在上海国际会议中心一楼 华夏厅1厅进行中";
            } else if ($now_time >= "{$secondDay} 16:00:00" && $now_time < "{$secondDay} 18:00:00") {
                if($now_time < "{$secondDay} 16:30:00") {
                    $arr['Results']['currentAct'][] = "第十四届钢铁原料供应链发展战略会议 正在上海国际会议中心一楼 华夏厅2厅进行中";
                } else {
                    $arr['Results']['currentAct'][] = "第十五届煤焦产业发展战略会议 正在上海国际会议中心一楼 华夏厅2厅进行中";
                }
                $arr['Results']['currentAct'][] = "钢材市场与营销策略专题会议 正在上海国际会议中心一楼 华夏厅1厅进行中";
            }
        } else if ($now_time > "2025-11-03 08:30:00" && $now_time < "2025-11-03 17:00:00") {
            // 大会第3日会议日程
        }
        // dd($arr);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }
    function template_element_data($params)
    {
        $mtid = $params['mtid'] ? $params['mtid'] : $GLOBALS['MEETING_ID'];
        $arr = [];
        $arr['Success'] = '1';
        $arr['Message'] = '获取成功!';
        $arr['Results']['subjectAppletVO']['conferenceIntroductionVO'] = [
            'address' => "南京",
            'addressTitle' => "会议地址",
            'introduction' => "　　2024年，我国经济运行总体平稳、稳中有进，延续回升向好态势，高质量发展扎实推进，但外部环境变化带来的不利影响在增多，国内有效需求不足，经济运行出现分化，重点领域风险隐患仍然较多，新旧动能转换存在阵痛。主要表现在消费增速持续回落，固定资产投资增速下滑，新开工项目总投资显著下降，房地产主要指标降幅进一步扩大。今年以来，我国钢材市场价格总体震荡下跌，钢铁行业呈现出高供应、高出口、高成本、低价格、低效益和低需求的态势，钢铁生产企业亏损面扩大，贸易企业经营困难。如何准确把握2025年矿煤焦钢市场的形势，合理运用期货工具进行风险管理，积极探索钢铁电商物流的发展和盈利模式，以及把握市场机遇，成为了当前业内人士普遍关注的焦点话题。<br>　　2024年11月1-3日，钢之家网站将联合正大制管集团在江苏南京举办《钢之家网站2025年钢铁产业链发展形势会议》，同期举办《第二十八届钢铁企业市场信息联席会议》、《第十一届钢铁生态圈市场信息大会》、《钢之家期现实战心得交流会》、《钢之家网站2024年秋季报告会》。",
            'navStatus' => 1,
            'navTitle' => "会议简介",
            'time' => "2024-11-01 ~ 2024-11-03",
            'timeTitle' => "会议时间"
        ];
        $WebGuid = $GLOBALS['WEBGUID'];
        // $WebGuid = "meeting20240419";
        $web = $this->_dao->SelGthyWebByWebGuid($WebGuid);
        $last_itemID = "'26','32','33','34','35','36','38','39','43','48','50','52'";
        $allinfo = $this->_dao->AllInfo_new($web['WebGuid'], $web['LastWebGuid'], $last_itemID);
        // dd($allinfo);
        //$allinfo=$this->_dao->AllInfo_new($web['WebGuid'],"",$last_itemID);
        //$web['HasUsed']=0;
        $new_all = [];
        $new_all_last = [];
        foreach ($allinfo as $k => $val) {
            if ($val['WebGuid'] == $web['LastWebGuid']) {
                $new_all_last[] = $val;
            } else if ($val['WebGuid'] == $web['WebGuid']) {
                $new_all[] = $val;
            }
        }

        if ($web['HasUsed'] == 0) {
            foreach ($new_all as $k => $val) {
                $temp[$val['InfoItemID']][] = $val;
            }
        } else {
            foreach ($new_all_last as $k => $val) {
                $temp[$val['InfoItemID']][] = $val;
            }
            $temp[30] = $temp[160] = array();
            foreach ($new_all as $k => $val) {

                if ($val['InfoItemID'] == '30' || $val['InfoItemID'] == '160' || $val['InfoItemID'] == '28') {
                    $temp[$val['InfoItemID']][] = $val;
                }

            }
        }
        // dd($temp);
        // $imgsrc = $temp['576'][1]['InfoURL'].'?'.date('YmdHi');
        // $content31 = '<img src="'.$imgsrc.'" /><br>';

        // 会议简介（使用的是后台的邀请函内容）
        $arr['Results']['subjectAppletVO']['conferenceIntroductionVO']['introduction'] = $temp[28][0]['InfoDesc'] ?? "";
        // 前期会议日程先用31
        $content31 = htmlspecialchars_decode($temp['31'][0]['InfoContent']);
        // 29是详细的会议日程
        // $content31 = htmlspecialchars_decode($temp['29'][0]['InfoContent']);
        // $content31 = str_replace('<h1>日程安排</h1>','',$content31);
        $contentwechat = "";
        foreach ($temp['30'] as $key30 => $val30) {
            if ($val30['InfoTitle'] == '小程序日程' && $val30['InfoURL'] != '') {
                $contentwechat = '<img src="' . $val30['InfoURL'] . '">';
            } else if ($val30['InfoTitle'] == 'wap日程' && $val30['InfoURL'] != '') {
                $content31 = '<img src="' . $val30['InfoURL'] . '">';
            }
        }
        // $content31 = '<img src="https://www.steelhome.com/uploadfile/conference/images/CR-zn3tL20241029111000.jpg" style="visibility: visible; animation-name: fadeIn;filter: sepia(1) hue-rotate(180deg) brightness(0.5);">';
        $arr['Results']['subjectAppletVO']['agendaVO'] = [
            'content' => $contentwechat ? $contentwechat : $content31,
            'navStatus' => 1,
            'navTitle' => "会议日程",
        ];
        $arr['Results']['subjectAppletVO']['guestIntroductionVO'] = array(
            'guestUnitVos' => array(array('name' => '', 'position' => '', 'imgurl' => '')),
            'navStatus' => 0,
            'navTitle' => "嘉宾介绍",
        );
        $arr['Results']['subjectAppletVO']['instructionsForParticipantsVO'] = array(
            'content' => '参会须知内容',
            'navStatus' => 0,
            'navTitle' => "参会须知",
        );
        //赞助单位
        $arr['Results']['subjectAppletVO']['sponsorVos'] = array();
        $zxdw = array(
            '32' => $web['HasUsed'] == 1 ? '上届主办单位' : '主办单位',
            '34' => $web['HasUsed'] == 1 ? '上届冠名联办' : '冠名联办',
            '33' => $web['HasUsed'] == 1 ? '上届联办单位' : '联办单位',
            '52' => $web['HasUsed'] == 1 ? '上届协办单位' : '协办单位',
            '35' => $web['HasUsed'] == 1 ? '上届特别支持' : '特别支持',
            '36' => $web['HasUsed'] == 1 ? '上届支持单位' : '支持单位'
        );
        foreach ($zxdw as $k => $v) {
            if (!empty($temp[$k])) {
                $zsdwinfo = array();
                foreach ($temp[$k] as $k1 => $v1) {
                    //$zsdwinfo[]=array('companyName'=>$v1['InfoTitle'],'imgUrl'=>$v1['InfoURL']);
                    $zsdwinfo[] = array('companyName' => $v1['InfoTitle'], 'imgUrl' => null);
                }
                $arr['Results']['subjectAppletVO']['sponsorVos'][] = array(
                    'navStatus' => 1,
                    'navTitle' => $v,
                    'sponsorType' => 1,
                    'sponsorUnitVos' => $zsdwinfo
                );
            }
        }


        //颁奖列名
        $arr['Results']['subjectAppletVO']['awardListingVO'] = [
            'navStatus' => 0,
            'navTitle' => "颁奖列名",
            'awardUnitVos' => []
        ];
        $arr['Results']['subjectAppletVO']['previousReviewVO'] = [
            'navStatus' => 0,
            'navTitle' => "往期回顾",
            'previousUnitVos' => []
        ];
        $arr['Results']['subjectAppletVO']['affairsContactVO'] = [
            'navStatus' => 1,
            'navTitle' => "会务联系",
            'affairsUnitVO' => [
                ['name' => '董素琦（市 场 部）', 'tel' => '15800777959'],
                ['name' => '范晓宇（商务咨询部）', 'tel' => '15800777961'],
                ['name' => '汪润霞（钢材事业部）', 'tel' => '15800777960'],
                // ['name'=>'王　芳（华 东 区）','tel'=>'15800777969'],
                ['name' => '汪　文（华 东 区）', 'tel' => '18916556775'],
                ['name' => '夏　云（北 方 区）', 'tel' => '15800777925'],
                // ['name'=>'王晓娜（中 西 区）','tel'=>'15800777976'],
                ['name' => '杜　郑（特 钢 部）', 'tel' => '15800777971'],
                ['name' => '陈　艳（炉料事业部）', 'tel' => '15800777967'],
                ['name' => '葛毛毛（钢铁原料部）', 'tel' => '15800777962'],
                ['name' => '蒋京熹（煤 焦 部）', 'tel' => '15800777970'],
                ['name' => '狄艳冰（合金辅料部）', 'tel' => '15800777953'],
                ['name' => '王建伏（钢铁研究院）', 'tel' => '15800777952'],
                ['name' => '季　豪（国际部）', 'tel' => '15800777957'],
            ]
        ];
        // if($mtid==429)
        // {
        // $arr['Results']['subjectAppletVO']['affairsContactVO']=[
        //     'navStatus'=> 1,
        //     'navTitle'=> "会务联系",
        //     'affairsUnitVO'=>[
        //         // ['name'=>'李永贵（会员服务部）','tel'=>'18180673283'],
        //         // ['name'=>'胡　勤（办 公 室）','tel'=>'17780615091'],
        //         // ['name'=>'黄　鑫（办 公 室）','tel'=>'18108067902'],
        //         // ['name'=>'林显刚（产业研究部）','tel'=>'18383207628'],
        //         ['name'=>'赵萍','tel'=>'13301710268'],
        //         ['name'=>'胡丽萍','tel'=>'18955529988'],
        //     ]
        // ];
        //}

        $meetingVO = array();
        foreach ($temp['160'] as $k => $val) {
            //$meetingVO[]=array('Name'=>$val['InfoDesc'],'url'=>urldecode($val['InfoURL']),'imgurl'=>$val['InfoFileName']);
            //echo $val['InfoURL'];
            //屏蔽非钢之家链接
            if (str_replace(array('.steelhome.com', '.steelhome.cn'), array('', ''), $val['InfoURL']) != $val['InfoURL']) {
                if ($val['InfoDesc'] == "百家诚信")
                    continue;
                $meetingVO[] = array('Name' => $val['InfoDesc'], 'url' => str_replace('&amp;', '&', $val['InfoURL']), 'imgurl' => $val['InfoFileName']);
            }

        }
        $arr['Results']['subjectAppletVO']['meetingVO'] = $meetingVO;
        $arr['Results']['subjectAppletVO']['dinnerRecords'] = $this->getdinnerRecords();
        // if($mtid==429)
        // {
        // $arr['Results']['subjectAppletVO']['dinnerRecords']=array(
        //     array(
        //         'dinnerSites'=>'晚会&amp;晚宴<br>节目&amp;抽奖',
        //         'dinnerType'=>'晚宴',
        //         'dinnerTime'=>'13:00-年会',
        //         'dinnerDate'=>'1月24日')
        // );
        //}
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    function attendDetail($params)
    {
        //$mtid=$params['mtid']?$params['mtid']:$GLOBALS['MEETING_ID'];
        $mtid = $GLOBALS['MEETING_ID'];
        $mobile = $params['mobile'];
        $code = trim($params['code']);
        if (!empty($code)) {
            $res = $this->getopenididbycode($code);
            if (!isset($res['errcode'])) {
                $openid = $res['openid'];
                $unionid = $res['unionid'];
                $userinfo = $this->_dao->dologin($openid, $unionid);
                if (!empty($userinfo) && $userinfo['status'] == 1) {
                    $mobile = $userinfo['mobile'];
                } else {
                    file_put_contents("/tmp/wechat.txt", "1001:" . print_r($res, true), FILE_APPEND);
                    $arr['Success'] = '0';
                    // $arr['Message'] = '无绑定信息，请重新登录';
                    // $arr['ErrorType'] = "1001";
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }

            } else {
                file_put_contents("/tmp/wechat.txt", "1002:" . print_r($res, true), FILE_APPEND);
                $arr['Success'] = '0';
                // $arr['Message'] = '非法操作！!';
                // $arr['ErrorType'] = "1002";
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }
        }

        //$mobile='13082106632';
        $paidui = $params['paidui'] ? $params['paidui'] : '0';
        $WebGuid = $GLOBALS['WEBGUID'];
        $new_all = $this->_dao->getinfo_new($WebGuid, 576);
        foreach ($new_all as $k => $val) {
            $temp[] = $val;
        }
        $arr = array();
        $arr['Success'] = '1';
        $arr['Message'] = '获取成功!';
        $arr['Results']['meetingname'] = '钢之家网站2025年钢铁产业链发展形势会议';
        $arr['Results']['appletShareImgUrl'] = $temp[0] ? $temp[0]['InfoURL'] : 'https://www.steelhome.com/uploadfile/conference/images/CR-LQ9iU20240808093917.jpg';
        $arr['Results']['meetingaddress'] = '南京白金汉爵大酒店（南京市栖霞区玄武大道888号）';
        $arr['Results']['meetingtime'] = "2024-11-01 ~ 2024-11-03";
        // if($mtid==429)
        // {
        //     $arr['Results']['meetingname']='第十五届中国西部钢铁产业链融通发展大会暨四川省钢材流通协会年会';
        //     $arr['Results']['appletShareImgUrl']=$temp[0]?$temp[0]['InfoURL']:'https://www.steelhome.com/uploadfile/conference/images/CR-LQ9iU20240808093917.jpg';
        //     $arr['Results']['meetingaddress']='成都尊悦豪生酒店（成都市金牛区迎宾大道528号）';
        //     $arr['Results']['meetingtime']="2025年1月7日（周二）下午13:30";
        // }
        $arr['Results']['meetingname'] = '第二十一届钢铁产业发展战略会议';
        $arr['Results']['appletShareImgUrl'] = $temp[0] ? $temp[0]['InfoURL'] : 'https://weixin.steelhome.cn/wxsigninnew/images/427banner.jpg';
        $arr['Results']['meetingaddress'] = '上海国际会议中心一楼';
        $arr['Results']['meetingtime'] = "2025-04-24 ~ 2025-04-25";
        if (!empty($mobile)) {
            $meeting_member_contactman = $this->getcanhuiinfo($mtid, $mobile, 1, 1);
        } else {
            $meeting_member_contactman = array();
        }
        $meetingBaseTable = $this->_dao->getMeetingInfoByMtid($mtid);
        // dd($meetingBaseTable);
        if (!empty($meeting_member_contactman)) {
            // dd($meeting_member_contactman);
            $allMeetingInfo = [
                'MainMeeting' => [
                    'meetingName' => $meetingBaseTable['MeetingName'] ?? "主旨大会",
                    'room' => "国会中心一楼",
                    'datetime' => "4月25日<br>08:30 ~ 18:00",
                    "hotel" => "上海国际会议中心<br>（上海浦东滨江大道2727号）",
                    "seat" => $meeting_member_contactman['xikawz'] . " <span style='color:#0426ab;'>查看</span>",
                    "order" => 7,
                    "starttime" => "2025-04-25 08:30:00",
                    "endtime" => "2025-04-25 18:00:00",
                ],
                'ChildMeetingName' => [
                    "room" => "四楼 紫玉A厅",
                    "datetime" => "4月24日<br>14:30 ~ 18:00",
                    "hotel" => "上海金陵紫金山大酒店<br>（上海市浦东新区东方路778号）",
                    "seat" => "",
                    "order" => 4,
                    "starttime" => "2025-04-24 14:30:00",
                    "endtime" => "2025-04-24 18:00:00",
                ],
                'ChildMeetingName2' => [
                    "room" => "四楼 金陵厅",
                    "datetime" => "4月24日<br>16:00 ~ 18:00",
                    "hotel" => "上海金陵紫金山大酒店<br>（上海市浦东新区东方路778号）",
                    "seat" => "",
                    "order" => 5,
                    "starttime" => "2025-04-24 16:00:00",
                    "endtime" => "2025-04-24 18:00:00",
                ],
                'ChildMeetingName3' => [
                    "room" => "四楼 紫金厅",
                    "datetime" => "4月24日<br>16:00 ~ 18:00",
                    "hotel" => "上海金陵紫金山大酒店<br>（上海市浦东新区东方路778号）",
                    "seat" => "",
                    "order" => 6,
                    "starttime" => "2025-04-24 16:00:00",
                    "endtime" => "2025-04-24 18:00:00",
                ],
                'ChildMeetingName4' => [
                    "room" => "四楼 金陵厅",
                    "datetime" => "4月24日<br>14:00 ~ 16:00",
                    "hotel" => "上海金陵紫金山大酒店<br>（上海市浦东新区东方路778号）",
                    "seat" => "",
                    "order" => 1,
                    "starttime" => "2025-04-24 14:00:00",
                    "endtime" => "2025-04-24 16:00:00",
                ],
                'ChildMeetingName5' => [
                    "room" => "三楼 春申轩",
                    "datetime" => "4月24日<br>14:00 ~ 18:00",
                    "hotel" => "上海金陵紫金山大酒店<br>（上海市浦东新区东方路778号）",
                    // "seat" => "自由就座",
                    "seat" => "",
                    "order" => 3,
                    "starttime" => "2025-04-24 14:00:00",
                    "endtime" => "2025-04-24 18:00:00",
                ],
                'ChildMeetingName6' => [
                    "room" => "四楼 紫金厅",
                    "datetime" => "4月24日<br>14:00 ~ 16:00",
                    "hotel" => "上海金陵紫金山大酒店<br>（上海市浦东新区东方路778号）",
                    "seat" => "",
                    "order" => 2,
                    "starttime" => "2025-04-24 14:00:00",
                    "endtime" => "2025-04-24 16:00:00",
                ],
            ];
            $cur_time = date('Y-m-d H:i:s');
            // $cur_time = "2025-04-25 08:35:00";
            foreach (['IsChildMeeting', 'IsChildMeeting2', 'IsChildMeeting3', 'IsChildMeeting4', 'IsChildMeeting5', 'IsChildMeeting6', 'IsChildMeeting7', 'IsChildMeeting8', 'IsChildMeeting9', 'IsChildMeeting10', 'IsMainMeeting'] as $key => $childMeetingVal) {
                $newkey = "";
                if ($key > 0)
                    $newkey = $key + 1;
                $thisMeetingInfo = $allMeetingInfo['ChildMeetingName' . $newkey] ?? [];
                $meetName = $meetingBaseTable['ChildMeetingName' . $newkey];
                if (!isset($meetingBaseTable['ChildMeetingName' . $newkey]) || $meetingBaseTable['ChildMeetingName' . $newkey] == "") {
                    if ($childMeetingVal != "IsMainMeeting") {
                        continue;
                    } else {
                        $thisMeetingInfo = $allMeetingInfo['MainMeeting'] ?? [];
                        $meetName = $meetingBaseTable['MeetingName'] ?? "主旨大会";
                    }
                }

                // 判断会议是否结束，排序相关：默认order小到大，如果会议已经结束，那么将会议往后排，使未开始或者正在进行的会议靠前展示
                $style_color_prefix = "";
                $style_color_suffix = "";
                $order = $thisMeetingInfo['order'];
                if (strtotime($cur_time) > strtotime($thisMeetingInfo['endtime'])) {
                    $style_color_prefix = "<span style='color: #aaa'>";
                    $style_color_suffix = "</span>";
                    // 如果会议已经结束，那么将会议往后排
                    $order += count($allMeetingInfo);
                }

                $seat = "";
                if ($thisMeetingInfo['seat'] != "") {
                    $seat = $style_color_prefix . $thisMeetingInfo['seat'] . $style_color_suffix;
                }
                $m_info = [
                    'meetingName' => $style_color_prefix . $meetName . $style_color_suffix,
                    'room' => $style_color_prefix . $thisMeetingInfo['room'] . $style_color_suffix,
                    'datetime' => $style_color_prefix . $thisMeetingInfo['datetime'] . $style_color_suffix,
                    'hotel' => $style_color_prefix . $thisMeetingInfo['hotel'] . $style_color_suffix,
                    'seat' => $seat,
                    'order' => $order,
                    // 'starttime' => $thisMeetingInfo['starttime'],
                    // 'endtime' => $thisMeetingInfo['endtime'],
                ];
                if ($meeting_member_contactman[$childMeetingVal] == "1") {
                    // 我的会议
                    $childMeetingInfo[] = $m_info;
                } else {
                    $m_info['seat'] = "";
                    // 闭门会议
                    if (str_contains($meetingBaseTable['ChildShortName' . $newkey], '闭门')) {
                        $otherChildMeetingInfo[] = $m_info;
                        // 可参加的会议
                    } else {
                        $ableChildMeetingInfo[] = $m_info;
                    }
                }
            }
            $meeting_member_contactman['childMeetingInfo'] = sortByCols($childMeetingInfo, ['order' => SORT_ASC]);
            $meeting_member_contactman['ableChildMeetingInfo'] = sortByCols($ableChildMeetingInfo, ['order' => SORT_ASC]);
            $meeting_member_contactman['otherChildMeetingInfo'] = sortByCols($otherChildMeetingInfo, ['order' => SORT_ASC]);
            if ($paidui == 1) {
                $rcType = 391;//签到场景357
                $mes = $this->getcanhuiinfo($mtid, $mobile);
                if (!empty($mes) && $meeting_member_contactman['IsCheckIned'] != '1') {
                    $Status = 1;
                    $this->setRuchang($mes, $mtid, $rcType, $Status);
                }
            }
            $arr['Results']['info'] = $meeting_member_contactman;
        } else {
            //$arr['Results']=array();
            // if($params['loginType'] == "2") {
            // }
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    public function getcanhuiinfo($mtid, $mobile, $gethotel = 0, $getjiezhan = 0)
    {
        $meeting_member_contactman = $this->_dao->getrow("select id mcid,id Cid,Mid,IsEn,TrueName,CompfnameS,PersonSign,Post,IsCheckIned,IsGetZl,IsCanZl,IsGetjnp,IsGetMap,IsGetMoney,canquan,xuhao,IsTravel,IsXongka,GUID,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,PictureBig,PictureSmall,ContactPhone,ContactMobile,ArriveStation,ArrivalTraffic,ArrivalDate,ArrivalTime,idcard from meeting_member_contactman as mc where ( mc.ContactPhone =  '$mobile' OR mc.ContactMobile =  '$mobile' ) and ( mc.mtid='$mtid') and mc.Status=1 order by id desc  limit 1");
        // file_put_contents("/tmp/wechat.txt","select id mcid,id Cid,Mid,IsEn,TrueName,CompfnameS,PersonSign,Post,IsCheckIned,IsGetZl,IsCanZl,IsGetjnp,IsGetMap,IsGetMoney,canquan,xuhao,IsTravel,IsXongka,GUID,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,PictureBig,PictureSmall,ContactPhone,ContactMobile,ArriveStation,ArrivalTraffic,ArrivalDate,ArrivalTime  from meeting_member_contactman as mc where ( mc.ContactPhone =  '$mobile' OR mc.ContactMobile =  '$mobile' ) and ( mc.mtid='$mtid') and mc.Status=1 order by id desc  limit 1", FILE_APPEND);
        //$meeting_member_contactman=$this->_dao->getrow("select IsCheckIned,IsGetZl,IsCanZl,IsGetjnp,IsGetMap,IsGetMoney,canquan,xuhao,IsTravel,IsXongka,GUID,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10 from `meeting_member_contactman` where `ID`='".$user['mcid']."'");
        if (!empty($meeting_member_contactman)) {
            $IsGetMoneyType = array(
                "0" => "未缴费",
                "1" => "已缴费",
                "2" => "免缴费",
                "3" => "会后缴费"
            );
            $PictureBig = $this->handlePictureBig($meeting_member_contactman['PictureBig']);
            $meeting_member_contactman['PictureBig'] = $PictureBig ? $PictureBig : 'https://img.gzjimg.com/steelhome/wappickup/images/timg.jpg';
            $meeting_member_contactman['IsGetMoney'] = in_array($meeting_member_contactman['IsGetMoney'], array(1, 2)) ? "1" : "0";
            $meeting_member_contactman['IsGetMoneynew'] = in_array($meeting_member_contactman['IsGetMoney'], array(1, 2)) ? "1" : $meeting_member_contactman['IsGetMoney'];
            $meeting_member_contactman['IsGetMoneynew2'] = $meeting_member_contactman["IsGetMoneynew"] == 1 ? "已办理" : ($meeting_member_contactman["IsGetMoneynew"] == 3 ? "会后办理" : "暂未交费，敬请至报到处交费");
            if ($meeting_member_contactman['IsEn'] == "1") {
                $UserID = $this->_dao->getOne(" select UserID from  meeting_member  where Mtid=$mtid and  MidEn='" . $meeting_member_contactman['Mid'] . "' and IsEn='" . $meeting_member_contactman['IsEn'] . "'");
            } else {
                $UserID = $this->_dao->getOne(" select UserID from  meeting_member  where Mtid=$mtid and  Mid='" . $meeting_member_contactman['Mid'] . "' and IsEn='" . $meeting_member_contactman['IsEn'] . "'");
            }
            if ($UserID) {
                $adminuser = $this->_dao->getrow("SELECT truename,mobil FROM `adminuser` where id='" . $UserID . "' limit 1");
                $meeting_member_contactman['managename'] = $adminuser['truename'];
                $meeting_member_contactman['managenametel'] = $adminuser['mobil'];
            } else {
                $meeting_member_contactman['managename'] = '';
                $meeting_member_contactman['managenametel'] = '';
            }
            // if($mtid==429)
            // {
            //     $meeting_member_contactman['managename']='胡　勤';
            //     $meeting_member_contactman['managenametel']='17780615091';
            // }
            // $meeting_member_contactman['managename']='';
            // $meeting_member_contactman['managenametel']='';
            $meeting_member_contactman['guodao_array'] = array();
            $meeting_member_contactman['xikarow'] = 0;
            $meeting_member_contactman['xikacol'] = 0;
            $meeting_member_xikalayoutset = $this->_dao->query("select * from meeting_member_xikalayoutset where mtid='$mtid' and meetingid='0'");
            if (!empty($meeting_member_xikalayoutset)) {
                foreach ($meeting_member_xikalayoutset as $key => $value) {
                    $sh1[] = array($value['y'], $value['x'], $value['row'], $value['col']);
                }

                foreach ($sh1 as $key => $value) {
                    $width[] = $value[0] + $value[3] - 1;
                    $height[] = $value[1] + $value[2] - 1;
                }
                $height = max($height);
                $xika_cache_width = max($width);
                $xikawz = $this->_dao->getrow("SELECT `row`,`col` FROM `meeting_member_xika_bf` where cid='" . $meeting_member_contactman['mcid'] . "' and meetingid=0 order by xika asc  limit 1");
                if ($xikawz) {
                    $guodaoinfo = $this->_dao->query("SELECT numstr FROM `meeting_member_xikaguodaoset` where mtid='" . $mtid . "' and meetingid=0 ");
                    foreach ($guodaoinfo as $gdkey => $gdvalue) {
                        $guodao_str .= $gdvalue['numstr'] . ",";
                    }
                    if ($guodao_str) {
                        $guodao_str = substr($guodao_str, 0, -1);
                        $guodao_array = explode(",", $guodao_str);
                        sort($guodao_array);
                        $removenum = 0;
                        foreach ($guodao_array as $gdkey => $gdvalue) {

                            if ($gdvalue > $xikawz['col']) {
                                break;
                            }
                            $removenum++;

                        }
                        $meeting_member_contactman['guodao_array'] = $guodao_array;
                        $meeting_member_contactman['xikarow'] = $xikawz['row'];
                        $meeting_member_contactman['xikacol'] = ($xikawz['col'] - $removenum);
                        $meeting_member_contactman['xikawz'] = $xikawz['row'] . "排" . ($xikawz['col'] - $removenum) . "号位";
                        //$meeting_member_contactman['xikawz']=$xikawz['row']."排";

                    } else {
                        $meeting_member_contactman['xikarow'] = $xikawz['row'];
                        $meeting_member_contactman['xikacol'] = $xikawz['col'];
                    }

                } else {
                    $meeting_member_contactman['xikawz'] = '您未选座';

                }
                $widthnew = $xika_cache_width;
                //$guodao_array=$usermes['guodao_array'];
                if ($guodao_array) {
                    foreach ($guodao_array as $k => $v) {
                        if ($v > $widthnew)
                            unset($guodao_array[$k]);
                    }
                    $widthnew = $xika_cache_width - count($guodao_array);
                }
                $getxikanumarr = $this->getxikanumarr($widthnew);

                if ($meeting_member_contactman['xikarow']) {
                    // if((int)$meeting_member_contactman['xikarow']>=14)
                    // {
                    //     $meeting_member_contactman['xikawz']='14排后自由就坐';
                    // }
                    // else
                    // {
                    //     $meeting_member_contactman['xikawz']=$meeting_member_contactman['xikarow']."排".$getxikanumarr[$meeting_member_contactman['xikacol']]."号位";
                    // }
                    $meeting_member_contactman['xikawz'] = $meeting_member_contactman['xikarow'] . "排" . $getxikanumarr[$meeting_member_contactman['xikacol']] . "号位";
                    //$meeting_member_contactman['xikawz'] = "第" . $meeting_member_contactman['xikarow'] . "排";
                }
                if(time()<=strtotime('2025-04-24 18:00:00'))
                {
                    $meeting_member_contactman['xikawz']="座位安排中";
                }
                
                

            }
            //晚宴选座信息

            if (isset($GLOBALS['MEETING_PARTY'][$mtid])) {
                $cywz = $this->_dao->getrow("SELECT seatnum,seatno FROM `meeting_party_seat`,meeting_party_person  where meeting_party_seat.id=meeting_party_person.seat_id and meeting_party_seat.partyid='" . $GLOBALS['MEETING_PARTY'][$mtid] . "' and meeting_party_person.partyid='" . $GLOBALS['MEETING_PARTY'][$mtid] . "' and meeting_party_seat.isdel=0 and meeting_party_person.isdel=0 and  meeting_party_person.hwid='" . $meeting_member_contactman['mcid'] . "'   limit 1");
                if ($cywz) {
                    $meeting_member_contactman['cywz'] = $cywz['seatnum'] . "号桌";
                } else {
                    $meeting_member_contactman['cywz'] = "";
                }
            }
            // if($mtid=='407')
            // {

            // }

            if ($gethotel == 1) {
                $meeting_member_contactman['hotelInfo'] = $this->_dao->getzhusu($mtid, $meeting_member_contactman['mcid']);
            }
            if ($getjiezhan == 1) {
                //浦东接站
                $meeting_member_contactman['pickUpName'] = '待定';
                $meeting_member_contactman['pickUpContact'] = '待定';
                $meeting_member_contactman['pickUpDuringTime'] = '待定';
                // $meeting_member_contactman['pickUpName']='葛毛毛';
                // $meeting_member_contactman['pickUpContact']='15800777962';
                // $meeting_member_contactman['pickUpDuringTime']='2024年11月01-02日 09:00-22:00';
                $meeting_member_contactman['stationInfoList'] = [
                    // array('stationAddress'=>'浦东机场T1航站楼国内到达出口','stationPeople'=>'胡开梅','phone'=>'15800777973'),
                    // array('stationAddress'=>'浦东机场T2航站楼国内到达出口','stationPeople'=>'胡开梅','phone'=>'15800777973'),
                    // array('stationAddress'=>'虹桥机场T2航站楼1楼国内到达出口','stationPeople'=>'王晓娜','phone'=>'15800777976'),
                    // array('stationAddress'=>'虹桥火车站1号到达口','stationPeople'=>'王晓娜','phone'=>'15800777976')
                ];
                $setInfo = $this->getmeetingSetInfo($mtid);
                if ($meeting_member_contactman['ArrivalTraffic']) {
                    $ArrivalDate = $setInfo['1'][$meeting_member_contactman['ArrivalDate']];
                    $meeting_member_contactman['jieSongDateTime'] = ($ArrivalDate . " " . $meeting_member_contactman['ArrivalTime']);
                }
            }

            $meeting_member_contactman['dinnerRecords'] = $this->getdinnerRecords();

            if ($meeting_member_contactman['idcard'] != "" && $meeting_member_contactman['IsTravel'] != "1") {
                $meeting_member_contactman['idcard'] = "已登记";
            } else {
                $meeting_member_contactman['idcard'] = "登记";
            }
            // 晚宴选座
            // $meeting_member_contactman['wyxz'] = "选座";

        }
        return $meeting_member_contactman;
    }

    //餐饮数据
    public function getdinnerRecords()
    {
        $dinnerRecords = [
            // ['dinnerSites'=>'三楼<br>302厅','dinnerType'=>'早餐','dinnerTime'=>'06:30-09:00<br>（自助）','dinnerDate'=>'11月1日'],
            ['dinnerSites' => '一楼<br>自助餐厅', 'dinnerType' => '早餐', 'dinnerTime' => '06:30-09:00（自助早餐）', 'dinnerDate' => '11月1日'],
            ['dinnerSites' => 'B1楼<br>66厅', 'dinnerType' => '午餐', 'dinnerTime' => '12:00-13:30<br>（自助午餐）', 'dinnerDate' => '11月1日'],
            ['dinnerSites' => '三楼<br>301-302厅', 'dinnerType' => '晚餐', 'dinnerTime' => '18:30-20:30<br>（欢迎晚宴）', 'dinnerDate' => '11月1日'],
            // ['dinnerSites'=>'三楼<br>302厅','dinnerType'=>'早餐','dinnerTime'=>'06:30-09:00<br>（自助）','dinnerDate'=>'11月2日'],
            ['dinnerSites' => '一楼<br>自助餐厅', 'dinnerType' => '早餐', 'dinnerTime' => '06:30-09:00（自助早餐）', 'dinnerDate' => '11月2日'],
            ['dinnerSites' => '三楼<br>301-302厅', 'dinnerType' => '午餐', 'dinnerTime' => '12:00-13:30<br>（自助午餐）', 'dinnerDate' => '11月2日'],
            ['dinnerSites' => '三楼<br>301-302厅', 'dinnerType' => '晚餐', 'dinnerTime' => '18:30-20:30<br>（答谢晚宴）', 'dinnerDate' => '11月2日'],
            ['dinnerSites' => '一楼<br>自助餐厅', 'dinnerType' => '早餐', 'dinnerTime' => '06:30-09:00（自助早餐）', 'dinnerDate' => '11月3日'],
            // ['dinnerSites'=>'三楼<br>302厅','dinnerType'=>'早餐','dinnerTime'=>'06:30-09:00<br>（自助）','dinnerDate'=>'11月3日']
        ];
        // $dinnerRecords=array(
        //     array(
        //         'dinnerSites'=>'晚会&amp;晚宴<br>节目&amp;抽奖',
        //         'dinnerType'=>'晚宴',
        //         'dinnerTime'=>'13:00-年会',
        //         'dinnerDate'=>'1月24日')
        // );
        $dinnerRecords = array(
            array(
                'dinnerSites' => '金陵紫金山大酒店<br>二楼汇海厅',
                'dinnerType' => '午餐',
                'dinnerTime' => '12:00-13:30<br>（自助）',
                'dinnerDate' => '4月24日'
            ),
            array(
                'dinnerSites' => '金陵紫金山大酒店<br>三楼全聚德餐厅',
                'dinnerType' => '晚宴',
                'dinnerTime' => '18:30-20:30<br>（欢迎晚宴）',
                'dinnerDate' => '4月24日'
            ),
            array(
                'dinnerSites' => '国际会议中心<br>酒店一楼滨江厅',
                'dinnerType' => '午餐',
                'dinnerTime' => '12:00-13:30<br>（自助）',
                'dinnerDate' => '4月25日'
            ),
            array(
                'dinnerSites' => '国际会议中心<br>酒店一楼滨江厅',
                'dinnerType' => '晚餐',
                'dinnerTime' => '18:30-20:30<br>（自助）',
                'dinnerDate' => '4月25日'
            ),
        );
        return $dinnerRecords;
    }

    public function wechatruchang($params)
    {
        $mtid = $params['mtid'];
        $hdid = $params['hdid'];
        $mobile = $params['mobile'];
        $code = trim($params['code']);

        if (!empty($code)) {
            $res = $this->getopenididbycode($code);

            if (!isset($res['errcode'])) {
                $openid = $res['openid'];
                $unionid = $res['unionid'];
                $userinfo = $this->_dao->dologin($openid, $unionid);
                if (!empty($userinfo) && $userinfo['status'] == 1) {
                    $mobile = $userinfo['mobile'];
                } else {
                    $arr['Success'] = '0';
                    $arr['Message'] = '无绑定信息，请重新登录';
                    $arr['ErrorType'] = "1001";
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }

            } else {
                $arr['Success'] = '0';
                $arr['Message'] = '非法操作！!';
                $arr['ErrorType'] = "1002";
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }
        }

        if (empty($hdid)) {
            $arr['Success'] = '0';
            $arr['Message'] = '请求失败，参数缺失';
            $arr['ErrorType'] = '1';
        } else {
            $meeting_set_rc = $this->_dao->getruchangmes($hdid);

            if (!empty($mobile)) {
                $mes = $this->getcanhuiinfo($mtid, $mobile);
            } else {
                $mes = array();
            }
            // dd($mes);
            if (!empty($mes)) {
                $Status = 1;
                if (($meeting_set_rc['IsRepast'] == '1' && $mes['canquan'] == '1') || $meeting_set_rc['IsRepast'] != '1') {

                    // IsMainMeeting
                    // IsChildMeeting  //钢铁联席会
                    // IsChildMeeting2 //电商会议
                    // 202411月会议 钢铁联席会需要判断入场权限
                    if ($hdid == "398") {
                        if ($mes['IsChildMeeting2'] == "1") {
                            $this->setRuchang($mes, $mtid, $hdid, $Status);
                            $arr['Success'] = '1';
                            $arr['Message'] = '入场成功';
                            $arr['ErrorType'] = '';
                            $arr['Results'] = $mes;
                        } else {
                            $arr['Success'] = '0';
                            $arr['Message'] = '您当前登录的账号无法参加本场次会议';
                            $arr['ErrorType'] = '1';
                            $arr['Results']['managename'] = $mes['managename'];
                            $arr['Results']['managenametel'] = $mes['managenametel'];
                        }
                    } else if ($hdid == "401") {
                        if ($mes['IsChildMeeting5'] == "1") {
                            $this->setRuchang($mes, $mtid, $hdid, $Status);
                            $arr['Success'] = '1';
                            $arr['Message'] = '入场成功';
                            $arr['ErrorType'] = '';
                            $arr['Results'] = $mes;
                        } else {
                            $arr['Success'] = '0';
                            $arr['Message'] = '您当前登录的账号无法参加本场次会议';
                            $arr['ErrorType'] = '1';
                            $arr['Results']['managename'] = $mes['managename'];
                            $arr['Results']['managenametel'] = $mes['managenametel'];
                        }
                    } else {
                        if (!($mes['IsCheckIned'] == '1' && $hdid == 391)) {
                            $this->setRuchang($mes, $mtid, $hdid, $Status);
                        }
                        $arr['Success'] = '1';
                        $arr['Message'] = '入场成功';
                        $arr['ErrorType'] = '';
                        $arr['Results'] = $mes;
                    }

                } else {
                    $this->setRuchang($mes, $mtid, $hdid, 9);
                    $arr['Success'] = '1';
                    $arr['Message'] = '入场失败，您没有餐券';
                    $arr['ErrorType'] = '2';
                    $arr['Results'] = $mes;
                }
            } else {
                $arr['Success'] = '0';
                $arr['Message'] = '入场失败，未查询到您的参会信息';
                $arr['ErrorType'] = '4';
            }

            if ($hdid == 378) {
                $arr['Success'] = '1';
                $arr['Message'] = '入场成功';
                $arr['ErrorType'] = '';
            }
            $arr['Results']['RuChang'] = $meeting_set_rc;
            $arr['Results']['RcName'] = $meeting_set_rc['RcName'];
        }
        $WebGuid = $GLOBALS['WEBGUID'];
        $temp = array();
        $new_all = $this->_dao->getinfo_new($WebGuid, 576);
        foreach ($new_all as $k => $val) {
            $temp[] = $val;
        }
        $arr['Results']['meetingname'] = '第二十一届钢铁产业发展战略会议';
        $arr['Results']['appletShareImgUrl'] = $temp[0] ? $temp[0]['InfoURL'] : 'https://weixin.steelhome.cn/wxsigninnew/images/427banner.jpg';
        $arr['Results']['meetingaddress'] = '上海国际会议中心一楼';
        $arr['Results']['meetingtime'] = "2025-04-24 ~ 2025-04-25";

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function getmeetingSetInfo($Mtid)
    {
        $setInfo = array();
        $mettingset = $this->_dao->getMettingSet($Mtid);
        foreach ($mettingset as $setkey => $setvalue) {
            $setInfo[$setvalue['SetType']][$setvalue['TypeValue']] = $setvalue['TypeName'];
        }
        return $setInfo;
    }
    function getxikanumarr($widthnew)
    {
        //$widthnew=5;
        $pydarr = array();
        $helfwidth = intval($widthnew / 2);
        if ($widthnew % 2 != 0) {
            $helfwidth++;
        }
        // echo $helfwidth;
        for ($i = 1; $i <= $widthnew; $i++) {

            if ($widthnew % 2 == 0) {
                if ($i <= $helfwidth) {
                    //$pydarr[$i]=$widthnew-($i-1)*2-1;
                    $pydarr[$i] = $widthnew - ($i - 1) * 2;
                } else {
                    //$pydarr[$i]=$i*2-$widthnew;
                    $pydarr[$i] = $i * 2 - $widthnew - 1;
                }
            } else {
                if ($i <= $helfwidth) {
                    $pydarr[$i] = $widthnew - ($i - 1) * 2;
                    //$pydarr[$i]=$widthnew-($i-1)*2-1;
                } else {
                    $pydarr[$i] = $i * 2 - $widthnew - 1;
                    //$pydarr[$i]=$i*2-$widthnew;
                }
            }

        }
        return $pydarr;
    }
    function getopenididbycode($code)
    {
        $wx_config = array(
            'appid' => WECHAT_APPID_AND_APPSECRET["1"]["appid"],
            'secret' => WECHAT_APPID_AND_APPSECRET["1"]["appsecret"]
        );
        $appid = $wx_config['appid'];
        $secret = $wx_config['secret'];
        $get_token_url = 'https://api.weixin.qq.com/sns/jscode2session?appid=' . $appid . '&secret=' . $secret . '&js_code=' . $code . '&grant_type=authorization_code';
        $res = file_get_contents($get_token_url);
        $res = json_decode($res, true);
        return $res;
    }
    function get_access_token($update = false)
    {
        //接站小程序配置
        $info = array(
            'appid' => WECHAT_APPID_AND_APPSECRET["1"]["appid"],
            'secret' => WECHAT_APPID_AND_APPSECRET["1"]["appsecret"]
        );
        // 微信开放平台一键绑定
        $access_token = $this->get_access_token_by_apppid($info['appid'], $info['secret'], $update);

        if ($update == false) {
            $url = 'https://api.weixin.qq.com/cgi-bin/getcallbackip?access_token=' . $access_token;
            $res = file_get_contents($url);
            $res = json_decode($res, true);
            if ($res['errcode'] == '40001') {
                $access_token = $this->get_access_token(true);
            }
        }
        return $access_token;
    }

    public function get_access_token_by_apppid($appid, $secret, $update = false)
    {
        if (empty($appid) || empty($secret)) {
            return 0;
        }
        $memcache_obj = new Memcache();
        $memcache_obj->connect(MEMCACHE_SERVER, MEMCACHE_PORT);

        $key = 'wx_access_token_apppid_' . $appid . '_' . $secret;
        //$res = S ( $key );
        $res = $memcache_obj->get($key);
        if ($res !== false && !$update) {
            $memcache_obj->close();
            return $res;
        }

        $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&secret=' . $secret . '&appid=' . $appid;
        $tempArr = json_decode(file_get_contents($url), true);
        // dd($tempArr);
        if (@array_key_exists('access_token', $tempArr)) {
            //S ( $key, $tempArr ['access_token'], $tempArr ['expires_in'] );
            $memcache_obj->set($key, $tempArr['access_token'], MEMCACHE_COMPRESSED, $tempArr['expires_in']);
            $memcache_obj->close();
            return $tempArr['access_token'];
        } else {
            $memcache_obj->close();
            return 0;
        }
    }

    public function post_data($url, $param, $is_file = false, $return_array = true)
    {
        set_time_limit(0);
        if (!$is_file && is_array($param)) {
            $param = $this->pri_JSON($param);
        }
        if ($is_file) {
            $header[] = "content-type: multipart/form-data; charset=UTF-8";
        } else {
            $header[] = "content-type: application/json; charset=UTF-8";
        }
        $ch = curl_init();
        // if (class_exists ( '/CURLFile' )) { // php5.5璺焢hp5.6涓殑CURLOPT_SAFE_UPLOAD鐨勯粯璁ゅ�间笉鍚�
        // 	curl_setopt ( $ch, CURLOPT_SAFE_UPLOAD, true );
        // } else {
        // 	if (defined ( 'CURLOPT_SAFE_UPLOAD' )) {
        // 		curl_setopt ( $ch, CURLOPT_SAFE_UPLOAD, false );
        // 	}
        // }
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $res = curl_exec($ch);
        $flat = curl_errno($ch);
        if ($flat) {
            $data = curl_error($ch);
            //addWeixinLog ( $flat, 'post_data flat' );
            //addWeixinLog ( $data, 'post_data msg' );
        }

        curl_close($ch);

        $return_array && $res = json_decode($res, true);

        return $res;
    }

    //2退出
    public function LoginOut($params)
    {
        $arr = array(
            'Success' => "1",
            'Message' => "登出成功!",
        );
        // $UserId = $params['UserId'];
        // $openid = $params['OpenId'];
        $unionid = $params['unionid'];
        $mtid = $params['mtid'];
        $sql1 = "select * from wechatuser where unionid=?";
        $info1 = $this->_dao->getrow($sql1, array($unionid));
        if ($info1) {
            $this->_dao->execute("update wechatuser set status='0' where id='$info1[id]'");
            if ($info1['type'] == 2) {
                $this->_dao->execute("update  CarInformation set Openid=''  where Openid='" . $info1['openid'] . "' and mtid='" . $mtid . "' ");
            }
            unset($_SESSION);
        } else {
            $arr = array(
                'Success' => "0",
                'Message' => "非法操作!",
            );
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    public function createShare($params)
    {
        //分享接口暂未实现
        $arr = array(
            'Success' => "1",
            'Message' => "记录成功!",
        );
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    public function recordMinoProgramLastTime($params)
    {

        $code = $params['code'];

        if ($code == "") {
            $arr['Success'] = '0';
            $arr['Message'] = '参数错误!';
        }
        $res = $this->getopenididbycode($code);

        if (!isset($res['errcode'])) {
            $openid = $res['openid'];
            $unionid = $res['unionid'];
            $this->_dao->execute("update wechatuser set lasttime=Now() where unionid='" . $unionid . "'");
            $arr = array(
                'Success' => "1",
                'Message' => "记录成功!",
            );
        } else {
            $arr['Success'] = '0';
            $arr['Message'] = '参数错误!';
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }
    public function fetchMessageCode($params)
    {
        $mobile = $params['mobile'];
        $openid = $params['openid'];
        $url = APP_URL_WWW . "/_v2app/sms.php?action=sendrand_wxchat&usermobile=" . $mobile . "&openid=" . $openid;
        if (!ISTEST) {
            $con = file_get_contents($url);
        } else {
            $con = '0';
        }
        if ($con == "0") {
            $arr['Success'] = '1';
            $arr['Message'] = '短信发送成功!';
        } else {
            $arr['Success'] = '0';
            $arr['Message'] = '系统繁忙,请稍后再试!';
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    public function bindPhoneNum($params)
    {

    }

    function Sendfwtzmessage($openid, $name, $meetingname, $content, $url = '')
    {
        $data = array(
            'name1' => array('value' => $name),
            'time2' => array('value' => date("Y-m-d H:i:s")),
            'thing3' => array('value' => $meetingname),
            'thing5' => array('value' => $content),
        );
        $template = array(
            'touser' => $openid,
            'template_id' => 'kaFF6N_kX2iN_UAnoo2Eg0FeuuaCKBSBLxCdf2nOhvg',
            'page' => $url,
            'data' => $data,
            'miniprogram_state' => 'formal',
            'lang' => 'zh_CN',
        );
        $json_template = json_encode($template);
        $url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=" . get_access_token();
        ;
        $dataRes = $this->post_data($url, urldecode($json_template));
    }

    //13图片上传
    public function uploadPic($params)
    {
        $arr = array(
            'Success' => "1",
            'Message' => "上传成功!",
        );
        $fromapp = $params['fromapp'] ?? "1";
        $path_params = "/uploadfile/goodsmanage/images";
        if ($fromapp == "2") {  //一站通会务小程序上传头像
            $path_params = "/uploadfile/yzthw/images";
            $unionid = $params['unionid'] ?? "";
        }
        $upfile = $_FILES['file'];
        if ($upfile['error'] == 0) {
            if (is_uploaded_file($upfile['tmp_name'])) {
                $uptypes = array(
                    'image/jpg',
                    'image/jpeg',
                    'image/png',
                    'image/pjpeg',
                    'image/gif',
                    'image/bmp',
                    'image/x-png'
                );
                if (in_array($upfile['type'], $uptypes)) {
                    $a = explode('.', $upfile['name']);
                    $extName = strtolower(end($a));
                    $filename = $this->random(5); // 设置随机数长度
                    $name1 = date('YmdHis') . "." . $extName;
                    $dir = "/usr/local/www/www.steelhome.cn" . $path_params;
                    if (!file_exists($dir)) {
                        if (mkdir($dir, 0777)) {
                            $dest = $dir . "/" . $filename . $name1;
                            $dir1 = $path_params . "/";
                            $picUrl = $dir1 . $filename . $name1;
                            if (move_uploaded_file($upfile['tmp_name'], $dest)) {
                                $arr['PicURL'] = $_SERVER['HTTP_HOST'] . $picUrl;
                            } else {
                                $arr['Success'] = '0';
                                $arr['Message'] = '上传失败!目录权限不足';
                            }
                        } else {
                            $arr['Success'] = '0';
                            $arr['Message'] = '目录' . $dir . '不存在，上传失败!';
                        }
                    } else {
                        $dest = $dir . "/" . $filename . $name1;
                        $dir1 = $path_params . "/";
                        $picUrl = $dir1 . $filename . $name1;
                        if ($fromapp == "2" && $unionid != "") {
                            $dest = $dir . "/" . $unionid . "." . $extName;
                            $picUrl = $dir1 . $unionid . "." . $extName;
                        }
                        if (move_uploaded_file($upfile['tmp_name'], $dest)) {
                            $arr['PicURL'] = 'https://' . $_SERVER['HTTP_HOST'] . $picUrl;
                            if ($fromapp == "2" && $unionid != "") {
                                $sql = "update wechatuser set avatar='" . $dest . "' where unionid='" . $unionid . "'";
                                $this->_dao->execute($sql);
                            }
                        } else {
                            $arr['Success'] = '0';
                            $arr['Message'] = '上传失败!目录权限不足';
                        }
                    }
                } else {
                    $arr['Success'] = '0';
                    $arr['Message'] = '上传失败!检查文件是否是图片';
                }
            } else {
                $arr['Success'] = '0';
                $arr['Message'] = '上传失败!';
                // clearstatcache(); //清除文件缓存信息
            }
        } else {
            $arr['Success'] = '0';
            $arr['Message'] = '上传失败!图片不能超过2MB';
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function bigFile($params)
    {
        $openid = $params['openid'];
        if (empty($openid)) {
            $arr["error"] = "4";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }
        $selectSql = "select * from PcManagementUser where OpenId=? and status=1";
        $userinfo = $this->_dao->getrow($selectSql, array($openid));  //用户信息验证
        if (empty($userinfo)) {
            $arr["error"] = "4";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }
        $file_arr = array();
        $arr = array();
        $path = "/usr/local/www/www.steelhome.cn/uploadfile/goodsmanage/doc";
        $memcache = new Memcache();
        $memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
        $expire = 1200;
        for ($i = 0; $i < 4; $i++) {
            $randnum .= mt_rand(0, 9);
        }
        $params['name'] = iconv("utf-8", "GBK", $params['name']);
        $arr['name'] = $params['name'];
        $arr['error'] = "0";
        $type = strtolower(substr(strrchr($params['name'], '.'), 1)); //获取文件类型
        $destication_frag_path = "/usr/local/www/www.steelhome.cn/uploadfile/goodsmanage/doc/uploads_tmp";  //分片上传的临时文件夹
        if (!file_exists($destication_frag_path)) {
            mkdir($destication_frag_path, 0777);
        }
        $destication_frag_path = $destication_frag_path . "/";
        $redis_key = $params['name'];
        $file_name = explode('.', $params['name']);
        $save_tmp_name = $destication_frag_path . $file_name[0] . "_" . $params['chunk'] . "-" . $randnum;

        $tmp_file_path = $_FILES['file']['tmp_name']; //上传的临时文件
        move_uploaded_file($tmp_file_path, $save_tmp_name);
        if ($params['chunk'] == 0) {
            $memcache->set($redis_key, array(), MEMCACHE_COMPRESSED, $expire);
        }

        $all_files_fen_pian = $memcache->get($redis_key);
        $all_files_fen_pian[$params['chunk']] = $save_tmp_name;
        $var = $memcache->set($redis_key, $all_files_fen_pian, MEMCACHE_COMPRESSED, $expire);
        $all_files_fen_pian = $memcache->get($redis_key); //获取 分片资源
        $uploaded_count = count($all_files_fen_pian);
        //分片资源上传完毕后，开始分片合并工作
        if ($uploaded_count == $params['chunks']) {
            if ($all_files_fen_pian && is_array($all_files_fen_pian)) {
                //创建要合并的最终文件资源
                $filename = $filename = date("Y") . date("m") . date("d") . date("H") . date("i") . date("s") . $randnum . "." . $type;
                $final_file = $path . "/" . $filename;
                if (file_exists($final_file)) {
                    //开始合并文件分片
                    foreach ($all_files_fen_pian as $fragmentation_file) {
                        $frag_file_handler = fopen($fragmentation_file, 'rb');
                        fclose($frag_file_handler);      //销毁分片文件资源
                        unlink($fragmentation_file);     //删除已经合并的分片文件
                        usleep(10000);
                    }
                    $arr["error"] = "1";
                    $json_string = $this->pri_JSON($arr);
                } else {
                    $final_file_handler = fopen($final_file, 'wb');
                    //开始合并文件分片
                    foreach ($all_files_fen_pian as $fragmentation_file) {
                        $frag_file_handler = fopen($fragmentation_file, 'rb');
                        $frag_file_content = fread($frag_file_handler, filesize($fragmentation_file));
                        fwrite($final_file_handler, $frag_file_content);
                        unset($frag_file_content);
                        fclose($frag_file_handler);      //销毁分片文件资源
                        unlink($fragmentation_file);     //删除已经合并的分片文件
                        usleep(10000);
                    }

                    if ($params['chunk'] == ($params['chunks'] - 1)) { //临时文件转移到目标文件夹
                        $arr["error"] = "0";
                        $arr["src"] = 'https://' . $_SERVER['HTTP_HOST'] . "/uploadfile/goodsmanage/doc/" . $filename;
                        $arr["name"] = $params['name'];
                    } else {
                        $arr["error"] = "2";
                    }
                }
            }
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;
    }

    //生成随机字符
    public function random($length)
    {
        $hash = 'CR-';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand((double) microtime() * 1000000);
        for ($i = 0; $i < $length; $i++) {
            $hash .= $chars[mt_rand(0, $max)];
        }
        return $hash;
    }

    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'trim', true);
        return json_encode($array);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

    public function replace($tmpList)
    {
        foreach ($tmpList as &$pro) {
            foreach ($pro as $kt => $t) {
                $pro[$kt] = str_replace("\r", " ", $t);
                $pro[$kt] = str_replace("\n", " ", $t);
                $pro[$kt] = str_replace("\r\n", " ", $t);
            }
        }
        return $tmpList;
    }

    public function getURLScheme($params)
    {
        header('Content-Type:application/json; charset=utf-8');
        // https://api.weixin.qq.com/wxa/generatescheme?access_token
        // 发送post请求
        $url = "https://api.weixin.qq.com/wxa/generatescheme?access_token=" . $this->get_access_token();
        $data = array(
            'jump_wxa' => array(
                'path' => 'pages/index/index',
                'query' => 'a=1&b=2'
            ),
            'is_expire' => false,
            'expire_time' => 1800,
        );
        $data = json_encode($data);
        $res = $this->post_data($url, $data);
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function getAwardInfoByMtidAndMobile($params)
    {
        // 407 会议根据不同二维码的参数判定是否自动领取奖品
        $isAuto = $params['isAuto'] ?? 0;  //是否自动领奖
        $rcType = 382;
        $awardType = [
            "1" => "一等奖",
            "2" => "二等奖",
            "3" => "三等奖",
            "4" => "特等奖",
            "5" => "幸运奖",
            "6" => "幸运奖(酒)",
            "7" => "阳光普照奖"
        ];
        $res = [
            'Success' => 1,
            'Message' => '',
            'Results' => [],
        ];
        $mobile = $params['mobile'] ?? '';
        $mtid = $params['mtid'] ?? '';
        // $mtid = 392;
        // 验证手机号是否合法
        if (!preg_match('/^1[3456789]\d{9}$/', $mobile)) {
            $res['Success'] = 0;
            $res['Message'] = '手机号不合法';
        }

        if (!(is_numeric($mtid) && is_int($mtid + 0) && $mtid > 0)) {
            $res['Success'] = 0;
            $res['Message'] = 'mtid不合法';
        }

        if ($res['Success'] == 1) {
            $sql = "SELECT a.ID,a.Mid,a.Cid,a.AwardType,a.AwardName,a.Status,a.PersonSign,c.CompfnameS,c.TrueName,c.Post,c.PictureBig,c.Sex,c.ContactMobile,c.ContactPhone,c.GUID FROM meeting_member_contactman c inner JOIN meeting_member_award a ON a.Cid=c.id  WHERE c.`ContactMobile` = ? AND c.`Mtid` = ? ";
            $self = $this;
            $info = array_map(function (&$item) use ($awardType, $self, $mtid, $isAuto) {
                $item['AwardTypeName'] = $awardType[$item['AwardType']] ?? $item['AwardType'];
                $item['PictureBig'] = $self->handlePictureBig($item['PictureBig']);
                $item['mcid'] = $item['Cid'];
                if (($item['AwardType'] == "3" || $item['AwardType'] == "2") && $mtid == "407" && $isAuto) {
                    $item['Status'] = "1";
                    $self->_dao->execute("update meeting_member_award set Status = 1, GetAwardTime=Now(),CancerTime=Now() where ID=" . $item['ID']);
                }
                return $item;
            }, $this->_dao->query($sql, [$mobile, $mtid]));
            $res['Results'] = $info;
            if ($info)
                $this->setRuchang($info[0], $mtid, $rcType, 1);
        }
        header("Content-type: application/json; charset=utf-8");
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function bindMobileByUserid($params)
    {
        // header("Content-type: application/json; charset=utf-8");
        $res = [
            'Success' => 1,
            'Message' => '',
            'Results' => [],
        ];
        $userid = $params['userid'] ?? '';
        if (!(is_numeric($userid) && is_int($userid + 0) && $userid > 0)) {
            $res['Success'] = 0;
            $res['Message'] = '参数不合法';
        }

        $sql = "select mobil from adminuser where id = ? and state=1 and mid=1";
        $mobile = $this->_dao->getone($sql, [$userid]);
        if ($mobile && $res['Success'] == 1) {
            $sql = "update wechatuser set status = 0 where mobile = ?";
            $this->_dao->execute($sql, [$mobile]);
            $sql = "update wechatuser set mobile = ?, status = 1 where userid = ?";
            $this->_dao->execute($sql, [$mobile, $userid]);
            $res['Message'] = '绑定成功';
        }
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function getTravellingInfoByContactmanId($params)
    {
        $travellingEndTime = $this->travellingEndTime;
        $res = [
            'Success' => 1,
            'Message' => '',
            'travellingEndTime' => $travellingEndTime,
            'Results' => [],
        ];
        $mtid = $GLOBALS['MEETING_ID'];
        $meetingBase = $this->_dao->getMeetingInfoByMtid($mtid);
        if (isset($meetingBase['fzjldeadline'])) {
            $res['travellingEndTime'] = $meetingBase['fzjldeadline'];
        }
        $tmpData = $this->_dao->getCanHuiInfoById($params['contactman_id']);
        /*if(isset($tmpData['IsCheckIned']) && $tmpData['IsCheckIned'] == "0") {
            $res['Message'] = '未报到';
            $res['Success'] = 0;
        } else {*/
        if ($tmpData && $tmpData['ContactMobile'] == $params['mobile'] && $params['mobile'] != "") {
            $UserID = $this->_dao->getOne(" select UserID from  meeting_member  where Mtid=$mtid and  Mid='" . $tmpData['Mid'] . "' and IsEn='" . $tmpData['IsEn'] . "'");
            if ($UserID) {
                $adminuser = $this->_dao->getrow("SELECT truename,mobil FROM `adminuser` where id='" . $UserID . "' limit 1");
                $res['Results']['managename'] = $adminuser['truename'];
                $res['Results']['managenametel'] = $adminuser['mobil'];
            } else {
                $res['Results']['managename'] = "";
                $res['Results']['managenametel'] = "";
            }

            if ($tmpData['idcard'] == "" || $tmpData['idcard'] == null) {
                $sql = "select ID,idcard from meeting_member_contactman where ContactMobile = ? and Mtid !=? and idcard is not null and idcard!='' and TrueName=? limit 1";
                $historyMeetingInfo = $this->_dao->getrow($sql, [$tmpData['ContactMobile'], $mtid, $tmpData['TrueName']]);
                if ($historyMeetingInfo) {
                    $res['Results']['allPeople'][0]['idcard'] = base64_encode($this->id_card_hyposensitization($historyMeetingInfo['idcard']));
                    $res['Results']['allPeople'][0]['historyContactmanId'] = $historyMeetingInfo['ID'];
                } else {
                    $res['Results']['allPeople'][0]['idcard'] = "";
                    $res['Results']['allPeople'][0]['historyContactmanId'] = "";
                }
            } else {
                $res['Results']['allPeople'][0]['idcard'] = base64_encode($this->id_card_hyposensitization($tmpData['idcard']));
                $res['Results']['allPeople'][0]['historyContactmanId'] = "";
            }
            $res['Results']['allPeople'][0]['TrueName'] = $tmpData['TrueName'];
            $res['Results']['TrueName'] = $tmpData['TrueName'];
            $res['Results']['IsTravel'] = $tmpData['IsTravel'];
            $res['Results']['TravelPersons'] = $tmpData['TravelPersons'];
            $res['Results']['TravelOpTime'] = $tmpData['TravelOpTime'];
            $res['Results']['CompfnameS'] = $tmpData['CompfnameS'];
            $res['Results']['Post'] = $tmpData['Post'];
            $res['Results']['PersonSign'] = $tmpData['PersonSign'];
            $res['Results']['PictureBig'] = $this->handlePictureBig(url: $tmpData['PictureBig']);
            $tmptralist = explode(";", $tmpData['tralist']);
            foreach (explode(";", $tmpData['idcard1']) as $key => $val) {
                if ($val == "")
                    continue;
                $res['Results']['allPeople'][] = [
                    'idcard' => base64_encode($this->id_card_hyposensitization($val)),
                    'TrueName' => $tmptralist[$key],
                ];
            }
            ;
            $res['Message'] = '数据获取成功';
        } else {
            $res['Message'] = '没有数据';
        }
        // }

        // dd($tmpData);
        header("Content-type: application/json; charset=utf-8");
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function saveTravellingInfo($params)
    {
        $travellingEndTime = $this->travellingEndTime;
        $res = [
            'Success' => 1,
            'Message' => '',
        ];
        $rcType = "368";
        // 判断是失去焦点的自动保存还是用户手动点击的按钮保存
        $autoSave = $params['autoSave'];
        $mtid = $GLOBALS['MEETING_ID'];
        $meetingBase = $this->_dao->getMeetingInfoByMtid($mtid);
        if (isset($meetingBase['fzjldeadline'])) {
            $travellingEndTime = $meetingBase['fzjldeadline'];
        }
        $cur_time = time();
        if ($cur_time <= strtotime($travellingEndTime)) {
            $tmpData = $this->_dao->getCanHuiInfoById($params['contactman_id']);
            /*if(isset($tmpData['IsCheckIned']) && $tmpData['IsCheckIned'] == "0") {
                $res['Message'] = '请先报到后再登记';
                $res['Success'] = 0;
            } else {*/
            if ($tmpData) {
                $idcard = "";
                $idcard1 = "";
                $tralist = "";
                $isTravel = $params['isTravel'];
                foreach ($params['allPeople'] as $key => $val) {
                    if ($key == 0) {
                        $idcard = base64_decode($val['idcard']);
                        // 自动调取历史参会信息中的身份证号
                        if (str_contains($idcard, "*") && $val['historyContactmanId'] != "") {
                            $sql = "select idcard from meeting_member_contactman where id=?";
                            $idcard = $this->_dao->getone($sql, [$val['historyContactmanId']]);
                        }
                    } else {
                        $tralist .= $val['TrueName'] . ";";
                        $idcard1 .= base64_decode($val['idcard']) . ";";
                    }
                }
                $TravelPersons = count($params['allPeople'] ?? []);
                $sql = "update meeting_member_contactman set TravelOpTime=NOW(),IsTravel=?,TravelPersons='" . $TravelPersons . "',idcard=?,tralist=?,idcard1=? where `ID`=?";

                $this->_dao->execute($sql, [$isTravel, $idcard, $tralist, $idcard1, $params['contactman_id']]);
                if (!$autoSave) {
                    $content = "";
                    // 状态没有变化，不需要通知
                    if ($isTravel == "1" && $tmpData['IsTravel'] != "1") {
                        // 参加旅游
                        $this->setRuchang($tmpData, $mtid, $rcType, 1);
                        $content = $tmpData['CompfnameS'] . $tmpData['TrueName'] . "共" . $TravelPersons . "人确认旅游，请知晓。";
                    } else if ($isTravel == "2" && $tmpData['IsTravel'] != "2") {
                        // 取消旅游
                        $content = $tmpData['CompfnameS'] . $tmpData['TrueName'] . "共" . $TravelPersons . "人取消旅游，请知晓。";
                    } else {
                        // 待定

                    }
                    if ($content != "") {
                        $this->send_xwork_message($content, "", "steelhomegmm");
                        // $this->send_xwork_message($content,"","ZhengNaiBin");
                    }
                }
                $res['Message'] = '保存成功';
            } else {
                $res['Success'] = 0;
                $res['Message'] = '未参会用户无法录入';
            }
            // }
        } else {
            $res['Success'] = 0;
            $res['Message'] = '登记失败，当前时间无法录入';
        }
        header("Content-type: application/json; charset=utf-8");
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function changePost($params)
    {
        $res = [
            'Success' => 1,
            'Message' => '',
        ];
        $tmpData = $this->_dao->getCanHuiInfoById($params['contactman_id']);
        if ($tmpData && $tmpData['ContactMobile'] == $params['mobile']) {
            if ($tmpData['Post'] != $params['post']) {
                $title = $tmpData['CompfnameS'] . $tmpData['TrueName'] . '小程序更新职务，操作人' . $tmpData['TrueName'];
                $content = "职务" . $tmpData['Post'] . "变更为" . $params['post'];
                $sql = "INSERT INTO `meeting_active_log` (`mtid`,`hwid`, `content`,`title`,`type`, `createtime`, `createuser`, `createuserid`,updatetime) VALUES( '" . $tmpData['Mtid'] . "','" . $tmpData['ID'] . "',?,?,'5', NOW(), '微信小程序', '0', NOW())";
                $this->_dao->execute($sql, [$content, $title]);
                $sql = "INSERT INTO `meeting_active_log` (`mtid`,`hwid`, `content`,`title`,`type`, `createtime`, `createuser`, `createuserid`,updatetime) VALUES( '" . $tmpData['Mtid'] . "','" . $tmpData['ID'] . "',?,?,'6', NOW(), '微信小程序', '0', NOW())";
                $this->_dao->execute($sql, [$content, $title]);
            }
            $sql = "update meeting_member_contactman set Post=? where `ID`=?";
            $this->_dao->execute($sql, [$params['post'], $params['contactman_id']]);
            $res['Message'] = '保存成功';
        } else {
            $res['Message'] = '保存失败，用户未参会';
            $res['Success'] = 0;
        }

        header("Content-type: application/json; charset=utf-8");
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    private function send_xwork_message($message, $userid = "", $touser = "")
    {
        $post = array();
        if ($userid) {
            $post = array(
                'adminid' => $userid,
                'msgtype' => 'text',
                'content' => $message
            );
        }
        if ($touser) {
            $post = array(
                'touser' => $touser,
                'msgtype' => 'text',
                'content' => $message
            );
        }

        $post_content = http_build_query($post);
        $options = array(
            'http' => array(
                'method' => 'POST',
                'header' => 'Content-type:application/x-www-form-urlencoded',
                'content' => $post_content,
            )
        );

        $url = APP_URL_WORK . "/qiye/SendMessage.php";
        $str = file_get_contents($url, false, stream_context_create($options));
    }

    // 设置入场
    private function setRuchang($info, $mtid, $rcType, $status = 1)
    {
        $RC = $this->_dao->getruchangmes($rcType);
        if ($RC && $RC['Mtid'] == $mtid) {
            $info['RuType'] = $rcType;
            $info['RcName'] = $RC['RcName'] ?? "";
            $this->_dao->ruchang($info, $mtid, $status);
        }
    }

    // meeting_member_contactman表中的头像字段处理
    private function handlePictureBig($url)
    {
        if ($url != "") {
            $tmpPath = explode("uploadfile/", $url);
            if (count($tmpPath) > 1)
                $url = APP_URL_WWW . "/" . "uploadfile/" . $tmpPath[1];
        } else {
            $url = "https://img.gzjimg.com/steelhome/wappickup/images/timg.jpg";
        }
        return $url;
    }

    public function getAdminInfoByMobile($params)
    {
        $res = [
            'Success' => 1,
            'Message' => '',
            'Results' => [],
        ];
        $mtid = $params['mtid'] ? $params['mtid'] : $GLOBALS['MEETING_ID'];
        $ch = $this->getcanhuiinfo($mtid, $params['mobile']);
        $res['Results']['managename'] = $ch['managename'];
        $res['Results']['managenametel'] = $ch['managenametel'];
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function getCompanyList($params)
    {
        $res = [
            'Success' => 1,
            'Message' => '',
            'Results' => [],
        ];
        if ($params['keyword'] != "") {
            $sql = "select mbid,compfname from member where compfname like ? or compabb like ? ";
            if (isset($params['mtid']) && !empty($params['mtid'])) {
                $sql = "select Mid as mbid,compfname from meeting_member where (compfname like ? or compabb like ? ) and Mtid='" . $params['mtid'] . "'";
            }
            $sqlres = $this->_dao->query($sql, ["%" . $params['keyword'] . "%", "%" . $params['keyword'] . "%"]);
            // $res['Results'] = array_column($sqlres, 'compfname', 'mbid');
            foreach ($sqlres as $val) {
                $res['Results'][] = [
                    "text" => $val['compfname'],
                    "value" => $val['mbid'],
                    "disabled" => false,
                ];
            }
            $res['count'] = count($res['Results']);
        } else {
            $res['Success'] = 0;
            $res['Message'] = '参数不合法';
        }
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public function saveGuest($params)
    {
        $data = file_get_contents('php://input');
        $data = json_decode($data, true);
        $res = [
            'Success' => 1,
            'Message' => '',
            'Results' => [],
        ];
        $company = $data['companyName'] ?? "";
        $name = $data['name'] ?? "";
        $mobile = $data['phoneNumber'] ?? "";
        //$mbid = $data['mbid']??"";
        $position = $data['position'] ?? ""; // 职位
        $mtid = $data['mtid'] ?? $GLOBALS['MEETING_ID'];
        $userid = $data['userid'] ?? "";
        $username = $data['username'] ?? "";
        $IsGetMoney = $data['IsGetMoney'] ?? "0";
        $IsXongka = $data['IsXongka'] ?? "1";
        $IsCanZl = $data['IsCanZl'] ?? "0";
        // dd($data);
        if ($name == "" || $mobile == "" || $company == "") {
            $res['Success'] = 0;
            $res['Message'] = '参数不合法';
        } else {
            $sql = "select ID,Status from meeting_member_contactman where  Mtid=? and ContactMobile=? and Status='1' ";
            $info = $this->_dao->getRow($sql, [$mtid, $mobile]);
            if (empty($info)) {
                $sql = "select Mid as mbid  from meeting_member where compfname=? and Mtid=? and IsEn=0";
                $meeting_member = $this->_dao->getRow($sql, [$company, $mtid]);
                $mbid = $meeting_member['mbid'];
                //echo "select Mid as mbid  from meeting_member where compfname='".$company."' and Mtid='".$mtid."' and IsEn=0";
                // 检测是否是参会单位,不存在的话就添加
                if (empty($mbid)) {
                    $sql = "select Mid,compfname,compabb,comtype,memberstate,membercity,DepartID  from meeting_member where compfname=?  and IsEn=0 and CreateDate>='" . date("Y-m-d 00:00:00", strtotime("-5 year")) . "'";
                    $meeting_member = $this->_dao->getrow($sql, [$company]);

                    if (!empty($meeting_member)) {
                        $sql = "insert into meeting_member set Mtid={$mtid},Mid='" . $meeting_member['mbid'] . "',MidEn=0,compfname='" . $meeting_member['compfname'] . "',compabb='" . $meeting_member['compabb'] . "',comtype='" . $meeting_member['comtype'] . "',FinanceType=0,memberstate='" . $meeting_member['memberstate'] . "',membercity='" . $meeting_member['membercity'] . "',IsMember=2,IsIntegrityCom=0,IsHuizhi=2,DepartID='" . $meeting_member['DepartID'] . "',DepartName='小程序录入',UserID='" . $userid . "',UserName='" . $username . "',InviteUserID='" . $userid . "',InviteUserName='" . $username . "',ContactmanNums=0,OrderC=3,CreateDate=NOW(),CreateUserId='" . $userid . "',UpdateDate=NOW(),UpdateUserId='" . $userid . "',Status=1";
                        $this->_dao->execute($sql);
                    } else {

                        $sql = "select Memberid as mbid from Account_pre where compfname like ?  order by CreateDate desc";
                        $member = $this->_dao->getrow($sql, ["%" . $company . "%"]);
                        if (!empty($member)) {
                            $sql = "select * from member where mbid ='" . $member['mbid'] . "'";
                            $member = $this->_dao->getrow($sql);
                            $mbid = $member['mbid'];
                            $adminname = "";
                            $admindept = "";
                            if ($member['adminid']) {
                                $sql = "select name,concat_ws(',', did, dzid, dzzid) AS d FROM dept WHERE `userid` = '" . $member['adminid'] . "'";
                                $depttable = $this->_dao->getrow($sql);
                                $adminname = $depttable['name'];
                                $admindept = $depttable['d'];
                            }
                            $sql = "insert into meeting_member set Mtid={$mtid},Mid={$mbid},MidEn=0,compfname='" . $member['compfname'] . "',compabb='" . $member['compabb'] . "',comtype='" . $member['comtype'] . "',FinanceType=0,memberstate='" . $member['state'] . "',membercity='" . $member['city'] . "',IsMember=2,IsIntegrityCom=0,IsHuizhi=2,DepartID='" . $admindept . "',DepartName='小程序录入',UserID='" . $member['adminid'] . "',UserName='" . $adminname . "',InviteUserID='" . $userid . "',InviteUserName='" . $username . "',ContactmanNums=0,OrderC=3,CreateDate=NOW(),CreateUserId='" . $userid . "',UpdateDate=NOW(),UpdateUserId='" . $userid . "',Status=1";
                            $this->_dao->execute($sql);
                        } else {
                            $mbid = 163877;
                            $sql = "select Mid  from meeting_member where Mid=? and Mtid=? and IsEn=0";
                            $meeting_member = $this->_dao->getRow($sql, [$mbid, $mtid]);
                            if (empty($meeting_member)) {
                                $adminname = "";
                                $admindept = "";
                                if ($userid) {
                                    $sql = "select name,concat_ws(',', did, dzid, dzzid) AS d FROM dept WHERE `userid` = '" . $userid . "'";
                                    $depttable = $this->_dao->getrow($sql);
                                    $adminname = $depttable['name'];
                                    $admindept = $depttable['d'];
                                }
                                $sql = "insert into meeting_member set Mtid={$mtid},Mid={$mbid},MidEn=0,compfname='" . $company . "',compabb='会务嘉宾',comtype='',FinanceType=0,memberstate='',membercity='',IsMember=2,IsIntegrityCom=0,IsHuizhi=2,DepartID='" . $admindept . "',DepartName='小程序录入',UserID='" . $userid . "',UserName='" . $adminname . "',InviteUserID='" . $userid . "',InviteUserName='" . $username . "',ContactmanNums=0,OrderC=3,CreateDate=NOW(),CreateUserId='" . $userid . "',UpdateDate=NOW(),UpdateUserId='" . $userid . "',Status=1";
                                $this->_dao->execute($sql);
                            }

                        }
                    }


                }
                // 检测是否在member_contact_user表
                $sql = "select count(*) c,user_id from member_contact_user where member_id=? and user_mobile=? and closed='0'";
                $member_contact_user_t = $this->_dao->getrow($sql, [$mbid, $mobile]);
                $maid = $member_contact_user_t['user_id'];
                if ($member_contact_user_t['c'] == 0) {
                    $sql = "insert into member_contact_user set member_id={$mbid},user_mobile='" . $mobile . "',user_name='" . $name . "',user_post='" . $position . "'";
                    $this->_dao->execute($sql);
                    $maid = $this->_dao->insert_id();
                }
                // 检测是否在meeting_member_contactman表
                // $sql = "select count(*) c from meeting_member_contactman where Mid=? and Mtid=? and ContactMobile=?";
                // $c3 = $this->_dao->getone($sql, [$mbid, $mtid, $mobile]);
                // if ($c3 > 0) {
                //     $res['Message'] = '该用户已参会，请勿重复添加';
                //     $res['Success'] = 0;
                // } else {

                //$personsign = $this->_dao->getOne("SELECT MAX(PersonSign) FROM meeting_member_contactman where Mtid=" . $mtid . " and  cast(PersonSign as unsigned)<=9099999 and  cast(PersonSign as unsigned)>=9000000 limit 1");
                $sql="SELECT max(round(substring(PersonSign,3))) from meeting_member_contactman where Mtid='". $mtid."' and PersonSign<='90999' and PersonSign>'10000'";
		        $MCODE = $this->_dao->getOne($sql);
                $personsign=90001;
                if(empty(($MCODE)))
                {
                    $personsign=90001;
                }
                else
                {
                    if($MCODE>=999)
                    {
                        $personsign = $this->_dao->getOne("SELECT MAX(PersonSign) FROM meeting_member_contactman where Mtid=" . $mtid . " and  cast(PersonSign as unsigned)<='909999' and  cast(PersonSign as unsigned)>='901000' limit 1");
                        if ($personsign - 901000 < 0)
                        $personsign = 901000;
                        else if ($personsign - 909999 >= 0)
                            $personsign = 909999;//等于号不可丢
                        else
                            $personsign += 1;
                    }
                    else
                    {
                        $personsign= 90001+$MCODE;
                    }

                    // if ($personsign - 9000000 < 0)
                    // $personsign = 9000000;
                    // else if ($personsign - 9099999 >= 0)
                    //     $personsign = 9099999;//等于号不可丢
                    // else
                    //     $personsign += 1;
                }
                
                
                

                $res['Message'] = '保存成功';
                $sql = "insert into meeting_member_contactman set PersonSign='" . $personsign . "',Mtid={$mtid},Mid={$mbid},TrueName='" . $name . "',ContactMobile='" . $mobile . "',maid='" . $maid . "',CreateDate=NOW(),CreateUserId='" . $userid . "',UpdateDate=NOW(),UpdateUserId='" . $userid . "',Status=1,CompfnameS='" . $company . "',Post='" . $position . "',IsPreHotel=2,IsGetMoney='" . $IsGetMoney . "',IsXongka='" . $IsXongka . "',IsCanZl='" . $IsCanZl . "',GUID=md5(UUID()),IsSongzhan=2,remark='小程序录入'";
                $this->_dao->execute($sql);
                $sql = "update meeting_member set ContactmanNums=ContactmanNums+1 where Mtid='$mtid' and Mid='$mbid' and IsEn=0 ";
                $this->_dao->execute($sql);
                $log = "操作人：" . $username . "日期：" . date('Y-m-d H:i:s') . "新增" . $company . "嘉宾" . $name . "信息";

                $ip = $this->_dao->getip();

                $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $mtid . "',LogsType='3',LogsTitle='新增嘉宾',LogsMid=" . $mbid . ", UserId='" . $userid . "',UserName='" . $username . "',UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");

                //}
            } else {
                $res['Message'] = '该用户已参会，请勿重复添加';
                $res['Success'] = 0;
            }


        }
        echo json_encode($res, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 身份证脱敏处理函数
     *
     * 该函数用于对身份证号码进行脱敏处理，保留前三位和后四位，中间部分用星号替换。
     *
     * @param string $id_card 需要脱敏的身份证号码
     * @return string 脱敏后的身份证号码
     */
    private function id_card_hyposensitization($id_card)
    {
        $id_card = strval($id_card);
        // 截取身份证号码的前三位和后四位，中间部分用星号替换
        $id_card = substr($id_card, 0, 3) . '***********' . substr($id_card, -4);

        return $id_card;
    }


    // 验证会议嘉宾
    public function validateMeetingGuest($params)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $payload = file_get_contents('php://input');
            $params = json_decode($payload, true);
            $this->fiterRequest($params);
            $res = [
                'Success' => '1',
                'Message' => '验证成功',
                'Results' => null,
            ];
            if (!($params['unionid']) || !($params['mtid'])) {
                $res['Success'] = '0';
                $res['Message'] = '必须输入mtid和unionid才能查询';
            } else {
                try {
                    $result = $this->_dao->validateMeetingGuest($params['mtid'], $params['unionid']);
                    if ($result) {
                        $res['Results'] = '1';
                    } else {
                        $res['Results'] = '0';
                    }
                } catch (Exception $e) {
                    $res['Success'] = '0';
                    $res['Message'] = $e->getMessage();
                }
            }
            echo json_encode($res);
        }
    }

    // 修补用户的昵称和头像
    // 逻辑很简单，就在这里实现了，不再借助DAO
    public function updateMissingAvatarAndNickname($params)
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $payload = file_get_contents('php://input');
            $params = json_decode($payload, true);
            $this->fiterRequest($params);
            $res = [
                'Success' => '1',
                'Message' => '更新成功',
                'Results' => null,
            ];
            try {
                $sql = "UPDATE wechatuser SET avatar = ?, nickname = ?, status = 1 WHERE unionid = ?";
                $this->_dao->execute($sql, [$params['avatar'], $params['nickname'], $params['unionid']]);
            } catch (Exception $e) {
                $res['Success'] = '0';
                $res['Message'] = $e->getMessage();
            }
            echo json_encode($res);
        }
    }

    public function getTravellingVehicleByContactmanId($params)
    {
        $res = [
            'Success' => '1',
            'Message' => '获取成功',
            'Results' => null,
        ];
        try {
            $sql = "SELECT * FROM `meeting_tourism_vehicles` WHERE `mtid` = ?";
            $vehicles = $this->_dao->query($sql, [$params['mtid']]);
            $found = false;
            foreach ($vehicles as $ve){
                $ve['vehicle_passengers'] = json_decode($ve['vehicle_passengers'], true);
                if (!($ve['vehicle_passengers']) || $ve['vehicle_passengers'] == ''){
                    continue;
                } else {
                    foreach ($ve['vehicle_passengers'] as $seatposition => $seatguest){
                        if ($seatguest[0]['id'] == $params['contactman_id']){
                            $res['Results'] = [
                                'position' => $seatposition,
                                'vehicle_type' => $ve['vehicle_type'],
                                'note' => '行数-列数，从上到下增大，从左到右增大'
                            ];
                            $found = true;
                            break;
                        }
                    }
                }
                if ($found) {
                    break;
                }
            }
        } catch (Exception $e) {
            $res['Success'] = '0';
            $res['Message'] = $e->getMessage();
        }
        echo json_encode($res);
    }

    // 适用于POST方法的保险函数
    private function fiterRequest(&$array)
    {
        foreach ($array as &$v) {
            if (is_array($v)) {
                $this->fiterRequest($v);
            } else {
                $v = htmlspecialchars(trim($v));
            }
        }
    }

    /**
     * 获取服务号关注用户列表
     * @param array $params 请求参数
     */
    public function getOfficialAccountFollowers($params)
    {
        // 钢之家wap
        $appid = WXAPPID;
        $secret = WXAPPSECRET;
        
        // 参数验证
        if (empty($appid) || empty($secret)) {
            $arr['Success'] = '0';
            $arr['Message'] = '服务号配置信息不完整，请补充appid和secret';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return;
        }
        
        // 获取access_token
        $access_token = $this->get_access_token_by_apppid($appid, $secret);
        
        if (empty($access_token)) {
            $arr['Success'] = '0';
            $arr['Message'] = '获取access_token失败';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return;
        }
        
        // 获取关注用户列表
        $followers = $this->get_followers_list($access_token);
        
        if ($followers === false) {
            $arr['Success'] = '0';
            $arr['Message'] = '获取关注用户列表失败';
        } else {
            $arr['Success'] = '1';
            $arr['Message'] = '获取关注用户列表成功';
            $arr['Results'] = $followers;
        }
        header('Content-type: application/json');
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;
    }

    /**
     * 获取关注用户列表
     * @param string $access_token 服务号access_token
     * @return array|bool 关注用户列表或false
     */
    private function get_followers_list($access_token)
    {
        if (empty($access_token)) {
            return false;
        }
        
        $url = 'https://api.weixin.qq.com/cgi-bin/user/get?access_token=' . $access_token;
        $res = json_decode(file_get_contents($url), true);
        
        if (isset($res['errcode']) && $res['errcode'] != 0) {
            return false;
        }
        
        return $res;
    }
    
    /**
     * 批量获取用户详细信息
     * @param array $params 请求参数
     */
    public function getFollowersDetailInfo($params)
    {
        require_once("/etc/steelconf/env/wxpay_env.php");

        // 钢之家WAP
        // $appid = WXAPPID;
        // $secret = WXAPPSECRET;

        // 上海钢之家网站
        $appid = APPID;
        $secret = "1699612571fb39c931a91d59b7a5b572";
        // dd($appid, $secret);
        // 参数验证
        if (empty($appid) || empty($secret)) {
            $arr['Success'] = '0';
            $arr['Message'] = '服务号配置信息不完整，请补充appid和secret';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return;
        }
        
        // 获取access_token
        $access_token = $this->get_access_token_by_apppid($appid, $secret);
        
        if (empty($access_token)) {
            $arr['Success'] = '0';
            $arr['Message'] = '获取access_token失败';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return;
        }
        
        // 获取关注用户列表
        $followers = $this->get_followers_list($access_token);
        
        if ($followers === false) {
            $arr['Success'] = '0';
            $arr['Message'] = '获取关注用户列表失败';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return;
        }
        
        // 获取用户详细信息
        $user_details = $this->get_users_detail_info($access_token, $followers['data']['openid']);
        
        if ($user_details === false) {
            $arr['Success'] = '0';
            $arr['Message'] = '获取用户详细信息失败';
        } else {
            $arr['Success'] = '1';
            $arr['Message'] = '获取用户详细信息成功';
            $arr['Results'] = array(
                'total' => $followers['total'],
                'count' => $followers['count'],
                'user_info_list' => $user_details
            );
        }
        
        header('Content-type: application/json');
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;
    }
    
    /**
     * 批量获取用户详细信息（每次最多100条）
     * @param string $access_token 服务号access_token
     * @param array $openid_list 用户openid列表
     * @return array|bool 用户详细信息列表或false
     */
    private function get_users_detail_info($access_token, $openid_list)
    {
        if (empty($access_token) || empty($openid_list)) {
            return false;
        }
        
        $user_info_list = array();
        
        // 微信接口每次最多拉取100条数据，需要分批处理
        $batch_size = 100;
        $total_openids = count($openid_list);
        $batch_count = ceil($total_openids / $batch_size);
        
        for ($i = 0; $i < $batch_count; $i++) {
            $start = $i * $batch_size;
            $end = min(($i + 1) * $batch_size, $total_openids);
            $batch_openids = array_slice($openid_list, $start, $end - $start);
            
            // 构造请求参数
            $post_data = array(
                'user_list' => array()
            );
            
            foreach ($batch_openids as $openid) {
                $post_data['user_list'][] = array(
                    'openid' => $openid,
                    'lang' => 'zh_CN'
                );
            }
            
            // 发送请求获取用户信息
            $url = 'https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token=' . $access_token;
            $options = array(
                'http' => array(
                    'method' => 'POST',
                    'header' => 'Content-type: application/json',
                    'content' => $this->pri_JSON($post_data)
                )
            );
            
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            $res = json_decode($result, true);
            
            if (isset($res['errcode']) && $res['errcode'] != 0) {
                return false;
            }
            
            // 合并用户信息
            if (isset($res['user_info_list'])) {
                $user_info_list = array_merge($user_info_list, $res['user_info_list']);
            }
        }
        
        return $user_info_list;
    }
    
    /**
     * 发送微信消息的公共方法（使用 w7corp/easywechat:^6.17）
     * @param string $openid 用户openid
     * @param array $data 消息数据
     * @param string $type 消息类型：template=模板消息，subscribe=订阅消息
     * @return array 处理结果
     */
    private function sendWechatMessage($openid, $data, $type = 'template')
    {
        // 参数验证
        if (empty($openid)) {
            $arr['Success'] = '0';
            $arr['Message'] = 'openid不能为空';
            return $arr;
        }
        
        try {
            // 获取配置信息
            if ($type === 'template') {
                // 服务号配置
                $config = [
                    'app_id' => WECHAT_APPID_AND_APPSECRET_OF_SERVICE["1"]['appid'],
                    'secret' => WECHAT_APPID_AND_APPSECRET_OF_SERVICE["1"]['appsecret'],
                ];
                
                // 创建 EasyWeChat 应用实例
                $app = new \EasyWeChat\OfficialAccount\Application($config);
            } else {
                // 小程序配置
                $config = [
                    'app_id' => WECHAT_APPID_AND_APPSECRET["1"]["appid"],
                    'secret' => WECHAT_APPID_AND_APPSECRET["1"]["appsecret"],
                ];
                
                // 创建 EasyWeChat 应用实例
                $app = new \EasyWeChat\MiniApp\Application($config);
            }
            
            // 获取 API Client
            $client = $app->getClient();
            
            // 根据消息类型发送不同的消息
            if ($type === 'template') {
                // 发送模板消息
                $response = $client->postJson('/cgi-bin/message/template/send', $data);
            } else {
                // 发送订阅消息
                $response = $client->postJson('/cgi-bin/message/subscribe/send', $data);
            }
            
            // 解析响应结果
            $result = $response->toArray();
            
            // 处理返回结果
            if (isset($result['errcode']) && $result['errcode'] == 0) {
                $arr['Success'] = '1';
                $arr['Message'] = '模板消息发送成功';
                $arr['Results'] = $result;
            } else {
                $arr['Success'] = '0';
                $arr['Message'] = '模板消息发送失败: ' . (isset($result['errmsg']) ? $result['errmsg'] : '未知错误');
                $arr['Results'] = $result;
            }
        } catch (\Exception $e) {
            $arr['Success'] = '0';
            $arr['Message'] = '发送消息时发生异常: ' . $e->getMessage();
            $arr['Results'] = [];
        }
        
        return $arr;
    }

    /**
     * 微信公众号(服务号)发送模板消息<当前接口使用 上海钢之家网站>
     * 
     * @param array $params 请求参数
     */
    private function sendTemplateMessage($params)
    {
        // 获取参数
        $openid = $params['openid'] ?? 'ogR_ljklL5bS9_f2WYNFvWc5tLzg';
        $call_time = $params['call_time'] ?? date('Y-m-d H:i:s');
        $remark = $params['remark'] ?? '公众号消息测试';
        
        // 会议预约成功通知：9OQFeeEi6WVpQyPPwuQzPEjMMpNX-G4ZmYiNaTrfH_o（带座位号）
        // 构造模板消息数据
        $template_data = [
            'touser' => $openid,
            'template_id' => "9OQFeeEi6WVpQyPPwuQzPEjMMpNX-G4ZmYiNaTrfH_o",
            'url' => "",
            'miniprogram' => [
                'appid' => WECHAT_APPID_AND_APPSECRET["1"]["appid"],
                'pagepath' => "/pages/index/index"
            ],  // 小程序跳转,非必填
            'data' => [
                'thing1' => ['value' => "主旨大会推送测试"],
                'thing3' => ['value' => "紫金城"],
                'character_string7' => ['value' => "A4001"],
                'time10' => ['value' => "2025-11-01 14:00"],
                'time14' => ['value' => "2025-11-01 18:00"],
            ],
        ];
        
        // 发送消息
        $result = $this->sendWechatMessage($openid, $template_data, 'template');
        
        return $result;
        // header('Content-type: application/json');
        // $json_string = $this->pri_JSON($result);
        // echo $json_string;
        // exit;
    }

    /**
     * 微信小程序发送订阅模板消息<当前接口使用 一站通会务>
     * @param array $params 请求参数
     */
    public function sendSubscribeMessage($params)
    {
        $openid = $params['openid'] ?? '';
        
        // 构造模板消息数据
        $template_data = array(
            'touser' => $openid,
            'template_id' => "kaFF6N_kX2iN_UAnoo2Eg0FeuuaCKBSBLxCdf2nOhvg",
            'data' => array(
                'name1' => array(
                    'value' => "测试人员"
                ),
                'time2' => array(
                    'value' => "2025-08-22 14:30:00"
                ),
                'thing3' => array(
                    'value' => "2025年10月大会"
                ),
                'thing5' => array(
                    'value' => "测试人员"
                )
            ),
            "miniprogram_state" => "formal",
            "lang" => "zh_CN"
        );
        
        // 发送消息
        $result = $this->sendWechatMessage($openid, $template_data, 'subscribe');
        
        header('Content-type: application/json');
        $json_string = $this->pri_JSON($result);
        echo $json_string;
        exit;
    }

    public function sendCronMessage()
    {
        $mtid = $GLOBALS['MEETING_ID'];
        $datetime = date("Y-m-d H:i:s");
        $select_mmc_field = ['Mtid','ContactMobile'];
        $sql = "select ".implode(",", $select_mmc_field)." from meeting_member_contactman where Mtid={$mtid} and ContactMobile!='' and ContactMobile is not null and Status=1";
        $mmc_list = $this->_dao->query($sql);
        $mobile_list = array_column($mmc_list, 'ContactMobile');
        $mobile_list = array_unique($mobile_list);
        if($datetime <= '2025-10-29 00:00:00') {
            // 内部人员测试
            $mobile_list = [
                '17628725464',
                '18370450307',
                '13085554576'
            ];
        }
        // dd($mobile_list);
        $sql = "select unionid from wechatuser where mobile in ('".implode("','", $mobile_list)."') and status=1";
        $wechat_user_list = $this->_dao->query($sql);
        $unionid = array_column($wechat_user_list, 'unionid');

        // 'o8AxHw7qLTg8pGuTMXTv1E2GszOQ'
        $sql = "select wpf.uid,wpf.openid from wp_user wu inner join wp_public_follow wpf on wpf.uid=wu.uid where unionid in ('".implode("','", $unionid)."') and token='gh_9083bda97a79' and has_subscribe=1";
        $wechat_follow_list = $this->wxdao->aquery($sql);

        // dd($wechat_follow_list);
        $res = [];
        foreach($wechat_follow_list as $wxuid => $openid) {
            $res[$openid] = $this->sendTemplateMessage(["openid"=>$openid]);
        }

        header("Content-Type: application/json");
        echo json_encode($res);
        exit;
    }
}
