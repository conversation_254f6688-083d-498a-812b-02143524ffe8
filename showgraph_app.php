<?php
error_reporting(E_ALL^E_NOTICE^E_WARNING);
/*
+--------------------------------------------------------------------------
|   Project: 钢材频道_市场资讯(messagecenter_gcpd.php,messagecenter_html.php)
|   ========================================
|   by huleshui
+--------------------------------------------------------------------------
|   > 修改日期: 2004－09－20
+--------------------------------------------------------------------------
// -------------------------------------------------------------------------
功能简述：steelhome项目--钢材频道首页显示程序
变量定义：
gcpd_gqzb : 供求专版
       id : 热点专题id
// -------------------------------------------------------------------------
*/
// session_start();
// ini_set("display_errors",true);
// error_reporting(E_ALL);
include_once('/etc/steelconf/config/isholiday.php');
require_once("/etc/steelconf/ips/denycheck.php");
require_once('include/config.php');
require_once("arrayfile.php");
require_once("include/funzst.php");
include 'charts.php';
require_once($_SERVER["DOCUMENT_ROOT"].'/Connections/steelhomemark.php');
include("/etc/steelconf/sthframe/steelhome_db_config.php");
include_once( "./biz/function/inc_tjcomp.php" );
/** 91主库4306 **/
$conn = ADONewConnection('mysqli');  # create a connection
$conn->PConnect($HOST_NAME_91W.":".$HOST_PORT_91W,$USER_NAME_91W,$USER_PAWD_91W,$DBSE_NAME_91W);
$conn2 = ADONewConnection('mysqli');
$conn2->PConnect($HOST_NAME_91R.":".$HOST_PORT_91R,$USER_NAME_91R,$USER_PAWD_91R,$DBSE_NAME_91R);
$conn3 = ADONewConnection('mysqli');
$conn3->PConnect($HOST_NAME_91S.":".$HOST_PORT_91S,$USER_NAME_91S,$USER_PAWD_91S,$DBSE_NAME_91S);

$conn_biz = ADONewConnection('mysqli');  # create a connection
$conn_biz->PConnect($HOST_NAME_98W.":".$HOST_PORT_98W,$USER_NAME_98W,$USER_PAWD_98W,$DBSE_NAME_98W);

//$power=max($_SESSION["gcpower"],$_SESSION["llpower"],$_SESSION["hgpower"],$_SESSION["yspower"],$_SESSION["mhgpower"],$_SESSION["thjpower"]);
		$_REQUEST['STime']=$_SESSION['STime'];
		$_REQUEST['ETime']=$_SESSION['ETime'];
		$power=$_COOKIE['maxpower'];
		if ( empty($power)) {
            $power=$_REQUEST['maxPower'];
        }

		$isLogin=$_REQUEST['isLogin'];
		$GUID=$_REQUEST['GUID'];
		$AppMode=$_REQUEST['AppMode'];
		$fromapp=isset($_REQUEST['fromapp'])?$_REQUEST['fromapp']:"";
		//add changhong 2018-03-06 wap 现货报价
		//echo '<pre/>';print_r($_COOKIE);
		//if($_REQUEST['iswap'] == 1 && $_COOKIE['ce'] !=''){
		if($_REQUEST['iswap'] == 1 ){
			$isLogin=1;
			$power=$_REQUEST['maxPower'];
			$iswap = $_REQUEST['iswap'];
			if(strpos($_SERVER['HTTP_HOST'],"steelhome.cn")!=false){//兼容cn的页面cookie
				setcookie('maxpower', $power, 0, '/', '.steelhome.cn');
			}
			else
				setcookie('maxpower', $power, 0, '/', '.steelhome.com');
		}
		//echo "<pre>";print_r($_REQUEST);
 //echo "power=".$power."<br>";
	//日期
		$year = date("Y");
		for($i=$year;$i>=2004;$i--){
			$array_year[] = $i;
		}
		for($j=1;$j<=12;$j++){
			if($j<10){
			$array_month[] = "0".$j;
			}else{
			$array_month[] = $j;
			}
		}
		for($k=1;$k<=31;$k++){
			if($k<10){
			$array_day[] = "0".$k;
			}else{
			$array_day[] = $k;
			}
		}
		if($_REQUEST['year1']!='' && $_REQUEST['year2']!=''){
			$_REQUEST['STime'] = $_REQUEST['year1']."-".$_REQUEST['month1']."-".$_REQUEST['day1'];
			$_REQUEST['ETime'] = $_REQUEST['year2']."-".$_REQUEST['month2']."-".$_REQUEST['day2'];
		}elseif($_REQUEST['stime']!='' && $_REQUEST['etime']!=''){
			$_REQUEST['STime'] = $_REQUEST['stime'];
			$_REQUEST['ETime'] = $_REQUEST['etime'];

		}elseif($_REQUEST['sYear']!='' && $_REQUEST['eYear']!=''){
			$_REQUEST['STime'] = $_REQUEST['sYear']."-".$_REQUEST['sMonth']."-".$_REQUEST['sDay'];
			$_REQUEST['ETime'] = $_REQUEST['eYear']."-".$_REQUEST['eMonth']."-".$_REQUEST['eDay'];
		}

		if(strtotime($_REQUEST['STime'])<strtotime($_REQUEST['ETime'])){
			$STime = $_REQUEST['STime'];
			$ETime = $_REQUEST['ETime'];
		}else{
			$STime = $_REQUEST['ETime'];
			$ETime = $_REQUEST['STime'];
		}
		if($STime !='' && $ETime !=''){
			$arr_STime = explode("-",$STime);
			$arr_ETime = explode("-",$ETime);
		}else{
			$arr_STime['0'] = date("Y",strtotime("-1 year",time()));
			$arr_STime['1'] = date("m");
			$arr_STime['2'] = date("d");

			$arr_ETime['0'] = date("Y");
			$arr_ETime['1'] = date("m");
			$arr_ETime['2'] = date("d");
		}
		/*
		$sssstime = strtotime('-6 month',$ETime);

		$bannian=strtotime($STime)-strtotime($ETime);

		if($sssstime>$bannian){
			$isSexmonth=1;
		}*/


		if($_REQUEST['cx_submit']!='' && ($_REQUEST['cx_submit']=='走势图查询'||$_REQUEST['cx_submit']=='Search')){
			if (!isset($_COOKIE['maxpower'])&&$GUID!=""){
					$contentTS="";
					$contentTS.="<html>
					<head>
					<meta http-equiv='Content-Type' content='text/html; charset=gb2312'>
					<meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0'>
					</head><body>";
					$contentTS.=($AppMode!="4" ? "您的账号已过期":"Your account has expired")."！<body></html>";
					echo $contentTS;
					exit;
			}
			//未登录  未登录时不能查看 非当日 或 跨度超过6个月的走势图
			if($isLogin==0){
				//if($ETime!=date("Y-m-d")||$isSexmonth==1){
					$contentTS="";
					$contentTS.="<html>
					<head>
					<meta http-equiv='Content-Type' content='text/html; charset=gb2312'>
					<meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0'>
					</head><body>";
					$contentTS.=($AppMode!="4" ? "该内容需登录后查看！":"This content needs to be logged in and viewed!")."<body></html>";
					echo $contentTS;
					exit;
			}else{
			//已登录
				if($power>=4){
					//可以查询
					$_SESSION['STime'] = $STime;
					$_SESSION['ETime'] = $ETime;
				}else{
					$contentTS="";
					$contentTS.="<html>
					<head>
					<meta http-equiv='Content-Type' content='text/html; charset=gb2312'>
					<meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0'>
					</head><body>";
					$contentTS.=($AppMode!="4" ? "该内容您尚无权限查看！请联系管理员。":"You do not have permission to view this content yet! Please contact the administrator.")."<body></html>";
					echo $contentTS;
					exit;
				}
			}
		}
		//起始时间
		$start_year = "<select name='year1' id='select1'>";
		foreach($array_year as $aykey=>$ayvalue){
			if($_REQUEST['year1']==$ayvalue){
			$start_year .="<option value='".$ayvalue."' selected>".$ayvalue.($AppMode=="4" ? "":"年")."</option>";
			}elseif($arr_STime['0']==$ayvalue){
			$start_year .="<option value='".$ayvalue."' selected>".$ayvalue.($AppMode=="4" ? "":"年")."</option>";
			}else{
			$start_year .="<option value='".$ayvalue."'>".$ayvalue.($AppMode=="4" ? "":"年")."</option>";
			}
		}
		$start_year .="</select>";

		$start_month = "<select name='month1' id='select2'>";
		foreach($array_month as $amkey=>$amvalue){
			if($_REQUEST['month1']==$amvalue){
			$start_month .="<option value='".$amvalue."' selected>".$amvalue.($AppMode=="4" ? "":"月")."</option>";
			}elseif($arr_STime['1']==$amvalue){
			$start_month .="<option value='".$amvalue."' selected>".$amvalue.($AppMode=="4" ? "":"月")."</option>";
			}else{
			$start_month .="<option value='".$amvalue."'>".$amvalue.($AppMode=="4" ? "":"月")."</option>";
			}

		}
		$start_month .="</select>";

		$start_day = "<select name='day1' id='select3'>";
		foreach($array_day as $adkey=>$advalue){
			if($_REQUEST['day1']==$advalue){
			$start_day .="<option value='".$advalue."' selected>".$advalue.($AppMode=="4" ? "":"日")."</option>";
			}elseif($arr_STime['2']==$advalue){
			$start_day .="<option value='".$advalue."' selected>".$advalue.($AppMode=="4" ? "":"日")."</option>";
			}else{
			$start_day .="<option value='".$advalue."'>".$advalue.($AppMode=="4" ? "":"日")."</option>";
			}

		}
		$start_day .="</select>";
		//结束时间
		$end_year = "<select name='year2' id='select4'>";
		foreach($array_year as $aykey2=>$ayvalue2){
			if($_REQUEST['year2']==$ayvalue2){
			$end_year .="<option value='".$ayvalue2."' selected>".$ayvalue2.($AppMode=="4" ? "":"年")."</option>";
			}elseif($arr_ETime['0']==$ayvalue2){
			$end_year .="<option value='".$ayvalue2."' selected>".$ayvalue2.($AppMode=="4" ? "":"年")."</option>";
			}else{
			$end_year .="<option value='".$ayvalue2."'>".$ayvalue2.($AppMode=="4" ? "":"年")."</option>";
			}

		}
		$end_year .="</select>";

		$end_month = "<select name='month2' id='select5'>";
		foreach($array_month as $amkey2=>$amvalue2){
			if($_REQUEST['month2']==$amvalue2){
			$end_month .="<option value='".$amvalue2."' selected>".$amvalue2.($AppMode=="4" ? "":"月")."</option>";
			}elseif($arr_ETime['1']==$amvalue2){
			$end_month .="<option value='".$amvalue2."' selected>".$amvalue2.($AppMode=="4" ? "":"月")."</option>";
			}else{
			$end_month .="<option value='".$amvalue2."'>".$amvalue2.($AppMode=="4" ? "":"月")."</option>";
			}

		}
		$end_month .="</select>";

		$end_day = "<select name='day2' id='select6'>";
		foreach($array_day as $adkey2=>$advalue2){
			if($_REQUEST['day2']==$advalue2){
			$end_day .="<option value='".$advalue2."' selected>".$advalue2.($AppMode=="4" ? "":"日")."</option>";
			}elseif($arr_ETime['2']==$advalue2){
			$end_day .="<option value='".$advalue2."' selected>".$advalue2.($AppMode=="4" ? "":"日")."</option>";
			}else{
			$end_day .="<option value='".$advalue2."'>".$advalue2.($AppMode=="4" ? "":"日")."</option>";
			}
		}
		$end_day .="</select>";

		// echo $date_select;

	if(isset($_REQUEST['danwei_usa']) && $_REQUEST['danwei_usa']!=''){
		$usa = "usa";
	}else{
		$usa = "";
	}
	if(isset($_REQUEST['luliaoteshu']) && $_REQUEST['luliaoteshu']!=''){
		$luliaoteshu = "luliaoteshu";
	}else{
		$luliaoteshu = "";
	}
	if(isset($_REQUEST['luliaoteshu2']) && $_REQUEST['luliaoteshu']!=''){
		$luliaoteshu2 = "luliaoteshu2";
	}else{
		$luliaoteshu2 = "";
	}

	 //是否钢市强度
	if($_REQUEST['gsqd']=='1'){
		$gsqd="&gsqd=1";
		$content_ss="<input type='hidden' name='gsqd' value='1' id='gsqd'>";
		/*$content_xx="<br /><a href=http://www.steelhome.cn/shpi/shpi_memo.php#gsqd_shuoming>说明：钢之家（中国）钢材价格强度指数，简称SHSDI，是反映中国国内钢材市场价格涨跌强度的指标。</a>";*/
	}else{
		$gsqd="";
	}
	if($_REQUEST['gs']=='1'){
		$gs_gs="&gs=1";
		$content_gs="<input type='hidden' name='gs' value='1' id='gs'>";
	}else{
		$gs_gs="";
	}
	//jxs==经销商
	if(!empty($_REQUEST['variety'])){
		foreach($_REQUEST['variety'] as $rkey=>$rvalue){
			$variety .= "||".$rvalue;
		}
		$arr_var = $_REQUEST['variety'];
		$jxs_var = $_REQUEST['variety']['0'];
	}elseif($_REQUEST['hidden_variety']!=''){
			$variety = $_REQUEST['hidden_variety'];
			$arr_var = explode("||",$variety);
			$jxs_var = $arr_var['1'];
	}elseif($_REQUEST['topicture']!=''){
		$variety = "||".$_REQUEST['topicture'];
		$jxs_var = $_REQUEST['topicture'];

	}else{
		$gsqd="&gsqd=1";
		$content_ss="<input type='hidden' name='gsqd' value='1' id='gsqd'>";
		/*$content_xx="<br /><a href=http://www.steelhome.cn/shpi/shpi_memo.php#gsqd_shuoming>说明：钢之家（中国）钢材价格强度指数，简称SHSDI，是反映中国国内钢材市场价格涨跌强度的指标。</a>";*/
	}

	$tmp_arr = $arr_var;
	$arr_var = array();
	foreach($tmp_arr as $key=>$value){
		if($value) $arr_var[] = $value;
	}

	$cityid ='00'.substr($jxs_var,0,2);

	$city_name = $conn2->getOne( "SELECT cityname FROM city WHERE cityid='$cityid' " );

	//update by hezp started 2018/03/12
    include_once( "jxs_message.php" );
    //获取品种ID和名称C18630
	/*$pz_keycode = substr($jxs_var,2,2);
	$pz_names = $conn2->getArray("SELECT * FROM `graphvariety`");
	foreach($pz_names as $pzkey=>$pzvalue){
		if($pzvalue['gvid'] == $pz_keycode){
		$pz_name = $pz_names[$pzkey]['varietyname'];
		}
	}
	$pz_id = substr($jxs_var, 2, 2);
	$pz_id = $GLOBALS['SHPI_PZ_K'][$pz_id];
	if($pz_id=='87'){
		$pz_id='83';
	}
	// echo "<pre>";print_r($GLOBALS['SHPI_PZ_K']);
	//获取城市KEY
	$cid = $conn_biz->getOne( "SELECT id FROM biz_key WHERE ktype=4 AND kname='".$city_name."'" );

	if($_REQUEST['topicture']=='123012'){

		$sqltemp1 = "SELECT * FROM reszb_all WHERE memberid = 23821 AND  ( pm_id = 175 OR pm_parentid = 175 OR pm_ppid = 175 ) and (jhdd_id=$cid or jhdda_id=$cid) ORDER BY mbdate DESC LIMIT 1";
		$sqltemp2 = "SELECT * FROM reszb_all WHERE memberid = 23821 AND  ( pm_id = 175 OR pm_parentid = 175 OR pm_ppid = 175 ) ORDER BY mbdate DESC LIMIT 1";
		$sqltemp3 = "SELECT * FROM reszb_all WHERE memberid = 23821  ORDER BY mbdate DESC LIMIT 1";
		$jxs_message_temp = $conn_biz->getArray( $sqltemp1 );
		$jxs_message_temp2 = $conn_biz->getArray( $sqltemp2 );
		$jxs_message_temp3 = $conn_biz->getArray( $sqltemp3 );
		if(!empty($jxs_message_temp)){
			$sqltemp = $sqltemp1;
			$jxs_message = $conn_biz->getArray("( $sqltemp ) UNION (SELECT * FROM reszb_all WHERE ( pm_id = '".$pz_id."' OR pm_parentid = '".$pz_id."' OR pm_ppid = '".$pz_id."' ) and (jhdd_id=$cid or jhdda_id=$cid) GROUP BY memberid ORDER BY mbdate DESC LIMIT 8)");
		}else if(!empty($jxs_message_temp2)){
			$sqltemp = $sqltemp2;
			$jxs_message = $conn_biz->getArray("( $sqltemp ) UNION (SELECT * FROM reszb_all WHERE ( pm_id = '".$pz_id."' OR pm_parentid = '".$pz_id."' OR pm_ppid = '".$pz_id."' ) and (jhdd_id=$cid or jhdda_id=$cid) GROUP BY memberid ORDER BY mbdate DESC LIMIT 8)");
		}else if(!empty($jxs_message_temp3)){
			$sqltemp = $sqltemp3;
			$jxs_message = $conn_biz->getArray("( $sqltemp ) UNION (SELECT * FROM reszb_all WHERE ( pm_id = '".$pz_id."' OR pm_parentid = '".$pz_id."' OR pm_ppid = '".$pz_id."' ) and (jhdd_id=$cid or jhdda_id=$cid) GROUP BY memberid ORDER BY mbdate DESC LIMIT 8)");
		}else{
			$jxs_message = $conn_biz->getArray("SELECT * FROM reszb_all WHERE ( pm_id = '".$pz_id."' OR pm_parentid = '".$pz_id."' OR pm_ppid = '".$pz_id."' ) and (jhdd_id=$cid or jhdda_id=$cid) GROUP BY memberid ORDER BY mbdate DESC LIMIT 9");
		}

	}else{

        if($pz_id!=''){
		$jxs_message = $conn_biz->getArray("SELECT * FROM reszb_all WHERE ( pm_id = '".$pz_id."' OR pm_parentid = '".$pz_id."' OR pm_ppid = '".$pz_id."' ) and (jhdd_id=$cid or jhdda_id=$cid) GROUP BY memberid ORDER BY mbdate DESC LIMIT 9");
        }
	}

	if(empty($jxs_message)){

        if($pz_id!=''){
		$jxs_message = $conn_biz->getArray("SELECT * FROM reszb_all WHERE ( pm_id = '".$pz_id."' OR pm_parentid = '".$pz_id."' OR pm_ppid = '".$pz_id."' ) GROUP BY memberid ORDER BY mbdate DESC LIMIT 9");
        }
	}

	if(empty($jxs_message)){
		$jxs_message = $conn_biz->getArray("SELECT * FROM reszb_all WHERE ( pm_id = 14 OR pm_parentid = 14 OR pm_ppid = 14 ) and (jhdd_id=1049 or jhdda_id=1049) GROUP BY memberid ORDER BY mbdate DESC LIMIT 9 ");
		$city_name = "上海";
		$pz_name = "螺纹钢";
	}*/
    //update by hezp ended 2018/03/12

	//added by tuxw start 20150806
	$avgflag=0;
	if(isset($_SESSION['mid'])){
		$mid = $_SESSION['mid'];
	}else{
		$mid='';
	}
	// power由7改成6, 使甲级会员能显示均价
	if($mid==1 || $_SESSION['maxpower']>=6)
	{
	  $avgflag=1;
	}

	$starttime = date('Y-m',strtotime($STime));
	$endtime = date('Y-m',strtotime($ETime));
	$price_arr = array(array());
	$arr_shpi = array(
		'tks_62'=>array('table'=>'shpi_material_pzp','colname'=>'vid','value'=>'3','price_col'=>'weipriceusb','date_col'=>'dateday'),
		'tks_58'=>array('table'=>'shpi_pp','colname'=>'bc_id','value'=>'20','price_col'=>'weiprice','date_col'=>'dateday'),
		'tks_61'=>array('table'=>'shpi_pp','colname'=>'bc_id','value'=>'18','price_col'=>'weiprice','date_col'=>'dateday'),
		'tks_63'=>array('table'=>'shpi_pp','colname'=>'bc_id','value'=>'16','price_col'=>'weiprice','date_col'=>'dateday'),
		'tks_65'=>array('table'=>'shpi_pp','colname'=>'bc_id','value'=>'9','price_col'=>'weiprice','date_col'=>'dateday'),
	);

	foreach($arr_var as $k=>$v) {
		$i = 0;
		$time = $endtime;

		while($time>=$starttime && $i<6) {
			$year_month = str_replace("-","",$time);
			if(!isset($arr_shpi[$v])){
				if(strlen($v)==7){
					$average = $conn2 ->getOne("select month_average from shpi_month_average where mastertopid='$v' and yearmonth='$year_month'");
				}else{
					$average = $conn2 ->getOne("select month_average from shpi_month_average where topicture='$v' and yearmonth='$year_month'");
				}
				if($average != null) {
					$price_arr[$k][$time] = $average;
				} else {
					if(strlen($v)==7){
						$sum = $conn2 ->getOne("select sum(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.mastertopid='$v' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
						$count = $conn2 ->getOne("select count(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.mastertopid='$v' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
					}else{
						$sum = $conn2 ->getOne("select sum(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.topicture='$v' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
						$count = $conn2 ->getOne("select count(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.topicture='$v' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
					}
                    $ave = $count > 0 ? $sum / $count : "-";
					$price_arr[$k][$time] = $ave;
				}
			}else{
				$ave = $conn2 ->getOne("select AVG(".$arr_shpi[$v]['price_col'].") from ".$arr_shpi[$v]['table']." where ".$arr_shpi[$v]['colname']."='".$arr_shpi[$v]['value']."' and DATE_FORMAT(".$arr_shpi[$v]['date_col'].",'%Y-%m')='$time'");
				$price_arr[$k][$time] = $ave;
			}
			$i++;
			$time = date('Y-m',strtotime('-1 month',strtotime($time)));
		}
	}


	$ffflag = 0;
	if($arr_var == null && $_REQUEST['topicture']) {
		$newid = [];
		foreach(explode("||",$_REQUEST['topicture']) as $key => $newid_val) {
			if(mb_strlen($newid_val) == 20) {
				array_push($newid, $newid_val);
			}
		}
		$newid = array_unique($newid);

		if(!empty($newid)) {
			$_20ids = "'".implode("','", $newid)."'";
			$_REQUEST['stime'] ??= date("Y-m-d");
			$_REQUEST['etime'] ??= date("Y-m-d");
			$sql = "select m.topicture,m.mastertopid,m.id,mpc.price_code from marketconditions_price_code mpc left join marketconditions m on mpc.marketconditions_id=m.id where mpc.price_code in ({$_20ids}) and mpc.mconmanagedate between '{$_REQUEST['stime']} 00:00:00' and '{$_REQUEST['etime']} 23:59:59'";
			$temparr1 = $conn2 ->getAll($sql);
			$newids = [];
			$prids = [];
			foreach($temparr1 as $val) {
				$newids[] = $val['price_code'];
				if($val['mastertopid']!="") {
					$prids[] = $val['mastertopid'];
				} else if($val['topicture']!="") {
					$prids[] = $val['topicture'];
				}
			}
			$newids = array_unique($newids);
			$prids = array_unique($prids);

			// if(!empty($prids)) {
			// 	$_REQUEST['topicture'] = implode("||", $prids);
			// 	$variety = "||".$_REQUEST['topicture'];
			// } else {
				$priceFetcher = (new Steelhome\SteelhomePrice\PriceFetcher())
				->setDao($conn2)
				->setPriceIds($newids)
				->setDateRange($_REQUEST['stime'], $_REQUEST['etime'])
				->setGroupByDate();
				// $prices = $priceFetcher->getPrices();
				$prices = $priceFetcher->getMonthAveragePrices();
				foreach($prices as $key => $val11) {
					$i = 1;
					foreach($val11 as $keyDate => $val22) {
						if($i>6) break;
						$price_arr[0][$val22['year']."-".$val22['month']] = (string)$val22['averageValue'];
						// $price_arr[0][$val22['year']."-".$val22['month']] = $val22['roundAverageValue']; //每个价格先进行了四舍五入取整，然后再除以个数
						$i++;
					}

					$ffflag = 1;
				}
			// }
		}

		if($ffflag == 0) {
			$t = $_REQUEST['topicture'];
			$i = 0;
			$time = $endtime;
			while($time>=$starttime && $i<6) {
				$year_month = str_replace("-","",$time);
				if(strlen($t)==7){
					$average = $conn2 ->getOne("select month_average from shpi_month_average where mastertopid='$t' and yearmonth='$year_month'");
				}else{
					$average = $conn2 ->getOne("select month_average from shpi_month_average where topicture='$t' and yearmonth='$year_month'");
				}
				if($average != null) {
					$price_arr[0][$time] = $average;
				} else {
					if(strlen($t)==7){
						$sum = $conn2 ->getOne("select sum(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.mastertopid='$t' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
						$count = $conn2 ->getOne("select count(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.mastertopid='$t' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
					}else{
						$sum = $conn2 ->getOne("select sum(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.topicture='$t' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
						$count = $conn2 ->getOne("select count(price) from marketconditions mc,marketrecord mr where mc.marketrecordid=mr.id and mc.topicture='$t' and DATE_FORMAT(managedate,'%Y-%m')='$time'");
					}
					if($count)
					$price_arr[0][$time] = $sum/$count;
				}
				$i++;
				$time = date('Y-m',strtotime('-1 month',strtotime($time)));
			}
		}
	}

    //added by hezp started 2017/01/09
	$tjcompstr .= "<TABLE 
                  style='BORDER-RIGHT: #CCCCCC 1px solid; BORDER-TOP: #CCCCCC 1px solid; BORDER-LEFT: #CCCCCC 1px solid; 
                  BORDER-BOTTOM: #CCCCCC 1px solid;margin-top:10px;margin-left:10px;' 
                  cellSpacing=0 cellPadding=0 width=97% align=center border=0>
  <TBODY>
      <TR>
        <TD vAlign=top><TABLE cellSpacing=0 cellPadding=0 width=97% 
                        background=images/w2_2.jpg border=0>
            <TBODY>
              <TR>
                <TD vAlign=top width=27><IMG height=35 
                              src='images/w2_1.jpg' width=27></TD>
                <TD vAlign=center><SPAN 
                              style=' FONT-SIZE: 14px; COLOR: #FB5C00;float:left; line-height:27px; '>$city_name $pz_name <a COLOR: #484848 > 经销商现货报价</SPAN>
			<!---此处修改 ---><SPAN style=' FONT-SIZE: 12px; COLOR: #FB5C00; float:right;'></SPAN>
							  
							  </TD>
              </TR>
            </TBODY>
        </TABLE></TD>
      </TR>";

	foreach( $jxs_message as $mbinfo ){

			   $mbinfo['adminuser_phone'] = str_replace( array( '086-', '86-' ), "", $mbinfo['adminuser_phone'] );
			   $mbinfo['adminuser_fax'] = str_replace( array( '086-', '86-' ), "", $mbinfo['adminuser_fax'] );
			   $mbinfo['compabb'] = mb_substr($mbinfo['compabb'],0,4);
			   $mbinfo['pm'] = mb_substr($mbinfo['pm'],0,3);
			   $mbinfo['cz'] = mb_substr($mbinfo['cz'],0,9);
			   $mbinfo['gg'] = mb_substr($mbinfo['gg'],0,9);

	           $tjcompstr .= " <tr height=30>
                            <td class=h12px >
		                    <div style='float:left; width:20%;'>&middot;&nbsp;$mbinfo[compabb]</div>
		                       
                            <div style='float:left; width:40%;padding-left:5px;'>&nbsp;$mbinfo[pm]-$mbinfo[cz]-$mbinfo[gg]</div>
			
			                <div style='float:left; width:13%;'>&nbsp;$mbinfo[jg]</div>
			                <div style='float:left; width:15%;'>&nbsp;<a href=\"tel:$mbinfo[adminuser_phone]\" >$mbinfo[adminuser_name]</a></div>
			                <div style='float:left; width:8%;'>&nbsp;<a href=\"tel:$mbinfo[adminuser_phone]\" ><img src=\"./images/phone.png\" height=\"14\" width=\"14\" /></a></div>
		                </td></tr>";
    }

	/*onclick=\"tel('tel:$mbinfo[adminuser_phone]')\"*/
    if($AppMode=="4")$tjcompstr="";
	$tjcompdiv = "<div style='float:center;padding-top:10px;'> $tjcompstr </TABLE></div>";

    //added by hezp ended 2017/01/09
    $startDate = $AppMode=="4" ? "From&nbsp;&nbsp;" : "开始时间：";
    $endDate = $AppMode=="4" ? "To&nbsp;&nbsp;" : "结束时间：";
    $zstcx = $AppMode=="4" ? "Search" : "走势图查询";
    $wbj = $AppMode=="4" ? "No Watermark" : "无背景";
    $month_average = $AppMode=="4" ? "Monthly Average Price" : "月均价";


	//$date_select = "<form name='form1' method='post' action='showgraph_app.php'><div align='left' class='shijianxuanze' >".$startDate."：".$start_year.$start_month.$start_day."<br>".$endDate."：".$end_year.$end_month.$end_day."<br>
	$date_select = "<form name='form1' method='post' action='showgraph_app.php'><table cellspacing='0' cellpadding='0' border='0' class='shijianxuanze'><tr><td " .($AppMode=="4"?"style='font-size:0.6rem;'":"").">".$startDate."</td><td>".$start_year.$start_month.$start_day."</td></tr><tr><td " .($AppMode=="4"?"style='font-size:0.6rem;'":"").">".$endDate."</td><td>".$end_year.$end_month.$end_day."</td></tr></table><div class='shijianxuanze'>
	<input type='submit' name='cx_submit' value='".$zstcx."' ".($AppMode=="4" ?"style='color:#FFF;font-size:0.6rem;'":"").">&nbsp; ".$wbj."&nbsp; <input type='checkbox' id='wbj'>
	<input type='hidden' name='hidden_variety' value='".$variety."'>".$content_ss.$content_gs."
	<input type='hidden' name='danwei_usa' value='".$usa."'>
	<input type='hidden' name='luliaoteshu' value='".$luliaoteshu."'>
	<input type='hidden' name='luliaoteshu2' value='".$luliaoteshu2."'>
	<input type='hidden' name='isLogin' value='".$isLogin."'>
	<input type='hidden' name='maxPower' value='".$power."'>
	<input type='hidden' name='iswap' value='".$iswap."'>
	<input type='hidden' name='AppMode' value='".$AppMode."'>
	<input type='hidden' name='fromapp' value='".$fromapp."'>
	<input type='hidden' name='GUID' value='".$GUID."'>
	<input type='hidden' id='p_lianjie' value='./accbarex2.php?STime=".$STime."&ETime=".$ETime."&variety=".$variety."&danwei_usa=".$usa."&luliaoteshu=".$luliaoteshu."&luliaoteshu2=".$luliaoteshu2.$gsqd.$gs_gs."&AppMode=".$AppMode."'>
	</div></form></br>";


	$content .= "<div align='left' style=' float:left;'>";
	$content .=$date_select;
	$content .="<img id='no_bg' src='./accbarex2.php?STime=".$STime."&ETime=".$ETime."&variety=".$variety."&danwei_usa=".$usa."&luliaoteshu=".$luliaoteshu."&luliaoteshu2=".$luliaoteshu2.$gsqd.$gs_gs."&AppMode=".$AppMode."' /><br />".$content_xx."<br />";

	$arr_color = array("#fb0404","#8a04fc","#0309fe","#03fbf8","#f9f003");
	//登录才可以看月均价
	if($isLogin==1){
	$content .= "<table class='jiange' cellpadding='0' cellspacing='0' border='0'><tr style='background-color:rgb(205,240,246);'><td class='td16' align=center>".$month_average."</td>";
	foreach(array_reverse($price_arr[0]) as $k=>$v) {
		$content .= "<td class='td14' align=center>$k</td>";
	}
	$content .= "</tr>";

	//add by zhudahua start 20160930
	$NotIntArr = array(
		"C18620",
		"A98630",
		"C18690",
		"B98650",
		"B98670",
		"tks_65",
		"tks_63",
		"tks_62",
		"tks_61",
		"tks_58",
		"I98640",
		"I98670",
		"H98640",
		"H98670",
		"H98620",
		"H98600",
	);
	//add by zhudahua end 20160930
	foreach($arr_var as $key=>$value) {
		//add by zhudahua start 20160930
		if(in_array($value,$NotIntArr))
		{
			$content .= "<tr><td align=center><hr width=15 noshade color='$arr_color[$key]'></td>";
			foreach(array_reverse($price_arr[$key]) as $key2=>$value2)
			{
				if((count($price_arr[$key])-$key2) <= 6)
				{
					$content .= "<td align=center>".number_format($price_arr[$key][$key2],2,".","")."</td>";
				}
			}
			$content .= "</tr>";
			continue;
		}
		//add by zhudahua end 20160930
		if(!in_array($value,array("lw_qh","rz_qh","tjf_qh","jt_qh","jm_qh","dlm_qh","gt_qh","gm_qh","lw_qh_jc","rz_qh_jc","tjf_qh_jc","jt_qh_jc","jm_qh_jc","dlm_qh_jc","gt_qh_jc","gm_qh_jc"))) {
		$content .= "<tr><td align=center><hr width=15 noshade color='$arr_color[$key]'></td>";
		foreach(array_reverse($price_arr[$key]) as $key2=>$value2) {
			if((count($price_arr[$key])-$key2) <= 6) {
                if ($price_arr[$key][$key2]<10){
                    $price = number_format( floatval($price_arr[$key][$key2]),2,".","");
                }else{
                    $price = number_format( floatval($price_arr[$key][$key2]),0,"","");
                }
                $content .= "<td align=center>".$price."</td>";
			}
		}
		$content .= "</tr>";
		}
	}
	//print_r($price_arr);
	if($arr_var == null) {

		$content .= "<tr><td align=center><hr width=15 noshade color='$arr_color[0]'></td>";
		//updated by quanjw for dpk
		if($_GET['topicture'] == "H98600"){
			foreach(array_reverse($price_arr[0]) as $key2=>$value2) {
				$content .= "<td align=center>".number_format($price_arr[0][$key2],2,".",",")."</td>";
			}
		}else{
			foreach(array_reverse($price_arr[0]) as $key2=>$value2) {
			    if ($price_arr[0][$key2]<10){
			        $price = number_format($price_arr[0][$key2],2,".","");
                }else{
                    $price = number_format($price_arr[0][$key2],0,"","");
                }
				$content .= "<td align=center>".$price."</td>";
			}
		}

		$content .= "</tr>";
	}
	}



	$content .= "</div>";

	$content .= "</div>";
	$content .=$tjcompdiv;

function mysubstr($str, $start, $len) {
    $tmpstr = "";
    $strlen = $start + $len;
    for($i = 0; $i < $strlen; $i++) {
        if(ord(substr($str, $i, 1)) > 0xa0) {
            $tmpstr .= substr($str, $i, 2);
            $i++;
        } else
            $tmpstr .= substr($str, $i, 1);
    }
    return $tmpstr;
}

// echo "<pre>";print_r($GLOBALS['SHPI_PZ_K']);

//根据城市名取得城市关健字id
function getCityKeywordByName( $cityname ){
    //   return $this->getOne( "SELECT id FROM biz_key WHERE ktype=4 AND kname='$cityname'" );
	return "";
  }

?>
<html>
<head>
<meta content="charset=utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">

<link href="css/style.css" rel="stylesheet" type="text/css">
<style>

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button{
    -webkit-appearance: none !important;
}
html {
    font-size : 22.5px;
}
@media only screen and (min-width: 320px){
    html {
        font-size: 20px !important;
    }
}
@media only screen and (min-width: 360px){
    html {
        font-size: 22.5px !important;
    }
}
@media only screen and (min-width: 375px){
    html {
        font-size: 23.4375px !important;
    }
}
@media only screen and (min-width: 400px){
    html {
        font-size: 25px !important;
    }
}
@media only screen and (min-width: 414px){
    html {
        font-size: 25.875px !important;
    }
}
@media only screen and (min-width: 428px){
    html {
        font-size: 26.75px !important;
    }
}
@media only screen and (min-width: 480px){
    html {
        font-size: 30px !important;
    }
}
@media only screen and (min-width: 569px){
    html {
        font-size: 35px !important;
    }
}
@media only screen and (min-width: 640px){
    html {
        font-size: 40px !important;
    }
}
@media only screen and (min-width: 768px){
    html {
        font-size: 45px !important;
    }
}


img{ border: none;}

*{ margin: 0; padding: 0;}

body{ width:100%; margin:0 auto; box-sizing: border-box; font-family: "microsoft yahei";}

.shijianxuanze{ margin:0.5rem; font-size:0.6rem;}

select{ width:3.6rem; height:0.9rem; line-height:0.9rem; font-size:0.5rem; margin-bottom:0.5rem;}

#select1,#select2,#select4,#select5{ margin-right:0.4rem;}

input[type='submit']{ height: 0.9rem; width:4rem; background:#4790E0; color:#000; border:none; font-size:0.4rem;font-family: "microsoft yahei";}

input[type='checkbox']{ position:relative; top:0.2rem;}

img#no_bg{ width:15rem; margin-left:0.45rem; border:0.05rem solid #ccc;}

table.jiange{ width:15rem; margin-left:0.5rem; font-family:Microsoft Yahei;border-collapse:collapse;}
table.jiange td{ border:1px solid #c3c3c3;}

table.jiange td.td16{ width:15%;}
table.jiange td.td10{ width:10%;}

</style>

</head>

<SCRIPT   LANGUAGE="JavaScript">
function   closeErrors()   {
  return   true;
  }
  window.onerror=closeErrors;
</SCRIPT>
<script language="JavaScript" type="text/JavaScript">
function shcbgname()
{
 // document.form1.submit();
 var select3 = document.getElementById("select3").value;
 var select = document.getElementById("select").value;
 var select2 = document.getElementById("select2").value;
 var select5 = document.getElementById("select5").value;
 var select6 = document.getElementById("select6").value;
 var select7 = document.getElementById("select7").value;
 var topicture_id = document.getElementById("topicture_id").value;

 var chart_zoomLine = new FusionCharts("FusionCharts/ZoomLine.swf", "zoomLine", "600", "400", "0", "1");
 chart_zoomLine.setDataURL("showg.php?stime="+select5+"-"+select6+"-"+select7+"&etime="+select3+"-"+select+"-"+select2+"&topicture="+topicture_id+"&bgname=0");
 chart_zoomLine.render("zoomLineDiv");
}


function showxhgp( id ){
    document.getElementById( "show_xhgp_" + id ).style.visibility='visible';
}

function hiddenxhgp( id ){
    document.getElementById( "show_xhgp_" + id ).style.visibility='hidden';
}
</script>
<script language="javascript" src="_v2js/prototype.js"  type="text/javascript"></script>
<script src="_v2js/jquery-1.8.3.min.js"></script>
<script src="_v2js/jquery-migrate-1.1.0.min.js"></script>
<script>
$(document).ready(function(){
	$("#wbj").click(function(){
		if ($("#wbj").attr("checked")) {
			var s =  $("#no_bg").attr("src")+"&wubj=1";
        }else{
			var s =  $("#p_lianjie").val();
		}
		// alert(s);
		$("#no_bg").attr("src",s);

	});
});
</script>



<body topmargin="0" oncontextmenu=self.event.returnValue=0 onkeydown="javascript:if(event.keyCode==27 || event.keyCode==78 && event.ctrlKey)return false;">
	<?php echo $content; ?>
</body>
</html>
</center>
