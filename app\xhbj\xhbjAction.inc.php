<?php
//error_reporting( E_ALL );
//ini_set( "display_errors", true );
include_once(APP_DIR . "/master/masterAction.inc.php");

class xhbjAction extends masterAction
{
    // MB指数单独处理
    private $MB_PRICEID = "I98640";
    // 过磅价格
    private $guobang = [352023, 287011, 437011, 417011, '087011'];

    public function __construct()
    {
        parent::__construct();
    }

    // 手动更新redis缓存
    public function setPriceIdListCache()
    {
        $key = REDIS_SPOT_QUOTATION_PRICEID_ARRAY;
        // require_once("allkeys.php");
        // echo $this->redis->set($key, json_encode($allkeys, JSON_UNESCAPED_UNICODE));
        exit;
    }

    public function index($params)
    {
        if ($params['type'] == "old") echo "<script>window.location.href='/Market_area_xhindex.php?type=old';</script>";
        require_once("allkeys.php");
        $newsListData['navigationCityList'] = $this->getNavigationCityList(array("pageSize" => 67));
        $newsListData['navigationVarietyList'] = $this->getNavigationVarietyList(array("pageSize" => 29));
        $this->handlePublicData($params);
        $this->assign("newsListData", $newsListData);
        $this->assign("pageName", "现货报价");
        $columnNavigation = $this->getSteelHomeDataPictureList(array("pageSize" => 17, "type" => 5));
        $columnNavigation = json_decode($columnNavigation, true);
        $this->assign("columnNavigation", $columnNavigation);
        $thisTime = date("H:i:s");
        $mid = 0;
        if (!empty($_COOKIE['Authorization']))
        {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo))
            {
                $islogin = 1;
                $mid = $userInfo[1]['sysUser']['mid'];
            }
            else
            {
                $islogin = 0;
            }
        }
        else
        {
            $islogin = 0;
        }
        //处理英文的
        $translate = array();
        $isEnglish = 0;
        
        if (isset($params['language']) && $params['language'] =='english') {
            if($_COOKIE['enm'] || $mid == 1 ) {
                $islogin = 1;
            }else{
                $islogin = 0;
            }
            //获取翻译数据
            $cityTranslate = $this->getTranslateList(array("isclass"=>1));
            foreach($cityTranslate as $cityTrans ) {
                $translate['city'][$cityTrans['chinatxt']] = $cityTrans['englishtxt'];
            }
            $varietyTranslate = $this->getTranslateList(array("isclass"=>2));
            foreach($varietyTranslate as $varietyTrans ) {
                $translate['variety'][$varietyTrans['chinatxt']] = $varietyTrans['englishtxt'];
            }
            $varietyTranslate = $this->getTranslateList(array("isclass"=>4));
            foreach($varietyTranslate as $varietyTrans ) {
                $translate['note'][$varietyTrans['chinatxt']] = $varietyTrans['englishtxt'];
            }
            $isEnglish = 1;
            // echo "<pre/>";print_r($translate);
        }
        $this->assign("islogin", $islogin);
        if ($thisTime < "08:30:00" && !isset($params['date']))
        {
            $date = date('Y-m-d', strtotime('-1 day'));
        }
        else if (isset($params['date']))
        {
            $date = $params['date'];
        }
        else
        {
            $date = date('Y-m-d');
        }
        $date = $this->validateAndFixDate($date);
        // $this->assign('date', $date);
        //处理历史日期没有登录的不给看
        if($islogin == 0 ) {
            if(isset($params['date']) && $params['date'] < $this->validateAndFixDate(date('Y-m-d')) ) {
                if($isEnglish == 1 ) {
                    echo "<script>window.location.href='".APP_URL_WWW_EN."/Login_new.php?urlstr=".APP_URL_WWW."/xhbj.php?view=english&date=". $params['date']. "';</script>";
                    exit;
                }else{
                    echo "<script>window.location.href='/MemberLogin.php?urlstr=/xhbj.php?date=". $params['date']. "';</script>";
                    exit;
                }
            }
        }
        $dateList = $this->getRowHeadList($date);
        $this->assign("date", $dateList[0]);
        $tmpStart = explode("-", $dateList[0]);
        $params['startYear'] = $tmpStart[0];
        $params['startMonth'] = $tmpStart[1];
        $params['startDay'] = $tmpStart[2];
        $this->handleListSearchTimeInfo($params);
        $this->assign("urlParams", urlencode("/xhbj.php?" . $_SERVER['QUERY_STRING']));
        $redis_key = $this->getCacheKeys($allkeys);

        // 从redis中取出所有的价格
        $this_price = $this->getAllPrices($redis_key, $dateList);
        // echo "<pre/>";print_r($this_price);
        /** MB指数 start */
        $MB_PRICEID = $this->MB_PRICEID;
        $MBzs_date = $dateList[1];
        if (6 == date("w", strtotime($MBzs_date)) || 0 == date("w", strtotime($MBzs_date))) {
            //获取前一天
            $MBzs_date = date("Y-m-d", strtotime($MBzs_date."-1 days"));
            $MBzs_date = $this->getLastWorkingDay($MBzs_date);
        }
        // echo"<pre/>";print_r($MBzs_date);
        // echo date("w", strtotime($MBzs_date));
        $tkskey = REDIS_API_KEY_PER_STRING . "2.3:$MB_PRICEID=1=" . $MBzs_date . "=" . $MBzs_date;
        $url = APP_URL_INTERFACE . "/apifront/market/price?topicture=$MB_PRICEID&priceType=1&startTime=" . $MBzs_date . "&endTime=" . $MBzs_date;
        $aa = $this->http_get($url);
        $aa = json_decode($aa['data'], true);
       
        if (!empty($aa['rows']) && !empty($aa['rows'][0]))
        {
            $aa = $aa['rows'][0];
            $this_price[$MB_PRICEID] = ['price' => $aa['price'], 'oldprice' => $aa['oldprice'], 'zd' => round((float)$aa['price'] - (float)$aa['oldprice'], 2)];
        }
        // echo"<pre/>";print_r($aa);
        // echo"<pre/>";print_r($this_price);
        /** MB指数 end */
        /** 钢之家指数 start */
        $tkskey = REDIS_API_KEY_PER_STRING . "9.2:3=" . $dateList[1] . "=" . $dateList[0];
        $url = APP_URL_INTERFACE . "/shpi/steelhome/material_pzp/shpi?vid=3&dateStart=" . $dateList[1] . "&dateEnd=" . $dateList[0];
        $aa = $this->http_get($url);
        $aa = json_decode($aa['data'], true);
        //处理数据为空的数据往前取一天
               
        if ($aa['rows'][0]['dateday']== $dateList[1] )
        {
            $dateList_gzjtemp = $this->getRowHeadList($dateList[1]);
            $tkskey = REDIS_API_KEY_PER_STRING . "9.2:3=" . $dateList_gzjtemp[1] . "=" . $dateList_gzjtemp[0];
            $url = APP_URL_INTERFACE . "/shpi/steelhome/material_pzp/shpi?vid=3&dateStart=" . $dateList_gzjtemp[1] . "&dateEnd=" . $dateList_gzjtemp[0];
            $aa = $this->http_get($url);
            $aa = json_decode($aa['data'], true);
        }else{
            $dateList_gzjtemp = $dateList;
        }
        //  echo"<pre/>";print_r($dateList_gzjtemp);
        if (!empty($aa['rows']))
        {
            foreach ($aa['rows'] as $avv)
            {
                if ($avv['dateday'] == $dateList_gzjtemp[0])
                {
                    $this_price['gzjtemp']['price'] = $avv['weipriceusb'];
                }
                else if ($avv['dateday'] == $dateList_gzjtemp[1])
                {
                    $this_price['gzjtemp']['oldprice'] = $avv['weipriceusb'];
                }
            }
            if (!empty($this_price['gzjtemp']['price']) && !empty($this_price['gzjtemp']['oldprice']))
            {
                $this_price['gzjtemp']['zd'] = round((float)$this_price['gzjtemp']['price'] - (float)$this_price['gzjtemp']['oldprice'], 2);
            }
            else
            {
                $this_price['gzjtemp']['zd'] = "";
            }
        }
        /** 钢之家指数 end */
        // print_r($this_price['gzjtemp']);exit;
        // print_r($redis_key);
        // print_r($this_price);exit;
        // print_r($allkeys['changcai28']);exit;
        
        $data = [];
        foreach ($allkeys as $tablek => $tablev)
        {
            if (in_array($tablek, ['yejinjiao_yejinmei1', 'yejinjiao_yejinmei2', 'tiekuangshi', 'tiehejin', 'gongyexiancai1', 'gongyexiancai2', 'gongyexiancai3']))
            {
                list($thead, $tbody) = $this->processTableData1(${$tablek . "_url"}, $tablek, $tablev, $dateList, $this_price, "",$isEnglish, $translate,$MBzs_date,$dateList_gzjtemp);
            }
            else
            {
                list($thead, $tbody) = $this->processTableData2(${$tablek . "_url"}, $tablek, $tablev, $dateList, $this_price, $isEnglish, $translate);
            }
            $data[$tablek]['thead'] = base64_encode($thead);
            $data[$tablek]['tbody'] = base64_encode($tbody);
        }

        if (isset($params['aaa']) && $params['aaa'] == "567")
        {
            header("Content-Type:text/json");
            echo json_encode($data);
            exit;
        }
        else
        {
            foreach ($data as $datak => $dataval)
            {
                $this->assign("thead" . $datak, base64_decode($dataval["thead"]));
                $this->assign("tbody" . $datak, base64_decode($dataval["tbody"]));
            }
        }

        $channelCode = "xhbj";
        $this->assign("channelCode", $channelCode);
        $this->assign("params", $params);
    }

     /**
     * @Author: shizg
     * @Date: 2024-11-15 09:06:11
     * @parms: 
     * @return: SYMBOL_TYPE 
     * @Description: 英文的现货报价
    */
    public function english($params) {
        $params['language'] = "english";
        $this->index($params);
    }

    public function steelsection($params)
    {
        $steelsection = [
            "5#角钢" => ["太原" => "A1401001002594140900","西安" => "A6101001002607051900","郑州" => "A4101001002607140900","兰州" => "A6201001002607051900","银川" => "A6401001002607140900","西宁" => "A6301001002607720100","包头" => "A1502001002622051900","成都" => "A5101001002607140900","重庆" => "A5000001002594140900","武汉" => "A4201001002607140900","泰安" => "A37200010026070509ey","唐山"=>"A1302001002594051900"],
            "16#槽钢" => ["太原" => "A1401005002766140900","西安" => "A6101005002766140900","郑州" => "A4101005002766140900","兰州" => "A6201005002770140900","银川" => "A6401005002766140900","西宁" => "A6301005002823170400","包头" => "A1502005002801056700","成都" => "A5101005002766570200","重庆" => "A5000005002766140900","武汉" => "A4201005002766140900","泰安" => "A3720005002823053200","唐山"=>"A1302005002845051900"],
            "25#工字钢" => ["太原" => "A1401007002788140900","西安" => "A6101007002788140900","郑州" => "A4101007002781140900","兰州" => "A6201007002788160100","银川" => "A6401007002788140900","西宁" => "A6301007002788401100","包头" => "A1502007002774805400","成都" => "A5101007002788570200","重庆" => "A5000007002788140900","武汉" => "A4201007002781140900","泰安" => "A3720007002788401100","唐山"=>"A1302007002788057900"],
            "200*200H型钢" => ["太原" => "A1401010002b66142400","西安" => "A6101009002b66160100","郑州" => "A4101009002d03351100","兰州" => "A6201009002b66160100","银川" => "A6401009002b66160100","西宁" => "A6301009002b66160100","包头" => "A1502009002b66160100","成都" => "A5101009002b66160100","重庆" => "A5000010002b66401100","武汉" => "A4201009002d03402600","泰安" => "A3720009002b66401100","唐山"=>"A1302009002b66046500"],
            "400*400H型钢" => ["太原" => "A1401010002b72142400","西安" => "A6101009002b72160100","郑州" => "A4101009002d66351100","兰州" => "A6201009002b72401100","银川" => "A6401009002b72160100","西宁" => "A6301009002b72160100","包头" => "A1502009002b72160100","成都" => "A5101009002b72160100","重庆" => "A5000010002b72351100","武汉" => "A4201009002d07351100","泰安" => "A3720009002b69401100","唐山"=>"A1302009002b72046500"]
        ];

        $steelsection_url = [
            "5#角钢" => APP_URL_WWW."/gc/market.php?varietyId=014,c04",
            "16#槽钢" => APP_URL_WWW."/gc/market.php?varietyId=014,c04",
            "25#工字钢" => APP_URL_WWW."/gc/market.php?varietyId=014,c04",
            "200*200H型钢" => APP_URL_WWW."/gc/market.php?varietyId=015,c04",
            "400*400H型钢" => APP_URL_WWW."/gc/market.php?varietyId=015,c04",
            "太原" => APP_URL_WWW."/gc/market.php?cityId=0043",
            "西安" => APP_URL_WWW."/gc/market.php?cityId=0056",
            "郑州" => APP_URL_WWW."/gc/market.php?cityId=0037",
            "兰州" => APP_URL_WWW."/gc/market.php?cityId=0057",
            "银川" => APP_URL_WWW."/gc/market.php?cityId=0059",
            "西宁" => APP_URL_WWW."/gc/market.php?cityId=0058",
            "包头" => APP_URL_WWW."/gc/market.php?cityId=0041",
            "成都" => APP_URL_WWW."/gc/market.php?cityId=0053",
            "重庆" => APP_URL_WWW."/gc/market.php?cityId=0052",
            "武汉" => APP_URL_WWW."/gc/market.php?cityId=0036",
            "泰安" => APP_URL_WWW."/gc/market.php?cityId=0045",
            "唐山" => APP_URL_WWW."/gc/market.php?cityId=0067",
        ];

        require_once("allkeys.php");
        // dd($allkeys);
        $allkeys = array_filter($allkeys, fn($k) => $k=="gangtieyuanliao", ARRAY_FILTER_USE_KEY);
        $allkeys["steelsection"] = $steelsection;
        // dd($allkeys);
        $thisTime = date("H:i:s");
        if ($thisTime < "08:30:00" && !isset($params['date']))
        {
            $date = date('Y-m-d', strtotime('-1 day'));
        }
        else if (isset($params['date']))
        {
            $date = $params['date'];
        }
        else
        {
            $date = date('Y-m-d');
        }
        $date = $this->validateAndFixDate($date);
        $dateList = $this->getRowHeadList($date);
        $this->assign("date", $dateList[0]);

        $redis_key = $this->getCacheKeys($allkeys);
        // 从redis中取出所有的价格
        $this_price = $this->getAllPrices($redis_key, $dateList);
        $data = [];
        foreach ($allkeys as $tablek => $tablev)
        {
            list($thead, $tbody) = $this->processTableData2(${$tablek . "_url"}, $tablek, $tablev, $dateList, $this_price, 0, []);
            $data[$tablek]['thead'] = base64_encode($thead);
            $data[$tablek]['tbody'] = base64_encode($tbody);
        }

        if (isset($params['interval']) && $params['interval'] == "60")
        {
            header("Content-Type:text/json");
            echo json_encode($data);
            exit;
        }
        else
        {
            foreach ($data as $datak => $dataval)
            {
                $this->assign("thead" . $datak, base64_decode($dataval["thead"]));
                $this->assign("tbody" . $datak, base64_decode($dataval["tbody"]));
            }
        }
    }

    private function processTableData1($urlsList, $tablek, $tablev, $dateList, $this_price, $isavg = false, $isEnglish, $translate,$MBzs_date,$dateList_gzjtemp)
    {
        if (in_array($tablek, ['gongyexiancai1', 'gongyexiancai2', 'gongyexiancai3'])) $isavg = true;
        $theday = $dateList[0];
        $a_edate = date("Y-m-d", strtotime($theday . "-3month"));
        // $urlsList = ${$tablek."_url"};
        $thead = "<thead><tr>";
        if($isEnglish==1) {
            $tbody = "<tbody><tr><td>Place of Origin</td>";
            if ($tablek == "tiekuangshi" || $tablek == "tiehejin") $tbody = "<tbody><tr><td>Date</td>";
            else if ($tablek == "gongyexiancai1" || $tablek == "gongyexiancai2" || $tablek == "gongyexiancai3") $tbody = "<tbody><tr><td>Market</td>";
        }else{
            $tbody = "<tbody><tr><td>产地</td>";
            if ($tablek == "tiekuangshi" || $tablek == "tiehejin") $tbody = "<tbody><tr><td>日期</td>";
            else if ($tablek == "gongyexiancai1" || $tablek == "gongyexiancai2" || $tablek == "gongyexiancai3") $tbody = "<tbody><tr><td>市场</td>";
        }

        $jjj = 0;
        $colspannum = 1;
        if ($isavg) $colspannum = 2;
        // 构造表头
        foreach ($tablev as $pzkt => $pzvt)
        {
            $colspan = count($pzvt);
            if ($jjj == 0) $colspan += $colspannum;
            if($isEnglish == 1 ) {
                
                //处理换行分割
                $tmppzkArray = explode("<br>",$pzkt);
                foreach ($tmppzkArray as $tmpkey => $tmpvalue){
                    $tmppzkArray[$tmpkey] = isset( $translate['variety'][$tmpvalue]) ? $translate['variety'][$tmpvalue] : $tmpvalue;
                }
                $pzkt = implode("<br>",$tmppzkArray);
                //处理空格分割
                $tmppzkArray = explode(" ",$pzkt);
                foreach ($tmppzkArray as $tmpkey => $tmpvalue){
                    if(isset( $translate['variety'][$tmpvalue])){
                        $tmppzkArray[$tmpkey] =  $translate['variety'][$tmpvalue];
                    }elseif(isset($translate['city'][$tmpvalue])){
                        $tmppzkArray[$tmpkey] =  $translate['city'][$tmpvalue];
                    }else{
                        $tmppzkArray[$tmpkey] = isset( $translate['note'][$tmpvalue]) ? $translate['note'][$tmpvalue] : $tmpvalue;
                    }
                    // if ($tablek == "tiehejin") print_r($tmppzkArray);
                }
                $pzkt = implode(" ",$tmppzkArray);
                $thead .= "<th colspan=$colspan>" . $pzkt . "</th>";
                // 构造第二排表头
                foreach ($pzvt as $cityk => $priceidv)
                {
                    if (isset($translate['city'][$cityk])) {
                        $translateCity = $translate['city'][$cityk];
                    }elseif($cityk == "山西长治"){
                        $translateCity = "Shanxi Changzhi";
                    }else{
                        //处理换行分割
                        $tmppzkArray = explode("<br>",$cityk);
                        foreach ($tmppzkArray as $tmpkey => $tmpvalue){
                            if (isset( $translate['variety'][$tmpvalue])) {
                                $tmppzkArray[$tmpkey] =  $translate['variety'][$tmpvalue] ;
                            }else{
                                $tmppzkArray[$tmpkey] = isset( $translate['city'][$tmpvalue]) ? $translate['city'][$tmpvalue] : $tmpvalue;
                            }
                            
                        }
                        $translateCity = implode("<br>",$tmppzkArray);
                        
                    }
                    if($translateCity == "钢之家"){
                        $translateCity = "steelHome";
                    }
                    if($translateCity == "日期" || $translateCity == " 日期 "){
                        $translateCity = "Date";
                    }
                    $cityk = $translateCity;
                    $tbody .= "<td>" .$cityk . "</td>";
                }
            }else{
                if (isset($urlsList) && $urlsList[$pzkt] != "")
                {
                    $thead .= "<th colspan=$colspan><a target='_blank' href='" . $urlsList[$pzkt] . "'>" . $pzkt . "</a></th>";
                }
                else
                {
                    $thead .= "<th colspan=$colspan>" . $pzkt . "</th>";
                }
                // 构造第二排表头
                foreach ($pzvt as $cityk => $priceidv)
                {
                    if (isset($urlsList) && $urlsList[$cityk] != "")
                    {
                        $tbody .= "<td><a target='_blank' href='" . $urlsList[$cityk] . "'>" . $cityk . "</a></td>";
                    }
                    else
                    {
                        $tbody .= "<td>" . $cityk . "</td>";
                    }
                }
            }
            
            $jjj++;
        }
        $thead .= "</tr></thead>";
        if ($isavg) $tbody .= $isEnglish == 1 ? "<td>Avg</td>" : "<td>均价</td>";
        $tbody .= "</tr><tr>";
        if ($tablek == "yejinjiao_yejinmei1" || $tablek == "yejinjiao_yejinmei2")
        {
            $dateList[] = $isEnglish == 1 ? "Note" : "备注";
        }
        // 构造body
        // print_r($dateList);
        foreach ($dateList as $kkkk => $strv)
        {
            $tdstr = "";
            if ($isEnglish ==1 ) {
                if ($kkkk < 2) $tdstr = date("M.d", strtotime($strv));
                else if ($kkkk == 2) $tdstr = "Up/Down";
                else if ($kkkk == 3) $tdstr = "Note";
            }else{
                if ($kkkk < 2) $tdstr = date("j日", strtotime($strv));
                else if ($kkkk == 2) $tdstr = "涨跌";
                else if ($kkkk == 3) $tdstr = "备注";
            }

            // 第一列
            $tbody .= "<td>" . $tdstr . "</td>";
            $isall = true;
            foreach ($tablev as $pzk => $pzv)
            {
                // 中间的每一列
                $totleprice = 0;
                $totleoldprice = 0;
                $indexnum = 0;
                foreach ($pzv as $cityk => $priceidv)
                {
                    if($isEnglish == 1) {
                        if (trim($cityk) == "日期")
                        {
                            if ($kkkk < 2 && $cityk == "日期")
                            {
                                $mbdate = $this->getLastWorkingDay($strv) ;
                                if (6 == date("w", strtotime($mbdate)) || 0 == date("w", strtotime($mbdate))) {
                                    //获取前一天
                                    $mbdate = date("Y-m-d", strtotime($mbdate."-1 days"));
                                    $mbdate = $this->getLastWorkingDay($mbdate);
                                }
                                if ( $kkkk == 1) {
                                    // echo $mbdate;
                                    // print_r($dateList);
                                    $tmpdate = $this->getLastWorkingDay($dateList[0]) ;
                                    if(  6 == date("w", strtotime($tmpdate)) || 0 == date("w", strtotime($tmpdate)) ){
                                        //获取前一天
                                        // $mbdate = date("Y-m-d", strtotime($mbdate."-1 days"));
                                        $mbdate = $this->getLastWorkingDay($mbdate);

                                    }
                                }

                                $tdstr = date("M.d", strtotime($mbdate));
                            }elseif ($kkkk < 2 && $cityk == " 日期 "){
                                $tdstr = date("M.d", strtotime($dateList_gzjtemp[$kkkk]));
                            }
                            $tbody .= "<td>" . $tdstr . "</td>";
                            continue;
                        }
                        if ((float)$this_price[$priceidv]['price'] == 0)
                        {
                            $isall = false;
                        }
                        $totleprice += $this_price[$priceidv]['price'];
                        $totleoldprice += $this_price[$priceidv]['oldprice'];
                        if ($kkkk == 0)
                        {
                            if ($cityk == "钢之家")
                            {
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/english/tksshpi/shpi_tkspz.php'>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price']) . "</a></td>";
                            }
                            else
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/english/enjgraphindex.php?topicture=" . $priceidv . "'>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price']) . "</a></td>";
                        }
                        else if ($kkkk == 1)
                        {
                            if ($cityk == "钢之家")
                            {
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/tksshpi/shpi_tkspz.php'><span>" . $this_price[$priceidv]['oldprice'] . "</span></a></td>";
                            }
                            else
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/english/enjgraphindex.php?topicture=" . $priceidv . "'><span>" . $this_price[$priceidv]['oldprice'] . "</span></a></td>";
                        }
                        else if ($kkkk == 2)
                        {
                            // 涨跌行
                            $tbody .= "<td>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd']) . "</td>";
                        }
                        else if ($kkkk == 3)
                        {
                            // 备注行
                            $tbody .= isset( $translate['note'][$this_price[$priceidv]['marketremarks']]) ?  "<td><span>" .$translate['note'][$this_price[$priceidv]['marketremarks']]. "</span></td>" : "<td><span>" .$this_price[$priceidv]['marketremarks']. "</span></td>";
                        }
                    }else{
                        if (trim($cityk) == "日期")
                        {
                            if ($kkkk < 2 && $cityk == "日期")
                            {
                                // echo $strv;
                                $mbdate = $this->getLastWorkingDay($strv) ;
                                if (6 == date("w", strtotime($mbdate)) || 0 == date("w", strtotime($mbdate))) {
                                    //获取前一天
                                    $mbdate = date("Y-m-d", strtotime($mbdate."-1 days"));
                                    $mbdate = $this->getLastWorkingDay($mbdate);
                                }
                                if ( $kkkk == 1) {
                                    // echo $mbdate;
                                    // print_r($dateList);
                                    $tmpdate = $this->getLastWorkingDay($dateList[0]) ;
                                    if(  6 == date("w", strtotime($tmpdate)) || 0 == date("w", strtotime($tmpdate)) ){
                                        //获取前一天
                                        // $mbdate = date("Y-m-d", strtotime($mbdate."-1 days"));
                                        $mbdate = $this->getLastWorkingDay($mbdate);

                                    }
                                }

                                $tdstr = date("j日", strtotime($mbdate));
                                // echo $mbdate;exit;
                                // echo "<pre/>";print_r($this_price);
                                // echo "<pre/>";print_r($priceidv);
                                // echo 1111;
                            }elseif ($kkkk < 2 && $cityk == " 日期 "){
                                $tdstr = date("j日", strtotime($dateList_gzjtemp[$kkkk]));
                            }
                            $tbody .= "<td>" . $tdstr . "</td>";
                            continue;
                        }
                        if ((float)$this_price[$priceidv]['price'] == 0)
                        {
                            $isall = false;
                        }
                        $totleprice += $this_price[$priceidv]['price'];
                        $totleoldprice += $this_price[$priceidv]['oldprice'];
                        if ($kkkk == 0)
                        {
                            if ($cityk == "钢之家")
                            {
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/tksshpi/shpi_tkspz.php'>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price']) . "</a></td>";
                            }
                            else{
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/graphshow.php?topicture=" . $priceidv . "&stime=" . $a_edate . "&etime=" . $theday . "'>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price']) . "</a></td>";
                            }
                        }
                        else if ($kkkk == 1)
                        {
                            if ($cityk == "钢之家")
                            {
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/tksshpi/shpi_tkspz.php'><span>" . $this_price[$priceidv]['oldprice'] . "</span></a></td>";
                            }
                            else {

                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/graphshow.php?topicture=" . $priceidv . "&stime=" . $a_edate . "&etime=" . $theday . "'><span>" . $this_price[$priceidv]['oldprice'] . "</span></a></td>";
                            }
                        }
                        else if ($kkkk == 2)
                        {
                            // 涨跌行
                            $tbody .= "<td>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd']) . "</td>";
                        }
                        else if ($kkkk == 3)
                        {
                            // 备注行
                            $tbody .= "<td><span>" . $this_price[$priceidv]['marketremarks'] . "</span></td>";
                        }
                    }
                    
                    $indexnum++;
                }
                // 最后的一列算出均价
                if ($isavg)
                {
                    if ($indexnum == 0 || (!$isall && $kkkk != 1))
                    {
                        // 如果数据不全则不算均价
                        $tbody .= "<td></td>";
                    }
                    else
                    {
                        if ($kkkk == 0)
                        {
                            $avg1 = round($totleprice / $indexnum, 0);
                            $tbody .= "<td>" . $this->setRiseAndFallColor($avg1 - round($totleoldprice / $indexnum, 0), $avg1) . "</td>";
                        }
                        else if ($kkkk == 1)
                        {
                            $avg = round($totleoldprice / $indexnum, 0);
                            $tbody .= "<td><span style='color: #000000;'>" . $avg . "</span></td>";
                        }
                        else if ($kkkk == 2)
                        {
                            // 涨跌行
                            $tbody .= "<td>" . $this->setRiseAndFallColor(round($totleprice / $indexnum, 0) - round($totleoldprice / $indexnum, 0)) . "</td>";
                        }
                    }
                }
            }
            $tbody .= "</tr>";
        }

        $tbody .= "</tbody>";
        return [$thead, $tbody];
    }

    private function processTableData2($urlsList, $tablek, $tablev, $dateList, $this_price, $isEnglish, $translate)
    {
        $guobang = $this->guobang;
        $theday = $dateList[0];
        $a_edate = date("Y-m-d", strtotime($theday . "-3month"));
        // $urlsList = ${$tablek."_url"};
        $tbody = "<tbody>";
        $thead = "";
        // echo "<pre/>";print_r($tablev);
        foreach ($tablev as $pzk => $pzv)
        {
            $rowspan = 3;
            $class = "th_width";
            if(in_array($tablek, ['gangtieyuanliao']) && $pzk=="铁精粉") {
                $rowspan = 4;
                $class = "";
            }
            $thead = $isEnglish == 1 ? "<thead><tr><th class='$class float-col'>Products</th><th class='float-col-second'>Date</th>" : "<thead><tr><th class='$class'>品种</th><th>日期</th>";
            if ($isEnglish == 1){
                //处理换行分割
                $tmppzkArray = explode("<br>",$pzk);
                foreach ($tmppzkArray as $tmpkey => $tmpvalue){
                    $tmppzkArray[$tmpkey] = isset( $translate['variety'][$tmpvalue]) ? $translate['variety'][$tmpvalue] : $tmpvalue;
                }
                $pzk = implode("<br>",$tmppzkArray);
                //处理空格分割
                $tmppzkArray = explode(" ",$pzk);
                foreach ($tmppzkArray as $tmpkey => $tmpvalue){
                    $tmppzkArray[$tmpkey] = isset( $translate['variety'][$tmpvalue]) ? $translate['variety'][$tmpvalue] : $tmpvalue;
                }
                $pzk = implode(" ",$tmppzkArray);
                $pzk = str_replace("2.0mm卷","2.0mm roll",$pzk);
                $tbody .= "<tr><td rowspan=3 class='float-col'>" . $pzk . "</td>";
                // 构造表头
                foreach ($pzv as $cityk => $priceidv)
                {
                    $thead .= "<th>" .$translate['city'][$cityk] . "</th>";
                }
            }else{
                if (isset($urlsList) && $urlsList[$pzk] != "")
                {
                    $tbody .= "<tr><td rowspan=$rowspan><a target='_blank' href='" . $urlsList[$pzk] . "'>" . $pzk . "</a></td>";
                }
                else
                    $tbody .= "<tr><td rowspan=$rowspan>" . $pzk . "</td>";
                // 构造表头
                foreach ($pzv as $cityk => $priceidv)
                {
                    if (isset($urlsList) && $urlsList[$cityk] != "")
                    {
                        $thead .= "<th><a target='_blank' href='" . $urlsList[$cityk] . "'>" . $cityk . "</a></th>";
                    }
                    else
                    {
                        $thead .= "<th>" . $cityk . "</th>";
                    }
                }
            }
            
            // 构造body
            $isall = true;
            $isall_old = true;
            foreach ($dateList as $kkkk => $strv)
            {
                // 前三行数据
                if ($isEnglish == 1){
                    if ($kkkk < 2) $tdstr = date(" M.d", strtotime($strv));
                    else $tdstr = "Up/Down";
                }else{
                    if ($kkkk < 2) $tdstr = date("j日", strtotime($strv));
                    else $tdstr = "涨跌";
                }

                // 第二列
                $tbody .= "<td class='float-col-second'>" . $tdstr . "</td>";
                // 中间的每一列
                $totleprice = 0;
                $totleoldprice = 0;
                $indexnum = 0;
                foreach ($pzv as $cityk => $priceidv)
                {
                    $totleprice += (float)$this_price[$priceidv]['price'];
                    $totleoldprice += (float)$this_price[$priceidv]['oldprice'];
                    $underline = false;
                    if (in_array($priceidv, $guobang))
                    {
                        $underline = true;
                    }
                    // 第一行（当天价格）
                    if ($kkkk == 0)
                    {
                        if ((float)$this_price[$priceidv]['price'] == 0)
                        {
                            $isall = false;
                        }
                        if ($isEnglish == 1){
                            $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/english/enjgraphindex.php?topicture=" . $priceidv ."'>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price'], $underline) . "</a></td>";
                        }else{
                            // 20位价格ID暂时不跳转到走势图
                            // if(strlen($priceidv) == 20) {
                            //     $tbody .= "<td>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price'], $underline) . "</td>";
                            // } else {
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/graphshow.php?topicture=" . $priceidv . "&stime=" . $a_edate . "&etime=" . $theday . "'>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd'], $this_price[$priceidv]['price'], $underline) . "</a></td>";
                            // }
                        }
                        
                    }
                    // 第二行（前一日价格）
                    else if ($kkkk == 1)
                    {
                        if ((float)$this_price[$priceidv]['oldprice'] == 0)
                        {
                            $isall_old = false;
                        }
                        if ($isEnglish == 1){
                            $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/english/enjgraphindex.php?topicture=" . $priceidv . "'>" . $this->setRiseAndFallColor(0, $this_price[$priceidv]['oldprice'], $underline) . "</a></td>";
                        }else{
                            // 20位价格ID暂时不跳转到走势图
                            // if(strlen($priceidv) == 20) {
                            //     $tbody .= "<td>" . $this->setRiseAndFallColor(0, $this_price[$priceidv]['oldprice'], $underline) . "</td>";
                            // } else {
                                $tbody .= "<td><a target='_blank' href='" . APP_URL_WWW . "/graphshow.php?topicture=" . $priceidv . "&stime=" . $a_edate . "&etime=" . $theday . "'>" . $this->setRiseAndFallColor(0, $this_price[$priceidv]['oldprice'], $underline) . "</a></td>";
                            // }
                        }
                    }
                    // 第三行（涨跌）
                    else if ($kkkk == 2)
                    {
                        $tbody .= "<td><span>" . $this->setRiseAndFallColor($this_price[$priceidv]['zd']) . "</span></td>";
                    }
                    $indexnum++;
                }
                // 最后的一列算出均价
                if ($indexnum == 0 || (!$isall && $kkkk != 1) || (!$isall_old && $kkkk != 2))
                {
                    // 如果数据不全则不算均价
                    $tbody .= "<td></td>";
                }
                else
                {
                    if ($kkkk == 0)
                    {
                        $avg1 = round($totleprice / $indexnum, 0);
                        $tbody .= "<td>" . $this->setRiseAndFallColor($avg1 - round($totleoldprice / $indexnum, 0), $avg1) . "</td>";
                    }
                    else if ($kkkk == 1)
                    {
                        $avg = round($totleoldprice / $indexnum, 0);
                        $tbody .= "<td><span style='color: #000000;'>" . $avg . "</span></td>";
                    }
                    else if ($kkkk == 2)
                    {
                        $tbody .= "<td>" . $this->setRiseAndFallColor(round($totleprice / $indexnum, 0) - round($totleoldprice / $indexnum, 0)) . "</td>";
                    }
                }
                $tbody .= "</tr><tr>";
            }
            // 
            if(in_array($tablek, ['gangtieyuanliao']) && $pzk=="铁精粉") {
                $tbody .= "<td>备注</td>";
                $tbody .= "<td>66%/到厂</td>";
                $tbody .= "<td>65%/出厂</td>";
                $tbody .= "<td>66%/到厂</td>";
                $tbody .= "<td>64%/承兑/出厂</td>";
                $tbody .= "<td>65%/出厂</td>";
                $tbody .= "<td>64%/到厂</td>";
                $tbody .= "<td>64%/出厂</td>";
                $tbody .= "<td>61%/出厂</td>";
                $tbody .= "<td></td>";
                // foreach($pzv as $cityNameKey => $pidVal) {
                //     $tbody .= "<td>".$this_price[$priceidv]['marketremarks']."</td>";
                // }
            }
            $thead .= $isEnglish == 1 ? "<th>Avg</th></tr></thead>" : "<th>均价</th></tr></thead>";
            $tbody .= "</tr>";
        }
        $tbody .= "</tbody>";
        return [$thead, $tbody];
    }

    // 获取所有的价格ID以及设置并返回Redis请求的key
    private function getCacheKeys($allkeys)
    {
        $redis_key = [];
        $topictureList = [];
        $mastertopidList = [];
        $priceCode = [];
        foreach ($allkeys as $tablev)
        {
            foreach ($tablev as $pzv)
            {
                foreach ($pzv as $priceidv)
                {
                    if (strlen($priceidv) == 6) $topictureList[] = $priceidv;
                    else if (strlen($priceidv) == 7) $mastertopidList[] = $priceidv;
                    else if(strlen($priceidv) == 20) $priceCode[] = $priceidv;
                }
            }
        }
        $redis_key["6"] = implode(",", $topictureList);
        $redis_key["7"] = implode(",", $mastertopidList);
        $redis_key["20"] = implode(",", $priceCode);
        return $redis_key;
    }

    private function getAllPrices($redis_key, $dateList)
    {
        $theday = $dateList[0];
        $lastday = $dateList[1];
        $prices = [];
        $a6 = [];
        $a7 = [];
        $a20 = [];
        if($redis_key['6']) {
            $url = APP_URL_INTERFACE . "/apifront/market/price?topicture=" . $redis_key['6'] . "&priceType=1&startTime=" . $lastday . "&endTime=" . $theday;
            $a6 = $this->http_get($url);
        }
        if($redis_key['7']) {
            $url = APP_URL_INTERFACE . "/apifront/market/price?mastertopid=" . $redis_key['7'] . "&priceType=1&startTime=" . $lastday . "&endTime=" . $theday;
            $a7 = $this->http_get($url);
        }
        if($redis_key['20']) {
            $url = APP_URL_INTERFACE . "/apifront/market/price?price_code=" . $redis_key['20'] . "&priceType=1&startTime=" . $lastday . "&endTime=" . $theday;
            $a20 = $this->http_get($url);
        }
        if (!empty($a6['data']))
        {
            $a6 = json_decode($a6['data'], true)['rows'];
            foreach ($a6 as $avv)
            {
                if (date('Y-m-d', strtotime($avv['mconmanagedate'])) == $theday)
                {
                    $prices[$avv['topicture']]['price'] = $avv['price'];
                    $prices[$avv['topicture']]['marketremarks'] = $avv['marketremarks'];
                }
                else if (date('Y-m-d', strtotime($avv['mconmanagedate'])) == $lastday)
                {
                    $prices[$avv['topicture']]['oldprice'] = $avv['price'];
                    $prices[$avv['topicture']]['marketremarks'] = $avv['marketremarks'];
                }
            }
        }
        if (!empty($a7['data']))
        {
            $a7 = json_decode($a7['data'], true)['rows'];
            foreach ($a7 as $avv)
            {
                if (date('Y-m-d', strtotime($avv['mconmanagedate'])) == $theday)
                {
                    $prices[$avv['mastertopid']]['price'] = $avv['price'];
                    $prices[$avv['mastertopid']]['marketremarks'] = $avv['marketremarks'];
                }
                else if (date('Y-m-d', strtotime($avv['mconmanagedate'])) == $lastday)
                {
                    $prices[$avv['mastertopid']]['oldprice'] = $avv['price'];
                    $prices[$avv['mastertopid']]['marketremarks'] = $avv['marketremarks'];
                }
            }
        }
        if(!empty($a20['data']))
        {
            $a20 = json_decode($a20['data'], true)['rows'];
            foreach ($a20 as $v20)
            {
                if (date('Y-m-d', strtotime($v20['mconmanagedate'])) == $theday)
                {
                    $prices[$v20['price_code']]['price'] = $v20['price'];
                    $prices[$v20['price_code']]['marketremarks'] = $v20['marketremarks'];
                }
                else if (date('Y-m-d', strtotime($v20['mconmanagedate'])) == $lastday)
                {
                    $prices[$v20['price_code']]['oldprice'] = $v20['price'];
                    $prices[$v20['price_code']]['marketremarks'] = $v20['marketremarks'];
                }
            }
        }
        // dump($prices['666310']);
        foreach ($prices as $prik => &$tval)
        {
            if (str_contains($tval['price'], "-"))
            {
                $pricelisttmp = explode("-", $tval['price']);
                $prices[$prik]['price'] = round(((float)$pricelisttmp[0] + (float)$pricelisttmp[1]) / 2, 0);
            }
            if (str_contains($tval['oldprice'], "-"))
            {
                $pricelisttmp = explode("-", $tval['oldprice']);
                $prices[$prik]['oldprice'] = round(((float)$pricelisttmp[0] + (float)$pricelisttmp[1]) / 2, 0);
            }
            if ((float)$tval['price'] == 0)
            {
                $prices[$prik]['zd'] = "";
            }
            else
                $prices[$prik]['zd'] = (float)$tval['price'] - (float)$tval['oldprice'];
        }
        return $prices;
    }

    private function getRowHeadList($theday)
    {
        if (!preg_match("/^\d{4}-\d{2}-\d{2}$/", $theday))
        {
            $theday = date("Y-m-d");
        }
        $i = 0;
        while ($this->isHoliday($theday) && $i < 10)
        {
            $theday = date("Y-m-d", strtotime($theday . "-1day"));
            $i++;
        }
        $lastday = $this->getLastWorkingDay($theday);
        return [$theday, $lastday, "zd"];
    }

    private function getLastWorkingDay($theday)
    {
        $lastday = date("Y-m-d", strtotime($theday . "-1day"));
        $i = 0;
        while ($this->isHoliday($lastday) && $i < 10)
        {
            $lastday = date("Y-m-d", strtotime($lastday . "-1day"));
            $i++;
        }
        return $lastday;
    }

    // 涨跌行
    public function setRiseAndFallColor($zdprice, $tprice = -1, $downline = false)
    {
        $str = "";
        if ($tprice == -1)
        {
            if ($zdprice > 0)
            {
                $str = "<span style='color:red'>↑" . $zdprice . "</span>";
            }
            else if ($zdprice == "")
            {
                $str = "";
            }
            else if ($zdprice < 0)
            {
                $str = "<span style='color:green'>↓" . abs($zdprice) . "</span>";
            }
            else if ($zdprice == 0)
            {
                $str = "<span style='color:black'>─</span>";
            }
        }
        else
        {
            if ($downline) $class = "text-decoration: underline;";
            else $class = "";
            if ($zdprice > 0)
            {
                $str = "<span style='color:red;" . $class . "'>" . $tprice . "</span>";
            }
            else if ($zdprice < 0)
            {
                $str = "<span style='color:green;" . $class . "'>" . $tprice . "</span>";
            }
            else
            {
                $str = "<span style='" . $class . "'>" . $tprice . "</span>";
            }
        }
        return $str;
    }

    private function http_post($url, $data)
    {
        $timeout = 3;
        $arr = [
            "status" => 200,
            "msg" => "success",
        ];
        $httpdata = http_build_query($data);
        $response = file_get_contents($url, false, stream_context_create([
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
            ],
            "http" => [
                "timeout" => 3,
                "method" => "POST",
                "header" => "Content-type: application/x-www-form-urlencoded\r\n" .
                    "Content-Length: " . strlen($httpdata) . "\r\n",
                "content" => $httpdata, // 请求体数据
            ],
        ]));
        if ($response === false)
        {
            $arr = [
                "status" => 504,
                "msg" => "请求时间超过" . $timeout . "秒或者响应内容为空",
            ];
        }
        else
        {
            $arr['data'] = $response;
        }
        return $arr;
    }

    private function http_get($url)
    {
        $timeout = 3;
        $arr = [
            "status" => 200,
            "msg" => "success",
        ];
        $response = file_get_contents($url, false, stream_context_create([
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
            ],
            "http" => [
                "timeout" => $timeout,
                "method" => "GET",
            ],
        ]));
        if ($response === false)
        {
            $arr = [
                "status" => 504,
                "msg" => "请求时间超过" . $timeout . "秒或者响应内容为空",
            ];
        }
        else
        {
            $arr['data'] = $response;
        }
        return $arr;
    }

    // 广告
    public function advertisement()
    {
        $advertisement = $this->getAdvertisementList(["pageType" => 108]);
        echo json_encode($advertisement, JSON_UNESCAPED_UNICODE);
        exit;
    }


    /**
     *
     * @return void
     * <AUTHOR> 2024-08-19
     * todo 重点市场价格行情
     */
    public function cityprice($params)
    {
        require_once("allkeys.php");
        $date = isset($params['date']) && $params['date'] > 1 ? date("Y-m-d", strtotime($params['date'])) : date("Y-m-d");
        $date = $this->validateAndFixDate($date);
        $dateList = $this->getRowHeadList($date);
        $date = $dateList[0];
        $this->assign("date", $dateList[0]);
        $tmpStart = explode("-", $dateList[0]);
        $params['startYear'] = $tmpStart[0];
        $params['startMonth'] = $tmpStart[1];
        $params['startDay'] = $tmpStart[2];
        $this->handleListSearchTimeInfo($params);
        $tmpId6 = array();
        $tmpId7 = array();
        foreach ($keyCitiesInChinaPriceIdArray as $type => $priceValue)
        {
            foreach ($priceValue as $priceID)
            {
                if (6 == strlen($priceID))
                {
                    $tmpId6[] = $priceID;
                }
                else
                {
                    $tmpId7[] = $priceID;
                }
            }
        }
        $topictures = implode(",", $tmpId6);
        $mastertopids = implode(",", $tmpId7);

        $price6 = $this->getMarketPriceList(["topicture" => $topictures, "priceType" => 1, "startTime" => $date, "endTime" => $date]);
        $price7 = $this->getMarketPriceList(["mastertopid" => $mastertopids, "priceType" => 1, "startTime" => $date, "endTime" => $date]);

        foreach ($price6 as $key6 => $value)
        {
            $value['cityname'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['cityname'];
            $value['varietyname'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['varietyname'];
            $value['material'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['material'];
            $value['specification'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['specification'];
            $value['zhangdie'] = $this->setRiseAndFallColor(round($value['price'] - $value['oldprice']));
            $priceInfo[$value['topicture']] = $value;
        }
        foreach ($price7 as $key7 => $value7)
        {
            $value7['cityname'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['cityname'];
            $value7['varietyname'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['varietyname'];
            $value7['material'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['material'];
            $value7['specification'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['specification'];
            $value7['zhangdie'] = $this->setRiseAndFallColor(round($value7['price'] - $value7['oldprice']));
            $priceInfo[$value7['mastertopid']] = $value7;
        }
        //        $priceInfo = array_merge($price6info, $price7info);
        //        echo "<pre/>";print_r($priceInfo);
        foreach ($keyCitiesInChinaPriceIdArray as $type => $priceValue)
        {
            foreach ($priceValue as $priceID)
            {
                if (!isset($priceInfo[$priceID]))
                {
                    $tmp = array();
                    $tmp['cityname'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['cityname'];
                    $tmp['varietyname'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['varietyname'];
                    $tmp['material'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['material'];
                    $tmp['specification'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['specification'];
                    $tmp['price'] = "";
                    $tmp['zhangdie'] = "";
                    $priceInfo[$priceID] = $tmp;
                }
            }
        }

        if (!empty($_COOKIE['Authorization']))
        {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo))
            {
                $islogin = 1;
            }
            else
            {
                $islogin = 0;
            }
        }
        else
        {
            $islogin = 0;
        }
        $this->assign("islogin", $islogin);
        $this->assign("keyCitiesInChinaPriceIdArray", $keyCitiesInChinaPriceIdArray);
        $this->assign("priceInfo", $priceInfo);
        $this->assign("params", $params);

        $newsListData['navigationCityList'] = $this->getNavigationCityList(array("pageSize" => 67));
        $newsListData['navigationVarietyList'] = $this->getNavigationVarietyList(array("pageSize" => 29));
        $this->handlePublicData($params);
        $this->assign("newsListData", $newsListData);
        $this->assign("pageName", "重点市场价格");
        $columnNavigation = $this->getSteelHomeDataPictureList(array("pageSize" => 17, "type" => 5));
        $columnNavigation = json_decode($columnNavigation, true);
        $this->assign("columnNavigation", $columnNavigation);
        $channelCode = "xhbj";
        $this->assign("channelCode", $channelCode);
    }


    /**
     *
     * @return void
     * <AUTHOR> 2024-08-19
     * todo 重点市场周价格行情
     */
    public function cityweekprice($params)
    {
        require_once("allkeys.php");
        $date  = date("Y-m-d");
        $date_w = date("w",strtotime($date));
        if ($date_w == 5){
            $dateNum = 7;
        }else{
            $dateNum = 14;
        }

        $startdate = isset($params['startdate']) && $params['startdate'] > 1 ? date("Y-m-d", strtotime($params['startdate'])) : date("Y-m-d",strtotime($date." friday -".$dateNum." days"));
        $enddate = isset($params['enddate']) && $params['enddate'] > 1 ? date("Y-m-d", strtotime($params['enddate'])) : date("Y-m-d",strtotime($date." friday -".($dateNum-7)." days"));
        $startdate = $this->validateAndFixDate($startdate);
        $startdateList = $this->getRowHeadList($startdate);
        $date = $startdateList[0];
        $this->assign("startdate", $startdateList[0]);
        $tmpStart = explode("-", $startdateList[0]);
        $params['startYear'] = $tmpStart[0];
        $params['startMonth'] = $tmpStart[1];
        $params['startDay'] = $tmpStart[2];
        $this->handleListSearchTimeInfo($params);
        $tmpId6 = array();
        $tmpId7 = array();
        foreach ($keyCitiesInChinaPriceIdArray as $type => $priceValue)
        {
            foreach ($priceValue as $priceID)
            {
                if (6 == strlen($priceID))
                {
                    $tmpId6[] = $priceID;
                }
                else
                {
                    $tmpId7[] = $priceID;
                }
            }
        }
        $topictures = implode(",", $tmpId6);
        $mastertopids = implode(",", $tmpId7);

        $weekprice6 = $this->getMarketPriceList(["topicture" => $topictures, "priceType" => 1, "startTime" => $date, "endTime" => $date]);
        $weekprice7 = $this->getMarketPriceList(["mastertopid" => $mastertopids, "priceType" => 1, "startTime" => $date, "endTime" => $date]);

        $enddate = $this->validateAndFixDate($enddate);
        $enddateList = $this->getRowHeadList($enddate);
        $enddate = $enddateList[0];
        $this->assign("enddate", $enddateList[0]);
        $tmpStart = explode("-", $enddateList[0]);
        $params['endYear'] = $tmpStart[0];
        $params['endMonth'] = $tmpStart[1];
        $params['endDay'] = $tmpStart[2];
        
        $price6 = $this->getMarketPriceList(["topicture" => $topictures, "priceType" => 1, "startTime" => $enddate, "endTime" => $enddate]);
        $price7 = $this->getMarketPriceList(["mastertopid" => $mastertopids, "priceType" => 1, "startTime" => $enddate, "endTime" => $enddate]);
        $weekpriceArray = array();
        foreach ($weekprice6 as $weekpricevalue) {
            $weekpriceArray[$weekpricevalue['topicture']] = $weekpricevalue['price'];
        }
        foreach ($weekprice7 as $weekprice7value) {
            $weekpriceArray[$weekprice7value['mastertopid']] = $weekprice7value['price'];
        }
// print_r( $weekpriceArray);
        foreach ($price6 as $key6 => $value)
        {
            $value['cityname'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['cityname'];
            $value['varietyname'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['varietyname'];
            $value['material'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['material'];
            $value['specification'] = $keyCitiesInChinaPriceIdShowContentArray[$value['topicture']]['specification'];
            $value['zhangdie'] = $this->setRiseAndFallColor(round($value['price'] - $weekpriceArray[$value['topicture']]));
            $priceInfo[$value['topicture']] = $value;
        }
        foreach ($price7 as $key7 => $value7)
        {
            $value7['cityname'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['cityname'];
            $value7['varietyname'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['varietyname'];
            $value7['material'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['material'];
            $value7['specification'] = $keyCitiesInChinaPriceIdShowContentArray[$value7['mastertopid']]['specification'];
            $value7['zhangdie'] = $this->setRiseAndFallColor(round($value7['price'] - $weekpriceArray[$value7['mastertopid']]));
            $priceInfo[$value7['mastertopid']] = $value7;
        }
        //        $priceInfo = array_merge($price6info, $price7info);
        //        echo "<pre/>";print_r($priceInfo);
        foreach ($keyCitiesInChinaPriceIdArray as $type => $priceValue)
        {
            foreach ($priceValue as $priceID)
            {
                if (!isset($priceInfo[$priceID]))
                {
                    $tmp = array();
                    $tmp['cityname'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['cityname'];
                    $tmp['varietyname'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['varietyname'];
                    $tmp['material'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['material'];
                    $tmp['specification'] = $keyCitiesInChinaPriceIdShowContentArray[$priceID]['specification'];
                    $tmp['price'] = "";
                    $tmp['zhangdie'] = "";
                    $priceInfo[$priceID] = $tmp;
                }
            }
        }

        if (!empty($_COOKIE['Authorization']))
        {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo))
            {
                $islogin = 1;
            }
            else
            {
                $islogin = 0;
            }
        }
        else
        {
            $islogin = 0;
        }
        $this->assign("islogin", $islogin);
        $this->assign("keyCitiesInChinaPriceIdArray", $keyCitiesInChinaPriceIdArray);
        $this->assign("priceInfo", $priceInfo);
        $this->assign("params", $params);

        $newsListData['navigationCityList'] = $this->getNavigationCityList(array("pageSize" => 67));
        $newsListData['navigationVarietyList'] = $this->getNavigationVarietyList(array("pageSize" => 29));
        $this->handlePublicData($params);
        $this->assign("newsListData", $newsListData);
        $this->assign("pageName", "重点市场价格");
        $columnNavigation = $this->getSteelHomeDataPictureList(array("pageSize" => 17, "type" => 5));
        $columnNavigation = json_decode($columnNavigation, true);
        $this->assign("columnNavigation", $columnNavigation);
        $channelCode = "xhbj";
        $this->assign("channelCode", $channelCode);
    }

     /**
     * @Author: shizg
     * @Date: 2024-11-21 08:54:42
     * @parms: 
     * @return: SYMBOL_TYPE 
     * @Description: 获取英文网站的头部、尾部
    */
    public function getEnglishPageHeaderAndFooter($params)
    {
        $type = isset($params['type']) && $params['type'] == "header" ? "header" : "footer";
        $result = "";
        require_once("/etc/steelconf/env/www_env.php");
        if($type == "header") {
            $result = ENTOP;
        }else{
            $result = ENFOOT;
        }
        echo json_encode(array(base64_encode($result)));
    }

}
