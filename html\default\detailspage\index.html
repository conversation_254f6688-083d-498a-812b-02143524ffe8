<html>
<head>
    <title><{$newsInfo.title}></title>
    <meta name="verify-v1" content="rfRws4Y1mwg6EQn44sY6gX/gxhKUSQteND7cp5jrAoY=" />
    <meta name="Description" content="钢之家网站从事钢铁行业信息服务,钢材交易,网站建设,准确及时的反映各地区钢材市场的钢材价格,钢铁价格以及钢材行情信息,是一个专注于钢铁行业的专业钢铁网站." />
    <meta name="Keywords" content="钢之家,钢材价格,现货报价,钢材市场,钢材行情,钢铁价格走势,钢材价格指数,钢之家钢铁网" />
    <meta name="Copyright" content="钢之家钢铁信息 - 钢材价格 - 钢材市场 - 钢铁价格 - 行情资讯网 - 钢之家资讯" />
    <meta property="wb:webmaster" content="8c9c4eb4c25b011c" />
    <meta http-equiv="Expires" CONTENT= "0">
    <meta http-equiv="Pragma" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-store">
    <meta http-equiv="Cache-Control" CONTENT="must-revalidate">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta content="text/html; charset=utf-8" http-equiv=Content-Type>

    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/js/www/layui/css/layui.css">
    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/css/www/base_new.css?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>">
    <script>
        document.createElement('header');
        document.createElement('section');
        document.createElement('footer');
        document.createElement('aside');
        document.createElement('article');
        document.createElement('figure');
    </script>
    <style>
        header, section, footer, aside, article, figure {display: block;}
        * {color: #000000;}
        * ,.info_details *{font-size: inherit;}
        em {color: inherit !important;}
        .newscontent table {margin-left: auto;margin-right: auto;width: 99%;}
        /* table {border-spacing: 1px;border-collapse: separate;} */
        * strong {color: unset;}
        #sth_content img {display: block;margin: auto;}
        #sth_content>p>img{width: 100%;}
        .news_show_summary_bt {font-family: '黑体';font-weight: bold;}
        .news_show_summary_ct {font-family: '华文楷体';}
        #content_foot a{font-family: '华文楷体';}
        .content_right .newslist a {width: 78%;}
        .info_details {z-index: 499;
            border: 1px solid #ccc;
            padding: 0px 10px 0px 10px;
        }
        .zhuanfa a.fx_qq {
            /* background: url(https://img.gzjimg.com/www/images/steelhome.com/fenxiang.png) no-repeat 0 0; */
            background: url(https://img.gzjimg.com/www/images/steelhome.com/fenxiang_new.png) no-repeat 0 0;
        }
        .zhuanfa a.fx_wx {
            background: url(https://img.gzjimg.com/www/images/steelhome.com/fenxiang_new.png) no-repeat -32px 0;
        }
        /* .content_right * {font-size: 12px;} */
        #txt a {color: #3a7dfa;}
        #txt a span{color: #3a7dfa;}
        #txt a:hover {text-decoration: underline; /* 悬浮时添加下划线 */}
        /* div#txt{padding-top:2px;font-size: 14px;line-height:150%;color: #000000;word-break: break-word;} */
        div#txt{padding-top:2px;font-size: 16px;line-height:30px;color: #000000;word-break: break-word;}
        div#txt #sth_content div {overflow: unset;}

        /* 兼容钢之家：2月4日中西钢市简报样式 nid=4114857&ntype=n */
        div#txt #sth_content div.foot {overflow: hidden;}
        .foot .ewm p {top: 0 !important;}
        .foot .contacts{top: 0 !important;}
        /* 兼容钢之家：2月4日中西钢市简报样式 nid=4114857&ntype=n */

        /* div#txt table {border-collapse: unset;border-spacing: 1;width: 100%;} */
        div#txt td {padding: revert-layer;}
        .layui-layer-hui .layui-layer-content {color: #fff;}
        p {margin-block-start: 1em;margin-block-end: 1em;margin-inline-start: 0px;margin-inline-end: 0px;}
        .col2 {overflow: hidden;text-overflow: ellipsis;}
        .content_right h2 ul.tabs li {width: 25%;}
        #content_top_adv a {padding-top: 8px;}
        /* 大屏 */
        @media screen and (min-width:1401px){
            .col6_8.info_details {width: 860px;}
            h2 ul.tabs li {padding: 0 9px;}
            .a_font_13 {font-size: 13px !important;}
            .a_font_12 {font-size: 14px !important;}
            .area {
                padding-bottom: 0px;
                width: 1198px;
                border-bottom: 1px solid #ccc;
                margin-bottom: 8px;
            }
            <{$css}>
        }
        /* 小屏 */
        @media screen and (max-width:1400px){
            .col2 {max-height: 39px;}
            h2 ul.tabs li {padding: 0 8px;}
            .a_font_13 {font-size: 13px !important;}
            .a_font_12 {font-size: 14px !important;}
            .footer * {font-size: 12px !important;}
            div.links * {font-size: 12px !important;}
            .area {
                padding-bottom: 0px;
                width: 1038px;
                border-bottom: 1px solid #ccc;
                margin-bottom: 8px;
            }
            <{$css}>
        }

        font[style*="color: #ffffff"] span { color: #FFFFFF; }
        font[style*="color: #FFFFFF"] span { color: #FFFFFF; }

        /* 针对 IE 11 的 CSS Hack */
        @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
            font[style*="color: red"] strong { color: red; }
            font[style*="color: green"] strong { color: green; }
        }

        .position_two {
            border: 0;
        }
        ul.newslist, dl.newslist {border: 0;}
        .content_right h2 {border: 0;}

        /*市场报告栏目下载按钮*/
        .download-btn {display: inline-flex;align-items: center;background-color: #1A52A5; /* 深蓝色 */color: #ffffff !important;padding: 6px 20px;border-radius: 5px;text-decoration: none;font-size: 16px;font-weight: bold;transition: background-color 0.3s;margin: 6px 6px 6px 0; user-select: none;}
        .download-btn:hover {background-color: #003b93 !important;color: #ffffff !important;text-decoration: none !important;}
        .download-btn svg {width: 18px;height: 18px;fill: #ffffff;}
        /*市场报告栏目下载按钮*/
    </style>
    <!-- AI解读 -->
    <style>
        /* 弹出层整体样式 */
        #ai-popup {
            width: 560px;
            height: 420px;
            background-color: #fefcea; /* 添加后备背景色 */
            /* D9D9D9   */
            /* background: linear-gradient(135deg, #FFB199, #E53503); */
            background: linear-gradient(135deg, #D9D9D9, #ffebde, #ffbdab);
            background-clip: padding-box; /* 防止背景穿透边缘 */
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            opacity: 1;
            z-index: 9999;
        }
    
        /* 默认使用绝对定位 */
        #ai-popup.absolute {
          position: absolute;
        }
        /* 固定模式下使用 fixed 定位 */
        #ai-popup.fixed {
          position: fixed;
        }
        /* 弹出层头部 */
        #ai-popup .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: default;
        }
        /* 标题样式 */
        #ai-popup .header .title a{
          font-size: 20px;
          font-weight: bold;
          color: #D35400;
        }
        /* 头部按钮样式 */
        #ai-popup .header .controls button {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 20px;
          margin-left: 5px;
        }
        /* 流式输出内容区域 */
        #ai-popup .content {
            flex-grow: 1;
            overflow-y: auto;
            font-size: 16px;
            color: #333;
            background: #fff;
            border: 1px solid #eee;
            padding: 0 10px 2px 10px;
            border-radius: 4px;
            margin-top: 10px;
            width: 100%;
            box-sizing: border-box;
            height: 370px;
            overflow-y: auto;
        }

        /** 内容区域正文样式 */
        #ai-popup .content p {
            margin: 0;
            padding: 0;
            line-height: 30px;
            text-align: justify;
        }

        #ai-popup .content li {
            list-style: auto;
            margin-left: 20px;
            line-height: 30px;
            text-align: justify;
        }
    
        /* Firefox */
        #ai-popup .content {
            scrollbar-width: thin;                /* 设置滚动条宽度为细 */
            scrollbar-color: #dbdad9 #f1f1f1;       /* 滚动条滑块颜色和轨道颜色 */
        }
    
        /* Chrome, Safari, Edge, Opera */
        #ai-popup .content::-webkit-scrollbar {
            width: 8px;                           /* 滚动条宽度 */
        }
    
        #ai-popup .content::-webkit-scrollbar-track {
            background: #f1f1f1;                  /* 轨道颜色 */
            border-radius: 4px;
        }
    
        #ai-popup .content::-webkit-scrollbar-thumb {
            background: #dbdad9;                  /* 滑块颜色，与主题色匹配 */
            border-radius: 4px;
        }
    
        #ai-popup .content::-webkit-scrollbar-thumb:hover {
            background: #dbdad9;                  /* 鼠标悬停时的颜色 */
        }
    
        /* 免责声明 */
        #ai-popup .disclaimer {
          font-size: 12px;
          color: #666;
          text-align: center;
          margin-top: 8px;
        }
    
        /* 加载动画样式 */
        .spinner {
            display: block;               /* 转为块级元素 */
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top: 4px solid #ee561b;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: auto;
            margin-top: 100px;
        }
    
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <!-- IE10及以下 -->
    <!--[if IE]>
    <style>
        font[style*="color: red"] strong { color: red; }
        font[style*="color: green"] strong { color: green; }
    </style>
    <![endif]-->
    <!-- 行情页面附加样式 -->
    <{if $newsInfo.type == "m"}>
    <style>
        table {border-spacing: 1px;border-collapse: separate;}
        font { word-break: keep-all; }
        .filter_panel:hover {background: #f1f1f1;}
        .filter_span {user-select: none;font-weight: normal;font-size: 14px;margin-top:6px;margin-bottom: 2px;display: inline-block;background-color: #fffdf9;padding:1px 6px;border: 1px solid #333333;color: #666666}
        .filter_span:hover {color: #f1f1f1;color: #000000}
    </style>
    <{else}>
    <style>
        /* table, th, td {border: 1px solid #888;} */
        <{if $newsInfo.title && strpos($newsInfo.title, '行情汇总') !== false}>
            table{
                border-collapse: separate;/* !important */
                border-spacing: 1px !important;
            }
        <{else}>
            table, th, td {border: 1px solid #888;}
        <{/if}>
    </style>
    <{/if}>
    <{if $isxgw == "1"}>
    <style>
        .content {width: 100% !important;}
        .col6_8.info_details {width: calc(100% - 20px) !important;}
        #ai-button {display: none;}
    </style>
    <{/if}>
    <script src="/js/jquery-1.7.2.min.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
    <script>
        const jQuery_1_8_3 = $;
    </script>
</head>

<body>
    <{if $isxgw == "0"}>
    <{include file="../components/$channelCodeForHeader/header.html"}>
    <figure>
        <div class="content" id="A2"></div>
    </figure>
    <{/if}>
    <section>
        <div class="content minheight_eleven">
            <div class="<{if $newsInfo.moban==1}>col6_8<{/if}> info_details" <{if $newsInfo.moban==1}>style="margin: 0 2px 0 0;"<{/if}> >
                <!-- <div class="content position_two" style="margin-top: 10px;">
                    <div class="position_two_title"><span>当前位置</span></div>
                    <{if $loginInfo eq null || ($loginInfo neq null && $loginInfo.memberBo.whereFrom!=51)}>
                    <a href="/" target="_blank">钢之家&nbsp;</a>
                    <{else}>
                    <a href="javascript:void(0);" target="_blank">钢之家&nbsp;</a>
                    <{/if}>
                    <{$linknext}>
                </div> -->
                <{if $isxgw == "0"}>
                <div class="content position_two" style="margin-top: 10px;border-bottom: 0;width: 100%;">
                    <!-- <div class="position_two_title"><span>当前位置</span></div> -->
                    <span style="color: #E53503;">&nbsp;&nbsp;&nbsp;&nbsp;当前位置：&nbsp;</span>
                    <{if $loginInfo eq null || ($loginInfo neq null && $loginInfo.memberBo.whereFrom!=51)}>
                    <a href="/" target="_blank">钢之家&nbsp;</a>
                    <{else}>
                    <a href="javascript:void(0);" target="_blank">钢之家&nbsp;</a>
                    <{/if}>
                    <{$linknext}>

                    <{if $loginInfo eq null || ($loginInfo neq null && $loginInfo.memberBo.whereFrom!=51)}>
                    <{if $isShowOldPage=='1'}>
                    <div style="display: inline;float: right;"><a href="<{$newsInfo.oldUrl}><{if $params.invitationcode!=''}>?invitationcode=<{$params.invitationcode}><{/if}>" target="_blank">旧版</a></div>
                    <{/if}>
                    <{/if}>
                </div>
                <{/if}>
                <{if $isMobile=='1'}>
                <div style="display: inline;"><a href="javascript: setcookie('ShowModeC', '2', '<{$newsInfo.oldUrl}><{if $params.invitationcode!=''}>?invitationcode=<{$params.invitationcode}><{/if}>');" style="color: #3a7dfa;">【手机版】</a></div>
                <{/if}>
                <div style="width: 100%;height: auto;margin: auto;display: flex;flex-wrap: wrap;align-content: stretch;justify-content: center;align-items: baseline;" id="content_top_adv">
                    <{$advStr}>
                </div>
                <!-- 此处放广告，暂无 -->
                <div class="show_ad" id="B1">
                </div>
                <div class="show_ad" id="B2">
                </div>
                <div class="show_ad" id="B3">
                </div>
                <div class="show_title" style="margin-bottom: 10px;">
                    <h3 style="word-wrap:break-word;word-break:break-all;height: auto;line-height: unset;margin: auto;"><{$newsInfo['title']}></h3>
                    <{if $newsInfo['title2']!=""}>
                    <h4 style="word-wrap:break-word;word-break:break-all;height: auto;line-height: unset;margin: 6px auto auto auto;font-size: 16px;">--<{$newsInfo['title2']}></h4>
                    <{/if}>
                </div>
                <span style="border-bottom: 1px dotted #dddddd;width: 100%;height: 1px;display: block;" class="title_line"></span>
                <{if $loginInfo eq null || ($loginInfo neq null && $loginInfo.memberBo.whereFrom!=51)}>
                <{include file='./titleAndShare.html'}>
                <{/if}>
                <{if $is_show_invitationcode}>
                <div style='width: 100%;'>
                    <{if $mid==1}>
                    <input style='margin-top:0px;width:60px;height: 25px;font-size: 14px;padding: 1px 4px;border-radius: 3px;' type='text' value='<{$newsInfo.invitationcode}>' id='invitation_code'>&nbsp;&nbsp;
                    <input style='margin-top:0px;height: 26px;font-size: 15px;padding: 1px 8px;border-radius: 3px;' type='button' value='复制邀请码' onclick='copyCode();'>
                    <{else}>
                    <!-- <input style='margin-top:0px;width:60px;height: 25px;font-size: 14px;padding: 1px 4px;border-radius: 3px;' type='text' value='' id='invitation_code'>&nbsp;&nbsp;
                    <input style='margin-top:0px;height: 26px;font-size: 15px;padding: 1px 8px;border-radius: 3px;' type='button' value='提交邀请码' onclick='gourl("<{$newsInfo.type}>","<{$newsInfo.id}>");'> -->
                    <{/if}>
                </div>
                <script>function copyCode(){var obj = document.getElementById('invitation_code');obj.select(); document.execCommand('copy');}
                    function gourl(type, nid){
                        var obj = document.getElementById('invitation_code');
                        var yqm = obj.value;
                        if(yqm!="" && yqm.length==6) {
                            var laod1 = layer.load(1, {offset: ['calc(50% - 38px)', 'calc(50% - 76px)']});
                            $.ajax({
                                type: "post",
                                dataType: "json",
                                url: "/detailspage.php",
                                data: {"action": 'check_invitationcode', 'nid': nid, 'ntype': type, 'invitationcode': yqm},
                                success: function (res) {
                                    if (res.code == 200){
                                        location.href="/detailspage.php?nid="+nid+"&ntype="+type+"&invitationcode="+yqm;
                                    } else {
                                        layer.close(laod1);
                                        layer.msg(res.msg, {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 3000});
                                    }
                                }
                            });
                        } else {
                            layer.msg("请输入正确的邀请码", {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 2000});
                        }
                    }
                </script>
                <{/if}>
                <{$temp_tips_html}>
                <div id="txt" class="show" style="width: 100%;margin: 0 auto 20px auto;">
                    <span class="newscontent"  style="width: 100%;margin: 0 auto;display: inline-block;line-height:30px;font-size: 16px;" id='<{if $newsInfo.moban=="1"}>sth_content<{/if}>'>
                        <{if $newsInfo.type=="n" && $nhzh != ""}>
                        <div class="show_img" style="margin-bottom: 10px;margin-right: 14px;">
                            <{$nhzh}>
                        </div>
                        <{/if}>
                        <{$baogao_tips}>
                        <{$newsInfo.content}>
                    </span>
                    <{if $is_show_pay_page=='1'}><{include file='./pay.html'}><{/if}>
                </div>
                <{if $newsInfo.content == "" && $is_show_pay_page!='1' }>
                <!-- 某些未知的情况下可能该页面没有去到登录界面 -->
                <div style="width: 100%;height: 30px;display: flex;justify-content: center;align-items: center;margin-bottom: 30px;">如果正文信息出现异常，请尝试重新<a href="/MemberLogin.php?urlstr=%2Fdetailspage.php%3Fnid%3D<{$newsInfo.id}>%26ntype%3D<{$newsInfo.type}>" style="color: #3a7dfa;font-size: 18px;text-decoration: underline;">登录</a></div>
                <{/if}>
                <{if $isshowlogintips==1}>
                <div class="show" style="text-align: center;background: url(/images/divtmbg.png) repeat-x 0 0;padding-top:30px;margin-top: -20px;width: 100%;">
                    欢迎访问钢之家网站，如需查看更多内容，请先<a href="/MemberLogin.php?urlstr=<{$urlParams}>" style="color: #3a7dfa;" title="点击登录">登录</a>！
                </div>
                <{/if}>
                <{if $mid=='1'}><div style="height: 20px;width: 100%;">阅读量：<{$newsInfo.readnumber}></div><{/if}>
                <div class="line"></div>
                <div class="gray">【免责声明】<{$copyright}></div>
                <{if $loginInfo eq null || ($loginInfo neq null && $loginInfo.memberBo.whereFrom!=51)}>
                <{if $newsInfo.type=="m"}>
                <div style="line-height:25px;font-size:14px;text-align:left;">
                    尊敬的钢之家客户：
                    <div style="text-indent:28px;">钢之家网站诚挚欢迎会员客户对钢之家网站发布的价格信息进行监督。</div>
                    <div style="text-indent:28px;">我们提供电话（4008115058 或 021-50581010）、在线客服QQ和网页投诉等多种渠道接受客户监督。</div>
                    <div style="text-indent:28px;">在使用上述方式投诉或监督的同时，请会员客户将书面（加盖公章）材料发送到网站服务邮箱（<EMAIL>）或发送给网站客服人员。我们将在收到书面材料24小时之内给予反馈。如果投诉情况属实，我们将修改网站相应的价格信息，并公告处理结果。</div>
                    <div style="text-indent:28px;">感谢广大会员客户对钢之家网站的关注和关爱。</div>
                </div>
                <{/if}>
                <{if $isxgw == "0"}>
                <div class="pl" style="margin-top: 20px;">
                    <textarea class="comment_text" style="width: 100%;height:70px;padding: 6px 10px" placeholder="欢迎<{$companyShortName}>对发表的价格或资讯发表评论或投诉"></textarea>
                    <div class="comment" style="float: left;margin: 0;">
                        <div class="right" style="float: left;padding-left: 4px;">
                            <span>评分：</span>
                            <div id="rate" style="margin-top: -1px;"></div>
                        </div>
                    </div>
                    <input class="comment_bt" type="submit" value="发布">
                </div>
                <{/if}>
                <{if $isxgw == "0"}>
                <{include file="../detailspage/pingjiaList.html"}>
                <{/if}>
                <{/if}>
            </div>
            <{if $isxgw == "0"}>
            <!-- 右侧列表 -->
            <{if $newsInfo.moban==1}>
            <div class=" content_right" style="height: auto;">
                <{foreach from=$related_news key=key item=allnews}>
                <div style="border: 1px solid #ccc;">
                    <!-- <h2 class="border-top"> -->
                    <h2>
                        <ul class="tabs" id="tabs<{$key}>">
                            <{foreach from=$allnews key=key item=news}>
                            <li class="col2" style="text-align: center">
                                <a href="<{$news.title_url}>" target="_blank"><{$news.show_title}></a>
                            </li>
                            <{/foreach}>
                        </ul>
                    </h2>
                    <ul class="tab_conbox newslist" id="tab_conbox<{$key}>">
                        <{foreach from=$allnews item=news}>
                        <{foreach from=$news.list item=thisInfoList}>
                        <li class="tab_con">
                            <dl>
                                <{foreach from=$thisInfoList item=info}>
                                    <dd>
                                        <a href="/<{$info.type}><{$info.nid}>" target="_blank" class="jiezi"><{$info.ntitle}></a>
                                        <span class="gray"><{$info.ndate|date_format: " %-m月%-e日"}></span>
                                    </dd>
                                <{/foreach}>
                            </dl>
                        </li>
                        <{/foreach}>
                        <{/foreach}>
                    </ul>
                </div>
                <div style="height: 2px; background-color: #fff;"></div>
                <{/foreach}>
                <{foreach from=$right_news key=key item=allnews}>
                <div style="border: 1px solid #ccc;">
                    <!-- <h2 class="border-top"> -->
                    <h2>
                        <ul class="tabs" id="tabs<{$key}>">
                            <{foreach from=$allnews key=key item=news}>
                            <li class="col2" style="text-align: center">
                                <!-- <a href="<{$news.title_url}>" target="_blank"><{$news.show_title}></a> -->
                                <a href="<{$news.show_secondary[0].url}>" <{if $news.show_title|mb_strlen > 4}>class='a_font_12'<{/if}> target="_blank"><{$news.show_title}></a>
                            </li>
                            <{/foreach}>
                        </ul>
                    </h2>
                    <ul class="tab_conbox newslist" id="tab_conbox<{$key}>">
                        <{foreach from=$allnews item=news}>
                        <{foreach from=$news.list item=thisInfoList}>
                        <li class="tab_con">
                            <dl>
                                <{foreach from=$thisInfoList item=info}>
                                    <dd>
                                        <a href="/<{$info.type}><{$info.nid}>" target="_blank" class="jiezi"><{$info.ntitle}></a>
                                        <span class="gray"><{$info.ndate|date_format: " %-m月%-e日"}></span>
                                    </dd>
                                <{/foreach}>
                            </dl>
                        </li>
                        <{/foreach}>
                        <{/foreach}>
                    </ul>
                </div>
                <div style="height: 2px; background-color: #fff;"></div>
                <{/foreach}>

                <!-- <div class="more">
                    <a href="#"><img src="<{$smarty.const.IMAGES_BASE_DIR}>more.png"></a>
                </div> -->
            </div>
            <{else}>
            <div class="content" style="margin-top: 2px;">
                <div class="col3_2">
                    <{foreach from=$related_news key=key item=allnews}>
                    <div>
                        <h2>
                            <ul class="tabs" id="tabs<{$key}>">
                                <{foreach from=$allnews key=key item=news}>
                                <li class="col2" style="text-align: center">
                                    <a href="<{$news.title_url}>" target="_blank"><{$news.show_title}></a>
                                </li>
                                <{/foreach}>
                            </ul>
                        </h2>
                        <ul class="tab_conbox newslist" id="tab_conbox<{$key}>">
                            <{foreach from=$allnews item=news}>
                            <{foreach from=$news.list item=thisInfoList}>
                            <li class="tab_con">
                                <dl>
                                    <{foreach from=$thisInfoList item=info}>
                                        <dd>
                                            <a href="/<{$info.type}><{$info.nid}>" target="_blank" class="jiezi"><{$info.ntitle}></a>
                                            <span class="gray"><{$info.ndate|date_format: " %-m月%-e日"}></span>
                                        </dd>
                                    <{/foreach}>
                                </dl>
                            </li>
                            <{/foreach}>
                            <{/foreach}>
                        </ul>
                    </div>
                    <!-- <div style="height: 2px;"></div> -->
                    <{/foreach}>
                </div>
                <!-- <div style="width: 2px;height: 2px;float: left;"></div> -->
                <{foreach from=$right_news key=key item=allnews}>
                <{if !empty($right_news[$key])}>
                <div class="col3_2">
                    <div>
                        <h2>
                            <ul class="tabs" id="tabs<{$key}>">
                                <{foreach from=$right_news[$key] item=news}>
                                <li class="col2" style="text-align: center;">
                                    <a href="<{$news.show_secondary[0].url}>" <{if $news.show_title|mb_strlen > 4}>class='a_font_13'<{/if}> target="_blank"><{$news.show_title}></a>
                                </li>
                                <{/foreach}>
                            </ul>
                        </h2>
                        <ul class="tab_conbox newslist" id="tab_conbox<{$key}>">
                            <{foreach from=$right_news[$key] item=news}>
                            <{foreach from=$news.list item=thisInfoList}>
                            <li class="tab_con">
                                <dl>
                                    <{foreach from=$thisInfoList item=info}>
                                        <dd>
                                            <a href="/<{$info.type}><{$info.nid}>" target="_blank" class="jiezi"><{$info.ntitle}></a>
                                            <span class="gray"><{$info.ndate|date_format: " %-m月%-e日"}></span>
                                        </dd>
                                    <{/foreach}>
                                </dl>
                            </li>
                            <{/foreach}>
                            <{/foreach}>
                        </ul>
                    </div>
                    <!-- <div style="height: 2px;"></div> -->
                </div>
                <{if $key=="1" || $key=="3" || $key=="4"}>
                <!-- <div style="width: 2px;height: 2px;float: left;"></div> -->
                <{/if}>
                <{/if}>
                <{/foreach}>
            </div>
            <{/if}>
            <{/if}>
        </div>
    </section>
    <{if $isxgw == "0"}>
    <div class="kong"></div>
    <section>
        <!-- 首页下方友情链接 -->
        <{include file="../components/friendly_links.html"}>
    </section>
    <{include file="../components/footer.html"}>
    <{/if}>
    <div id="ai-popup" class="absolute" data-original-left="0px" data-original-top="400px" style="position: absolute; display: none;">
        <div class="header">
            <div class="title">
                <a href="/aisearch/index.php" target="_blank">AI 解读</a>
                <button title="刷新" style="background: none;border: none;margin-left: 6px;">
                    <b><i class="layui-icon layui-icon-refresh" style="font-size: 18px;color: #e53503;"></i></b>
                </button>
            </div>
            <div class="controls">
                <button title="固定/取消固定" style="display: none;">📍</button>
                <button title="关闭"><b><i class="layui-icon layui-icon-close" style="font-size: 22px;color: #e53503;"></i></b></button>
            </div>
        </div>
        <div id="assistant_1" class="content"></div>
        <div class="disclaimer">免责声明：解读内容由AI生成，不构成投资建议。</div>
    </div>
</body>

<script>
    const iserror = "<{$error}>";
    const ischannel = "<{$newsInfo['channelid']}>";
    const islogin = "<{$islogin}>";
    const moban = "<{$newsInfo.moban}>";
    const isxgw = "<{$isxgw}>";
    let advParams = "&pageType=110&channelId=<{$newsInfo['channelid']}>&varietyId=<{$newsInfo['varietyid']}>&cityId=<{$newsInfo['cityid']}>";
    var advPageType = 2;
    var font_n = 14;
    var font_line = {12:26,14:28,16:30,18:32,20:34,22:36};
    if(localStorage.getItem("detailsPageContentFontSize<{$userid}>")) {
        font_n = localStorage.getItem("detailsPageContentFontSize<{$userid}>");
        font_n = parseInt(font_n);
        $('.newscontent').css('font-size', font_n.toString() + 'px');
        $('.newscontent').css('line-height', font_line[font_n] + 'px');
    }
    // if(localStorage.getItem("detailsPageContentLineHeight<{$userid}>")) {
    //     var line_h = localStorage.getItem("detailsPageContentLineHeight<{$userid}>");
    //     $('.newscontent').css('line-height', line_h + 'px');
    // }
    document.getElementById('font_big').onclick = function () {
        font_n = font_n*1;
        font_n = font_n + 2;
        if (font_n >= 22){
            font_n=22;
        }
        $('.newscontent').css('font-size', font_n + 'px');
        $('.newscontent').css('line-height', font_line[font_n] + 'px');
        // localStorage.setItem("detailsPageContentLineHeight<{$userid}>", font_line[font_n]);
        localStorage.setItem("detailsPageContentFontSize<{$userid}>", font_n);
    }
    document.getElementById('font_small').onclick = function () {
        font_n = font_n*1;
        font_n = font_n - 2;
        if (font_n <= 12){
            font_n=12;
        }
        $('.newscontent').css('font-size', font_n + 'px');
        $('.newscontent').css('line-height', font_line[font_n] + 'px');
        // localStorage.setItem("detailsPageContentLineHeight<{$userid}>", font_line[font_n]);
        localStorage.setItem("detailsPageContentFontSize<{$userid}>", font_n);
    }

    function printarticle(){
        var tt = $($(".show_title")[1]).text().replace("[打印]", "");
        tt = tt.replace("A+", "");
        tt = tt.replace("A-", "");
        tt = tt.replace("+关注", "");
        tt = tt.replace("-取消关注", "");
        var title123 = $($(".show_title")[0]).text()
        $('#sth_content').prepend('<h3 style="word-wrap:break-word;word-break:break-all;height: auto;line-height: unset;margin: auto;text-align:center;margin:10px 0;">'+title123+"</h3><div style='text-align:center;margin:0px;'>"+tt+"</div>")
        document.body.innerHTML = "<div class='show'>"+ $('#txt').html()+"</div><div class='gray'>"+$(".gray").html()+"</div>";
        window.print();
    }
</script>
<script src="/js/common.min.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<script src="<{$smarty.const.STATIC_URL}>/js/www/layui/layui.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<script src="/js/search.js" type="text/javascript"></script>
<{if $filter!=""}>
<script src="/js/<{$filter}>.js?v=20250514917"></script>
<{/if}>
<{include file="../components/common_js.html"}>
<script src="/aisearch/js/marked.min.js"></script>
<script>
    /* AI解读 */
    // 全局状态对象，用于管理流式输出及相关状态
    var setThisPageOptions = {
        text: "",
        originalText: "",   // 用于保存累积的 Markdown 文本
        displayQueue: [],
        isProcessing: false,
        chat_num: 1,
        typewriterTimeout: null, // 保存打字机效果的timeout引用
        getSS: function(key) {
            return sessionStorage.getItem(key);
        },
        reformatContent: function() {
            // 如需对内容做额外处理，可在此扩展
        },
        setScroll: function() {
            var element = $('#assistant_' + setThisPageOptions.chat_num);
            if (element.length) {
                element.scrollTop(element[0].scrollHeight);
            }
        }
    };

    // 用于拖拽的全局变量
    var isDragging = false;
    var dragOffsetX = 0, dragOffsetY = 0;

    /**
     * 更新缓存：
     * 使用 compositeKey（ntype+nid）作为 key，将 content 存入 localStorage，
     * 同时维护一个数组存储所有缓存的 key（最多 10 个），超过时删除最早的。
     */
    function updateCache(compositeKey, content) {
        // 读取当前缓存 key 数组
        var keys = localStorage.getItem("aiResponseCacheKeys");
        if (keys) {
            keys = JSON.parse(keys);
        } else {
            keys = [];
        }
        // 如果该 key 已存在，则删除（后续重新添加到末尾）
        var index = keys.indexOf(compositeKey);
        if (index !== -1) {
            keys.splice(index, 1);
        }
        keys.push(compositeKey);
        // 超过 10 个时，删除最早的那个 key 和对应的缓存内容
        if (keys.length > 10) {
            var removeKey = keys.shift();
            localStorage.removeItem(removeKey);
        }
        localStorage.setItem("aiResponseCacheKeys", JSON.stringify(keys));
        localStorage.setItem(compositeKey, content);
    }

    /**
     * 打开 AI 解读弹出层，并将 nid 与 ntype 保存到 sessionStorage
     * 修改后不再动态创建 HTML，而是直接使用页面上已有的 #ai-popup，
     * 并在进入时先检查 localStorage 缓存，如果有对应内容则直接显示。
     */
    function goAi(nid, ntype) {
        if(islogin == 0){
            window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
            return; 
        }

        if("<{$iscanlook}>" !== "1") {
            layer.msg("您没有权限使用AI解读，请先购买该条信息或联系客服", {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 2000});
            return
        }

        // 保存会话数据
        sessionStorage.setItem('news_session_of_aichat', nid + '|^|' + ntype);

        // 若没有缓存，则正常显示弹出层并发起接口请求
        var popup = document.getElementById('ai-popup');
        // 重置状态：确保每次打开都恢复为默认的绝对定位状态
        popup.classList.remove('absolute');
        popup.classList.add('fixed');
        popup.style.position = 'fixed';

        // popup.classList.remove('fixed');
        // popup.classList.add('absolute');
        // popup.style.position = 'absolute';
        // 根据按钮定位（如果存在 ai-button）
        var button = document.getElementById('ai-button');
        if(popup.style.display == "none") {
            // 显示弹出层
            popup.style.display = 'block';
            if (button) {
                var rect = button.getBoundingClientRect();
                let left, top;
                if (moban == "1") {
                    left = rect.left - 170;
                    top = rect.top - 155;
                } else {
                    left = rect.left - 500;
                    top = rect.top - 155;
                }
                
                // 调整弹出层位置，确保不会超出屏幕边界
                const popupWidth = popup.offsetWidth;
                const popupHeight = popup.offsetHeight;
                // 水平边界检测
                if (left + popupWidth > window.innerWidth) {
                    left = window.innerWidth - popupWidth;
                }
                if (left < 0) {
                    left = 0;
                }
                // 垂直边界检测
                if (top + popupHeight > window.innerHeight) {
                    top = window.innerHeight - popupHeight;
                }
                if (top < 0) {
                    top = 0;
                }
                popup.style.left = left + 'px';
                popup.style.top = top + 'px';
            } else {
                popup.style.left = (window.innerWidth - 400) / 2 + 'px';
                popup.style.top = (window.innerHeight - 320) / 2 + 'px';
            }
        } else {
            // 隐藏弹出层
            popup.style.display = 'none';
            return; 
        }
        
        // 保存原始定位，用于后续恢复
        popup.dataset.originalLeft = popup.style.left;
        popup.dataset.originalTop  = popup.style.top;

        enableDragging();

        // 组合缓存 key
        var cacheKey = ntype + nid;
        // 保存到全局，供请求结束后更新缓存时使用
        window.currentCacheKey = cacheKey;
        // 检查缓存中是否存在该 key 的内容
        var cachedResponse = localStorage.getItem(cacheKey);
        var contentArea = document.getElementById('assistant_' + setThisPageOptions.chat_num);
        if (cachedResponse) {
            // 如果存在缓存，则直接显示解析后的内容（使用 marked 解析 Markdown）
            contentArea.innerHTML = marked.parse(cachedResponse);
            // 同时绑定其它按钮的事件（刷新、固定、关闭），以保证功能一致
            bindPopupEvents();
            return;
        }

        // 绑定弹出层内按钮的事件
        bindPopupEvents();

        // 发起 AI 解读请求
        doChat(3);
    }

    /**
     * 为弹出层内的按钮（刷新、固定/取消固定、关闭）绑定事件，
     * 该方法可在 goAi 中调用，确保绑定逻辑一致。
     */
    function bindPopupEvents() {
        var popup = document.getElementById('ai-popup');
        var header = popup.querySelector('.header');
        var refreshBtn = popup.querySelector('.title button[title="刷新"]');
        // var fixedBtn = popup.querySelector('.controls button[title="固定/取消固定"]');
        var closeBtn = popup.querySelector('.controls button[title="关闭"]');
        // var button = document.getElementById('ai-button');

        refreshBtn.onclick = function(e) {
            e.stopPropagation();
            // 先停止当前运行的操作
            handleStop();
            // 然后重新开始对话
            clearConversation();
            doChat(3);
        };
        
        /*var isFixed = popup.classList.contains('fixed'); // 当前是否为固定状态
        fixedBtn.onclick = function(e) {
            e.stopPropagation();
            // 获取当前弹出层在视口中的位置
            var rect = popup.getBoundingClientRect();
            
            if (!isFixed) {
                // 切换为 fixed 状态，保持当前位置
                fixedBtn.innerText = '📌';
                popup.classList.remove('absolute');
                popup.classList.add('fixed');
                popup.style.position = 'fixed';
                popup.style.left = rect.left + 'px';
                popup.style.top = rect.top + 'px';
            } else {
                // 切换为 absolute 状态，保持当前位置（fixed 转 absolute 时需加上滚动偏移）
                fixedBtn.innerText = '📍';
                popup.classList.remove('fixed');
                popup.classList.add('absolute');
                popup.style.position = 'absolute';
                popup.style.left = (rect.left + window.scrollX) + 'px';
                popup.style.top = (rect.top + window.scrollY) + 'px';
            }
            
            // 更新状态
            isFixed = !isFixed;
        };*/

        closeBtn.onclick = function(e) {
            e.stopPropagation();
            popup.style.display = 'none';
        };
    }

    /**
     * 绑定拖拽功能
     */
    function enableDragging() {
        var popup = document.getElementById('ai-popup');
        var header = popup.querySelector('.header');

        header.style.cursor = 'grab'; // 提示可以拖拽

        header.addEventListener('mousedown', function(e) {
            isDragging = true;
            dragOffsetX = e.clientX - popup.offsetLeft;
            dragOffsetY = e.clientY - popup.offsetTop;

            header.style.cursor = 'grabbing'; // 拖拽时的鼠标样式
            e.preventDefault(); // 阻止默认行为，避免拖拽时选中文本
        });

        document.addEventListener('mousemove', function(e) {
            if (isDragging) {
                var newLeft = e.clientX - dragOffsetX;
                var newTop = e.clientY - dragOffsetY;

                // 限制范围，防止拖出视口
                var maxLeft = window.innerWidth - popup.offsetWidth;
                var maxTop = window.innerHeight - popup.offsetHeight;

                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                newTop = Math.max(0, Math.min(newTop, maxTop));

                popup.style.left = newLeft + 'px';
                popup.style.top = newTop + 'px';

                popup.dataset.originalLeft = popup.style.left;
                popup.dataset.originalTop = popup.style.top;
            }
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
            header.style.cursor = 'grab';
        });
    }

    /**
     * 清空当前会话内容并重置状态
     */
    function clearConversation() {
        setThisPageOptions.text = "";
        setThisPageOptions.originalText = "";
        setThisPageOptions.displayQueue = [];
        setThisPageOptions.isProcessing = false;
        // 如果有timeout在运行，清除它
        if (setThisPageOptions.typewriterTimeout) {
            clearTimeout(setThisPageOptions.typewriterTimeout);
            setThisPageOptions.typewriterTimeout = null;
        }
        $('#assistant_' + setThisPageOptions.chat_num).html('<div class="spinner"></div>');
    }

    /**
     * 处理停止按钮点击
     */
    function handleStop() {
        // 如果有正在运行的请求，中止它
        if (window.currentController) {
            window.currentController.abort();
            window.currentController = null;
        }
        
        // 清理打字机效果
        if (setThisPageOptions.typewriterTimeout) {
            clearTimeout(setThisPageOptions.typewriterTimeout);
            setThisPageOptions.typewriterTimeout = null;
        }
        
        // 立即显示所有接收到的内容
        setThisPageOptions.isProcessing = false;
        const contentElement = $('#assistant_' + setThisPageOptions.chat_num);
        if (contentElement.length && setThisPageOptions.text) {
            contentElement.html(marked.parse(setThisPageOptions.text));
        }
        
        // 清空队列
        setThisPageOptions.displayQueue = [];
    }

    /**
     * 流式输出队列中的 Markdown 文本，模拟打字机效果
     */
    async function processQueue(flag = 0) {
        if (setThisPageOptions.displayQueue.length === 0) return;
        setThisPageOptions.isProcessing = true;
        const result = setThisPageOptions.displayQueue.shift();
        const contentElement = $('#assistant_' + setThisPageOptions.chat_num);
        if (flag === 1) { contentElement.html(""); }
        
        // 将字符串转换为字符数组，确保正确处理中文等多字节字符
        const chars = Array.from(result);
        
        // 使用字符数组进行逐字符处理
        for (let i = 0; i < chars.length; i++) {
            setThisPageOptions.originalText += chars[i];
            contentElement.html(marked.parse(setThisPageOptions.originalText));
            // 保存timeout引用，便于停止时清除
            await new Promise(resolve => {
                setThisPageOptions.typewriterTimeout = setTimeout(resolve, 20);
            });
        }
        
        setThisPageOptions.isProcessing = false;
        setThisPageOptions.setScroll();
        processQueue();
    }

    /**
     * 发送 POST 请求至 deepseek 接口，并流式读取响应数据
     * URL 中必须包含 action=chat、prompt_type=3、model=deepseek-v3 参数，
     * 请求体中传递 chattext 和 chat_num
     */
    async function Chat(promptType, chattext) {
        clearConversation();
        
        // 使用 AbortController 设置 30 秒超时
        const controller = new AbortController();
        // 保存controller以便可以在其他地方停止请求
        window.currentController = controller;
        
        const timeoutId = setTimeout(() => {
            controller.abort();  // 超时后中止请求
        }, 30000);
        
        let response;
        try {
            response = await fetch("/aisearch/index.php?action=chat&prompt_type=" + promptType + "&model=newsDetail", {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    "content": chattext,
                    "chat_num": setThisPageOptions.chat_num
                }),
                signal: controller.signal  // 将信号传给 fetch
            });
            clearTimeout(timeoutId); // 请求成功时清除超时
        } catch (error) {
            // 如果超时或其它错误，显示提示信息并停止程序后续执行
            if (error.name === 'AbortError') {
                $('#assistant_' + setThisPageOptions.chat_num).html("服务繁忙，请稍后再试！");
            } else {
                $('#assistant_' + setThisPageOptions.chat_num).html("请求失败，请稍后再试！");
            }
            return; // 停止后续处理
        }
        
        var statusCode = response.status;
        if (statusCode !== 200) {
            $('#assistant_' + setThisPageOptions.chat_num).html("服务繁忙，请稍后再试！");
            return;
        }
        if (!response.ok) {
            throw new Error('请求失败');
        }
        
        // 处理响应流
        var reader = response.body.getReader();
        var cs = 1;
        var streamDataAll = "";
        var service_error = 0;
        var thinking = 0;
        var lastT = "";
        // 新增标识，判断是否有有效输出数据
        let hasOutput = false;
        
        while (true) {
            var { done, value } = await reader.read();
            if (done) {
                setThisPageOptions.is_ok = true;
                // 响应结束后，将累积内容存入缓存
                updateCache(window.currentCacheKey, setThisPageOptions.text);
                break;
            }
            var streamData = new TextDecoder().decode(value).trim();
            if (streamData.indexOf(": keep-alive") !== -1) {
                service_error = 1;
            }
            
            streamData.split("data:").forEach(function (dataitem) {
                dataitem = dataitem.replace("\n", "").trim();
                var data = dataitem.replace("data:", "").trim();
                var newInfo = {};
                try {
                    if (data == "[DONE]" || data == "") {
                        return;
                    } else {
                        newInfo = JSON.parse(data);
                    }
                } catch (error) {
                    var tempTextErr = "";
                    try {
                        // 清理数据，尝试修复可能被分段的JSON
                        dataitem = dataitem.replace(": keep-alive", "").trim();
                        dataitem = dataitem.replace(":keep-alive", "").trim();
                        
                        // 更加谨慎地处理可能被分段的JSON数据
                        let combinedJson = lastT + dataitem;
                        try {
                            // 首先尝试直接解析组合后的JSON
                            newInfo = JSON.parse(combinedJson);
                            lastT = ""; // 成功解析后清空缓存
                        } catch (directParseError) {
                            // 如果直接解析失败，则继续尝试逐行解析
                            const items = dataitem.split("\n");
                            let combined = lastT;
                            
                            for (let item of items) {
                                let replaceStr = item.replace("data:", "").trim();
                                if (replaceStr.length === 0) continue;
                                
                                combined += replaceStr;
                                try {
                                    let newTempJsonData = JSON.parse(combined);
                                    lastT = ""; // 成功解析后清空缓存
                                    tempTextErr += newTempJsonData.result || "";
                                    newInfo = newTempJsonData;
                                    break; // 成功解析就退出循环
                                } catch (parseErr) {
                                    // 解析失败，继续累加
                                }
                            }
                            
                            // 如果所有尝试都失败，则保存当前组合等待下一次补充
                            if (Object.keys(newInfo).length === 0) {
                                lastT = combined;
                                return; // 直接返回，等待更多数据
                            }
                        }
                    } catch (error) {
                        // 如果出现其他错误，将当前片段累加到lastT等待下一次处理
                        lastT += dataitem;
                        return;
                    }
                }
                if (newInfo.error_code) {
                    console.error('错误:', newInfo.error_msg);
                    $('#assistant_' + setThisPageOptions.chat_num).html(`出现错误: ${newInfo.error_msg}`);
                    return;
                }
                // 收到有效数据，标记为已输出
                hasOutput = true;
                
                var temp_content = "";
                if (newInfo.choices && newInfo.choices[0].delta && newInfo.choices[0].delta.reasoning_content != null) {
                    if (cs === 1) {
                        temp_content = "推理过程：\n" + newInfo.choices[0].delta.reasoning_content;
                    } else {
                        temp_content = newInfo.choices[0].delta.reasoning_content;
                    }
                    thinking = 1;
                } else {
                    if (thinking == 1) {
                        temp_content = "\n推理结果：\n\n" + (newInfo.choices[0].delta.content || "");
                    } else {
                        temp_content = newInfo.choices[0].delta.content || "";
                    }
                    thinking = 0;
                }
                setThisPageOptions.displayQueue.push(temp_content);
                setThisPageOptions.text += temp_content;
                if (!setThisPageOptions.isProcessing) {
                    processQueue(cs);
                }
                cs++;
            });
        }
        
        if (service_error == 1) {
            $('#assistant_' + setThisPageOptions.chat_num).html("服务繁忙，请稍后再试！");
            return;
        }
        // 如果响应结束后没有任何有效输出，则提示服务错误
        if (!hasOutput) {
            $('#assistant_' + setThisPageOptions.chat_num).html("服务错误，请稍后再试！");
            return;
        }
    }

    /**
     * 对话入口函数，固定 promptType 为 3，如果 chattext 为空则从 sessionStorage 获取会话数据
     */
    async function doChat(promptType, chattext) {
        var news_session_of_aichat = chattext || setThisPageOptions.getSS('news_session_of_aichat');
        await Chat(promptType, news_session_of_aichat);
    }
</script>
<{if empty($loginInfo) && !$iscanlook}>
<script>
    setTimeout(function(){
        window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
    }, 500);
</script>
<{/if}>
<script>
    function setcookie(cname, cvalue, url){
        window.location.href = url;
        document.cookie = cname + "=" + cvalue + ";path=/;domain=.steelhome.com"
    }

    function downPdf(j, p) {
        if(j == "1") {
            layer.alert('请先购买后再下载！', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)']});
        } else {
            const link = document.createElement('a');
            link.href = p;
            link.download = p.split('/').pop();
            link.target = '_blank';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // 监听打印窗口是否关闭
    window.onafterprint = function () {
        // $("body").hide();
        layui.layer.load(1, {offset: ['calc(50% - 38px)', 'calc(50% - 76px)'], shade: [0.2,'#eee']});
        location.reload();
    };
    let score = 0;
    layui.use(['layer'],function(){
        var rate = layui.rate;
        var layer = layui.layer;

        // 渲染
        rate.render({
            elem: '#rate',
            choose: function(value){
                if(islogin==='0') {
                    // show('loginHtml');
                    window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
                } else {
                    score = value;
                }
            }
        });

        // 评论
        $(".comment_bt").click(function() {
            if(islogin==='0') {
                // show('loginHtml');
                window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
                return false;
            }
            var text = $(".comment_text").val().trim();
            if(score === 0) {
                layer.msg('请先评分哦', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 1000});
                return false;
            }
            if(iserror === '1') {
                layer.msg('内容不存在不可以评论哦', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 1000});
                return false;
            }
            if(text=="" || text==null || text==undefined || text.length==0 ) {
                layer.msg('请先填写评论内容哦', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 1000});
                return false;
            }
            var tlaod1 = layer.load(1, {offset: ['calc(50% - 38px)', 'calc(50% - 76px)']});
            $.ajax({
                url: '/detailspage.php',
                type: 'post',
                data: {
                    'ntype': '<{$params.ntype}>',
                    'nid': '<{$params.nid}>',
                    'content': text,
                    'adminid': '<{$newsInfo.adminid}>',
                    'score': score,
                    'action': 'comment'
                },
                dataType: 'json',
                success: function (res) {
                    layer.close(tlaod1);
                    if(res.code == '0') {
                        layer.msg('评论成功,待审核', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 2000}, function(){
                            location.reload();
                        });
                    } else if(res.code == '2') {
                        layer.msg('请重新登录', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 2000}, function(){
                            // show('loginHtml');
                            window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
                        });
                    } else if(res.code == '1002') {
                        layer.msg('评论内容过长，请缩短评论内容哦', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 2000});
                    } else {
                        layer.msg('评论失败！请稍后重试~', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 2000}, function(){
                            location.reload();
                        });
                    }
                },
                error: function (res) {
                    layer.close(tlaod1);
                    layer.msg('评论出错啦！请稍后重试', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 1000});
                }
            });
        })

        // 关注
        $("#follow_a").click(function() {
            if(islogin==='0') {
                // show('loginHtml');
                window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
                return false;
            }
            if(iserror === '1') {
                layer.msg('内容不存在无法关注哦', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],anim: 6,time: 1000});
                return false;
            }
            var tlaod1 = layer.load(1, {offset: ['calc(50% - 38px)', 'calc(50% - 76px)']});
            var status = "-";
            if($(this).hasClass('add_follow')) {
                status = "+";
            }
            // return false;
            $.ajax({
                url: '/detailspage.php',
                type: 'post',
                data: {
                    'ntype': '<{$params.ntype}>',
                    'nid': '<{$params.nid}>',
                    'channelid': ischannel,
                    'do': status,
                    'action': 'follow'
                },
                dataType: 'json',
                success: function (res) {
                    layer.close(tlaod1);
                    if(res.code === '0') {
                        layer.msg(res.msg, {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 1000}, function() {
                            // location.reload();
                            if(status === "+") {
                                $("#follow_a").removeClass('add_follow');
                                $("#follow_a").addClass('del_follow');
                                $("#follow_a").html('取消关注');
                            } else {
                                $("#follow_a").removeClass('del_follow');
                                $("#follow_a").addClass('add_follow');
                                $("#follow_a").html('+关注');
                            }
                        });
                    } else if(res.code === '2') {
                        layer.msg('请重新登录', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 2000}, function(){
                            // show('loginHtml');
                            window.location.href = "/MemberLogin.php?urlstr=<{$urlParams}>";
                        });
                    } else {
                        // layer.msg('关注失败', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 1000}, function() {
                        //     location.reload();
                        // });
                    }
                },
                error: function (res) {
                    layer.close(tlaod1);
                    layer.msg('关注出错啦！请稍后重试', {offset: ['calc(50% - 46px)', 'calc(50% - 118px)'],time: 1000});
                }
            });
        })

    });

    var iscopy  = "<{$iscopy}>";
    var copy_status = false;
    if(iscopy == '0'){
        document.body.oncopy = function my_copy(event){
            var time = 0;
            var num = 0;
            var numarray = new Array();
            var ua = window.navigator.userAgent;
            var is_IE = ua.match(/(rv:|msie )\d+/i);
            var IE_Version = is_IE ? parseInt(is_IE[0].split(/:| /g)[1]) : 9;
            if(!event){
            event = window.event;
            }
            if(IE_Version <= 8){
                //window.clipboardData.setData("text","dddd");
                copyToClipboard_ie8(event);
                //return false;
            } 
            if( copy_status==false){
                if (event.stopPropagation){
                    event.stopPropagation();  
                }
                else{
                    event.cancelBubble=true;  
                }
        
                if (event.preventDefault){ 
                    event.preventDefault();
                }
                else{
                    event.returnValue=false;  
                }         
                event.preventDefault();
                var con = window.confirm("是否复制当前内容？");
                if(con){
                    copy_status = true;
                    //copyTextToClipboard();
                    copyToClipboard_ie8(event);
                    copy_status = false;
                }
            
            }else {
                copy_status = false;
            }
    
            function getParentNode(htmlText){
                var nod = document.body;
            }
        
            function createElement(name){
                var ele = document.createElement(name);
            
                ele.style.position = 'fixed';
                ele.style.top = 0;
                ele.style.left = 0;
            
                ele.style.width = '100%';
                ele.style.height = '1px';

                if(name=="table"){
                ele.setAttribute("cellspacing", "1");
                ele.setAttribute("border", "1");
                }
            
                ele.style.padding = 0;
            
                ele.style.border = 'none';
                ele.style.outline = 'none';
                ele.style.boxShadow = 'none';
                ele.style.overflow="hidden";
            
                ele.style.background = 'transparent';

                return ele;
            }


            function copyToClipboard_ie8(event){
                var e = event;
                if (e.clipboardData){ 
                    //alert("chrome clipboard");
                    var txt=window.getSelection()+"";
                    txt = shuffle2(txt.split("")).join("");
                    e.clipboardData.setData('text', txt);
                }else if (window.clipboardData){
                    //alert("IE clipboard");
                    var txt= window.getSelection?window.getSelection():document.selection.createRange().text;
                    var txt= txt+"";
                    txt = shuffle2(txt.split("")).join("");
                    window.clipboardData.setData('text', txt);
                }
            }
            
            
            
            function copyTextToClipboard(){
                var selection = window.getSelection();
                var range = selection.getRangeAt(0);
                var startNode = range.startContainer;
                var pnode =startNode.parentNode;
                var elename = "div";

                if( startNode.nodeName=="TD" || (startNode.nodeType==3 && pnode.nodeName=="TD") || (startNode.nodeType==3 && pnode.parentNode.nodeName=="TD") || startNode.nodeName=="TR" || (startNode.nodeType==3 && pnode.nodeName=="TR")){
                    elename="table";
                }
                var tmp_element = createElement(elename);
                tmp_element.appendChild(range.cloneContents());
                count(tmp_element);
                //alert(num);
                //for(i=0;i<6;i++){
                for(i=0;i<1;i++){
                    numarray[i] = Math.ceil(Math.random()*num);
                }
                shuffle(tmp_element);

                if(pnode)
                pnode.appendChild(tmp_element);
                else
                document.body.appendChild(tmp_element);
            

                var range = document.createRange();
                range.selectNodeContents(tmp_element);
                selection.removeAllRanges();
                selection.addRange(range);
                selection += " 信息来源：" + window.location.href + " ";
            
                try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'successful' : 'unsuccessful';
                console.log('Copying text command was ' + msg);
                } catch (err) {
                console.log('Oops, unable to copy');
                }
            
                if(pnode)
                pnode.removeChild(tmp_element);
                else {
                document.body.removeChild(tmp_element);
                }
            }

            function randomString(){
                var chars = 'ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                var maxPos = chars.length;
                var pwd = '';
                for (var i = 0; i < 6; i++){
                pwd += chars.charAt(Math.floor(Math.random() * maxPos));
                }
                return pwd;
            }

            function shuffle2(array){
                var strnum = 10;
                //var time = 2;
                var time = 1;
                var str = new Array(strnum);
                for(var i=0;i<strnum;i++){
                    str[i] = " 信息来源：" + window.location.href + " ";
                }
                for(var i=0;i<time;i++){
                    strins = str[Math.floor(Math.random() * str.length)];
                    index = Math.floor(Math.random() * array.length);
                    array.splice(0,0,strins);
                    array.splice(index,0,strins);
                    array.splice(array.length,0,strins);
                }
                return array;
            }

            function count(node){
                nodeName = node.nodeName;
                if(nodeName=="IMG"){
                return;
                }

                if(node.hasChildNodes()){
                var children = node.childNodes;
                var nodeName="";
                for(var i=0; i < children.length; i++){
                    count(children[i]);
                }
                }else if(node.nodeType==3){
                if(node.textContent && node.textContent.trim()){
                    num++;
                    return;
                }else if(node.nodeValue && node.nodeValue.trim()){
                    num++;
                    return;
                }
                }
            }

            function isInArray(arr,value){
                for(var i = 0; i < arr.length; i++){
                    if(value == arr[i]){
                        //alert(value);
                        return true;
                    }
                }
                return false;
            }

            function shuffle(node){
                nodeName = node.nodeName;
                console.log(node);

                if(nodeName=="IMG"){
                return;
                }

                //totalnum++;
                if(num == 1){
                    if(node.textContent && node.textContent.trim()){
                        var str = " 信息来源：" + window.location.href + " ";
                        node.textContent += str;
                    }else if(node.nodeValue && node.nodeValue.trim()){
                        var str = " 信息来源：" + window.location.href + " ";
                            node.nodeValue += str;
                    }
                    return;
                }

                if(node.hasChildNodes()){
                var children = node.childNodes;
                var nodeName="";
                for(var i=0; i < children.length; i++){
                    shuffle(children[i]);
                }
                }else if(node.nodeType==3){
                    if(node.textContent && node.textContent.trim()){
                    if(isInArray(numarray,time)){
                        //var str = randomString()+" 信息来源：<{$smarty.const.STEELHOME_SITE_HTTPS}> "+randomString();
                        var str = " 信息来源：" + window.location.href + " ";
                        node.textContent += str;
                    }
                    time++;
                    return;
                    }else if(node.nodeValue && node.nodeValue.trim()){
                    if(isInArray(numarray,time)){
                        //var str = randomString()+" 信息来源：<{$smarty.const.STEELHOME_SITE_HTTPS}> "+randomString();
                        var str = " 信息来源：" + window.location.href + " ";
                        node.nodeValue += str;
                    }
                    time++;
                    return;
                    }
                }
            }
        }
    }
</script>
<html>