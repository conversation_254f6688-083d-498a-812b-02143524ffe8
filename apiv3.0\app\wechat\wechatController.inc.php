<?php
/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2024-03-13 15:47:33
 * @LastEditTime: 2024-09-26 09:54:51
 * @FilePath: \oa.steelhome.cnk:\www\www.steelhome.cn\apiv3.0\app\wechat\wechatController.inc.php
 * @Description: 
 * @Copyright: © 2021, SteelHome. All rights reserved.
 */
// error_reporting( E_ALL & ~E_NOTICE );

include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");



class wechatController extends AbstractController
{

    public function __construct()
    {
        parent::__construct();
        $this->_action->setDao(new wechatDao('91W', '91R'));
        $this->_action->wxdao = new wechatDao('WX', 'WXR');
    }


    public function do_getjscode2session()
    {
        $this->_action->getjscode2session($this->_request);
    }
    //1号接口
    public function do_Login()
    {
        $this->_action->Login($this->_request);
    }
    public function do_LoginOut()
    {
        $this->_action->LoginOut($this->_request);
    }
    public function do_detail_data()
    {
        $this->_action->detail_data($this->_request);
    }
    public function do_template_element_data()
    {
        $this->_action->template_element_data($this->_request);
    }
    public function do_attendDetail()
    {
        $this->_action->attendDetail($this->_request);
    }
    public function do_wechatruchang()
    {
        $this->_action->wechatruchang($this->_request);
    }
    public function do_createShare()
    {
        $this->_action->createShare($this->_request);
    }
    public function do_recordMinoProgramLastTime()
    {
        $this->_action->recordMinoProgramLastTime($this->_request);
    }
    public function do_fetchMessageCode()
    {
        $this->_action->fetchMessageCode($this->_request);
    }
    public function do_bindPhoneNum()
    {
        $this->_action->bindPhoneNum($this->_request);
    }

    public function do_bigFile()
    {
        $this->_action->bigFile($this->_request);
    }

    //图片上传接口
    public function do_uploadPic()
    {
        $this->_action->uploadPic($this->_request);
    }

    //文件上传测试页面
    public function v_upload()
    {
        $this->_action->upload($this->_request);
    }

    public function do_getURLScheme()
    {
        $this->_action->getURLScheme($this->_request);
    }
    public function do_getAwardInfoByMtidAndMobile()
    {
        $this->_action->getAwardInfoByMtidAndMobile($this->_request);
    }

    public function do_bindMobileByUserid()
    {
        $this->_action->bindMobileByUserid($this->_request);
    }

    public function do_getTravellingInfoByContactmanId()
    {
        $this->_action->getTravellingInfoByContactmanId($this->_request);
    }

    public function do_saveTravellingInfo()
    {
        $this->_action->saveTravellingInfo($this->_request);
    }

    public function do_changePost()
    {
        $this->_action->changePost($this->_request);
    }

    public function do_getAdminInfoByMobile()
    {
        $this->_action->getAdminInfoByMobile($this->_request);
    }

    public function do_getCompanyList()
    {
        $this->_action->getCompanyList($this->_request);
    }

    public function do_saveGuest()
    {
        $this->_action->saveGuest($this->_request);
    }

    // 验证会议嘉宾
    public function do_validateMeetingGuest()
    {
        $this->_action->validateMeetingGuest($this->_request);
    }

    // 更新头像和昵称
    public function do_updateMissingAvatarAndNickname()
    {
        $this->_action->updateMissingAvatarAndNickname($this->_request);
    }
    
    public function do_getTravellingVehicleByContactmanId()
    {
        $this->_action->getTravellingVehicleByContactmanId($this->_request);
    }

    public function do_getOfficialAccountFollowers() {
        $this->_action->getOfficialAccountFollowers($this->_request);
    }

    public function do_getFollowersDetailInfo() {
        $this->_action->getFollowersDetailInfo($this->_request);
    }

    public function do_sendTemplateMessage() {
        $this->_action->sendTemplateMessage($this->_request);
    }

    public function do_sendSubscribeMessage() {
        $this->_action->sendSubscribeMessage($this->_request);
    }

    public function do_sendCronMessage() {
        $this->_action->sendCronMessage($this->_request);
    }
}