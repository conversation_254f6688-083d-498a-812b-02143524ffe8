<?php

//https://www.steelhome.com/?PayerID=8BQDC9PHAUZRW&st=Completed&tx=53130411DT7014057&cc=USD&amt=0.01&cm=paypal&payer_email=sb-5n6p544968269%40personal.example.com&payer_id=8BQDC9PHAUZRW&payer_status=VERIFIED&first_name=John&last_name=Doe&txn_id=53130411DT7014057&mc_currency=USD&mc_fee=0.01&mc_gross=0.01&protection_eligibility=ELIGIBLE&payment_fee=0.01&payment_gross=0.01&payment_status=Completed&payment_type=instant&handling_amount=0.00&shipping=0.00&item_name=%E8%B4%AD%E4%B9%B0%E6%95%B0%E6%8D%AE&item_number=20180828080706000033&quantity=1&txn_type=web_accept&payment_date=2025-09-08T08%3A37%3A29Z&receiver_id=Q8CFQ5NUYY5YG&notify_version=UNVERSIONED&custom=paypal&invoice=0202509081637270001&verify_sign=AmH177qxMLAtqir0oGPBXnwl6iL3AxFI7PtY6JrgmC7bpLd4zwsiV1dJ

// PayPal IPN监听器
//$raw_post_data = file_get_contents('php://input');
//$raw_post_data ="PayerID=8BQDC9PHAUZRW&st=Completed&tx=53130411DT7014057&cc=USD&amt=0.01&cm=paypal&payer_email=sb-5n6p544968269%40personal.example.com&payer_id=8BQDC9PHAUZRW&payer_status=VERIFIED&first_name=John&last_name=Doe&txn_id=53130411DT7014057&mc_currency=USD&mc_fee=0.01&mc_gross=0.01&protection_eligibility=ELIGIBLE&payment_fee=0.01&payment_gross=0.01&payment_status=Completed&payment_type=instant&handling_amount=0.00&shipping=0.00&item_name=%E8%B4%AD%E4%B9%B0%E6%95%B0%E6%8D%AE&item_number=20180828080706000033&quantity=1&txn_type=web_accept&payment_date=2025-09-08T08%3A37%3A29Z&receiver_id=Q8CFQ5NUYY5YG&notify_version=UNVERSIONED&custom=paypal&invoice=0202509081637270001&verify_sign=AmH177qxMLAtqir0oGPBXnwl6iL3AxFI7PtY6JrgmC7bpLd4zwsiV1dJ";

file_put_contents('ipn.log', date('[Y-m-d H:i:s]').print_r($_GET,true), FILE_APPEND);
//$raw_post_array = explode('&', $raw_post_data);
$raw_post_array=$_GET;
$myPost = array();
$myPost[]='cmd=_notify-validate';
foreach ($raw_post_array as $key=>$val) {
    // $keyval = explode('=', $keyval);
    // if (count($keyval) == 2)
    //     $myPost[$keyval[0]] = urldecode($keyval[1]);
    //echo $key."->".$val."<br>";
    $myPost[] = $key."=".$val;
}

// 读取IPN消息并验证
$req = 'cmd=_notify-validate';
// foreach ($_GET as $key => $value) {
//     //$value = urlencode($value);
//     $req .= "&$key=$value";
// }
$req=implode('&', $myPost);
echo $req;
// 向PayPal验证IPN
$ch = curl_init('https://ipnpb.sandbox.paypal.com/cgi-bin/webscr');
curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $req);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
$res = curl_exec($ch);
curl_close($ch);

// 处理验证结果
if (strcmp($res, "VERIFIED") == 0) {
    // IPN验证成功，处理业务逻辑
    $payment_status = $_POST['payment_status'];
    $txn_id = $_POST['txn_id'];
    $receiver_email = $_POST['receiver_email'];
    $payment_amount = $_POST['mc_gross'];
    echo "IPN验证成功，处理业务逻辑";
    // 在此处添加您的业务处理代码
    ///file_put_contents('ipn.log', date('[Y-m-d H:i:s]')." Verified IPN: $txn_id\n", FILE_APPEND);
    
} else if (strcmp($res, "INVALID") == 0) {
    // IPN验证失败
    echo "IPN验证失败";
    //file_put_contents('ipn.log', date('[Y-m-d H:i:s]')." Invalid IPN\n", FILE_APPEND);
}
?>
