<?php
// echo 1111;
// error_reporting(E_ALL);
// ini_set("display_errors",1);   
$tmpArray = [
["001","001","594","3511"],
["001","001","607","3511"],
["001","002","594","3511"],
["001","002","607","3511"],
["001","001","629","3511"],
["001","001","688","3511"],
["001","002","629","3511"],
["001","002","688","3511"],
["001","001","657","3511"],
["001","001","699","3511"],
["001","002","657","3511"],
["001","002","699","3511"],
["001","001",'',"5001"],
["001","002",'',"5001"],
["005","001",'',"5001"],
["005","002",'',"5001"],
["007","001",'',"5001"],
["007","002",'',"5001"],
["030","066",'',"3513"],
["032","066",'',"3513"],
["030","066",'',"3910"],
["032","066",'',"3910"],
["030","066",'',"2274"],
["032","066",'',"2274"],
["030","066",'',"3511"],
["032","066",'',"3511"],
["030","066",'',"4012"],
["032","066",'',"4012"],
["030","066",'',"4052"],
["032","066",'',"4052"],

["116",'','',"4011"],
["120",'','',"4011"],
["066",'','',"4011"],
["073",'','',"4011"],
["257",'','',"2255"],
["255",'','',"2255"],
["257",'','',"2297"],
["255",'','',"2297"],
["032",'','',"4026"],
["030",'','',"4056"],
["031",'','',"4056"],
["032",'','',"4056"],
["030",'','',"2290"],
["031",'','',"2290"],
["032",'','',"2290"],
["030",'','',"2237"],
["031",'','',"2237"],
["032",'','',"2237"],
["030",'','',"2286"],
["031",'','',"2286"],
["032",'','',"2286"],
["030",'','',"3506"],
["031",'','',"3506"],
["032",'','',"3506"],
["030",'','',"3515"],
["031",'','',"3515"],
["032",'','',"3515"],
["001",'','',"5202"],
["005",'','',"5202"],
["007",'','',"5202"],
["061",'','',"5202"],
["119",'','',"5202"],
["107",'','',"0487"],
["110",'','',"0487"],
["107",'','',"0532"],
["110",'','',"0532"],
["255",'','',"5428"],
["257",'','',"5428"],
["001",'','',"1912"],
["005",'','',"1912"],
["007",'','',"1912"],
["001",'','',"5711"],
["005",'','',"5711"],
["007",'','',"5711"],
["001",'','',"1601"],
["005",'','',"1601"],
["007",'','',"1601"],
["116",'','',"0495"],
["117",'','',"0495"],
["118",'','',"0495"],
["119",'','',"0495"],
["120",'','',"0495"],
["116",'','',"5203"],
["117",'','',"5203"],
["118",'','',"5203"],
["119",'','',"5203"],
["120",'','',"5203"],
["030",'','',"2208"]
];

foreach ($tmpArray as $key => $value) {
    echo "<br>";
    echo "INSERT INTO `steelhome`.`market_price_check_Illegal_data`  set `manufactor_code`='".$value[3]."', `variety_code`='".$value[0]."',`material_code`='".$value[1]."',`specification_code`='".$value[2]."', `status`=1, `update_time`='2025-08-29 16:43:01', `update_user`='417037';";
}