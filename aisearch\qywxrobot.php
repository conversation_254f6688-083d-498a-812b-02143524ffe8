<?php
/**
 * 企业微信机器人发送群消息通知
 */

class WeChatWorkRobot {
    private $webhookUrl;
    
    public function __construct($webhookUrl) {
        $this->webhookUrl = $webhookUrl;
    }
    
    /**
     * 发送文本消息
     * @param string $content 消息内容
     * @param array $mentionedList 需要@的用户列表，如["@all"]表示@所有人
     * @param array $mentionedMobileList 需要@的手机号列表
     * @return array
     */
    public function sendText($content, $mentionedList = [], $mentionedMobileList = []) {
        $data = [
            'msgtype' => 'text',
            'text' => [
                'content' => $content,
                'mentioned_list' => $mentionedList,
                'mentioned_mobile_list' => $mentionedMobileList
            ]
        ];
        
        return $this->sendRequest($data);
    }
    
    /**
     * 发送Markdown消息
     * @param string $content Markdown内容
     * @param string $version markdown、markdown_v2
     * @param array $mentionedList 需要@的用户列表，如["@all"]表示@所有人
     * @return array
     */
    public function sendMarkdown($content, $version="markdown", $mentionedList = []) {
        // 在Markdown内容末尾添加@提醒
        if (!empty($mentionedList)) {
            $mentionText = "\n";
            foreach ($mentionedList as $mention) {
                $mentionText .= "<@{$mention}> ";
            }
            $content .= $mentionText;
        }
        
        $data = [
            'msgtype' => $version,
            $version => [
                'content' => $content
            ]
        ];
        
        return $this->sendRequest($data);
    }
    
    /**
     * 发送图片消息
     * @param string $base64 图片base64编码
     * @param string $md5 图片md5值
     * @return array
     */
    public function sendImage($base64, $md5) {
        $data = [
            'msgtype' => 'image',
            'image' => [
                'base64' => $base64,
                'md5' => $md5
            ]
        ];
        
        return $this->sendRequest($data);
    }
    
    /**
     * 发送图文消息
     * @param array $articles 图文消息数组
     * @return array
     */
    public function sendNews($articles) {
        $data = [
            'msgtype' => 'news',
            'news' => [
                'articles' => $articles
            ]
        ];
        
        return $this->sendRequest($data);
    }
    
    /**
     * 发送请求
     * @param array $data
     * @return array
     */
    private function sendRequest($data) {
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->webhookUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($jsonData)
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'http_code' => $httpCode,
            'response' => json_decode($response, true)
        ];
    }
}

// 使用示例
/*$robot = new WeChatWorkRobot("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d64c7c51-68f9-4ceb-bb9f-57de65e6058d");

$text = <<<TEXT
|表头1|表头2|
|:----:|:---|
|1|2|
|1|2|
|1|2|

```python
def hello_world():
    print("Hello World!")

hello_world()
```
TEXT;
$robot->sendMarkdown($text);*/
// $robot->sendText("1111");
?>