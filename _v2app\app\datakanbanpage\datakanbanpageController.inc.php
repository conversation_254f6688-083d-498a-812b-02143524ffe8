<?php
include_once APP_DIR . "/master/masterController.inc.php";
class datakanbanpageController extends masterController
{
	private $allowedMethods = [
		'calcData',
		'calcChannelData',
		'calcDailyStaticData',
	];

	public function __construct()
	{
		parent::__construct();
		$this->_action->setDao(new datakanbanpageDao('91W', '91R'));
	}

	public function __call($method, $args)
	{
		// 方法名格式必须是 do_开头
		if (strpos($method, 'do_') === 0) {
			$action = substr($method, 3); // 去掉 do_
			if (in_array($action, $this->allowedMethods)) {
				$this->_action->$action($this->_request);
			} else {
				// 非法方法访问处理
				http_response_code(404);
				exit;
			}
		} else if (strpos($method, 'v_') === 0) {
			$view = substr($method, 2); // 去掉 v_
			if (in_array($view, $this->allowedMethods)) {
				$this->_action->$view($this->_request);
			} else {
				// 非法方法访问处理
				http_response_code(404);
				exit;
			}
		} else {
			// 非 do_、v_ 方法，不处理
			http_response_code(404);
			exit;
		}
	}
}