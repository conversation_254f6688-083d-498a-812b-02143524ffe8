<!DOCTYPE html>
<html>
<head>
    <title><{$pageName}>-钢之家钢铁网</title>
    <meta name="verify-v1" content="rfRws4Y1mwg6EQn44sY6gX/gxhKUSQteND7cp5jrAoY=" />
    <meta name="Description" content="钢之家<{$pageName}>-及时准确提供国内外钢材价格及走势、行业动态、统计数据、市场分析预判、供求信息等" />
    <meta name="Keywords" content="钢之家,钢材价格,现货报价,钢材市场,钢材行情,钢铁价格走势,钢材价格指数,钢之家钢铁网" />
    <meta name="Copyright" content="<{$pageName}>- - 钢之家资讯" />
    <meta property="wb:webmaster" content="8c9c4eb4c25b011c" />
    <meta http-equiv="Expires" CONTENT= "0">
    <meta http-equiv="Pragma" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-store">
    <meta http-equiv="Cache-Control" CONTENT="must-revalidate">
    <meta http-equiv=”X-UA-Compatible” content="IE=Edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta content="text/html; charset=utf-8" http-equiv=Content-Type>

    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/js/www/layui/css/layui.css?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>">
    <link rel="stylesheet" type="text/css" href="<{$smarty.const.STATIC_URL}>/css/www/base_new.css?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>">
    <!-- <link rel="stylesheet" type="text/css" href="/css/base_new.css?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>"> -->
    <script>
        document.createElement('header');
        document.createElement('section');
        document.createElement('footer');
        document.createElement('aside');
        document.createElement('article');
        document.createElement('figure');
    </script>
    <style>
        header, section, footer, aside, article, figure {
            display: block;
        }
         /* 品种行的每个品种之间增加距离 */
         .content+.pinzhong a{
            padding-right: 20px;
        }
        /* 城市行的每个城市之间增加距离 */
        .content+.area a{
            padding-right: 30px;
        }
        /* .tab_conbox底下的li除了第一个，后面的都隐藏掉  */
        .tab_conbox li.tab_con:not(:first-child) {
            display: none;
        }
        .container {margin-bottom: 2px;}
        .hot_ziyuan{height:384px}
        .hot_news{ width:calc(38% + 6px);margin: 0 1%;}
        .col7_3{ width:calc(72% + 8px)}
        .col2_6{height:323px;}
        .fgjg{height: 26px;}
        .col2_6 ul.tab_conbox{padding: 0;}
        @media screen and (max-width:1400px){
            .hot_ziyuan{height:337px}
            .hot_news{ width:38%}
            .col7_3{ width:calc(72% + 2px)}
            .col2_6{height:291px;}
            .fgjg{height: 10px;}
        }
    </style>
</head>
<body>
    <!-- 顶部banner -->
    <{include file="../components/fg/home_banner.html" }>
    <!-- 头部(登录、导航、搜索、栏目、品种、城市) -->
    <{include file="../components/fg/header.html"}>
    <!-- 热点专题 -->
    <div class="content hot_topic" style="margin: 0 auto;padding-top: 10px;padding-bottom: 10px;">
        热点专题：
        <a href="<{$smarty.const.APP_URL_NEWS}>/list.php?view=special&type=hot&nspecial=326" target="_blank" title="">废钢准入企业</a>&nbsp;&nbsp;&nbsp;
        <a href="<{$smarty.const.APP_URL_NEWS}>/list.php?view=special&type=hot&nspecial=327" target="_blank" title="">废钢进出口政策</a>&nbsp;&nbsp;&nbsp;
        <a href="<{$smarty.const.APP_URL_NEWS}>/list.php?view=special&type=hot&nspecial=286" target="_blank" title="">废钢税收政策</a>&nbsp;&nbsp;&nbsp;
        <a href="<{$smarty.const.APP_URL_NEWS}>/list.php?view=special&type=hot&nspecial=328" target="_blank" title="">废钢标准</a>&nbsp;&nbsp;&nbsp;
    </div>
    <div class="content line" style="border-color: #000;"></div>
    <!-- 行情价格及走势图 -->
    <section>
        <div class="content price_zhishi xianhuo">
            <{include file="../components/channel_spot_quotation.html"}>
        </div>
    </section>
    <!-- 第一排广告 -->
    <figure>
        <div class="content ad5" id="A2">
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
        </div>
    </figure>
    <!-- 轮播图及热点专题、热点聚焦这一行 -->
    <section>
        <div class="content">
            <!-- 左侧轮播及热点专题 -->
            <{include file="../components/fg/left_top_picture.html"}>

            <!-- 热点聚焦、资讯推荐等 -->
            <div class="hot_news menubg_gray">
                <ul class="tabs" id="tabs1">
                    <li class="col1_5"><a href="/list.php?view=special&type=focus&channelId=<{$channelId}>" target="_blank">热点聚焦</a></li>
                    <li class="col1_5"><a href="/list.php?view=recommend" target="_blank">资讯推荐</a></li>
                    <li class="col1_5"><a href="/list.php?view=special&type=hotread&channelId=<{$channelId}>" target="_blank">热点导读</a></li>
                    <li class="col1_5"><a href="<{$redisNewsList[0].title_url}>" target="_blank"><{$redisNewsList[0].show_title}></a></li>
                    <li class="col1_5"><a href="<{$redisNewsList[1].title_url}>" target="_blank"><{$redisNewsList[1].show_title}></a></li>
                    <li class="col1_5"><a href="<{$redisNewsList[2].title_url}>" target="_blank"><{$redisNewsList[2].show_title}></a></li>
                </ul>
                <ul class="tab_conbox" id="tab_conbox1">
                    <!-- 热点聚焦 -->
                    <li class="tab_con">
                        <{foreach from=$newsListData.newsFocusList key=newsFocusKey item=newsFocusData}>
                        <{if $newsFocusKey lt 9}>
                        <div><a href="/2<{$newsFocusData.nid}>" target="_blank" title="<{$newsFocusData.ntitle}>" class="jiezi"><{$newsFocusData.ntitle}></a> <span class="gray"><{$newsFocusData.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <!-- 资讯推荐 -->
                    <li class="tab_con">
                        <{foreach from=$newsListData.recommendationList key=recommendationKey item=recommendationData}>
                            <{if $recommendationData.newstype neq null }>
                                <div><a href="/<{$recommendationData.newstype}><{$recommendationData.newsid}>" target="_blank" title="<{$recommendationData.ntitle}>" class="jiezi"><{$recommendationData.ntitle}></a> <span class="gray"><{$recommendationData.ndate|date_format: " %-m月%-e日"}></span>
                                </div>
                            <{else}>
                                <div><a href="/<{$recommendationData.type}><{$recommendationData.nid}>" target="_blank" title="<{$recommendationData.ntitle}>" class="jiezi"><{$recommendationData.ntitle}></a> <span class="gray"><{$recommendationData.ndate|date_format: " %-m月%-e日"}></span>
                                </div>
                            <{/if}>
                        <{/foreach}>
                    </li>
                    <!-- 热点导读 -->
                    <li class="tab_con">
                        <{foreach from=$newsListData.hotReadList key=hotReadKey item=hotReadData}>
                            <div><a href="/2<{$hotReadData.hid}>" target="_blank" title="<{$hotReadData.htitle}>" class="jiezi"><{$hotReadData.htitle}></a> <span class="gray"><{$hotReadData.addtimes|date_format: " %-m月%-e日"}></span>
                            </div>
                        <{/foreach}>
                    </li>
                    <li class="tab_con">
                        <{foreach from=$redisNewsList[0].list[0] key=listKey0 item=listData0}>
                        <div><a href="/<{$listData0.type}><{$listData0.nid}> " target="_blank" title="<{$listData0.ntitle}>" class="jiezi"><{$listData0.ntitle}></a> <span class="gray"><{$listData0.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/foreach}>
                    </li>
                    <li class="tab_con">
                        <{foreach from=$redisNewsList[1].list[0] key=listKey1 item=listData1}>
                        <div><a href="/<{$listData1.type}><{$listData1.nid}> " target="_blank" title="<{$listData1.ntitle}>" class="jiezi"><{$listData1.ntitle}></a> <span class="gray"><{$listData1.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/foreach}>
                    </li>
                    <li class="tab_con">
                        <{foreach from=$redisNewsList[2].list[0] key=listKey2 item=listData2}>
                        <div><a href="/<{$listData2.type}><{$listData2.nid}> " target="_blank" title="<{$listData2.ntitle}>" class="jiezi"><{$listData2.ntitle}></a> <span class="gray"><{$listData2.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/foreach}>
                    </li>
                </ul>
            </div>

            <!-- 废钢调查 电炉开工率 -->
            <div class="hot_news menubg_gray" style="width: 26%;margin: 0;">
                <ul class="tabs" id="tabs3">
                    <li class="col1_5" style="width:38%;"><a href="https://www.steelhome.com/map.php?type=fg" target="_blank">废钢企业地图</a></li>
                    <li class="col1_5" style="width:28%;"><a href="<{$redisNewsList[14].title_url}>" target="_blank"><{$redisNewsList[14].show_title}></a></li>
                    <li class="col1_5" style="width:30%;"><a href="<{$redisNewsList[15].title_url}>" target="_blank"><{$redisNewsList[15].show_title}></a></li>
                </ul>
                <ul class="tab_conbox" id="tab_conbox3">
                <li class="tab_con">
                    <a href="https://www.steelhome.com/map.php?type=fg" target="_blank"><img src="https://www.steelhome.com/stgfiles/202508/fgmapnew.png" style="width: 94%;margin: 15px 15px 20px 5px;border-radius: 3px;" onclick="window.open('https://www.steelhome.com/map.php?type=fg')"></a>
                </li>
                <li class="tab_con">
                    <{foreach from=$redisNewsList[14].list[0] key=listKey14 item=listData14}>
                    <div><a href="/<{$listData14.type}><{$listData14.nid}> " target="_blank" title="<{$listData14.ntitle}>" class="jiezi" style="width:75%;"><{$listData14.ntitle}></a> <span class="gray"><{$listData14.ndate|date_format: " %-m月%-e日"}></span>
                    </div>
                    <{/foreach}>
                </li>
                <li class="tab_con">
                    <{foreach from=$redisNewsList[15].list[0] key=listKey15 item=listData15}>
                    <div><a href="/<{$listData15.type}><{$listData15.nid}> " target="_blank" title="<{$listData15.ntitle}>" class="jiezi" style="width:75%;"><{$listData15.ntitle}></a> <span class="gray"><{$listData15.ndate|date_format: " %-m月%-e日"}></span>
                    </div>
                    <{/foreach}>
                </li>
                </ul>
            </div>
        </div>
    </section>
    <!-- 第二排广告 -->
    <!--<figure>
        <div class="content ad5" id="A3">
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
            <a href="javascript:void(0);" target="_blank"><img src="" lay-src=""></a>
        </div>
    </figure>-->
    <!-- 价格行情以及推荐经销商 -->
    <section>
        <div class="content">
            <div class="col7_3 price_index">
                <h2><a href="<{$redisNewsList[3].title_url}>" target="_blank"><span><{$redisNewsList[3].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs2">
                    <{foreach from=$redisNewsList[3].show_secondary key=secondaryTitleKey3 item=secondaryTitleData3}>
                    <li><a href="<{$secondaryTitleData3.url}>" target="_blank"><{$secondaryTitleData3.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox2">
                    <{foreach from=$redisNewsList[3].list key=listKey3 item=listData3}>
                    <li class="tab_con" style="display: none;">
                        <{foreach from=$listData3 key=listsecondaryKey3 item=listsecondaryData3}>
                        <{if $listsecondaryData3 neq ""}>
                        <{if $listsecondaryKey3 eq 0 or $listsecondaryKey3 eq 8}>
                            <div class="col4_8 ">
                        <{/if}>
                            <a href="/<{$listsecondaryData3.type}><{$listsecondaryData3.nid}> " target="_blank" title="<{$listsecondaryData3.ntitle}>" class="jiezi"><{$listsecondaryData3.ntitle}></a> <span class="gray"><{$listsecondaryData3.ndate|date_format: " %-m月%-e日"}></span>
                        <{if $listsecondaryKey3 eq 7 or $listsecondaryKey3 eq 15}>
                            </div>
                        <{/if}>

                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>
            <div class="col2_6">
                <h2>
                <ul class="tabs  tab_jxs" id="tabs4">
                    <li class="thistab"><a href="<{$redisNewsList[4].title_url}>" target="_blank"><{$redisNewsList[4].show_title}></a></li>
                    <li class=""><a href="<{$smarty.const.APP_URL_WWW}>/shpi_zh/shpi_qzhtb.php?class=fg" target="_blank">废钢采购价格指数</a></li>
                </ul>
                </h2>
                <ul class="tab_conbox" id="tab_conbox4">
                    <li class="tab_con">
                        <div  class="fgjg"></div>
                        <table border="1" style="line-height:32px;width: 100%;">
                            <tr>
                                <th style="width: 33%;text-align: center;">6-10mm重废</th>
                                <th style="width: 33%;text-align: center;"><{$materialDate}></th>
                                <th style="width: 33%;text-align: center;">涨跌</th>
                            </tr>
                            <{foreach from=$materialPrice key=materialPriceK item=materialPriceV}>
                            <tr>
                                <td style="height: 20px;text-align: center;">
                                    <{$materialPriceV.area}>
                                </td>
                                <td style="height: 20px;text-align: center;">
                                    <{$materialPriceV.price}>
                                </td>
                                <td style="height: 20px;text-align: center;">
                                    <{if $materialPriceV.zd>0}>
                                    <span class="red">↑<{$materialPriceV.zd2}></span>
                                    <{else if $materialPriceV.zd==0}>
                                    <span>-</span>
                                    <{else if $materialPriceV.zd<0}>
                                    <span class="green">↓<{$materialPriceV.zd2}></span>
                                    <{/if}>
                                </td>
                            </tr>
                            <{/foreach}>
                        </table>
                    </li>
                    <li class="tab_con">
                        <div class="fgjg"></div>
                        <table border="1" style="line-height:32px;width: 100%;">
                            <tr>
                                <th style="width: 20%;text-align: center;"><{$shpi_steelscrap_cgDate}></th>
                                <th style="width: 20%;text-align: center;">含税</th>
                                <th style="width: 20%;text-align: center;">涨跌</th>
                                <th style="width: 20%;text-align: center;">不含税</th>
                                <th style="width: 20%;text-align: center;">涨跌</th>
                            </tr>
                            <{foreach from=$shpi_steelscrap_cgPrice key=shpi_steelscrap_cgDateK item=shpi_steelscrap_cgDateV}>
                            <tr>
                                <td style="height: 20px;text-align: center;">
                                    <{$shpi_steelscrap_cgDateV.area}>
                                </td>
                                <td style="height: 20px;text-align: center;">
                                    <{$shpi_steelscrap_cgDateV.price}>
                                </td>
                                <td style="height: 20px;text-align: center;">
                                    <{if $shpi_steelscrap_cgDateV.zd>0}>
                                    <span class="red">↑<{$shpi_steelscrap_cgDateV.zd2}></span>
                                    <{else if $shpi_steelscrap_cgDateV.zd==0}>
                                    <span>-</span>
                                    <{else if $shpi_steelscrap_cgDateV.zd<0}>
                                    <span class="green">↓<{$shpi_steelscrap_cgDateV.zd2}></span>
                                    <{/if}>
                                </td>
                                <td style="height: 20px;text-align: center;">
                                    <{$shpi_steelscrap_cgDateV.priceex}>
                                </td>
                                <td style="height: 20px;text-align: center;">
                                    <{if $shpi_steelscrap_cgDateV.zdex>0}>
                                    <span class="red">↑<{$shpi_steelscrap_cgDateV.zd2ex}></span>
                                    <{else if $shpi_steelscrap_cgDateV.zdex==0}>
                                    <span>-</span>
                                    <{else if $shpi_steelscrap_cgDateV.zdex<0}>
                                    <span class="green">↓<{$shpi_steelscrap_cgDateV.zd2ex}></span>
                                    <{/if}>
                                </td>
                            </tr>
                            <{/foreach}>
                        </table>
                    </li>
                </ul>
            </div>
        </div>
    </section>
    <!-- 第一排三栏 行情汇总 市场分析 企业资讯 -->
    <section>
        <div class="content">
            <div class="col3_2">
                <h2><a href="<{$redisNewsList[5].title_url}>" target="_blank"><span><{$redisNewsList[5].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs5">
                    <{foreach from=$redisNewsList[5].show_secondary key=secondaryTitleKey5 item=secondaryTitleData5}>
                    <li><a href="<{$secondaryTitleData5.url}>" target="_blank"><{$secondaryTitleData5.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox5">
                    <{foreach from=$redisNewsList[5].list key=listKey5 item=listData5}>
                    <li class="tab_con">
                        <{foreach from=$listData5 key=listsecondaryKey5 item=listsecondaryData5}>
                        <{if $listsecondaryData5 neq ""}>
                        <div><a href="/<{$listsecondaryData5.type}><{$listsecondaryData5.nid}> " target="_blank" title="<{$listsecondaryData5.ntitle}>" class="jiezi"><{$listsecondaryData5.ntitle}></a> <span class="gray"><{$listsecondaryData5.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

            <div class="col3_2">
                <h2><a href="<{$redisNewsList[6].title_url}>" target="_blank"><span><{$redisNewsList[6].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs6">
                    <{foreach from=$redisNewsList[6].show_secondary key=secondaryTitleKey6 item=secondaryTitleData6}>
                    <li><a href="<{$secondaryTitleData6.url}>" target="_blank"><{$secondaryTitleData6.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox6">
                    <{foreach from=$redisNewsList[6].list key=listKey6 item=listData6}>
                    <li class="tab_con">
                        <{foreach from=$listData6 key=listsecondaryKey6 item=listsecondaryData6}>
                        <{if $listsecondaryData6 neq ""}>
                        <div><a href="/<{$listsecondaryData6.type}><{$listsecondaryData6.nid}> " target="_blank" title="<{$listsecondaryData6.ntitle}>" class="jiezi"><{$listsecondaryData6.ntitle}></a> <span class="gray"><{$listsecondaryData6.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

            <div class="col3_2 margin_right_none">
                <h2><a href="<{$redisNewsList[7].title_url}>" target="_blank"><span><{$redisNewsList[7].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs7">
                    <{foreach from=$redisNewsList[7].show_secondary key=secondaryTitleKey7 item=secondaryTitleData7}>
                    <li><a href="<{$secondaryTitleData7.url}>" target="_blank"><{$secondaryTitleData7.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox7">
                    <{foreach from=$redisNewsList[7].list key=listKey7 item=listData7}>
                    <li class="tab_con">
                        <{foreach from=$listData7 key=listsecondaryKey7 item=listsecondaryData7}>
                        <{if $listsecondaryData7 neq ""}>
                        <div><a href="/<{$listsecondaryData7.type}><{$listsecondaryData7.nid}> " target="_blank" title="<{$listsecondaryData7.ntitle}>" class="jiezi"><{$listsecondaryData7.ntitle}></a> <span class="gray"><{$listsecondaryData7.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>
        </div>
    </section>
    <!-- 第二排三栏 市场调查 期货市场 海外市场 -->
    <section>
        <div class="content">
            <div class="col3_2">
                <h2><a href="<{$redisNewsList[8].title_url}>" target="_blank"><span><{$redisNewsList[8].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs8">
                    <{foreach from=$redisNewsList[8].show_secondary key=secondaryTitleKey8 item=secondaryTitleData8}>
                    <li><a href="<{$secondaryTitleData8.url}>" target="_blank"><{$secondaryTitleData8.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox8">
                    <{foreach from=$redisNewsList[8].list key=listKey8 item=listData8}>
                    <li class="tab_con">
                        <{foreach from=$listData8 key=listsecondaryKey8 item=listsecondaryData8}>
                        <{if $listsecondaryData8 neq ""}>
                        <div><a href="/<{$listsecondaryData8.type}><{$listsecondaryData8.nid}> " target="_blank" title="<{$listsecondaryData8.ntitle}>" class="jiezi"><{$listsecondaryData8.ntitle}></a> <span class="gray"><{$listsecondaryData8.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

            <div class="col3_2">
                <h2><a href="<{$redisNewsList[9].title_url}>" target="_blank"><span><{$redisNewsList[9].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs9">
                    <{foreach from=$redisNewsList[9].show_secondary key=secondaryTitleKey9 item=secondaryTitleData9}>
                    <li><a href="<{$secondaryTitleData9.url}>" target="_blank"><{$secondaryTitleData9.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox9">
                    <{foreach from=$redisNewsList[9].list key=listKey9 item=listData9}>
                    <li class="tab_con">
                        <{foreach from=$listData9 key=listsecondaryKey9 item=listsecondaryData9}>
                        <{if $listsecondaryData9 neq ""}>
                        <div><a href="/<{$listsecondaryData9.type}><{$listsecondaryData9.nid}> " target="_blank" title="<{$listsecondaryData9.ntitle}>" class="jiezi"><{$listsecondaryData9.ntitle}></a> <span class="gray"><{$listsecondaryData9.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

            <div class="col3_2 margin_right_none">
                <h2><a href="<{$redisNewsList[10].title_url}>" target="_blank"><span><{$redisNewsList[10].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs10">
                    <{foreach from=$redisNewsList[10].show_secondary key=secondaryTitleKey10 item=secondaryTitleData10}>
                    <li><a href="<{$secondaryTitleData10.url}>" target="_blank"><{$secondaryTitleData10.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox10">
                    <{foreach from=$redisNewsList[10].list key=listKey10 item=listData10}>
                    <li class="tab_con">
                        <{foreach from=$listData10 key=listsecondaryKey10 item=listsecondaryData10}>
                        <{if $listsecondaryData10 neq ""}>
                        <div><a href="/<{$listsecondaryData10.type}><{$listsecondaryData10.nid}> " target="_blank" title="<{$listsecondaryData10.ntitle}>" class="jiezi"><{$listsecondaryData10.ntitle}></a> <span class="gray"><{$listsecondaryData10.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

        </div>
    </section>
    <!-- 第三排三栏 统计资料 研究中心 综合资讯 -->
    <section>
        <div class="content">
            <div class="col3_2">
                <h2><a href="<{$redisNewsList[11].title_url}>" target="_blank"><span><{$redisNewsList[11].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs11">
                    <{foreach from=$redisNewsList[11].show_secondary key=secondaryTitleKey11 item=secondaryTitleData11}>
                    <li><a href="<{$secondaryTitleData11.url}>" target="_blank"><{$secondaryTitleData11.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox11">
                    <{foreach from=$redisNewsList[11].list key=listKey11 item=listData11}>
                    <li class="tab_con">
                        <{foreach from=$listData11 key=listsecondaryKey11 item=listsecondaryData11}>
                        <{if $listsecondaryData11 neq ""}>
                        <div><a href="/<{$listsecondaryData11.type}><{$listsecondaryData11.nid}> " target="_blank" title="<{$listsecondaryData11.ntitle}>" class="jiezi"><{$listsecondaryData11.ntitle}></a> <span class="gray"><{$listsecondaryData11.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

            <div class="col3_2">
                <h2><a href="<{$redisNewsList[12].title_url}>" target="_blank"><span><{$redisNewsList[12].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs12">
                    <{foreach from=$redisNewsList[12].show_secondary key=secondaryTitleKey12 item=secondaryTitleData12}>
                    <li><a href="<{$secondaryTitleData12.url}>" target="_blank"><{$secondaryTitleData12.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox12">
                    <{foreach from=$redisNewsList[12].list key=listKey12 item=listData12}>
                    <li class="tab_con">
                        <{foreach from=$listData12 key=listsecondaryKey12 item=listsecondaryData12}>
                        <{if $listsecondaryData12 neq ""}>
                        <div><a href="/<{$listsecondaryData12.type}><{$listsecondaryData12.nid}> " target="_blank" title="<{$listsecondaryData12.ntitle}>" class="jiezi"><{$listsecondaryData12.ntitle}></a> <span class="gray"><{$listsecondaryData12.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>

            <div class="col3_2 margin_right_none">
                <h2><a href="<{$redisNewsList[13].title_url}>" target="_blank"><span><{$redisNewsList[13].show_title}></span></a></h2>
                <ul class="tabs bgcolor_red" id="tabs13">
                    <{foreach from=$redisNewsList[13].show_secondary key=secondaryTitleKey13 item=secondaryTitleData13}>
                    <li><a href="<{$secondaryTitleData13.url}>" target="_blank"><{$secondaryTitleData13.title}></a></li>
                    <{/foreach}>
                </ul>
                <ul class="tab_conbox" id="tab_conbox13">
                    <{foreach from=$redisNewsList[13].list key=listKey13 item=listData13}>
                    <li class="tab_con">
                        <{foreach from=$listData13 key=listsecondaryKey13 item=listsecondaryData13}>
                        <{if $listsecondaryData13 neq ""}>
                        <div><a href="/<{$listsecondaryData13.type}><{$listsecondaryData13.nid}> " target="_blank" title="<{$listsecondaryData13.ntitle}>" class="jiezi"><{$listsecondaryData13.ntitle}></a> <span class="gray"><{$listsecondaryData13.ndate|date_format: " %-m月%-e日"}></span>
                        </div>
                        <{/if}>
                        <{/foreach}>
                    </li>
                    <{/foreach}>
                </ul>
            </div>
        </div>
    </section>

    <!-- 商务版块 -->
    <div class="kong"></div>
    <section>
        <div class="content">
            <!-- 下方热点资源 -->
            <{include file="../components/hot_resource_table.html"}>
            <div class="show-border">
                <!-- 下方易钢等搜索 -->
                <{include file="../components/isec_search.html"}>
                <!-- 下方资源品种导航 -->
                <{include file="../components/resource_variety_link.html"}>
            </div>

        </div>
    </section>

    <!-- 友情链接 -->
    <section>
        <{include file="../components/friendly_links.html"}>
    </section>
    <!-- 底部版权信息 -->
    <{include file="../components/footer.html"}>
</body>
<script src="/js/jquery-1.7.2.min.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<!-- login 弹出可重复-->
<script>
    function show(tag) {
        var SonContent = document.getElementById(tag);
        SonContent.style.display = 'block';
    }

    function hide(tag) {
        var SonContent = document.getElementById(tag);
        SonContent.style.display = 'none';
    }
</script>
<script src="/js/common.min.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<script type="text/javascript">
    let spotQuotationList = '<{$spotEchartsData}>';
    spotQuotationList = JSON.parse(spotQuotationList);
    let advParams = "&pageType=103&channelId=<{$channelId}>";
</script>
<!-- focus -->
<script type="text/javascript" src="/js/swipeslider.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>"></script>
<script src="<{$smarty.const.STATIC_URL}>/js/www/layui/layui.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<{include file="../components/common_js.html"}>
<script src="/js/search.js" type="text/javascript"></script>
<!-- <script src="/js/switch_pictures.js" type="text/javascript"></script> -->
<script src="<{$smarty.const.STATIC_URL}>/js/www/echarts.min.js"></script>
<script src="/js/home_adv_show.js" type="text/javascript"></script>
<script src="/js/spot_quotation_picture.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<script src="/js/show_steelhome_data_picture.js?v=<{$smarty.const.JS_CSS_VERSION_CONTROL}>" type="text/javascript"></script>
<html>
