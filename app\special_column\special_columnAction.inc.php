<?php
//error_reporting( E_ALL );
//ini_set( "display_errors", true );
include_once(APP_DIR . "/master/masterAction.inc.php");

class special_columnAction extends masterAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function checkSessionStation($params)
    {
        // $_COOKIE['Authorization'] = "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjo0MTcwMzcsInVzZXJfa2V5IjoiNzhkMTAyYTktNmJmYS00YTFhLWI0YzMtNjQ0NGNjNDIyOWNjIiwidXNlcm5hbWUiOiJzaGl6ZzIwMTYifQ.xtgC7X7UvAUmQeL4dEVX6N_EfcA-824OpfUb__ua2Q_G-oGXs4VhTPd_p4H3Qo-lAoYdSAOBVjhg1XfXuIulPg";
        if (empty($_COOKIE['Authorization']) && empty($params['userid']) ) {
            echo "<script>window.location.href='/MemberLogin.php?urlstr=" . urlencode($_SERVER['HTTP_REFERER']) . "'</script>";
            exit;
        }
    }

    /**
     *
     * @param $params
     * @return void
     * <AUTHOR> 2024-03-04
     * todo 专栏栏目信息录入页面
     */
    public function index($params)
    {
        $columnPowerInfo = array();
        $newsinfo = array();
        $channelId = "";
        $newsPrice = 50;
        $nowTime = date("Y-m-d H:i:s");
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                //专栏授权信息
                $specialColumnInfo = $this->getSpecialColumnManageList(array("columnId" => "", "userId" => $loginInfo['userid']));
                if (empty($specialColumnInfo) && $loginInfo['sysUser']['phoneNumber']) {
                    $specialColumnInfo = $this->getSpecialColumnManageList(array("columnId" => "", "userId" => "", "userMobile" => $loginInfo['sysUser']['phoneNumber']));
                }
                $newsPrice = $specialColumnInfo[0]['columnPrice'];
                //栏目信息
                $columnInfo = $this->getColumnList();

                foreach ($columnInfo as $columnValue) {
                    if ($specialColumnInfo[0]['columnId'] == $columnValue['columnid']) {
                        $columnId = $columnValue['mcolKeys'] ? $columnValue['mcolKeys'] . "," . $columnValue['columnid'] : $columnValue['bigcolumnid'] . "," . $columnValue['columnid'];
                        $columnPowerInfo[$columnId] = $columnValue['columnname'];
                        $channelId = str_replace(array("[", "]"), "", $columnValue['channelids']);
                    }
                }
//               echo "<pre/>"; print_r($loginInfo); print_r($columnPowerInfo);
            }
            //修改信息
            if (isset($params['nid']) && $params['nid'] > 0) {
                $newsinfo = $this->getNewsTableInfo(array("nid" => $params['nid']));
                if ($newsinfo) {
                    $newsPrice = $this->getNewsPrice(array("uniqueid" => "n" . $params['nid']));
                    $nowTime = date("Y-m-d H:i:s", strtotime($newsinfo['ndate']));
                    $newsContentsUrl = APP_URL_WWW . "/_v2app/get_information_content.php?filepath=" . $newsinfo['filepath'] . "&getToken=" . CODE_CONTAIN_TOKEN_PUBLIC;
                    $newsContentsStr = file_get_contents($newsContentsUrl);
                    $newsContentsJson = json_decode($newsContentsStr, true);
                    $newsContents = $newsContentsJson['contents'];

                }

            }
        }
//        echo "<pre/>"; print_r($nowTime); print_r($newsinfo['nid']);
        $this->assign("columnPowerInfo", $columnPowerInfo);
        $this->assign("channelId", $channelId);
        $this->assign("specialColumnInfo", $specialColumnInfo[0]);
        $this->assign("newsPrice", round($newsPrice));
        $this->assign("nowTime", $nowTime);
        $this->assign("newsContents", $newsContents);
        $this->assign("newsinfo", $newsinfo);

        $this->handlePublicData($params);
        $this->handlePublicSettingData($params);
        file_put_contents("/tmp/home_page_time.log", "searchstart::" . date("Y-m-d H:i:s") . "\n", FILE_APPEND);
    }

    /**
     *
     * @param $params
     * @return void
     * <AUTHOR> 2024-03-05
     * todo 执行录入专栏信息
     */
    public function addnews($params)
    {
        $returnData['msg'] = "录入成功";
        include_once("/usr/local/www/libs/webservice/nusoap/lib/nusoap.php");
        $news_price = $params['news_price'];

        $nmanageid = $params['nmanageid'];
        $ndate = date("Y-m-d H:i:s", strtotime($params['date_time']));
        $ntitle = $params['ntitle'];
        $ntitle2 = "";
        $nsourse = "";
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                //专栏授权信息
                $specialColumnInfo = $this->getSpecialColumnManageList(array("columnId" => "", "userId" => $loginInfo['userid']));
                if (empty($specialColumnInfo) && $loginInfo['sysUser']['phoneNumber']) {
                    $specialColumnInfo = $this->getSpecialColumnManageList(array("columnId" => "", "userId" => "", "userMobile" => $loginInfo['sysUser']['phoneNumber']));
                }
                //修改栏目默认价格
                $this->updateSpecialColumnManage(array("id" => $specialColumnInfo[0]['id'], "columnPrice" => $news_price));
                $nsourse = $loginInfo['sysUser']['trueName'];
            }
        }
        $isimport = 0;
        $nchannelid = $params['channelId'];
        $ncolumnid = $params['columnId'];
        $rights = 0;
        $nvarietyid = "";
        $ncityid = "";
        $nfocus = "";
        $nspecial = "";
        $nremark = "";
        $nkeys = "";
        $ntype = "1";
        $ismarket = "0";
        $gc_name = "";
        $gc_name2 = "";
        $summary = "";
        $marketid = "0";
        if ($news_price == 0) {
            $isfree = "6";
        } else {
            $isfree = "2";
        }
        $hygs_gj = "";
        $ismain = "0";
        $fromhttp = "";
        $mobanhzh = "1,1";
        $ncontent = $params['news_contents'];
        $ncontent = stripslashes(htmlspecialchars_decode($ncontent));
        $client = new nusoap_client (APP_URL_WWW . '/webservice/nusoap/news/news_server_tj.php?wsdl', true);
        $parameters = array($nmanageid, $ndate, $ntitle, $ntitle2, $nsourse, $isimport, $nchannelid, $ncolumnid, $rights, $nvarietyid, $ncityid, $nfocus, $nspecial, $ncontent, $nremark, $nkeys, $ntype, $ismarket, $gc_name, $marketid, $isfree, $hygs_gj, $ismain, $fromhttp, $mobanhzh, $gc_name2, $summary);
//         echo "<pre>";print_r($parameters);exit;

        $str = $client->call('newspost', $parameters);
        $newsid = trim($str);
        $this->addCustomNewsPrice(array("newsid" => $newsid, "price" => $news_price, "status" => 1));
        echo json_encode($returnData, JSON_UNESCAPED_UNICODE);
    }

    /**
     *
     * @param $params
     * @return void
     * <AUTHOR> 2024-03-06
     * todo 执行修改信息
     */
    public function updatearticle($params)
    {
        $code = 0;
        $msg = "操作成功!";

        include_once("/usr/local/www/libs/webservice/nusoap/lib/nusoap.php");
        $newsid = $params['newsid'];
        $ndate = date("Y-m-d H:i:s", strtotime($params['date_time']));
        $ntitle = $params['ntitle'];
        $ncontent = $params['news_contents'];
        $ncontent = stripslashes(htmlspecialchars_decode($ncontent));
        if ($newsid > 0) {
            $newsinfo = $this->getNewsTableInfo(array("nid" => $newsid));
        }
        if (isset($params['actiontype']) && $params['actiontype'] == 2) {
            $msg = "删除成功!";
            $ntitle = $newsinfo['ntitle'];
            $newsContentsUrl = APP_URL_WWW . "/_v2app/get_information_content.php?filepath=" . $newsinfo['filepath'] . "&getToken=" . CODE_CONTAIN_TOKEN_PUBLIC;
            $newsContentsStr = file_get_contents($newsContentsUrl);
            $newsContentsJson = json_decode($newsContentsStr, true);
            $ncontent = $newsContentsJson['contents'];
        }
        $news_price = isset($params['news_price']) && $params['news_price'] > 0 ? $params['news_price'] : 0;
        $ntitle2 = "";
        $nsourse = "";
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                $nmanageid = $loginInfo['userid'];
                $nsourse = $loginInfo['sysUser']['trueName'];
            }
        }
        $filepath = $newsinfo['filepath'];
        $nchannelid = $params['channelId'];
        $ncolumnid = $params['columnId'];
        $nvarietyid = "";
        $ncityid = "";
        $nfocus = "";
        $nkeys = "";

        $gc_name = "";
        $gc_name2 = "";
        $hygs_gj = "";
        $ismain = "0";
        if ($news_price == 0) {
            $isfree = "6";
        } else {
            $isfree = "2";
        }
        $isshow = "1";
        if (isset($params['actiontype']) && $params['actiontype'] == 2) {
            $isshow = "-1";
            $this->updateCustomNewsPrice(array("newsid" => $newsid, "price" => $news_price, "status" => 0));
        } else {
            $this->updateCustomNewsPrice(array("newsid" => $newsid, "price" => $news_price, "status" => 1));
        }

        $client = new nusoap_client (APP_URL_WWW . '/webservice/nusoap/news/news_server_tj.php?wsdl', true);
        $parameters = array($newsid, $nmanageid, $ndate, $ntitle, $ntitle2, $filepath, $nchannelid, $ncolumnid, $nvarietyid, $ncityid, $ncontent, $nkeys, $gc_name, $hygs_gj, $ismain, $gc_name2, $nfocus, $isshow, "", $isfree);
        // echo "<pre>";print_r($parameters);exit;

        $str = $client->call('editnewspost', $parameters);
//        var_dump($parameters);
//        var_dump($params);
//        var_dump($str);
        $return_array = array(
            "code" => $code,
            "msg" => $msg
        );
        echo json_encode($return_array);
    }

    /**
     *
     * @param $params
     * @return void
     * <AUTHOR> 2024-03-05
     * todo 专栏栏目信息管理页面
     */
    public function manage($params)
    {
        $totalMoney = 0;
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                $nmanageid = $loginInfo['userid'];
                $totalMoney = $this->getPurchasedNewsTotalMoney(array("manageId" => $nmanageid));
            }
        }
        $this->assign("totalMoney", round($totalMoney['totalMoney'], 2));
        $defaultTime = date("Y-m-d", strtotime("-1 month")) . " - " . date("Y-m-d");
        $this->assign("defaultTime", $defaultTime);
        $this->handlePublicData($params);
        $this->handlePublicSettingData($params);
    }

    /**
     *
     * @param $params
     * @return void
     * <AUTHOR> 2024-03-05
     * todo 获取信息列表
     */
    public function getarticlelist($params)
    {

        $total = 0;
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                //专栏授权信息
                $specialColumnInfo = $this->getSpecialColumnManageList(array("columnId" => "", "userId" => $loginInfo['userid']));
                if (empty($specialColumnInfo) && $loginInfo['sysUser']['phoneNumber']) {
                    $specialColumnInfo = $this->getSpecialColumnManageList(array("columnId" => "", "userId" => "", "userMobile" => $loginInfo['sysUser']['phoneNumber']));
                }
                //栏目信息
                $columnInfo = $this->getColumnList();
                $columnId = "";
                foreach ($columnInfo as $columnValue) {
                    if ($specialColumnInfo[0]['columnId'] == $columnValue['columnid']) {
                        $columnId = $columnValue['mcolKeys'] ? $columnValue['mcolKeys'] . "," . $columnValue['columnid'] : $columnValue['bigcolumnid'] . "," . $columnValue['columnid'];
                    }
                }
                //获取发布信息的列表
                $newsListData = $this->getElasticSearchList(array("redisKeyString" => "2==002,306===","ntitle"=>"","type"=>0,"matchContents"=>0, "adminid" => $loginInfo['userid'], "startTime" => $params['startTime'], "endTime" => $params['endTime'], "pageNum" => $params['page'], "pageSize" => $params['limit']));
                $newsListTatalData = $this->getElasticSearchList(array("redisKeyString" => "2==002,306===","ntitle"=>"","type"=>0,"matchContents"=>1, "adminid" => $loginInfo['userid'],"startTime" => $params['startTime'], "endTime" => $params['endTime'], "pageNum" => 1, "pageSize" => 600));
                $total = empty($newsListTatalData) ? 1 : count($newsListTatalData);

//                $newsListData = $this->getNewsList(array("channelId" => "", "columnId" => $columnId, "varietyId" => "", "cityId" => "", "steelId" => "", "startTime" => $params['startTime'], "endTime" => $params['endTime'], "nmanageId" => $loginInfo['userid'], "nTitle" => $params['ntitle'], "pageNum" => $params['page'], "pageSize" => $params['limit']));
//                $newsListTatalData = $this->getNewsList(array("channelId" => "", "columnId" => $columnId, "varietyId" => "", "cityId" => "", "steelId" => "", "startTime" => $params['startTime'], "endTime" => $params['endTime'], "nmanageId" => $loginInfo['userid'], "nTitle" => $params['ntitle'], "pageNum" => 1, "pageSize" => 600));
//                $total = count($newsListTatalData);
                $tempNid = array();
                $newsPurchasedInfo = array();
                $newsPurchasedMoney = array();
                foreach ($newsListData as $temp) {
                    $tempNid[] = $temp['nid'];
                }
                if ($tempNid) {
//                    $newsIds = implode("%7C",$tempNid);
                    $newsIds = implode("|", $tempNid);
                    $newsPurchasedInfo = $this->getPurchasedNewsInfo(array("type" => 1, "newsid" => $newsIds));
                }
                foreach ($newsPurchasedInfo as $purchasedInfo) {
                    $newsPurchasedMoney[$purchasedInfo['newsid']] += $purchasedInfo['price'];
                }
//                echo "<pre/>";print_r($newsPurchasedMoney);print_r($newsPurchasedInfo);

                foreach ($newsListData as &$articleInfo) {
                    //本人7天内有权
                    $timeSub = time() - strtotime($articleInfo['ndate']) - 3600 * 24 * 7;
                    if ($timeSub < 0) {
                        $articleInfo['power'] = 1;
                    } else {
                        $articleInfo['power'] = 0;
                    }
                    $newsPrice = $this->getNewsPrice(array("uniqueid" => "n" . $articleInfo['nid']));
                    $articleInfo['newsPrice'] = $newsPrice <= 0 ? "免费" : $newsPrice;
                    $articleInfo['newsInCome'] = $newsPurchasedMoney[$articleInfo['nid']] == 0 ? "暂无" : $newsPurchasedMoney[$articleInfo['nid']];
                    $articleInfo['truename'] = $loginInfo['sysUser']['trueName'];

                }
            }
        }


        $code = 0;
        if ($newsListData) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $newsListData,
            "count" => $total
        );
        echo json_encode($return_array);

    }

    /**
     *
     * @param $params
     * @return void
     * <AUTHOR> 2024-03-05
     * todo 专栏栏目信息管理页面
     */
    public function income($params)
    {
        $newsid = isset($params['newsid']) && $params['newsid'] ? $params['newsid'] : "";
        $totalMoney = 0;
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                $nmanageid = $loginInfo['userid'];
                $totalMoney = $this->getPurchasedNewsTotalMoney(array("manageId" => $nmanageid, "type" => 1, "newsid" => $newsid));
            }
        }
        $this->assign("totalMoney", round($totalMoney['totalMoney'], 2));
        $this->handlePublicData($params);
        $this->handlePublicSettingData($params);
    }

     /**
     * @Author: shizg
     * @Date: 2025-08-20 09:42:31
     * @parms: 
     * @return: SYMBOL_TYPE 
     * @Description: 我购买的信息页面
    */
     public function my_purchase($params)
     {
        $this->handlePublicData($params);
        $this->handlePublicSettingData($params);
     }

    public function getincomelist($params)
    {
        $total = 0;
        if ($_COOKIE['Authorization']) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];

                //获取发布信息的列表

                $newsListData = $this->getPurchasedNewsInfo(array("manageId" => $loginInfo['userid'],"type" => 1, "newsid" => $params['newsid'], "startTime" => $params['startTime'], "endTime" => $params['endTime'], "pageNum" => $params['page'], "pageSize" => $params['limit']));
                $newsListTatalData = $this->getPurchasedNewsInfo(array("manageId" => $loginInfo['userid'],"type" => 1, "newsid" => $params['newsid'], "startTime" => $params['startTime'], "endTime" => $params['endTime'], "pageNum" => 1, "pageSize" => 600));
                $total = count($newsListTatalData);
//                print_r($newsListData);
//                print_r($total);
                $newsPurchasedMoney = array();
                foreach ($newsListData as &$articleInfo) {
                    $getNewsTalbeInfo = $this->getNewsTableInfo(array("nid"=>$articleInfo['newsid']));
                    $articleInfo['ntitle'] = $getNewsTalbeInfo['ntitle'];
                    $articleInfo['ndate'] = date("Y-m-d H:i:s",strtotime($getNewsTalbeInfo['ndate']));
                    $articleInfo['nid'] = $getNewsTalbeInfo['nid'];
                    $articleInfo['orderno'] = $articleInfo['orderno1'].$articleInfo['orderno2'];

                }
            }
        }


        $code = 0;
        if ($newsListData) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $newsListData,
            "count" => $total
        );
        echo json_encode($return_array);
    }

     /**
     * @Author: shizg
     * @Date: 2025-08-20 10:10:33
     * @parms: 
     * @return: SYMBOL_TYPE 
     * @Description: 获取我购买的信息
    */
     public function getpurchasenewslist($params)
     {
        $userType = isset($params['UserType']) && $params['UserType'] ? $params['UserType'] : "";
        $userid = isset($params['userid']) && $params['userid'] ? $params['userid'] : "";
        $total = 0;
        if ($_COOKIE['Authorization'] || ($userType  && $userid) ) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo) || ($userType  && $userid) ) {
                $newsListData = $this->getPurchasedNewsInfo([ "userid" => $userid,"usertype" => $userType, "pageNum" => $params['page'], "pageSize" => $params['limit']]);
                $newsListTatalData = $this->getPurchasedNewsInfo(array( "userid" => $userid,"usertype" => $userType, "pageNum" => 1, "pageSize" => 600));
                $total = count($newsListTatalData);
            //    print_r($newsListData);
            //    print_r($total);
                $newsPurchasedMoney = array();
                foreach ($newsListData as $key => &$articleInfo) {
                    if ($articleInfo['type'] == 1) {
                        $getNewsTalbeInfo = $this->getNewsTableInfo(array("nid"=>$articleInfo['newsid']));
                        if ( empty($getNewsTalbeInfo)) {
                            unset($newsListData[$key]);
                            continue;
                        }
                        $articleInfo['ntitle'] = $getNewsTalbeInfo['ntitle'];
                        $articleInfo['ndate'] = date("Y-m-d H:i:s",strtotime($getNewsTalbeInfo['ndate']));
                        $articleInfo['nid'] = $getNewsTalbeInfo['nid'];
                        $articleInfo['newstype'] = "n";
                    }elseif ($articleInfo['type'] == 2) {
                        $getNewsTalbeInfo = $this->getNewsMrhqTableInfo(array("nid"=>$articleInfo['newsid']));
                        if ( empty($getNewsTalbeInfo)) {
                            unset($newsListData[$key]);
                            continue;
                        }
                        // print_r($getNewsTalbeInfo);
                        $articleInfo['ntitle'] = $getNewsTalbeInfo['ntitle'];
                        $articleInfo['ndate'] = date("Y-m-d H:i:s",strtotime($getNewsTalbeInfo['ndate']));
                        $articleInfo['nid'] = $articleInfo['newsid'];
                         $articleInfo['newstype'] = "m";
                    }


                }

                //获取发布信息的列表

                
            }
        }
        
        $code = 0;
        if ($newsListData) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $newsListData,
            "count" => $total
        );
        echo json_encode($return_array);
     }

}
