<?php
$GLOBALS['WeChatMessageUserId']['default'] = ["108217", "108216", "29220", "452770"];//默认提醒人员：杜郑，范晓宇，董素琦，章玲
$GLOBALS['WeChatMessageUserId']['3_0'] = ["108216", "452770"];//范晓宇，章玲
$GLOBALS['WeChatMessageUserId']['4_0'] = ["108216", "29220", "452770"];//范晓宇，董素琦，章玲
$GLOBALS['WeChatMessageUserId']['3_1'] = ["108216"];//：已送到：范晓宇

//超管权限、如果是司机或者跟车以 司机、跟车 为优先权限
$GLOBALS['userid'] = [
	"17",  // 董事长
	"501692", // 陈煜
	"108217", // 杜郑
	"108216", // 范晓宇
	"417037", // 史忠苟
	"147803", // 张康
	"452459", // 郑乃斌
	"444830", // 吴运豪
	"431130", //陈成
	"492160", //杨峰
	"400881", //秦伟芬
	// "455369", //陈信源
	"389621", // 黄灏
	"29220", // 董素琦
	"24", // 陈艳
	"452770", // 章玲
]; 


//如果是跟车或者司机想要超管权限，添加这里为超管
$GLOBALS['userid2'] = [
	"108217",  //杜郑
	"431130", //陈成
	"108216", //范晓宇
	"29220", //董素琦
	"24", //陈艳
	"417037", // 史忠苟
];

// UserType 1:跟车  0：司机 2：超管  3：管理员
$GLOBALS['SEAT_TYPE'] = [
	"1" => "主桌",
	"2" => "次主桌",
	"3" => "嘉宾桌",
	"4" => "普通桌",
];

$GLOBALS['SEAT_TYPE_SHOW_CLASS'] = [
	// "1"=>" red ",
	// "2"=>" yellow ",
	// "3"=>" blue ",
	// "4"=>" pt ",
	// "5"=>" kong "
	"1" => " pt ",
	"2" => " pt ",
	"3" => " pt ",
	"4" => " pt ",
	"5" => " kong ",
	"6" => " pt "
];

$GLOBALS['SUPER_MANAGE'] = [
	"项斌",
	"孙艳",
	"邬文雄",
	"韩丽",
	"430054"
];

$GLOBALS['GUST_MANAGE'] = [
	"范晓宇",
	"张木子",
	"韩丽",
	"孙艳",
	"周阳春"
];

$GLOBALS['GUST_MANAGE_MOBILE'] = [
	"18156590749",
	// "13085554576",
	// "13601635041"
];

$GLOBALS['WEBGUID'] = "meeting443";
$GLOBALS['MEETING_ID'] = "443";
$GLOBALS['MEETING_PARTY'] = [//微信选座
	"407"=>'14',
	"429"=>'15',
	//"427"=>'16',
];

$GLOBALS['MEETING_SHOWFZJL'] = [//是否显示分组交流
	"427"=>'0',
	"443"=>'1'
];

// $GLOBALS['MEETING_ID'] = "392";