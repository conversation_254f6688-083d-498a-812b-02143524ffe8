<?php
//error_reporting( E_ALL );
//ini_set( "display_errors", true );
include_once(APP_DIR . "/master/masterAction.inc.php");
// use GuzzleHttp\Client;
use Smalot\PdfParser\Parser;
use setasign\Fpdi\Fpdi;

class detailspageAction extends masterAction
{
    private $newszhpower = [
        "011" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-hgzx/'>&nbsp;> 宏观资讯 > </a><a href='".APP_URL_NEWS."/zhzx/col-011/'>国内宏观</a>"],
        "012" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-hgzx/'>&nbsp;> 宏观资讯 > </a><a href='".APP_URL_NEWS."/zhzx/col-012/'>国际宏观</a>"],
        "013" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-hgzx/'>&nbsp;> 宏观资讯 > </a><a href='".APP_URL_NEWS."/zhzx/col-013/'>财经要闻</a>"],
        "184" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-hgzx/'>&nbsp;> 宏观资讯 > </a><a href='".APP_URL_NEWS."/zhzx/col-184/'>综合观察</a>"],
        "014" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-hgzx/'>&nbsp;> 宏观资讯 > </a><a href='".APP_URL_NEWS."/zhzx/col-014/'>政策法规</a>"],
        "015" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-hgzx/'>&nbsp;> 宏观资讯 > </a><a href='".APP_URL_NEWS."/zhzx/col-015/'>宏观数据</a>"],
        "016" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-016/'>基础建设</a>"],
        "017" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-017/'>房地产业</a>"],
        "018" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-018/'>建材行业</a>"],
        "031" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-031/'>能源电力</a>"],
        "019" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-019/'>机械行业</a>"],
        "020" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-020/'>家电行业</a>"],
        "021" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-021/'>汽车行业</a>"],
        "022" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-022/'>造船行业</a>"],
        "023" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-023/'>石油化工</a>"],
        "024" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-024/'>物流运输</a>"],
        "026" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-026/'>其他行业</a>"],
        "027" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-xghy/'>&nbsp;> 相关行业 > </a><a href='".APP_URL_NEWS."/zhzx/col-027/'>会展信息</a>"],
        "028" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-jyzd/'>&nbsp;> 经营之道 > </a><a href='".APP_URL_NEWS."/zhzx/col-028/'>业内访谈</a>"],
        "032" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-jyzd/'>&nbsp;> 经营之道 > </a><a href='".APP_URL_NEWS."/zhzx/col-032/'>老总专访</a>"],
        "029" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-jyzd/'>&nbsp;> 经营之道 > </a><a href='".APP_URL_NEWS."/zhzx/col-029/'>经营之道</a>"],
        "033" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/bigcol-jyzd/'>&nbsp;> 经营之道 > </a><a href='".APP_URL_NEWS."/zhzx/col-033/'>营销案例</a>"],
        "030" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/col-030/'>&nbsp;> 网站动态</a>"],
        "078" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/col-030/'>&nbsp;> 网站动态 > </a><a href='".APP_URL_NEWS."/zhzx/col-078'>最新产品</a>"],
        "201" => ['dummy', "<a href='".APP_URL_NEWS."/zhzx/col-201/'>&nbsp;> 期货知识</a>"],
        "099" => ['dummy', "<a href='".APP_URL_NEWS."/#ch#/col-056/'>&nbsp;> 行情汇总</a>"],
    ];

    /*private $newsglypower = [
        '053'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '每日分析', '每日分析', '每日分析', '国内市场'],
        '055'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '每日行情', '每日行情', '每日行情', '国内市场'],
        '054'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '市场汇总', '', '', '国内市场'],
        '056'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '品种汇总', '行情汇总', '国内市场'],
        '057'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '每日分析', '每日分析', '每日分析', '国内市场'],
        '058'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '每周综述', '每周综述', '每周综述', '国内市场'],
        '240'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '每日短信', '每日短信', '每日短信', '国内市场'],
        '242'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '期现基差', '期现基差', '期现基差', '国内市场'],
        '059'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '国内综述', '国内综述', '国内综述', '国内市场'],
        '265'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '市场均价', '市场均价', '市场均价', '国内市场'],
        '186'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '钢市观察', '', '', '国内市场'],
        '063'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '市场库存', '市场库存', '市场库存', '国内市场'],
        '068'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '对外贸易', '对外贸易', '对外贸易', '国内市场'],
        '069'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '出口报价', '出口报价', '出口报价', '国内市场'],
        '196'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '期货评述', '期货评述', '期货评述', '国内市场'],
        '197'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '', '', '证券投资', '国内市场'],
        '070'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '普氏', '普氏', '普氏', '国内市场'],
        '072'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", 'TSI', 'TSI', 'TSI', '国内市场'],
        '073'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '掉期', '掉期', '掉期', '国内市场'],
        '074'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", 'SMX', 'SMX', 'SMX', '国内市场'],
        '075'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '招标', '招标', '招标', '国内市场'],
        '076'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '指数分析', '指数分析', '指数分析', '国内市场'],
        '235'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '企业库存', '企业库存', '企业库存', '国内市场'],
        '241'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '大商所', '大商所', '大商所', '国内市场'],
        '061'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际行情', '国际行情', '国际行情', '海外市场'],
        '185'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '期货行情', '期货行情', '期货行情', '海外市场'],
        '062'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际综述', '国际综述', '国际综述', '海外市场'],
        '060'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际贸易', '国际贸易', '国际贸易', '海外市场'],
        '170'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '海外钢厂', '海外企业', '海外企业', '海外市场'],
        '112'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际统计', '国际统计', '国际统计', '海外市场'],
        '134'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际冶金', '国际冶金', '国际冶金', '海外市场'],
        '065'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-007/'>&nbsp;> 研究中心</a>", '曾文工作室', '曾文工作室', '曾文工作室', '研究中心'],
        '064'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-007/'>&nbsp;> 研究中心</a>", '国内研究', '国内研究', '国内研究', '研究中心'],
        '188'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-007/'>&nbsp;> 研究中心</a>", '国际研究', '国际研究', '国际研究', '研究中心'],
        '140'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-007/'>&nbsp;> 研究中心</a>", '技术应用', '技术应用', '技术应用', '研究中心'],
        '066'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-007/'>&nbsp;> 研究中心</a>", '库存汇总', '库存汇总', '库存汇总', '研究中心'],
        '067'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-007/'>&nbsp;> 研究中心</a>", '调价汇总', '调价汇总', '调价汇总', '研究中心'],
        '110'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '', '采购价格', '采购价格', '企业资讯'],
        '109'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '调价信息', '', '', '企业资讯'],
        '108'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '出厂价格', '出厂价格', '出厂价格', '企业资讯'],
        '115'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '计划检修', '', '', '企业资讯'],
        '106'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '行业动态', '行业动态', '行业动态', '企业资讯'],
        '107'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '钢厂动态', '', '', '企业资讯'],
        '175'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '新品开发', '', '', '企业资讯'],
        '176'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '经营管理', '', '', '企业资讯'],
        '189'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯 > </a><a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '公司概况', '', '', '企业资讯'],
        '192'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯 > </a><a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '财务数据', '', '', '企业资讯'],
        '193'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯 > </a><a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '公司公告', '', '', '企业资讯'],
        '194'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯 > </a><a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '公司报告', '', '', '企业资讯'],
        '114'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '', '企业动态', '企业动态', '企业资讯'],
        '239'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '钢厂资源', '', '', '企业资讯'],
        '266'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '行业政策', '', '', '企业资讯'],
        '077'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '成本分析', '', '', '企业资讯'],
        '282'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-003/'>&nbsp;> 企业资讯</a>", '钢厂库存', '', '', '企业资讯'],
        '111'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '国内统计', '国内统计', '国内统计', '统计资料'],
        '113'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '进出口统计', '进出口统计', '进出口统计', '统计资料'],
        '171'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '行业统计', '行业统计', '行业统计', '统计资料'],
        '172'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '海关统计', '海关统计', '海关统计', '统计资料'],
        '131'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '钢铁目录', '', '有色目录', '统计资料'],
        '132'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '钢铁标准', '', '有色标准', '统计资料'],
        '133'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '国内钢厂', '', '有色企业', '统计资料'],
        '135'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '', '炉料目录', '', '统计资料'],
        '137'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '', '国内企业', '', '统计资料'],
        '136'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '', '炉料标准', '', '统计资料'],
        '138'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-004/'>&nbsp;> 统计资料</a>", '企业名录', '企业名录', '', '统计资料'],
        '199'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/standard.php/'>&nbsp;> 标准汇编</a>", '', '', ''],
        '161'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '运输行情', '运输行情', '运输行情', '国内市场'],
        '162'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '拍卖价格', '拍卖价格', '拍卖价格', '国内市场'],
        '278'=>['dummy', "", '指数均值', '指数均值', '指数均值', '国内市场'],
        '269'=>['dummy', '', '普氏Platts每日行情', '', '普氏Platts金属数据'],
        '270'=>['dummy', '', '普氏Platts每周行情', '', '普氏Platts金属数据'],
        '271'=>['dummy', '', '普氏Platts每月行情', '', '普氏Platts金属数据'],
        '272'=>['dummy', '', '普氏Platts产量统计', '', '普氏Platts金属数据'],
        '273'=>['dummy', '', '普氏Platts发货量统计', '', '普氏Platts金属数据'],
        '274'=>['dummy', '', 'NYMEX', '', '普氏Platts金属数据'],
        '275'=>['dummy', '', 'LME', '', '普氏Platts金属数据'],
        '276'=>['dummy', '', 'SHFE', '', '普氏Platts金属数据'],
        '277'=>['dummy', '', '普氏Platts新闻', '', '普氏Platts金属数据'],
        '280'=>['dummy', '', '普氏Platts每季行情', '', '普氏Platts金属数据'],
        '285'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/bigcol-008/'>&nbsp;> 国内市场</a>", '市场指数', '市场指数', '市场指数', '市场指数']
    ];*/
    private $newsglypower = [
        '053'=>['dummy', "", '每日分析', '每日分析', '每日分析', '国内市场'],
        '055'=>['dummy', "", '每日行情', '每日行情', '每日行情', '国内市场'],
        '054'=>['dummy', "", '市场汇总', '', '', '国内市场'],
        '056'=>['dummy', "", '品种汇总', '行情汇总', '国内市场'],
        '057'=>['dummy', "", '每日分析', '每日分析', '每日分析', '国内市场'],
        '058'=>['dummy', "", '每周综述', '每周综述', '每周综述', '国内市场'],
        '240'=>['dummy', "", '每日短信', '每日短信', '每日短信', '国内市场'],
        '242'=>['dummy', "", '期现基差', '期现基差', '期现基差', '国内市场'],
        '059'=>['dummy', "", '国内综述', '国内综述', '国内综述', '国内市场'],
        '265'=>['dummy', "", '市场均价', '市场均价', '市场均价', '国内市场'],
        '186'=>['dummy', "", '钢市观察', '', '', '国内市场'],
        '063'=>['dummy', "", '市场库存', '市场库存', '市场库存', '国内市场'],
        '068'=>['dummy', "", '对外贸易', '对外贸易', '对外贸易', '国内市场'],
        '069'=>['dummy', "", '出口报价', '出口报价', '出口报价', '国内市场'],
        '196'=>['dummy', "", '期货评述', '期货评述', '期货评述', '国内市场'],
        '197'=>['dummy', "", '', '', '证券投资', '国内市场'],
        '070'=>['dummy', "", '普氏', '普氏', '普氏', '国内市场'],
        '072'=>['dummy', "", 'TSI', 'TSI', 'TSI', '国内市场'],
        '073'=>['dummy', "", '掉期', '掉期', '掉期', '国内市场'],
        '074'=>['dummy', "", 'SMX', 'SMX', 'SMX', '国内市场'],
        '075'=>['dummy', "", '招标', '招标', '招标', '国内市场'],
        '076'=>['dummy', "", '指数分析', '指数分析', '指数分析', '国内市场'],
        '235'=>['dummy', "", '企业库存', '企业库存', '企业库存', '国内市场'],
        '241'=>['dummy', "", '大商所', '大商所', '大商所', '国内市场'],
        '061'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际行情', '国际行情', '国际行情', '海外市场'],
        '185'=>['dummy', "", '期货行情', '期货行情', '期货行情', '海外市场'],
        '062'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际综述', '国际综述', '国际综述', '海外市场'],
        '060'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际贸易', '国际贸易', '国际贸易', '海外市场'],
        '170'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '海外钢厂', '海外企业', '海外企业', '海外市场'],
        '112'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际统计', '国际统计', '国际统计', '海外市场'],
        '134'=>['dummy', "<a href='#hwgs#'>&nbsp;> 海外市场</a>", '国际冶金', '国际冶金', '国际冶金', '海外市场'],
        '065'=>['dummy', "", '曾文工作室', '曾文工作室', '曾文工作室', '研究中心'],
        '064'=>['dummy', "", '国内研究', '国内研究', '国内研究', '研究中心'],
        '188'=>['dummy', "", '国际研究', '国际研究', '国际研究', '研究中心'],
        '140'=>['dummy', "", '技术应用', '技术应用', '技术应用', '研究中心'],
        '066'=>['dummy', "", '库存汇总', '库存汇总', '库存汇总', '研究中心'],
        '067'=>['dummy', "", '调价汇总', '调价汇总', '调价汇总', '研究中心'],
        '110'=>['dummy', "", '', '采购价格', '采购价格', '企业资讯'],
        '109'=>['dummy', "", '调价信息', '', '', '企业资讯'],
        '108'=>['dummy', "", '出厂价格', '出厂价格', '出厂价格', '企业资讯'],
        '115'=>['dummy', "", '计划检修', '', '', '企业资讯'],
        '106'=>['dummy', "", '行业动态', '行业动态', '行业动态', '企业资讯'],
        '107'=>['dummy', "", '钢厂动态', '', '', '企业资讯'],
        '175'=>['dummy', "", '新品开发', '', '', '企业资讯'],
        '176'=>['dummy', "", '经营管理', '', '', '企业资讯'],
        '189'=>['dummy', "<a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '公司概况', '', '', '企业资讯'],
        '192'=>['dummy', "<a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '财务数据', '', '', '企业资讯'],
        '193'=>['dummy', "<a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '公司公告', '', '', '企业资讯'],
        '194'=>['dummy', "<a href='".APP_URL_WWW."/MessageCenter_Scompany.php?column=008'>上市公司</a>", '公司报告', '', '', '企业资讯'],
        '114'=>['dummy', "", '', '企业动态', '企业动态', '企业资讯'],
        '239'=>['dummy', "", '钢厂资源', '', '', '企业资讯'],
        '266'=>['dummy', "", '行业政策', '', '', '企业资讯'],
        '077'=>['dummy', "", '成本分析', '', '', '企业资讯'],
        '282'=>['dummy', "", '钢厂库存', '', '', '企业资讯'],
        '111'=>['dummy', "", '国内统计', '国内统计', '国内统计', '统计资料'],
        '113'=>['dummy', "", '进出口统计', '进出口统计', '进出口统计', '统计资料'],
        '171'=>['dummy', "", '行业统计', '行业统计', '行业统计', '统计资料'],
        '172'=>['dummy', "", '海关统计', '海关统计', '海关统计', '统计资料'],
        '131'=>['dummy', "", '钢铁目录', '', '有色目录', '统计资料'],
        '132'=>['dummy', "", '钢铁标准', '', '有色标准', '统计资料'],
        '133'=>['dummy', "", '国内钢厂', '', '有色企业', '统计资料'],
        '135'=>['dummy', "", '', '炉料目录', '', '统计资料'],
        '137'=>['dummy', "", '', '国内企业', '', '统计资料'],
        '136'=>['dummy', "", '', '炉料标准', '', '统计资料'],
        '138'=>['dummy', "", '企业名录', '企业名录', '', '统计资料'],
        '199'=>['dummy', "<a href='".APP_URL_NEWS."/#ch#/standard.php/'>&nbsp;> 标准汇编</a>", '', '', ''],
        '161'=>['dummy', "", '运输行情', '运输行情', '运输行情', '国内市场'],
        '162'=>['dummy', "", '拍卖价格', '拍卖价格', '拍卖价格', '国内市场'],
        '278'=>['dummy', "", '指数均值', '指数均值', '指数均值', '国内市场'],
        '269'=>['dummy', '', '普氏Platts每日行情', '', '普氏Platts金属数据'],
        '270'=>['dummy', '', '普氏Platts每周行情', '', '普氏Platts金属数据'],
        '271'=>['dummy', '', '普氏Platts每月行情', '', '普氏Platts金属数据'],
        '272'=>['dummy', '', '普氏Platts产量统计', '', '普氏Platts金属数据'],
        '273'=>['dummy', '', '普氏Platts发货量统计', '', '普氏Platts金属数据'],
        '274'=>['dummy', '', 'NYMEX', '', '普氏Platts金属数据'],
        '275'=>['dummy', '', 'LME', '', '普氏Platts金属数据'],
        '276'=>['dummy', '', 'SHFE', '', '普氏Platts金属数据'],
        '277'=>['dummy', '', '普氏Platts新闻', '', '普氏Platts金属数据'],
        '280'=>['dummy', '', '普氏Platts每季行情', '', '普氏Platts金属数据'],
        '285'=>['dummy', "", '市场指数', '市场指数', '市场指数', '市场指数'],
    ];

    private $columnNavType = [
        // "01" => "4",
        '02' => '5',
        "04" => "11",
        "08" => "29",
        "11" => "32",
        "12" => "8",
        "15" => "14",
        "19" => "20",
        // "25" => "23",
        "26" => "17",
        "27" => "26",
    ];

    private $isShowOldPage = 1;

    private $isCustomerNews = 0;

    private $copyright = COPYRIGHT;

    public function __construct()
    {
        parent::__construct();
    }

    public function index($params) {
        $nid = $params['nid'];
        $ntype = $params['ntype'];  // 接收m|n
        $typenum = $ntype=="n"?2:1;
        $invitationcode = $params['invitationcode']??"";
        $isxgw = $params['isxgw']??0;
        if (!preg_match("/^[0-9]{1,10}$/", $nid) || ($ntype!='m' && $ntype!='n')) {
            echo "资讯ID错误";exit;
        }
        $newsInfo = $this->getNewsData($nid, $ntype);
        // dd($newsInfo);exit;
        $ShowMode = $_COOKIE['ShowModeC'];//1： 网页版 2：WAP
        $isMobile = $this->isMobile();
        if ($isMobile && ($ShowMode == 2 || $ShowMode == '')) {
            require_once('/etc/steelconf/env/news_env.php');
            $sessut = $_GET['sessut'];
            $nid = $_GET['nid'];
            $newsmrhq = $_GET['ntype'];
            $my = $_GET['year']??date("Y", strtotime($newsInfo['date']));
            $mm = $_GET['mon']??date("m", strtotime($newsInfo['date']));
            $md = $_GET['day']??date("d", strtotime($newsInfo['date']));
            $userid = $_GET['userid'];
            $invitationcode = $_GET['invitationcode'];
            $url = APP_URL_NEWS . '/wap/testlogin/news3/messageshow.php?year=' . $my . '&mon=' . $mm . '&day=' . $md . '&newstype=' . $newsmrhq . '&nid=' . $nid . '&invitationcode=' . $invitationcode;
            echo "<script>location.href='$url'</script>";
            exit;
        }
        list($islogin, $token, $loginInfo) = $this->detail_islogin();
        // $loginInfo['memberBo']['whereFrom'] = 51;
        $adminName = $loginInfo['memberBo']['adminName']??"";
        $mid = $loginInfo['memberBo']['mid']??"";
        $companyShortName = $loginInfo['memberBo']['companyShortName']??"您";
        $adminId = $loginInfo['memberBo']['adminId']??"";
        $userid = $loginInfo['sysUser']['userId']??"";
        // 权限；文档中频道参数多余，不需要传
        $tpower = $this->getPower(['type'=>$typenum, 'nid'=>$nid, 'invitationcode'=>$invitationcode]);
        $iscanlook = $tpower['powerFlag']??0;  // =0则无权限查看
        // 临时处理方案
        if(empty($tpower['tips'])) {
            $tpower = $this->getPower(['type'=>$typenum, 'nid'=>$nid, 'invitationcode'=>$invitationcode]);
            $iscanlook = $tpower['powerFlag']??0;  // =0则无权限查看
            if(empty($tpower['tips'])) {
                $tpower = $this->getPower(['type'=>$typenum, 'nid'=>$nid, 'invitationcode'=>$invitationcode]);
                $iscanlook = $tpower['powerFlag']??0;  // =0则无权限查看
            }
        }
        // print_r($newsInfo);exit;
        // echo $newsInfo['channelid'];exit;
        $this->handlePublicSettingData(["channelId"=>$newsInfo['channelid']]);
        $params['redisKey'] = "1=".$newsInfo['channelid']."=055===";
        $baseTableData = array();
        $baseTableData['columnShowList'] = $this->getColumnShowDataList();
        $baseTableData['cityShowList'] = $this->getcityShowDataList($params['redisKey']);
        $baseTableData['varietyList'] = $this->getMarkVarietyList();
        
        $error = 0;
        if(!empty($newsInfo)){
            // 所有频道
            $channelList = $this->getChannelList();
            foreach ($channelList as $value) {
                if ($value['channelid'] == explode(",", $newsInfo['channelid'])[0]) {
                    $newsInfo['channelname'] = $value['channelname'];
                    $newsInfo['channelcode'] = $value['channelcode'];
                    break;
                }
            }
            
            $is_show_errorstr_page = 0;
            $intercept = 1; //0不截取
            $tips = '';
            $iscopy = $tpower['copyPower'];
            $errstr = '';
            $Premoney = 0;
            $NewsPrice = 0;
            $payUrl = [];
            $isshowlogintips = 1;
            $zhifu_display = "";
            $is_show_invitationcode = 0;
            $rights = $newsInfo['rights'];
            $columnid = $newsInfo['columnid'];
            $this->redirectCheck($newsInfo);

            // $headerDir = $this->headerDir($newsInfo, $channelList);
            // 当前位置
            list($linknext) = $this->set_linknext($newsInfo, $channelList);
            if(isset($loginInfo['memberBo']['whereFrom']) && $loginInfo['memberBo']['whereFrom'] == 51) {
                // dump($linknext);
                $linknext = preg_replace('/href[\s]*=[\s]*"[^"]*"/i', 'href="javascript:void(0);"', $linknext);
                $linknext = preg_replace("/href[\s]*=[\s]*'[^']*'/i", 'href="javascript:void(0);"', $linknext);
                // dd($linknext);
            }

            // 客户发布的专栏使用的免责声明
            if($this->isCustomerNews == 1) {
                $this->copyright = "本信息中的陈述、数据和观点，仅代表专栏作者个人，不构成对任何机构或者个人的市场操作建议或者投资依据。未经钢之家书面许可，任何机构和个人不得以任何形式转载、复制和对外发布。如引用、转载或者刊发，需征得钢之家授权同意，并注明出处为钢之家，且不得对本评述进行有悖原意的引用、删节和修改。";
            }

            $aa = $this->getRedisDataObjectByRedisKey(['redisKeyString'=>'steelHomeCache:SteelHome:adminuser:'.$adminId]);
            $tela = $aa['data']['tel'];
            $mobil = $aa['data']['mobil'];
            if (!empty($mobil) && !empty($tela)) {
                $tishi_message = $mobil . '或' . $tela . '或4008115058';
            } else if (empty($mobil) && !empty($tela)) {
                $tishi_message = $tela . '或4008115058';
            } else if (!empty($mobil) && empty($tela)) {
                $tishi_message = $mobil . '或4008115058';
            } else {
                $tishi_message = '4008115058';
            }
            if($islogin==0) {
                $is_show_invitationcode = 0;
                if($iscanlook=='1') {
                    $intercept = 0;
                    $isshowlogintips = 0;
                } else {
                    if(!$islogin) {
                        goURL(APP_URL_WWW."/MemberLogin.php?urlstr=%2Fdetailspage.php%3Fnid%3D".$nid."%26ntype%3D".$ntype);
                        exit;
                    }
                }
            } else {
                // 已登录但是无权限查看，需要弹出支付页面
                if($iscanlook=='0') {
                    $sub_date = ceil((strtotime(date("Y-m-d H:i:s")) - strtotime($newsInfo['date'])) / 86400);
                    if ($sub_date > $rights) {
                        $rights = 0;
                    }
                    $is_show_errorstr_page = 1;
                    $newsInfo['invitationcode'] = $newsInfo['invitationcode']==""&&$newsInfo['type']=='n'?$tpower['invitationcode']:$newsInfo['invitationcode'];
                    if($newsInfo['invitationcode']!="" && $rights>0) {
                        $is_show_invitationcode = 1;
                    }
                    if(preg_match('/^，/', $tpower['tips'])) {
                        $tpower['tips'] = preg_replace('/，/', '', $tpower['tips'], 1);
                    }
                    // $tpower['tips'] = "";
                    if(!empty($tpower['tips']) && $tpower['tips'] != '') {
                        $tips = '尊敬的' . $loginInfo['memberBo']['companyShortName'] . '领导，' . $tpower['tips'];
                    } else {
                        $tips = '内容调取出错了，请联系管理员';
                    }

                    if ($adminName == '') {
                        $errstr = '<br/>具体请与您的客服联系：' . $tishi_message;
                    } else {
                        if($adminId == "17") {
                            $tishi_message = "021-50581010或4008115058";
                            $errstr = '<br/>请与管理员联系：' . $tishi_message;
                        } else {
                            $errstr = '<br/>具体请与您的管理员联系：' . $adminName . ' ' . $tishi_message;
                        }
                    }
                    $fuqian_url = 'ntype=' . $newsInfo['type'] . '&nid=' . $nid . '&userid=' . $userid . '&mid=' . $mid . '&Is_Mobile=0&rights=0&isnew=1';
                    //付钱url
                    $payUrl = [
                        'zhifubao' => APP_URL_WWW . '/zhifubao.php?' . $fuqian_url,
                        'weixin' => APP_URL_WWW . '/weixin.php?' . $fuqian_url,
                        'yue' => APP_URL_WWW . '/yue.php?' . $fuqian_url,
                        'chongzhi' => APP_URL_WWW . '/fuqianla/prepay.php?' . $fuqian_url,
                    ];
                    $Premoney = $this->getBalance();
                    $NewsPrice = $this->getNewsPrice(['uniqueid'=>$ntype.$nid]);
                    if($rights > 0 || empty($NewsPrice)){
                        // $is_show_errorstr_page = 1;
                        $zhifu_display = "display:none;";
                        // echo $this->isCustomerNews;exit;
                        if($this->isCustomerNews==1) {
                            $zhifu_display = "";
                        }
                    }
                    // 客户发布的资讯免费的时候，直接显示所有的内容
                    // if($this->isCustomerNews==1 && $NewsPrice == '0') {
                    //     $intercept = 0;
                    //     $is_show_errorstr_page = 0;
                    // }

                } else {
                    $intercept = 0;
                    if($rights>0 && $newsInfo['invitationcode']!="") {
                        if($newsInfo['invitationcode']!=$invitationcode) {
                            $is_show_invitationcode = 1;
                        }
                    }

                }
                $isshowlogintips = 0;
            }
            // $intercept = 0;
            // 获取文章内容
            // $resContentInfo = $this->getNewsInfo(['nid'=>$nid, 'ntype'=>$ntype, 'intercept'=>$intercept]);
            $resContentInfo = $this->getNewsInfo(['nid' => $nid, 'ntype' => $ntype, 'intercept' => 0]);
            $wxShareDesc = $resContentInfo['ncontents'];
            // 如果ES接口有问题的话就直接读取资讯源文件
            if(empty($resContentInfo) || (isset($resContentInfo['ncontents']) && trim($resContentInfo['ncontents'] == ''))) {
                if($iscanlook=='1') {
                    $a =  file_get_contents(APP_URL_WWW."/_v2app/get_information_content.php?filepath=".$newsInfo['filepath']."&getToken=".CODE_CONTAIN_TOKEN_PUBLIC, false, stream_context_create(["ssl" => ["verify_peer"=>false,"verify_peer_name"=>false]]));
                    $arr = json_decode($a, true);
                    $resContentInfo['ncontents'] = $arr['contents'];
                    $wxShareDesc = $arr['contents'];
                }
            }
            $jiequPdf = 0;
            // 无权限的情况
            if($iscanlook!='1') {
                $resContentInfo = $this->getNewsInfo(['nid' => $nid, 'ntype' => $ntype, 'intercept' => 1]);
                $jiequPdf = 1;
            }
            // dd($resContentInfo);
            // '26' =>'一周市场研究报告’
            // '27'=>'周中市场研究报告'
            // '28'=>'周五市场研究报告'

            // "29'=>"华东一周市场研究报告"
            // '30'=>’中西一周市场研究报告!
            // "31'=>"北方一周市场研究报告”
            // '32'=>"国际一周市场研究报告"
            // '33'=>"研究院一周市场研究报告

            // '34'=>"特钢棒线材及不锈钢一周市场研究报告
            // '35'=>"炉料一周市场报告’
            // '36'=>"煤焦一周市场研究报告’
            // dd($wxShareDesc);
            $baogao_tips = '';
            $newsInfo['pdfView'] = "";
            // if($nid == "4413218" || str_contains($columnid, '007,307')) {
            if(str_contains($columnid, '007,307') || $nid == "4484752") {
                // 市场报告栏目
                [$charCount, $charCountStr, $pdfPath] = $this->getPdf($wxShareDesc, $nid, $tpower['reportType'], $jiequPdf);
                if($adminId == "17") {
                    $tishi_message = "021-50581010或4008115058";
                    $lx = "，请与管理员联系";
                } else {
                    $lx = "，请与管理员联系：".$adminName."，联系电话：".$tishi_message;
                }
                $baogao_tips = "<p style='font-family:华文楷体;'>　　本报告仅提供给钢之家市场报告单独购买客户查看，如您已购买".$lx."。";
                if(in_array($tpower['reportType'], ['26', '27', '28'])) {
                    $baogao_tips .= "<br>　　报告简介：本报告聚焦国内外矿煤焦钢市场变化及后市预判，旨在为钢铁产业链客户提供全面的市场分析和参考依据，每周更新三次（周六、周三、周五发布），全年共计150期。报告内容涵盖主要钢材市场（华东、北方、中南及西部）、主要钢材品种（建筑钢材、热轧板卷、中厚板、冷轧板卷、电工钢、涂镀板卷、大中型材、带钢、优特钢、工业线材、不锈钢）、主要原燃料品种（焦炭、煤炭、铁矿石、钢坯、废钢、铁合金）的市场/钢厂价格、库存、检修、成交情况等以及国际市场、宏观数据、宏观要闻、期货市场等影响分析，同时提供主要观点、后市预测及操作建议等。<br>";
                }
                $baogao_tips .= "</p>";
                $tttt = explode("/uploadfile", $pdfPath);
                $pdfDownloadPath = $pdfPath;
                if(count($tttt)>1) {
                    $pdfDownloadPath = APP_URL_WWW."/uploadfile".$tttt[1];
                }
                $pdfDownloadButton = '<a class="download-btn" onclick="downPdf('.$jiequPdf.',\''.$pdfDownloadPath.'\')" download><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="white" viewBox="0 0 24 24"><path d="M5 20h14v-2H5v2zM11 4h2v9h3l-4 4-4-4h3V4z"/></svg>下载完整版报告</a>';
                $newsInfo['pdfView'] = "<br>".$pdfDownloadButton."<iframe src='/js/pdf/web/viewer.html?file=".$pdfPath."' style='width:100%;height: 640px;margin-bottom: 10px;'></iframe>";
                if($jiequPdf) {
                    $c = strip_tags($wxShareDesc);
                    $c = str_replace([" ","\t", "&nbsp;","\u{3000}"], ["", "", "", ""], $c);
                    if(mb_strlen($c) > 100) {
                        $c = mb_substr($c, 0, 100, "utf-8")."...";
                    }
                    $resContentInfo['ncontents'] = "<br><p style='text-indent: 2em;margin-bottom:-10px;'>".$c."</p>";
                }
            }
            //判断是否受监控阅读量
            $monitorVisitsInfo = $this->getRedisDataObjectByRedisKey(["redisKeyString"=>REDIS_CACHE_KEY_PER_STRING.":MonitorVisits:".substr($mid,0,2).":".$mid]);
            $newsInfo['content'] = $this->convertContent($resContentInfo, $newsInfo, $intercept);
            if ( $mid != 1 && isset($monitorVisitsInfo['data']) && isset($monitorVisitsInfo['data']['maxnum'])) {
                $allowMaxVisitsNum = $GLOBALS['MEMBER_MAX_READER_NUM'][$monitorVisitsInfo['data']['maxnum']];
                $visitLogInfo = $this->getAccessLogList(["date"=>date("Y-m-d"),"pageNum"=>1,"pageSize"=>$allowMaxVisitsNum]);
                if ($visitLogInfo && count($visitLogInfo) >= $allowMaxVisitsNum) {

                    if ($adminName == '') {
                        $errstr = '具体请与您的客服联系：' . $tishi_message;
                    } else {
                        $errstr = '具体请与您的管理员联系：' . $adminName . ' ' . $tishi_message;
                    }
                    $is_show_errorstr_page = 0;
                    $newsInfo['content'] = "<h3>您的访问量异常，超过限制，".$errstr."</h3>";
                    $sendMessageUrl = APP_URL_WWW."/cron_97_system/qiyeweixin/front_page_monitor_visit_message.php?requestToken=".CODE_CONTAIN_TOKEN_PUBLIC."&content=会员id：".$mid.",用户id：".$userid."访问内容详情页时超过后台监控限制的浏览量";
                //    file_get_contents($sendMessageUrl, false, stream_context_create(["ssl" => ["verify_peer"=>false,"verify_peer_name"=>false]]));
                }elseif($intercept== 1 && $newsInfo['type']=='m') {
                    $newsInfo['content'] = "";
                }
            }else{
                if($intercept== 1 && $newsInfo['type']=='m') {
                    $newsInfo['content'] = "";
                }
            }

            // 中联重科处理
            if(isset($loginInfo['memberBo']['whereFrom']) && $loginInfo['memberBo']['whereFrom'] == 51) {
                // 去掉$newsInfo['content']中的a标签的href链接
                /* $newsInfo['content'] = preg_replace('/<a.*?href="(.*?)".*?>(.*?)<\/a>/i', '\2', $newsInfo['content']); */
                $newsInfo['content'] = preg_replace('/href[\s]*=[\s]*"[^"]*"/i', 'href="javascript:void(0);"', $newsInfo['content']);
                $newsInfo['content'] = preg_replace("/href[\s]*=[\s]*'[^']*'/i", 'href="javascript:void(0);"', $newsInfo['content']);
            }
            // 画中画
            $this->hzh($newsInfo, $loginInfo);
            // 是否关注
            $newsInfo['isFollow'] = $this->followStatus($newsInfo);
            // 页面表格的头部筛选功能<只有行情需要>
            $filter = "";
            if ($newsInfo["channelid"] != null && $ntype=="m" && $intercept=='0') {
                $filter = "filter";
            // }elseif ( $newsInfo["channelid"] != null && $ntype=="n" ){
            //     $filter = "filter2";
            }
            // print_r($newsInfo);exit;
            // 设置广告参数传到前台页面
            list($wenzi, $advStr) = $this->setAdvParams($newsInfo, $intercept);

            // 右侧的相关资讯
            $related_news = $this->getRelatedNewsList($newsInfo);
            // 右侧新闻列表
            $right_news = $this->getRightNewsList($newsInfo);
            // print_r($right_news[1]);exit;
            if($newsInfo['channelcode'] == 'zh') {
                $this->assign("pageName", '综合资讯');
            }
            // $ContentInfo = $this->getNewsInfo(['nid' => $nid, 'ntype' => $ntype, 'intercept' => 0]);
            // $wxShareDesc = $ContentInfo['ncontents'];
            $weixin_desc = str_replace('[{$t_h}]',"",$wxShareDesc);
            $weixin_desc = str_replace("\r\n"," ",$weixin_desc);
            $weixin_desc = str_replace("\n"," ",$weixin_desc);
            $weixin_desc = str_replace("　　","",$weixin_desc);
            $weixin_desc = str_replace(" ","",$weixin_desc);
            $weixin_desc = str_replace("	","",$weixin_desc);
            $weixin_desc = mb_substr(strip_tags($weixin_desc), 0, 35);
            if( $weixin_desc == "" ){
                $weixin_desc = $newsInfo['title'];
            }
            $weixin_desc .= "...";
            $params['pageTitle'] = $newsInfo['title'];
            $params['description'] = $weixin_desc;
            $pingjia_html = $this->get_pingjia_list(['newsid' => $nid, 'memberid' => $mid, 'type' => $ntype]);

            $temp_tips_html = "";
            if(str_contains($newsInfo['title'], "中国进口锰矿石外盘价格")) {
                $temp_tips_html = '<p style="font-size: 16px;">2025年6月9日起，将中国进口锰矿石外盘价格从价格行情栏目下的“锰系”迁移至“品种汇总”，行情标题改为<a href="https://www.steelhome.com/1=15=002,056%7C001,056===/1.html" style="color: #3a7dfa;text-decoration: underline;">国内市场（进口）锰矿石行情汇总。</a></p>';
            } else if(str_contains($newsInfo['title'], "北京地区镁锭价格行情")) {
                $temp_tips_html = '<p style="font-size: 16px;">2025年6月9日起，北京地区镁锭价格行情不再更新，如需要参考镁锭市场行情，请前往铁合金频道中价格行情中“镁系”栏目，行情标题为<a href="https://www.steelhome.com/1=15=002,055%7C001,055=704%7C710%7C711==/1.html" style="color: #3a7dfa;text-decoration: underline;">国内市场金属镁价格行情。</a></p>';
            } else if(str_contains($newsInfo['title'], "天津地区镁锭价格行情")) {
                $temp_tips_html = '<p style="font-size: 16px;">2025年6月9日起，天津地区镁锭价格行情不再更新，如需要参考镁锭市场行情，请前往铁合金频道中价格行情中“镁系”栏目，行情标题为<a href="https://www.steelhome.com/1=15=002,055%7C001,055=704%7C710%7C711==/1.html" style="color: #3a7dfa;text-decoration: underline;">国内市场金属镁价格行情。</a></p>';
            } else if(str_contains($newsInfo['title'], "济南地区镁锭价格行情")) {
                $temp_tips_html = '<p style="font-size: 16px;">2025年6月9日起，济南地区镁锭价格行情不再更新，如需要参考镁锭市场行情，请前往铁合金频道中价格行情中“镁系”栏目，行情标题为<a href="https://www.steelhome.com/1=15=002,055%7C001,055=704%7C710%7C711==/1.html" style="color: #3a7dfa;text-decoration: underline;">国内市场金属镁价格行情。</a></p>';
            } else if(str_contains($newsInfo['title'], "沈阳地区镁锭价格行情")) {
                $temp_tips_html = '<p style="font-size: 16px;">2025年6月9日起，沈阳地区镁锭价格行情不再更新，如需要参考镁锭市场行情，请前往铁合金频道中价格行情中“镁系”栏目，行情标题为<a href="https://www.steelhome.com/1=15=002,055%7C001,055=704%7C710%7C711==/1.html" style="color: #3a7dfa;text-decoration: underline;">国内市场金属镁价格行情。</a></p>';
            } else if(str_contains($newsInfo['title'], "无锡地区镁锭价格行情")) {
                $temp_tips_html = '<p style="font-size: 16px;">2025年6月9日起，无锡地区镁锭价格行情不再更新，如需要参考镁锭市场行情，请前往铁合金频道中价格行情中“镁系”栏目，行情标题为<a href="https://www.steelhome.com/1=15=002,055%7C001,055=704%7C710%7C711==/1.html" style="color: #3a7dfa;text-decoration: underline;">国内市场金属镁价格行情。</a></p>';
            }

            $this->assign('temp_tips_html', $temp_tips_html);
            $this->assign('pingjia_html', $pingjia_html);
            $this->assign("baseTableData", $baseTableData);
            $this->assign('islogin', $islogin);
            $this->assign('Premoney', $Premoney);
            $this->assign('NewsPrice', $NewsPrice);
            $this->assign('zhifu_display', $zhifu_display);
            $this->assign('userid', $userid);
            $this->assign('mid', $mid);
            $this->assign('advStr', $advStr);
            $this->assign('iscopy', $iscopy);
            $this->assign('wenzi', $wenzi);
            $this->assign('isshowlogintips', $isshowlogintips);
            $this->assign('is_show_pay_page', $is_show_errorstr_page);
            $this->assign('is_show_invitationcode', $is_show_invitationcode);
            $this->assign('payUrl', $payUrl);
            $this->assign('errstr', $errstr);
            $this->assign('baogao_tips', $baogao_tips);
            $this->assign('tips', $tips);
            $this->assign('filter', $filter);
            $this->assign('linknext', $linknext);
            $this->assign('channelCode', $newsInfo['channelcode']);
            $this->assign('channelId', $newsInfo['channelid']);
            $this->assign('right_news', $right_news);
            $this->assign('related_news', $related_news);
            $this->assign('intercept', $intercept);
            $this->assign('isShowOldPage', $this->isShowOldPage);
            if(str_contains($newsInfo['title'], "日国内钢市")) {
                $this->assign('css', ".info_details td {font-size: 12px !important;}.info_details table tr td:nth-child(1) {width: 50px !important;}");
            }
        } else {
            $error = 1;
            $newsInfo['title'] = "内容已不存在，更多信息去首页看看吧~";
        }

        $this->assign("companyShortName", $companyShortName);
        $this->assign("newsInfo", $newsInfo);
        $this->assign('error', $error);
        $this->assign('iscanlook', $iscanlook);
        $this->assign('copyright', $this->copyright);
        $this->assign('isMobile', $isMobile);
        $this->assign('isxgw', $isxgw); //型钢网三级页面嵌套控制
        $this->assign("urlParams", urlencode("/detailspage.php?".$_SERVER['QUERY_STRING']));
        $this->handlePublicData($params);
    }

    // 重定向到其他站点的三级页面
    private function redirectCheck($newsInfo) {
        $channelid = explode(",", $newsInfo['channelid']);
        // dd($channelid[0]);
        // dd($newsInfo);
        switch ($channelid[0]) {
            case '10':
                // 不锈钢
                $u = 'news';
                if($newsInfo['type'] =='m') $u = 'mrhq';
                goURL(APP_URL_EBUXIU."/news/{$u}?id={$newsInfo['id']}");
                break;
            case '17':
                // 化工/messageshow.php?newstype=m&nid=11782301
                goURL(APP_URL_HG."/messageshow.php?newstype=".$newsInfo['type']."&nid=".$newsInfo['id']);
                break;
            case '25':
                // 新能源
                // goURL(APP_URL_NEWS."/".$newsInfo['type'].$newsInfo['id']);
                goURL($newsInfo['oldUrl']);
                break;
            case '40':
                // 黑色系大宗商品
                $dz_lm_tmp_l = explode("030,", $newsInfo['columnid']);
                $column_id = "";
                if(isset($dz_lm_tmp_l[1])) {
                    $column_id = substr($dz_lm_tmp_l[1], 0, 3);
                }
                goURL( APP_URL_DZWZ."/bulkmetals/detailspage.php?nid={$newsInfo['id']}&ntype=n&column_id={$column_id}");
                break;
            default:
                # code...
                break;
        }
    }

    // 获取并整合news、news_mrhq表的数据
    private function getNewsData($nid, $ntype) {
        $info = [];
        if ($ntype == 'n') {
            $tableData = $this->getNewsTableInfo(['nid' => $nid]);
            // print_r($tableData);exit;
            if(isset($tableData['nid'])) {
                $info['id'] = $nid;
                $info['type'] = $ntype;
                $info['typenum'] = '2';
                $info['columnid'] = $tableData['ncolumnid'];
                $info['varietyid'] = $tableData['nvarietyid'];
                $info['cityid'] = $tableData['ncityid'];
                $info['moban'] = $tableData['moban'] ?? '1';
                $info['hzh'] = $tableData['hzh'] ?? '1';
                $info['channelid'] = $tableData['nchannelid'];
                $info['title'] = $tableData['ntitle'];
                $info['date'] = date("Y-m-d H:i:s", strtotime($tableData['ndate']));
                $info['isimport'] = $tableData['isimport'];
                $info['invitationcode'] = $tableData['invitationcode'];
                $info['readnumber'] = $tableData['readnumber'];
                $info['adminid'] = $tableData['nmanageid'];
                $info['gc_name'] = $tableData['gcName'];
                $info['gc_name2'] = $tableData['gcName2'];
                $info['is_super_admin'] = $tableData['isSuperAdmin'];
                $info['rights'] = $tableData['rights'];
                $info['summary'] = $tableData['summary'];
                $info['filepath'] = $tableData['filepath'];
                $info['focus'] = $tableData['nfocus'];
                $info['title2'] = $tableData['ntitle2'];
                $info['keys'] = $tableData['nkeys'];
            }
        } else if($ntype == 'm') {
            $tableData = $this->getNewsMrhqTableInfo(['nid' => $nid]);
            if(isset($tableData['newsid'])) {
                $info['id'] = $nid;
                $info['type'] = $ntype;
                $info['typenum'] = '1';
                $info['hzh'] = '1';
                $info['columnid'] = $tableData['columnid'];
                $info['varietyid'] = $tableData['varietyid'];
                $info['cityid'] = $tableData['cityid'];
                $info['moban'] = '1';
                if ($info['cityid'] == '' || $info['columnid'] == '285') {
                    $info['moban'] = '2';
                }
                switch ($tableData['ischannel']) {
                    case '2':
                        $info['channelid'] = '02';
                        break;
                    case '3':
                    case '4':
                        $info['channelid'] = '04';
                        break;
                    case '8':
                        $info['channelid'] = '08';
                        break;
                    default:
                        $info['channelid'] = $tableData['ischannel'];
                        break;
                }
                $info['title'] = $tableData['ntitle'];
                $info['date'] = $tableData['ndate'];
                $info['isimport'] = $tableData['isimport'];
                $info['readnumber'] = $tableData['readnumber'];
                $info['adminid'] = $tableData['adminid'];
                $info['filepath'] = $tableData['filepath'];
                $info['focus'] = $tableData['isfocus'];
                $info['title2'] = "";  //行情无副标题
                $info['keys'] = $tableData['nkeys'];
            }
        }
        // $info['moban'] = 2;
        if(isset($tableData['ndate']) && $tableData['ndate'] != '') {
            $YMD = date("Y/m/d", strtotime($tableData['ndate']));
            $info['oldUrl'] = APP_URL_NEWS."/".$YMD."/".$info['type'].$info['id'].".html?type=old";
        }

        // 欢迎关注钢之家网站新媒体平台这一条资讯的日期需要变动
        if($info['id'] == '4050597' && $info['type'] == 'n') {
            $info['date'] = date('Y-m-d');
        }

        return $info;
    }

    // 头部的栏目、品种、城市三行导航栏
    private function headerDir($newsInfo,$channelList) {
        $headerDir = '/';
        $columnNavType = $this->columnNavType;
        $channelid = explode(",", $newsInfo['channelid'])[0]; // 多个频道的时候取第一个频道
        // echo $channelid;exit;
        if (in_array($channelid, array_keys($columnNavType))) {
            foreach ($channelList as $channelValue) {
                if ($channelValue['channelid'] == $channelid) {
                    $headerDir = $channelValue['channelcode'] . '/';
                    break;
                }
            }
        }
        // $columnType = $columnNavType[$channelid] ?? '5';
        // $columnNavigation = $this->getSteelHomeDataPictureList(['type' => $columnType]);
        // $varietyNavigation = $this->getSteelHomeDataPictureList(['type' => (int)$columnType + 1]);
        // $cityNavigation = $this->getSteelHomeDataPictureList(['type' => (int)$columnType + 2]);
        // $columnNavigation = json_decode($columnNavigation, true);
        // $varietyNavigation = json_decode($varietyNavigation, true);
        // $cityNavigation = json_decode($cityNavigation, true);
        // return [$headerDir, $columnNavigation, $varietyNavigation, $cityNavigation];
        return $headerDir;
    }

    // 处理content正文
    private function convertContent($data, $table, $intercept) {
        $con = $data['ncontents'];
        $summary = $table['summary'];
        $channelid = $table['channelid'];
        $type = $table['type'];
        $ntitle = $data['ntitle'];
        $con = str_replace('/usr/local/www/www.steelhome.cn', APP_URL_WWW, $con);
        $con = str_replace('[{##}]', "", $con);
        $con = str_replace('\\"', '"', $con);
        $con = str_replace("\\'", '\'', $con);
        $con = str_replace(APP_URL_OA, APP_URL_WWW, $con);
        $con = preg_replace("/bgstyle=.+?color:.+?#([0-9a-fA-F]{6})./i", "style='background-color: #$1'", $con);
        $con = preg_replace("/\scolor=[\"|']?#([0-9a-fA-F]{6})[\"|']?/i", "  style='color: #$1'", $con);
        $con = preg_replace("/\scolor=[\"|']?(green|red)[\"|']?/i", "  style='color: $1'", $con);
        // $con = preg_replace("/https:\/\/news.steelhome.com\/gc\/col-055\/var-c02\/0025.shtml/i", "/1=02=002,055=102=0025=/1.html", $con);
        if($type=='n' && $intercept==0 && $channelid=='11') {
            // 煤焦频道这两类资讯表格高度问题处理
            // https://localwww.steelhome.com/detailspage.php?nid=4098766&ntype=n
            // https://localwww.steelhome.com/detailspage.php?nid=4097747&ntype=n
            $isTable = preg_match("/(<table.*?)height=[\"|']?100%[\"|']?/i", $con);
            if($isTable) {
                $con = preg_replace("/(<table.*?)height=[\"|']?100%[\"|']?/i", "$1", $con)."<style>td{line-height:150%}</style>";
            }
        }

        if($type=='n' && $summary != "" && $intercept==0) {
            $this_zy = '<span  class="news_show_summary_bt">【摘要】</span><span class="news_show_summary_ct">' . $summary . '</span><br><br>';
            // 钢之家：9月4日铁矿石日报 不显示摘要
            if ($data['ncolumnid'] === "002,057" && 
                preg_match('/^钢之家：\d{1,2}月\d{1,2}日(铁矿石|废钢|钢坯)日报$/u', $ntitle)) {
                $this_zy = "";
            }
            $con = $this_zy.$con;
        }

        $pdfView = $table['pdfView'];
        // 正文最后的编撰信息统一替换处理
        $pattern = "/[（(](采编|编撰|来源)[:：].*?[）)]/u";
        if(preg_match($pattern, $con, $match)) {
            $con = preg_replace($pattern, "", $con);
            if($pdfView) {
                $con = str_replace("<p>	</p>", "", $con);
                $con .= $pdfView;
            }
            $con .= "<p style='font-family:华文楷体;margin-top: 1em;margin-bottom: 1em;' id='content_foot'>".$match[0]."</p>";
        } else {
            if($pdfView) {
                $con .= $pdfView;
            }
        }

        // 007,307 市场报告栏目删掉pdf的下载链接
        if($pdfView) {
            $pattern = '/<a\s+[^>]*href\s*=\s*"https?:[^"]*?\.pdf"[^>]*>(.*?)<\/a>/is';
            if(preg_match($pattern, $con, $match22)) {
                $con = preg_replace(
                    $pattern,
                    '$1',
                    $con
                );
            // } else {
            //     $con .= $pdfView;
            }
        }
        // dd($con);
        return $con;
    }

    // 处理画中画（画中画也可设置为广告）
    private function hzh(&$data, &$loginInfo) {
        $t_h = "";
        $cityid = $data['cityid']??'';
        $nid = $data['id']??'';
        $ntype = $data['type']??'';
        $columnid = $data['columnid'];
        
        $varietyid = $data['varietyid'];
        $nchannelid = $data['channelid'];
        $nhzh = '';
        if($ntype == 'n') {
            // 资讯
            if ($data['hzh'] == '2') {
                $jiatu = rand(1, 4);
                // $jiaimage = file_get_contents(APP_URL_WWW . '/data/dataimage' . $jiatu . '.txt');
                $jiaimage = file_get_contents(BASE_DIR . '/data/dataimage' . $jiatu . '.txt');
                // $A0 = '<div id=adv_a0 style="width:250px;"><a href="' . APP_URL_WWW . '/data/index.html"><img src="' . IMAGES_URL_STEELHOME . '/stgfiles/201803/head.jpg" border="0" /><img src="' . $jiaimage . '" border="0" width="250px" height="147px" /></a></div>';
                $nhzh = '<a href="' . APP_URL_WWW . '/data/index.html"><img src="' . IMAGES_URL_STEELHOME . '/stgfiles/201803/head.jpg" border="0" /><img src="' . $jiaimage . '" border="0" width="250px" height="147px" /></a>';
                if(strstr($columnid,"060") || strstr($columnid,"061") || strstr($columnid,"062") || strstr($columnid,"112") || strstr($columnid,"134") || strstr($columnid,"170") || strstr($columnid,"195")) {
                    $nhzh='<div id=adv_a0 style="width:250px;"><a href="'.APP_URL_WWW.'/n3676750"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202111/hwsteelbg.png" width="264" height="244" border="0"></a></div>';
                }

                //海外频道三级页面广告
                if(str_contains($columnid,"060") || str_contains($columnid,"061") || str_contains($columnid,"062") || str_contains($columnid,"112") || str_contains($columnid,"134") || str_contains($columnid,"170") || str_contains($columnid,"195")) {
                    $nhzh='<div id=adv_a0 style="width:250px;"><a href="'.APP_URL_NEWS.'/2021/11/03/n3676750.html"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202111/hwsteelbg.png" width="264" height="244" border="0"></a></div>';
                }

                if($data['moban']=='2') $nhzh = '';
            }
        } else {
            // 行情
            switch ($data['channelid']) {
                case '02':
                    $t_h = '<iframe src="' . APP_URL_NEWS . '/hq_hzh.php?city=' . $cityid . '&hq_id=' . $nid . '" width="200"  height="185" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no"></iframe>';
                    break;
                case '04':
                    $t_h = '<iframe src="' . APP_URL_NEWS . '/hq_hzh_ll.php" width="200"  height="185" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no"></iframe>';
                    break;
                case '11':
                    $t_h = '<iframe src="' . APP_URL_NEWS . '/hq_hzh_mj.php" width="200"  height="185" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no"></iframe>';
                    break;
                case '12':
                    $t_h = '<iframe src="' . APP_URL_NEWS . '/hq_hzh_tg.php?city=' . $cityid . '" width="200"  height="185" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no"></iframe>';
                    break;
                default:
                    $t_h = '<a href="/2=02=007,065===/1.html"><img src="'.APP_URL_WWW.'/stimages/2010/08/zengwen200x185.gif" width="200" height="185" border="0"></a>';
                    break;
            }
            
            if ($cityid == '0028' && ($varietyid == '102' || $varietyid == '103')) {
                $t_h = '<a href="//yhmy.steelhome.cn"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201902/yhsm20190226_200180.gif" width="200" height="185" border="0"></a>';
            }
        
            // if ($cityid == '0028' && $varietyid == '179') {
            //     $t_h = "<img src='".IMAGES_URL_STEELHOME."/stgfiles/202006/gdgzy.gif' border='0'>";
            // }
        
            if ($cityid == '00V1' && $varietyid == '101') {
                $t_h = '<a href="http://zyhsd.steelhome.cn/"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202312/zyhsd.gif" width="200" height="185" border="0"></a>';
            }
        
            if ($cityid == '0036' && $varietyid == '130') {
                $t_h = '<a href="//whwj.steelhome.cn"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201107/whwj.gif" width="200" height="185" border="0"></a>';
            }else if ($cityid == '0036') {
                $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202405/zheshangzhongtuo200x185.jpg" width="200" height="185" border="0">';
            }
        
            if ($cityid == '0007' && $varietyid == '113') {
                // $t_h = '<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201308/shzs.gif" width="200" height="185" border="0"></a>';
            }

            if ($cityid == '00N6' && $varietyid == '101') {
                $t_h = '<a href="'.APP_URL_WWW.'/conference/index.php?view=index&WebGuid=zzmeeting20190521"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201905/scmc_200185.gif" width="200" height="185" border="0"></a>';
            }
            // if ($cityid == '0028' && $varietyid == '101') {
            //     $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202111/guangzhourss.gif" width="200" height="185" border="0">';
            // }
            if ($cityid == '0053' && $varietyid == '178') {
                $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202202/sichuanrjb.gif" width="200" height="185" border="0">';
            }
            if ($cityid == '0053' && $varietyid == '117') {
                $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202412/20241210scjd200185.gif" width="200" height="185" border="0">';
            }
            if ($cityid == '0055' && $varietyid == '102') {
                $t_h = '<a href="https://www.yngyjt.com/"><img src="".IMAGES_URL_STEELHOME."/stgfiles/202303/ylgy20001.gif" width="200" height="185" border="0"></a>';
            }
        
            if ($cityid == '0052' && $varietyid == '102') {
                $t_h = '<a href="http://www.cqgrgc.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201811/cqgygc.gif" width="200" height="185" border="0"></a>';
            }

            if($cityid == '0056') {
                if($varietyid == '101') {
                    $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202108/sxxlzsteel.gif" width="200" height="185" border="0">';
                } elseif($varietyid == '102') {
                    $t_h = '<a href="//sxhysm.steelhome.cn"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202409/hyyd200x185.gif" width="200" height="185" border="0"></a>';
                }
            }

            if ($cityid == '0054' && $varietyid == '101') {
                $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202008/zyhsd.gif" width="200" height="185" border="0">';
            }
            if ($cityid == '0059' && ($varietyid == '102' || $varietyid == '179')) {
                $t_h = '<a href="http://kh.lgmi.com/nxjhx/news.html"><img src="'.APP_URL_WWW.'/stgfiles/202309/20230921nxjhx200185.gif" width="200" height="185" border="0"></a>';
            }
        
            // if (($cityid == '0024' || $cityid == '0025' || $cityid == '0027' || $cityid == '0009' || $cityid == '0026') && $varietyid == '179') {
            //     $t_h = "<a href='http://xmmyy.steelhome.cn/home.php'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202205/minyiyuan2025.gif' border='0'></a>";
            // }

            //福州 泉州 大中型材 
            if (($cityid == '0024'||$cityid == '0027') && $varietyid == '112') {
                $t_h = "<a href='http://xmmyy.steelhome.cn/home.php'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202205/minyiyuan2025.gif' border='0'></a>";
            }


            //福州 厦门 泉州   中厚板
            if (($cityid == '0024'||$cityid == '0026'||$cityid == '0027') && $varietyid == '102') {
                $t_h = "<a href='http://xmmyy.steelhome.cn/home.php'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202205/minyiyuan2025.gif' border='0'></a>";
            }




            if ($cityid == '0060' && $varietyid == '121') {
                $t_h = "<a href='http://www.xjhhwf.com/'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202306/xjhhsteel.png' border='0'></a>";
            }
        
            // 热轧
            if ($cityid == '0053' && ($varietyid == '114')) {
                $t_h = '<a href="//scty.steelhome.cn"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201505/sctyh.gif" width="200" height="185" border="0"></a>';
        
            }
            if ($cityid == '0053' && $varietyid == '179') {
                $t_A13 = '<div class="adv_right" id="GG_A13"><a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202203/cqgbs.gif" border="0"></a></div>';
            }
        
            // 有色频道
            if ($nchannelid == '08') {
                $t_h = '<iframe src="'.APP_URL_NEWS.'/hq_hzh_ys.php" width="200"  height="185" frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no"></iframe>';
        
            }
        
            //衬塑管、涂塑管、
            // if ($varietyid == '959' || $varietyid == '982' || $varietyid == '960') {
            //     $t_h = "<a href='http://www.yfgg.com/' target='_blank'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202211/yf200x185_2024.gif' border='0'></a>";
            // }
        
            //焊管、镀锌管、方矩管
            // if ($varietyid == '122' || $varietyid == '120' || $varietyid == '949') {
            //     $t_h = "<a href='https://www.yfgg.com/' target='_blank'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202211/yf200x185_2024.gif' border='0'></a>";
            // }
        
            if ($cityid == '0068' && $varietyid == '179') {
                $t_h = '<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201803/ayrzbj_hnyzg.gif" width="200" height="185" border="0"></a>';
            }
            // 新增贵阳市场广告
            if ($cityid == '0054') {
                $t_h = '<a href="http://gyqxly.steelhome.cn/index13.php"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/20201216gyqxly200%C3%97185.gif" width="200" height="185" border="0"></a>';
            }
            
            // 新增长沙市场、冷轧广告
            if ($cityid == '0035' && $varietyid == '178') {
                $t_h = '<a href="http://csyh.steelhome.cn/"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/changshayuehan200185.gif" width="200" height="185" border="0"></a>';
            }

            // 新增南宁市场、大众型材广告
            if ($cityid == '0033' && $varietyid == '112') {
                $t_h = '<img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202406/guangxibanglong200x185.gif" width="200" height="185" border="0">';
            }

            // if (strpos($content, '[{$t_h}]') === false) {
            // if(!str_contains($data['content'], '[{$t_h}]')) {
            //     //衬塑管、涂塑管、焊管、镀锌管、方矩管
            //     if ($varietyid == '959' || $varietyid == '982' || $varietyid == '960' || $varietyid == '122' || $varietyid == '120' || $varietyid == '949') {
            //         $data['content'] = "<a href='//www.yfgg.com/' target='_blank' style='text-align: center;'><img src='".IMAGES_URL_STEELHOME."/stgfiles/202403/690x95youfa_2024.gif' border='0'></a><br>".$data['content'];
            //     }
            // }
            // }
            // echo $data['content'];exit;
            $data['content'] = str_replace('[{$t_h}]', $t_h, $data['content']);
        }
        if(isset($loginInfo['memberBo']['whereFrom']) && $loginInfo['memberBo']['whereFrom'] == 51) {
            $nhzh = preg_replace('/href[\s]*=[\s]*"[^"]*"/i', 'href="javascript:void(0);"', $nhzh);
            $nhzh = preg_replace("/href[\s]*=[\s]*'[^']*'/i", 'href="javascript:void(0);"', $nhzh);
        }
        $this->assign('nhzh', $nhzh);
    }

    // 是否关注
    private function followStatus($news) {
        $type = $news['type'];
        $varietyid = $news['varietyid'];
        $columnid = $news['columnid'];
        $cityid = $news['cityid'];
        $channelid = $news['channelid'];
        $isimport = $news['isimport'];
        
        $followStatus = 0;
        $getdata= $this->getFollowList();
        $list = [];
        if(!empty($getdata)) {
            if($type == 'm') {
                $list = $getdata['followHq'];
                foreach($list as $val) {
                    if($val['varietyid'] == $varietyid && $val['columnid'] == $columnid && $val['cityid'] == $cityid && (int)$val['ischannel'] == (int)$channelid) {
                        $followStatus = 1;
                        break;
                    }
                }
            } else if($type == 'n') {
                $list = $getdata['followNews'];
                $gcname = $news['gc_name'];
                // echo $varietyid."--".$columnid."--".$channelid."--".$isimport."--".$gcname."--".$cityid."--".$gcname;
                // print_r($list);exit;
                foreach($list as $val) {
                    if($val['ncityid']=="") {
                        if(str_contains($varietyid, $val['nvarietyid']) && str_contains($columnid, $val['ncolumnid']) && str_contains($channelid, $val['nchannelid']) && str_contains($isimport, $val['isimport']) && str_contains($gcname, $val['gcName'])) {
                            $followStatus = 1;
                            break;
                        }
                    } else {
                        if(str_contains($varietyid, $val['nvarietyid']) && str_contains($columnid, $val['ncolumnid']) && str_contains($channelid, $val['nchannelid']) && str_contains($isimport, $val['isimport']) && str_contains($gcname, $val['gcName']) && str_contains($cityid, $val['ncityid'])) {
                            $followStatus = 1;
                            break;
                        }
                    }
                }
            }
        }
        return $followStatus;
    }

    // 当前位置 导航文字
    private function set_linknext($news, $channelList) {
        $linknext = "";
        $ncolumnid = $news['columnid'];
        // 根据以前messageshow文件中的代码，行情的栏目id前面需要拼接上002
        if($news['type']=='m') $ncolumnid = "002,".$news['columnid'];
        $nchannelid = $news['channelid'];
        // 多频道的时候取第一个频道
        if(count(explode(",", $news['channelid']))>1) $nchannelid = explode(",", $news['channelid'])[0];
        $newspower = [];
        $arrcolumnid = explode(",", $ncolumnid);
        $tchs = explode(",", $news['channelid']);
        $hy_name = [];
        // print_r($arrcolumnid);exit;
        // print_r($news);exit;

        // 获取频道列表信息
        if(empty($channelList)){
            $ch_array = ['01' => '财经频道', '02' => '钢材频道', '04' => '炉料频道', '08' => '有色频道', '10' => '不锈频道', '11' => '煤焦频道', '12' => '特钢频道', '15' => '铁合金频道', '17' => '化工频道', '19' => '水泥频道', '24' => '普氏Platts数据', '25' => '新能源频道', '26' => '铁矿石频道', '27' => '废钢频道'];
            $churl_array =['01' => 'zx', '02' => 'gc', '04' => 'll', '08' => 'ys', '10' => 'bx', '11' => 'mj', '12' => 'tg', '15' => 'thj', '17' => 'mj', '19' => 'sn', '24' => 'ps', '25' => 'ny', '26' => 'tks', '27' => 'fg'];
        } else {
            foreach ($channelList as $value) {
                $ch_array[$value['channelid']] = $value['channelname'];
                $churl_array[$value['channelid']] = $value['channelcode'];
            }
        }
        // 获取栏目列表信息
        $arr_readlevel = [];
        $customerColumns = [];  //期货专栏之类的外部客户发布的资讯用的栏目
        $columnList = $this->getColumnList();
        $allColumnName = [];
        // print_r($columnList);exit;
        foreach ($columnList as $value) {
            $arr_readlevel[$value['columnid']] = $value['readlevel'];
            if($value['readlevel'] == '110')
            $customerColumns[] = $value['mcolKeys'].','.$value['columnid'];
            $allColumnName[$value['mcolKeys'].','.$value['columnid']] = $value['columnname'];
        }

        $churlhwgs_array = ['02' => APP_URL_NEWS.'/hwgs/', '04' => APP_URL_WWW.'/hwgsll.php', '08' => APP_URL_WWW.'/hwgsys.php'];
        $churl_site_array = ['02' => 2, '04' => 3, '08' => 4, '11' => 4, '12' => 2, '10' => 10, '15' => 3];
        $newszhpower = $this->newszhpower;
        $newsglypower = $this->newsglypower;

        // print_r($arr_readlevel);exit;
        foreach ($arr_readlevel as $kcolumnid => $readlevel) {
            if (isset($newsglypower[$kcolumnid][0])) {
                $newsglypower[$kcolumnid][0] = $readlevel;
            }
            if (isset($newszhpower[$kcolumnid][0])) {
                $newszhpower[$kcolumnid][0] = $readlevel;
            }
        }
        // print_r($newsglypower);exit;
        // print_r($newszhpower);exit;
        
        $allColumns = [];
        for ($i = 1; $i <count($arrcolumnid); $i += 2) {
            $tcol = $arrcolumnid[$i];  //055
            $tcol2 = $arrcolumnid[$i-1].",".$arrcolumnid[$i]; // 002,055
            $allColumns[] = $tcol2;
            $tpower = $newszhpower[$tcol][0];
            if ($tpower > 0) {
                $tch = $tchs[0];
                $newspower[0] = $tpower; //the bigest power
                $newspower[1] = $tch;
                $hy_name[1] = $tch; //the channel have bigest power
                $newspower[2] = $tcol; //the column have bigest power
                $newspower[$tch] = $tpower; //power
                $newspower[10] = $tcol2;
            } else {
                $tpower = $newsglypower[$tcol][0];
                foreach ($tchs as $tch) {
                    if ($tpower > $newspower[$tch])
                        $newspower[$tch] = $tpower; //power
                    $hy_name[$tch] = $tpower;
                }
                if ($tpower > $newspower[0]) {
                    $newspower[0] = $tpower; //the bigest power
                    $newspower[2] = $tcol; //the column have bigest power
                    $newspower[10] = $tcol2;
                }
            }

        }
        // 
        if ($newspower[1] != '01') {
            $newspower[1] = '02';
            $hy_name[1] = '02';
            foreach ($tchs as $tch) {
                if ($newsglypower[$newspower[2]][$churl_site_array[$tch]] != '' || $newspower[2] == '199') {
                    if ($tch != '10') {
                        $hy_name[1] = $tch;
                    }
                }

                if ($newsglypower[$newspower[2]][$churl_site_array[$tch]] != '') {
                    $newspower[1] = $tch;
                }
        
            }
        }
        if($churl_array[$nchannelid]=="zh") $churl_array[$nchannelid] = "zx";
        if($nchannelid == "40") {
            $churl_array[$nchannelid] = "/www.zgdzwz.com/index_2025.php";
        }
        $linknext = "<a href ='/" . $churl_array[$nchannelid] . "/'>&gt;&nbsp;" . $ch_array[$nchannelid] . "</a>";
        if ($newspower[1] == '01') {
            $linknext .= " " . $newszhpower[$newspower[2]][1];
        } else if ($newspower[1] == '11') {
            // 期限基差 当前没列表页 跳转到 期货行情页面
            if ($newspower[2] == "242") {
                // $linknext .= " <a>&nbsp;> " . $newsglypower[$newspower[2]][5] . "</a> <a href='".APP_URL_NEWS."/#ch#/col-185/'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                $linknext .= " <a>&nbsp;> " . $newsglypower[$newspower[2]][5] . "</a> <a href='/2=#ch#=002,185===/1.html'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
            } else if($newspower[2] == "056") {
                $linknext .= " <a>&nbsp;> " . $newsglypower[$newspower[2]][3] . "</a>";
            } else {
                // $linknext .= " <a>&nbsp;> " . $newsglypower[$newspower[2]][5] . "</a><a href='".APP_URL_NEWS."/#ch#/col-" . $newspower[2] . "/'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                $linknext .= " <a>&nbsp;> " . $newsglypower[$newspower[2]][5] . "</a><a href='/".$news['typenum']."=#ch#=" . $newspower[10] . "===/1.html'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
            }
        } else if ($newspower[1] == '12') {
            if ($newspower[2] == "239" && $nchannelid == "02") {
                // $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a>&nbsp;></a> <a href='".APP_URL_NEWS."/gc/gctj-" . $newspower[2] . "/'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a>&nbsp;></a> <a href='/2=02=003,109===/1.html'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
            } else {
                // 期限基差 当前没列表页 跳转到 期货行情页面
                if ($newspower[2] == "242") {
                    // $linknext .= " " . $newsglypower[$newspower[2]][1] . "<a href='".APP_URL_NEWS."/#ch#/col-185/'>&nbsp;>" . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                    $linknext .= " " . $newsglypower[$newspower[2]][1] . "<a href='/2=#ch#=002,185===/1.html'>&nbsp;>" . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                } else {
                    // $linknext .= " " . $newsglypower[$newspower[2]][1] . "<a href='".APP_URL_NEWS."/#ch#/col-" . $newspower[2] . "/'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                    $linknext .= " " . $newsglypower[$newspower[2]][1] . "<a href='/".$news['typenum']."=#ch#=" . $newspower[10] . "===/1.html'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                }
            }
        } else {
            // 期限基差 当前没列表页 跳转到 期货行情页面
            if ($newspower[2] == "242") {
                $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a>&nbsp;></a> <a href='/2=#ch#=002,185===/1.html'>" . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
            } else if ($newspower[2] == "109" && $newspower[1] == "02") { // 钢材频道调价信息页面
                $linknext .= "<a href='/gczl.php'>&nbsp;> 企业资讯</a>" . " <a>&nbsp;>&nbsp;</a> <a href='/2=02=003,109===/1.html'>" . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                // $linknext .= "<a href='".APP_URL_NEWS."/gc/gcvalue-all/'>&nbsp;> 企业资讯</a>" . " <a>&nbsp;>&nbsp;</a> <a href='".APP_URL_NEWS."/gc/gctj-109/'>" . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
            } else {
                // $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a href='".APP_URL_NEWS."/#ch#/col-" . $newspower[2] . "/'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                // $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a href='".APP_URL_WWW."/".$news['typenum']."=#ch#=" . $newspower[10] . "===/1.html'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                // https://www.steelhome.com/1=02=002,055===/1.html

                if($newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] == "" || $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] == null) {
                    $c = $newspower[10];
                    if($c == null || $c == "" ) {
                        $c = $allColumns[0];
                    }
                    $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a href='".APP_URL_WWW."/".$news['typenum']."=#ch#=".$c."===/1.html'>&nbsp;> ".$allColumnName[$allColumns[0]]."</a>";
                } else {
                    $linknext .= " " . $newsglypower[$newspower[2]][1] . " <a href='".APP_URL_WWW."/".$news['typenum']."=#ch#=" . $newspower[10] . "===/1.html'>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
                }
            }
        }
        /*if (str_contains($ncolumnid, '065')) {
            $nchannelid = '02';
        }*/
        // var_dump($news);
        // $linknext = str_replace('https://news.steelhome.com/#ch#/col-', "/{$news['typenum']}=$nchannelid=", $linknext);
        // $linknext = str_replace('#ch#', $churl_array[$nchannelid], $linknext);
        /**期货专栏(客户发布的栏目) */
        $customerColumnName = "";
        $customerColumn = "";
        foreach($allColumns as $colval) {
            if(in_array($colval, $customerColumns)) {
                $this->isCustomerNews = 1;
                $customerColumnName = $allColumnName[$colval];
                $customerColumn = $colval;
                break;
            }
        }
        if($this->isCustomerNews == 1) {
            $nextList = explode("</a>", $linknext);
            $linknext = $nextList[0] . "</a><a href='/{$news['typenum']}=$nchannelid=$customerColumn===/1.html'>&nbsp;> " . $customerColumnName . "</a>";
            $this->isShowOldPage = 0;
            /**期货专栏(客户发布的栏目) */
        } else {
            $linknext = str_replace('#ch#', $nchannelid, $linknext);
        }
        $linknext = str_replace('#hwgs#', $churlhwgs_array[$nchannelid], $linknext);
        // print_r($linknext."<br>");exit;
        // print_r($newspower);exit;
        if($churl_array[$nchannelid] == 'ny') {
            $linknext = "<a href=''>&nbsp;> " . $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]] . "</a>";
        }
        if($churl_array[$nchannelid] == 'ny' && $newsglypower[$newspower[2]][$churl_site_array[$newspower[1]]]=="") {
            if(isset($ncolumnid)){
                if(str_contains($ncolumnid, '003,287'))
                $linknext .= "<a href=''>&nbsp;> 产量统计</a>";
                else if(str_contains($ncolumnid, '003,114'))
                $linknext .= "<a href=''>&nbsp;> 企业动态</a>";
                else if(str_contains($ncolumnid, '303,047'))
                $linknext .= "<a href=''>&nbsp;> 新能源汽车</a>";
                else if(str_contains($ncolumnid, '303,048'))
                $linknext = "<a href=''>&nbsp;> 储能</a>";
                else if(str_contains($ncolumnid, '004,190'))
                $linknext .= "<a href=''>&nbsp;> 基础知识</a>";
            }
        }

        if (str_contains($ncolumnid, '053')) {
            $linknext = str_replace('053', '057', $linknext);
        }
    
        if (strstr($ncolumnid, '199') && $nchannelid == '02') {
            // $linknext = "<a>&nbsp;></a><a href ='/gc/'>钢材频道</a><a>&nbsp;></a><a href='".APP_URL_NEWS."/gc/col-199/'>标准汇编</a>";
            $linknext = "<a>&nbsp;></a><a href ='/gc/'>钢材频道</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=004,199===/1.html'>标准汇编</a>";
        }
        if (strstr($ncolumnid, '199') && $nchannelid == '04') {
            // $linknext = "<a>&nbsp;></a><a href ='/ll/'>炉料频道</a><a>&nbsp;></a><a href='".APP_URL_NEWS."/ll/col-199/'>标准汇编</a>";
            $linknext = "<a>&nbsp;></a><a href ='/ll/'>炉料频道</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=004,199===/1.html'>标准汇编</a>";
        }
    
        //煤焦汇编
        if (strstr($ncolumnid, '199') && $nchannelid == '11') {
            // $linknext = "<a>&nbsp;></a><a href ='/mj/'>煤焦频道</a><a>&nbsp;></a><a href='".APP_URL_NEWS."/mj/col-199/'>标准汇编</a>";
            $linknext = "<a>&nbsp;></a><a href ='/mj/'>煤焦频道</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=004,199===/1.html'>标准汇编</a>";
        }
    
        if (strstr($ncolumnid, '198') && $nchannelid == '02') {
            // $linknext = "<a>&nbsp;></a><a href ='/gc/'>钢材频道</a><a>&nbsp;></a><a href='".APP_URL_NEWS."/gc/col-198/'>期货调查</a>";
            $linknext = "<a>&nbsp;></a><a href ='/gc/'>钢材频道</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=002,198===/1.html'>期货调查</a>";
        }
    
        //当综合网站动态与其它频道复选时
        if (strstr($ncolumnid, '030') && $nchannelid == '01') {
            // $linknext = "<a>&nbsp;></a><a href ='".APP_URL_NEWS."/zhzx/'>财经频道</a><a>&nbsp;></a><a href='".APP_URL_NEWS."/zhzx/col-030/'>网站动态</a>";
            $linknext = "<a>&nbsp;></a><a href ='".APP_URL_NEWS."/zhzx/'>财经频道</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=001,030===/1.html'>网站动态</a>";
        }
    
        if (str_contains($ncolumnid, '284')) {
            // $linknext .= " <a href ='".APP_URL_NEWS."/gc/bigcol-008/'>国内市场</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=02=008,284===/1.html'>成交调查</a>";
        }
        if (strstr($ncolumnid, '283') && strstr($nchannelid, '02')) {
            $linknext .= " <a href ='".APP_URL_NEWS."/gc/gcvalue-all/'>钢厂资讯</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=003,283===/1.html'>开工调查</a>";
        }
        if (strstr($ncolumnid, '283') && $nchannelid == '04') {
            $linknext .= " <a href ='".APP_URL_NEWS."/ll/bigcol-003/'>企业资讯</a><a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=003,283===/1.html'>开工调查</a>";
        }
        if (strstr($ncolumnid, '283') && $nchannelid == '11') {
            // $linknext .= " 企业资讯<a>&nbsp;></a><a href='".APP_URL_NEWS."/mj/col-283/'>开工调查</a>";
            $linknext .= " 企业资讯<a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=003,283===/1.html'>开工调查</a>";
        }
        if (strstr($ncolumnid, '283') && $nchannelid == '12') {
            // $linknext .= " <a href ='".APP_URL_NEWS."/tg/bigcol-003/'>钢厂资讯<a>&nbsp;></a><a href='".APP_URL_NEWS."/tg/col-283/'>开工调查</a>";
            $linknext .= " <a href ='".APP_URL_NEWS."/tg/bigcol-003/'>钢厂资讯<a>&nbsp;></a><a href='".APP_URL_WWW."/2=$nchannelid=003,283===/1.html'>开工调查</a>";
        }

        return [$linknext];
    }

    // 评分评论
    public function doComment($params) {
        header('Content-Type:application/json; charset=utf-8');
        $resMsg = [
            "code" => 1001,
            "msg" => "错误",
        ];
        $nid = $params['nid'];
        $ntype = $params['ntype'];
        $score = $params['score'];
        $remark = trim($params['content']);
        if (!preg_match("/^[0-9]{1,10}$/", $nid) || ($ntype!='m' && $ntype!='n')) {
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        }
        if (!preg_match("/^[1-5]{1,1}$/", $score) || $score<1 || $score>5) {
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        }
        if($remark=="" || $remark==null || mb_strlen($remark)==0) {
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        } else if(mb_strlen($remark)>200) {
            $resMsg['code'] = 1002;
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        }

        $type = $ntype=='m' ? 2 : 1;
        $userType = 1;
        $userId = -1;
        $trueName = "";
        $manageid = -1;
        $manageid = $params['adminid'] ?? -1;
        list($islogin, $token, $loginInfo) = $this->detail_islogin();
        // print_r($loginInfo);exit;
        if ($islogin) {
            $userType = $loginInfo["userType"];
            $userId = $loginInfo["userid"];
            $trueName = $loginInfo['sysUser']["trueName"]??"-1";
        } else {
            $resMsg["msg"] = "请先登录";
            $resMsg["code"] = "2";
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        }

        $paramsss = [
            'type' => $type,
            'newsId' => $nid,
            'managerId' => $manageid,
            'UserType' => $userType,
            'UserId' => $userId,
            'trueName' => $trueName,
            'score' => $score,
            'remark' => $remark,
            'ip' => $this->get_client_ip(),
            'whereFrom' => 1,
            'reviewed' => 0,
        ];

        $resJson = $this->send("/apifront/steelhome/comments/add", $paramsss, "POST", [], false);
        // print_r($resJson);exit;
        // $resJson = json_decode($resJson, true);
        if($resJson["code"]== '200'){
            $resMsg["msg"] = "评论成功";
            $resMsg["code"] = "0";
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode("评论出错啦", JSON_UNESCAPED_UNICODE);
        }
        exit;
    }

    // 关注、取消关注
    public function doFollow($params) {
        header('Content-Type:application/json; charset=utf-8');
        $resMsg = [
            "code" => 1001,
            "msg" => "错误",
        ];
        $nid = $params['nid'];
        $ntype = $params['ntype'];
        $do = $params['do'];
        $channelid = $params['channelid'];
        $remark = $params['content'];
        if (!preg_match("/^[0-9]{1,10}$/", $nid) || ($ntype!='m' && $ntype!='n')) {
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        }

        $type = $ntype=='m' ? 2 : 1;
        $userType = 1;
        $userId = -1;
        // $manageid = $params['adminid'] ?? 0;
        list($islogin, $token, $loginInfo) = $this->detail_islogin();
        if ($islogin) {
            $userType = $loginInfo["userType"];
            $userId = $loginInfo["userid"];
        } else {
            $resMsg["msg"] = "请先登录";
            $resMsg["code"] = "2";
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);exit;
        }

        $msg = "";
        if($do == "+"){
            $paramsss = [
                'type' => $type,
                'newsId' => $nid,
                'isChannel' => $channelid,
                'userType' => $userType,
                'userId' => $userId,
                // 'cityid' => 'NULL',
                'ip' => $this->get_client_ip(),
            ];
            $resJson = $this->putFollow($paramsss);
            $msg = "关注成功";
        } else if($do == "-") {
            $paramsss = [
                'type' => $type,
                'newsId' => $nid,
            ];
            $resJson = $this->putCancelFollow($paramsss);
            $msg = "成功取消关注";
        }
        if($resJson["code"]== '200'){
            $resMsg["msg"] = $msg;
            $resMsg["code"] = "0";
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode($resMsg, JSON_UNESCAPED_UNICODE);
        }
        exit;
    }

    // 登录信息
    private function detail_islogin()
    {
        $token = '';
        $loginInfo = [];
        $islogin = 0;
        if (!empty($_COOKIE['Authorization'])) {
            $userInfo = $this->getUserInfoByLoginCookie();
            if (!empty($userInfo)) {
                $token = $userInfo[0];
                $loginInfo = $userInfo[1];
                $islogin = 1;
            }
        }
        return [$islogin, $token, $loginInfo];
    }

    // 获取窄模板右侧的新闻列表
    private function getRightNewsList($newsInfo){
        $whereFrom = $this->getWhereFrom();

        $nchannelid = explode(",", $newsInfo['channelid'])[0];
        $redisKeyHomePageList = $this->makePageListRedisKey($GLOBALS['PAGE_TYPE'][6], $nchannelid, '0', '', '0');
        // print_r($redisKeyHomePageList);
        $right_news = $this->getRedisNewsList($redisKeyHomePageList);
        // print_r($right_news);exit;
        if(empty($right_news)) {
            $redisKeyHomePageList = $this->makePageListRedisKey($GLOBALS['PAGE_TYPE'][6], '0', '0', '', '0');
            $right_news = $this->getRedisNewsList($redisKeyHomePageList, $nchannelid);
        }
        $right_news = array_filter($right_news, function($item) {
            return !empty($item['list'][0]);
        });

        $right_news1 = array_slice($right_news, 0, 4);
        $right_news2 = array_slice($right_news, 4, 4);
        $right_news3 = array_slice($right_news, 8, 4);
        $right_news4 = array_slice($right_news, 12, 4);
        $right_news5 = array_slice($right_news, 16, 4);
        $right_news6 = array_slice($right_news, 20, 4);

        $d[1] = $right_news1;
        if(!empty($right_news2)) $d[2] = $right_news2;
        if(!empty($right_news3)) $d[3] = $right_news3;
        if(!empty($right_news4)) $d[4] = $right_news4;
        if(!empty($right_news5)) $d[5] = $right_news5;
        if(!empty($right_news6)) $d[6] = $right_news6;
        // 特殊处理
        foreach($d as $key => &$val) {
            foreach($val as $key2 => &$val2) {
                //百川详情页去掉论坛相关栏目
                if (($val2['show_title']=="钢铁论坛" || $val2['show_title']=="大话钢市") && $whereFrom == 51){
                    unset($val[$key2]);
                }
                if($val2['show_title']=="钢铁论坛") {
                    foreach($val2['list'][0] as $key3 => &$val3) {
                        $val3['nid'] = "/luntan.steelhome.cn/forum.php?mod=viewthread&tid=".$val3['newsid'];
                    }
                } else if($val2['show_title']=="大话钢市") {
                    foreach($val2['list'][0] as &$val4) {
                        $val4['nid'] = "/luntan.steelhome.cn/forum.php?mod=viewthread&tid=".$val4['newsid'];
                    }
                }
            }
        }
        // print_r($d[5]);exit;
        return $d;
    }

    // 获取相关资讯
    private function getRelatedNewsList($newsInfo) {
        $nchannelid = explode(",", $newsInfo['channelid'])[0];
        $nvarietyid = $newsInfo['varietyid'];
        $ncityid = $newsInfo['cityid'];
        $nid = $newsInfo['id'];
        $keys = $newsInfo['keys'];
        $columnid = $newsInfo['columnid'];
        $list = [];
        // print_r($newsInfo);exit;
        if($newsInfo['type'] == "n") {
            $tmplist = $this->getElasticSearchList(['type'=>'3', 'ntitle'=>$keys, 'matchContents'=>0]);
            $tmplist = array_filter($tmplist??[], function($item) use($nid) {
                return $item['nid'] != $nid;
            });
            if(empty($tmplist)){
                $tmpColumnidList = explode(",", $columnid);
                $allColumnidList = [];
                for($i=0; $i<count($tmpColumnidList); $i+=2) {
                    $allColumnidList[] = $tmpColumnidList[$i].",".$tmpColumnidList[$i+1];
                }
                $redisKeyString = $newsInfo['typenum']."=".$nchannelid."=".implode("|", $allColumnidList)."===";
                // echo $redisKeyString;
                $tmplist = $this->getElasticSearchList(['redisKeyString'=>$redisKeyString, 'type'=>2]);
                $list[10][0]['title_url'] = "/".$redisKeyString."/1.html";
                // print_r($tmplist);exit;
                $tmplist = array_filter($tmplist??[], function($item) use($nid) {
                    return $item['nid'] != $nid;
                });
            } else {
                $list[10][0]['title_url'] = "/search.php?view=index&type=0&searchKeys=".urlencode($keys);
            }
            $list[10][0]['show_title'] = "相关资讯";
            $list[10][0]['list'][0] = array_slice($tmplist, 0, 5);
            // 4145250  4144889
            // print_r($tmplist);exit;
        } else {
            $d = $this->getCityList(['cityid'=>$ncityid]);
            $province = $ncityid;
            if(isset($d[0]['province']) && $d[0]['province']!="") {
                $province = $d[0]['province'];
            }
            if(in_array($ncityid, ['0007','0040','0039', '0052'])) {
                $province = $ncityid;
            }
            $redisKeyString = $newsInfo['typenum']."=".$nchannelid."=002,$columnid=".$nvarietyid."=".$province."=";
            $tmplist = $this->getElasticSearchList(['redisKeyString'=>$redisKeyString, 'type'=>1,  'pageSize'=>'10', "ntitle"=>"", "matchContents"=>0, "startTime"=>"2000-01-01", "endTime"=>"", "pageNum"=>"0"]);
            $tmplist = array_filter($tmplist??[], function($item) use($nid) {
                return $item['nid'] != $nid;
            });
            if(empty($tmplist)) {
                $redisKeyString = $newsInfo['typenum']."=".$nchannelid."=$columnid=".$nvarietyid."==";
                $tmplist = $this->getElasticSearchList(['redisKeyString'=>$redisKeyString, 'type'=>1,  'pageSize'=>'10', "ntitle"=>"", "matchContents"=>0, "startTime"=>"2000-01-01", "endTime"=>"", "pageNum"=>"0"]);
                $tmplist = array_filter($tmplist??[], function($item) use($nid) {
                    return $item['nid'] != $nid;
                });
            }
            $list[10][0]['show_title'] = "相关行情";
            $list[10][0]['title_url'] = "/".$redisKeyString."/1.html";
            $list[10][0]['list'][0] = array_slice($tmplist, 0, 5);
        }

        return $list;
    }

    // 设置一些广告变量等
    private function setAdvParams($news, $intercept) {
        $columnid = $news['columnid'];
        $wenzi = '';
        // print_r($news);exit;
        if (strstr($columnid, "060") || strstr($columnid, "061") || strstr($columnid, "062") || strstr($columnid, "112") || strstr($columnid, "134") || strstr($columnid, "170") || strstr($columnid, "195")) {
            $wenzi = '<a href="'.APP_URL_WWW.'/n3676750" target="_blank" style="color:red;font-size:14px;width:unset;float: right;margin-right:10px;">“海外钢铁煤炭资源及进出口贸易系列报告”</a>';
        }
        if($intercept) {
            // $wenzi = '';
        }
        if($news['type'] == "n") {
            $allAdv = $this->news_adv($news);
        } else {
            $allAdv = $this->news_mrhq_adv($news);
        }
        $advStr = "";
        // print_r($allAdv);exit;
        foreach($allAdv as $advName => $val) {
            if($val!="") {
                $advStr .= $val;
            }
        }
        return [$wenzi, $advStr];
    }

    // 资讯广告
    private function news_adv($news) {
        $ncolumnid = $news['columnid'];
        $hzh = $news['hzh'];
        $nvarid = $news['varietyid'];
        $ncityid = $news['cityid'];
        $nchannelid = $news['channelid'];
        $nfocus = $news['focus'];
        $allAdv = [];
        $A1 = "";
        $A2 = "";
        // $A1='<a href="'.APP_URL_WWW.'/100_integrity/2023/hx.php"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201712/bjcx_541x72_1221.jpg" width="541" height="72" border="0"></a>';

        // if(strstr($nvarid,"c09")&&strstr($ncolumnid,"057"))
        // {
        //     $A1='<a href="'.APP_URL_WWW.'/MessageShow_newszb.php?mid=40500"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/hzyjs392x62.gif" width="392" height="62" border="0"></a>';
        // }

        //if(strstr($nchannelid,"01") || strstr($nchannelid,"02") || strstr($nchannelid,"08")|| strstr($nchannelid,"10")|| strstr($nchannelid,"15")){
        $A1='<a href="'.APP_URL_WWW.'/conference/index.php?view=index&WebGuid=meeting443"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202508/meeting443x690x95.jpg" width="690" height="95" border="0"></a>';
        //}
        $allAdv = ["A1"=>$A1, "A2"=>$A2];
        return $allAdv;
    }

    // 行情广告
    private function news_mrhq_adv($news) {
        $Alladv = [];
        $A1 = "";
        $A2 = "";
        $A3 = "";
        $A4 = "";
        $A5 = "";
        $cityid = $news['cityid'];
        $varietyid = $news['varietyid'];
        $columnid = $news['columnid'];
        $nchannelid = $news['channelid'];
        
        // $A1='<a href="'.APP_URL_WWW.'/100_integrity/"><img src="'.APP_URL_WWW.'/stgfiles/201606/160531_bjcx_541x72.jpg" width="541" height="72" border="0"></a>';

        // 根据城市、品种设置广告
        switch($cityid . '_' . $varietyid) {
            case '0056_107':
                $A3='<a href="//sxjt.steelhome.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201111/20240319jintai69095.jpg"  border="0"></a>';
            break;
            case '0056_120':
                // $A2='<a href="//www.sxldgg.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201111/111109_glida_248x72_hanguan.gif" border="0"></a>';
                // $neirong_1='<a href="//sxxld.steelhome.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/xld0001.gif" border="0"></a>';
                // $t_A5 = '<div class="adv_right_double"><a href="//whwj.steelhome.cn"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202007/sxcggg.gif" width="185" height="90" border="0"></a></div>';
            break;
            case '0056_122':
                $neirong_1='<a href="//sxxld.steelhome.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/xld0001.gif" border="0"></a>';
            break;
            case '0033_101':
                // $neirong_1='<a href="/MessageShow_newszb.php?mid=931"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202007/100127_anzheng_570x40.gif" width="570" height="40" border="0"></a>';
                $neirong_3='<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202112/lzmc570400001.gif" border="0"></a>';
            break;
            case '0033_179':
                $neirong_1='<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202112/lzmc570400001.gif" border="0"></a>';
            break;
            case '0034_101':
            case '0034_179':
                $neirong_1='<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202112/lzmc570400001.gif" border="0"></a>';
            break;
            case '0043_101':
                // $A2='<a href="//www.sxjbjt.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201601/20160121_jb_248x72.gif" width="248" height="72" border="0"></a>';
                // $A55='<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201801/wangshengxing37090.gif" width="370" height="90" border="0"></a>';
            break;
            case '0041_101':
                $A5='<a href="'.APP_URL_WWW.'/MessageShow_newszb.php?mid=223300"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201606/160617_jbt_370x90.gif" width="370" height="90" border="0"></a>';
            break;
            

        }

        // 根据栏目、品种设置广告
        switch($columnid . '_' . $varietyid) {
            case '056_022':
            case '056_136':
            case '056_137':
            case '056_138':
                $A2='<a href="//yjsteel.steelhome.com"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/20240319shyj69095.gif" width="690" height="95" border="0"></a>';
            break;
        }

        // 根据栏目、城市设置广告
        switch($columnid . '_' . $cityid) {
            case '055_0052':
                // $A3='<a href="//xpzwz.steelhome.com/index15.php"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/201911/cqxpz_185x90_20191118.gif" width="185" height="90" border="0"></a>';
            break;
            // case '055_0028':
            //     $A3='<a href="'.APP_URL_WWW.'/MessageShow_newszb.php?mid=100231"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202007/yangfan370x90.gif" border="0" /></a>';
            // break;
        }

        // 根据栏目、品种、城市设置广告
        switch($columnid . '_' . $varietyid . '_' . $cityid) {
            case '055_101_0052':
                $neirong_1='<a href="#"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202403/20240319yukun69095.gif" width="690" height="95" border="0"></a>';
            break;
        }

        // if(strstr($nchannelid,"01") || strstr($nchannelid,"02") || strstr($nchannelid,"08")|| strstr($nchannelid,"10")|| strstr($nchannelid,"15")){
            $A4='<a href="'.APP_URL_WWW.'/conference/index.php?view=index&WebGuid=meeting443"><img src="'.IMAGES_URL_STEELHOME.'/stgfiles/202508/meeting443x690x95.jpg" width="690" height="95" border="0"></a>';
        //}
        $Alladv = ["A1"=>$A1, "A2"=>$A2, "A3"=>$A3, "A4"=>$A4, "A5"=>$A5, "neirong_1"=>$neirong_1, "neirong_3"=>$neirong_3];
        return $Alladv;
    }

    // 邀请码验证
    public function check_invitationcode($params) {
        $invitationcode = $params['invitationcode'];
        $nid = $params['nid'];
        $ntype = $params['ntype'];
        if (empty($invitationcode)) {
            echo json_encode(["code" => 419, "msg" => "邀请码不能为空"], JSON_UNESCAPED_UNICODE);
            exit;
        }

        if (strlen($invitationcode)!= 6) {
            echo json_encode(["code" => 419, "msg" => "邀请码长度不正确"], JSON_UNESCAPED_UNICODE);
            exit;
        }
        $newsInfo = $this->getNewsData($nid, $ntype);
        if ($newsInfo['invitationcode']!= $invitationcode) {
            echo json_encode(["code" => 419, "msg" => "邀请码不正确"], JSON_UNESCAPED_UNICODE);
            exit;
        } else {
            echo json_encode(["code"=>200, "msg"=>"验证成功"], JSON_UNESCAPED_UNICODE);
        }
        exit;
    }

    // 获取评价列表
    public function get_pingjia_list($params, $api = 0) {
        $url = APP_URL_NEWS."/zxhq_pingjia_list.php";
        $pingjia_html = file_get_contents($url, false,
            stream_context_create([
                "http" => [
                    "method" => "POST",
                    "header" => "Content-type: application/x-www-form-urlencoded",
                    "content" => http_build_query([
                        'newsid' => $params['newsid'],
                        'pagetype' => $params['pagetype']??"",
                        'page' => $params['page']??"",
                        'memberid' => $params['memberid'],
                        'type' => $params['type']
                    ])
                ]
            ])
        );
        if($api) {
            echo $pingjia_html;
            exit;
        } else {
            return $pingjia_html;
        }
    }

    // 截取pdf
    private function getPdf($con, $nid, $type, $isjiequ = 0) {
        $outputPath = "";
        $pdfPath = "";
        $charCount = 0;
        $charCountStr = "0万";
        if($con) {
            $pattern = '/https?:\/\/[^\s\'"]+\.pdf/i';
            preg_match($pattern, $con, $matches);
            $pdfUrls = $matches[0];

            $pdfPath = $pdfUrls;
            if($pdfUrls) {
                $tmp = explode("uploadfile/", $pdfUrls);
                $newUrl = $pdfUrls;
                if(count($tmp) > 1) {
                    $newUrl = BASE_DIR."/uploadfile/" . $tmp[1];
                }
                if($isjiequ) {
                    if(str_contains($pdfUrls, "http")) {
                        $tmpFile = tempnam(sys_get_temp_dir(), 'pdf_');
                        // file_put_contents($tmpFile, file_get_contents($pdfUrls));
                        $pdfData = $this->fetchPdfByCurl($pdfUrls);
                        if ($pdfData) {
                            file_put_contents($tmpFile, $pdfData);
                        }
                    }
                    
                    $currentSize = filesize($newUrl);
                    $originalDir = dirname($newUrl);
                    $randomFilename = (string)$nid . "_5.pdf";
                    if(intval($currentSize) > 1000) {
                        // $randomFilename = (string)$currentSize."_".$nid.".pdf";
                        $randomFilename = (string)$currentSize."_".$nid."_".time().".pdf";
                    }
                    $outputPath = $originalDir . "/" . $randomFilename;
                    // if (!file_exists($outputPath)) {
                    if (1==1) {
                        //文件不存在的情况需要切割出一个文件并存起来
                        if (!file_exists($originalDir)) {
                            mkdir($originalDir, 0777, true);
                        }
                        $pdf = new Fpdi();
                        $newUrl = $tmpFile;

                        $catalogPage = 0;
                        try {
                            ini_set('memory_limit', '512M');
                            $parser = new Parser();
                            $pdfParser = $parser->parseFile($newUrl);
                            $pages = $pdfParser->getPages();
                        
                            foreach ($pages as $index => $page) {
                                $text = $page->getText();
                                $text = str_replace(["\t", " "], "", $text);
                                if (mb_strpos($text, "目录") !== false) {
                                    $catalogPage = $index + 1;
                                    break;
                                }
                            }
                        } catch (\Throwable $th) {}
                        // dd($catalogPage);
                        try {
                            $pageCount = $pdf->setSourceFile($newUrl);
                        } catch (\Throwable $th) {
                            $pageCount = $pdf->setSourceFile($tmpFile);
                        } finally {
                            $pagesToExtract = [];
                            // dump($type);
                            switch ((string)$type) {
                                case '26':
                                    $pagesToExtract = [1, 8, 9];
                                    if($catalogPage > 1) {
                                        $pagesToExtract = [1, $catalogPage, $catalogPage+1];
                                    } 
                                    break;
                                case '27':
                                    $pagesToExtract = [1, 8];
                                    if($catalogPage > 1) {
                                        $pagesToExtract = [1, $catalogPage];
                                    } 
                                    break;
                                case '28':
                                    $pagesToExtract = [1, 9];
                                    if($catalogPage > 1) {
                                        $pagesToExtract = [1, $catalogPage];
                                    } 
                                    break;
                                case '32':  //国际
                                    $pagesToExtract = [1];
                                    if($catalogPage > 1) {
                                        $pagesToExtract = [1, $catalogPage];
                                    }
                                    break;
                                default:
                                    $pagesToExtract = [1, 2];
                                    // dump($pagesToExtract);
                                    if($catalogPage > 1) {
                                        $pagesToExtract = [1, $catalogPage];
                                    }
                                    // dump($pagesToExtract);
                                    break;
                            }
                            // dd($pagesToExtract);
                            // 遍历需要提取的页码
                            foreach ($pagesToExtract as $pageNum) {
                                if ($pageNum <= $pageCount) { // 防止请求不存在的页码
                                    $tplId = $pdf->importPage($pageNum);
                                    $size = $pdf->getTemplateSize($tplId);
                                    $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                                    $pdf->useTemplate($tplId);
                                }
                            }

                            $pdf->Output('F', $outputPath);
                        }
                    }
                } else {
                    $outputPath = $newUrl;
                }
                try {
                    // 读取正文的字数
                    /*$parser = new Parser();
                    $pdfDoc = $parser->parseFile($newUrl);
                    $text = $pdfDoc->getText();
                    $charCount = mb_strlen(preg_replace('/\s+/', '', $text));
                    $charCountStr = round($charCount / 10000, 1)."万";*/
                } catch (\Throwable $th) {}
            }

            $tmp = explode("uploadfile/", $outputPath);
            if(count($tmp) > 1) {
                // $pdfPath = APP_URL_WWW."/uploadfile/" . $tmp[1];
                $pdfPath = "/uploadfile/" . $tmp[1];
            }

        }
        return [$charCount, $charCountStr, $pdfPath];
    }

    private function fetchPdfByCurl($url) {
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false, // 如有自签证书问题
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            CURLOPT_TIMEOUT => 20,
        ]);
        $data = curl_exec($ch);
        if (curl_errno($ch)) {
            // echo 'CURL 错误: ' . curl_error($ch);
            return false;
        }
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        if ($httpCode !== 200) {
            // echo "HTTP 请求失败，状态码: $httpCode";
            return false;
        }
        return $data;
    }

}
