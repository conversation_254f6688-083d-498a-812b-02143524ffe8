<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta property="wb:webmaster" content="8c9c4eb4c25b011c" />
    <meta http-equiv="Expires" CONTENT= "0">
    <meta http-equiv="Pragma" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-cache">
    <meta http-equiv="Cache-Control" CONTENT="no-store">
    <meta http-equiv="Cache-Control" CONTENT="must-revalidate">
    <meta http-equiv = "X-UA-Compatible" content = "IE=Edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta content="text/html; charset=utf-8" http-equiv=Content-Type>
    <title>{steelhome_unite_title}</title>

    <link rel="stylesheet" type="text/css" href="js/huadong/style.css?v=27"/>
    <link rel="stylesheet" type="text/css" href="https://static.gzjimg.com/js/www/layui/css/layui.css?v=v4.2024032201">
    <link rel="stylesheet" type="text/css" href="./css/memberloginss_v2.css?v=v4.2024032501">
</head>

<body>
<div id="header">{top}</div>

<div class="show_login_title">
    <blockquote class="layui-elem-quote login_title"> 会 员 登 录 <a href="/" target="_blank">欢迎访问钢之家网站</a>
    </blockquote>
</div>

<div class="login_form">
    <div class=" bg">
        <div class="login_main">
            <ul class="tabs" id="tabs">
                <li><a>账号密码登录</a></li>
                <li><a>手机验证登录</a></li>
            </ul>
            <ul class="tab_conbox" id="tab_conbox">
                <li class="tab_con  layui-form">
                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-username"></i>
                            </div>
                            <input type="text" name="username" value="" lay-verify="required" placeholder="用户名"
                                   lay-reqtext="请填写用户名" autocomplete="on" class="layui-input" lay-affix="clear">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-password"></i>
                            </div>
                            <input type="password" name="password" value="" lay-verify="required" placeholder="密   码"
                                   lay-reqtext="请填写密码" autocomplete="on" class="layui-input" lay-affix="eye">
                            <input type="hidden" name="passwordmd5" value="">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <input type="checkbox" name="remember" lay-skin="" lay-filter="remember_password"
                               title="下次自动登录" checked>
                        <a href="<{$smarty.const.APP_URL_WWW}>/Member_lostpasswd.php"
                           style="float: right; margin-top: 7px;">忘记密码？</a>
                    </div>
                    <div id="loginHtml"></div>
                    <div class="login_submit_border">
                        <input class="" type="submit" id="user_name_login_submit" lay-submit
                               lay-filter="user_name_login" value="登录">
                        <input name="urlstr" type="hidden" value="{Souso}">
                        <input type="hidden" name="loginType" value="1">
                        <input type="hidden" id="autoLoginFlag" value="{autoLoginFlag}">
                    </div>
                    <div class="layui-form-item">
                        <input type="checkbox" name="readPolicy1" lay-verify="required" lay-skin="primary"
                               title="已阅读并同意" checked>
                        <a href="/index_2023.php?view=user_privacy&type=service_agreement" target="_blank"
                           style="position: relative; top: 6px; left: -15px;">
                            <ins>《用户服务协议》</ins>
                        </a>
                        <a href="/index_2023.php?view=user_privacy&type=privacy" target="_blank"
                           style="position: relative; top: 6px; left: -15px;">
                            <ins>《隐私政策》</ins>
                        </a>
                    </div>
                    <div class="layui-form-item">
                        <a href="/MemberLogin.php?type=old&urlstr={Souso}" target="_blank"
                           style="">
                            <ins>《去旧版登录》</ins>
                        </a>
                    </div>
                    <!--                    <div><input type="checkbox" name="remember" class="left" checked><span>记住密码</span><a class="fright" href="#">忘记密码?</a></div>-->


                </li>
                <li class="tab_con  layui-form" lay-filter="mobileLogin">
                    <div class="layui-form-item">
                        <div class="layui-input-wrap">
                            <div class="layui-input-prefix">
                                <i class="layui-icon layui-icon-cellphone"></i>
                            </div>
                            <input type="text" name="cellphone" value="" lay-verify="required|phone"
                                   placeholder="手机号" lay-reqtext="请填写手机号" autocomplete="off"
                                   class="layui-input" id="reg-cellphone">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-row">
                            <div class="layui-col-xs7">
                                <div class="layui-input-wrap">
                                    <div class="layui-input-prefix">
                                        <i class="layui-icon layui-icon-vercode"></i>
                                    </div>
                                    <input type="text" name="vercode" value="" lay-verify="required"
                                           placeholder="验证码" lay-reqtext="请填写验证码" autocomplete="off"
                                           class="layui-input">
                                </div>
                            </div>
                            <div class="layui-col-xs5">
                                <div style="margin-left: 11px;">
                                    <button type="button" id="getCheckCodeButton"
                                            class="layui-btn layui-btn-fluid layui-btn-primary tncode"
                                            lay-on="reg-get-vercode">获取验证码
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="login_submit_border">
                        <input class="col4_5" type="submit" lay-submit lay-filter="user_mobile_login" value="登录">
                        <input name="urlstr" type="hidden" value="{Souso}">
                        <input type="hidden" name="loginType" value="2">
                    </div>

                    <div class="layui-form-item">
                        <input type="checkbox" name="readPolicy2" lay-verify="required" lay-skin="primary"
                               title="已阅读并同意" checked>
                        <a href="/index_2023.php?view=user_privacy&type=service_agreement" target="_blank"
                           style="position: relative; top: 6px; left: -15px;">
                            <ins>《用户服务协议》</ins>
                        </a>
                        <a href="/index_2023.php?view=user_privacy&type=privacy" target="_blank"
                           style="position: relative; top: 6px; left: -15px;">
                            <ins>《隐私政策》</ins>
                        </a>
                    </div>
                </li>
            </ul>
        </div>
        <div class="thirdLogin">
            <div>
                您还不是会员，马上<a href="Register.php" target="_blank"> 注册 </a> 或联系我们
            </div>
            <div>
                如需用手机号或邮箱登录，<a href="/_v2MemberCenter_union_unload.php" target=_blank>请点击这里绑定</a>
            </div>
            <div>
                <hr>
                <dl>
                    <dt>其他登录方式</dt>
                    <dd><a href="<{$smarty.const.APP_URL_WWW}>/_v2app/user.php?view=wxlogin&urlstr=/Member/index.php"
                           target="_blank"><img src="<{$smarty.const.IMAGES_BASE_DIR}>icon_wx.jpg"></a></dd>
                    <dd>
                        <a href="<{$smarty.const.APP_URL_WWW}>/_v2app/user.php?view=mblogin&urlstr=/Member/index.php&type=mobile"
                           target="_blank"><img src="<{$smarty.const.IMAGES_BASE_DIR}>icon_tel.jpg"></a></dd>
                    <dd><a href="<{$smarty.const.APP_URL_WWW}>/_v2app/user.php?view=qqcode&urlstr=/Member/index.php"
                           target="_blank"><img src="<{$smarty.const.IMAGES_BASE_DIR}>icon_qq.jpg"></a></dd>
                    <dd><a href="<{$smarty.const.APP_URL_WWW}>/_v2app/user.php?view=wbcode&urlstr=/Member/index.php"
                           target="_blank"><img src="<{$smarty.const.IMAGES_BASE_DIR}>icon_wb.jpg"></a></dd>
                    <dd>
                        <a href="<{$smarty.const.APP_URL_WWW}>/_v2app/user.php?view=mblogin&urlstr=/Member/index.php&type=email"
                           target="_blank"><img src="<{$smarty.const.IMAGES_BASE_DIR}>icon_email.jpg"></a></dd>
                </dl>
            </div>
        </div>
    </div>

</div>


<div class="contact_us">
    <h3><i class="layui-icon layui-icon-cellphone" style="color: #E53503;"></i>联系电话</h3>
    <ul>
        <li><span> <div> 客服电话 </div></span> <span>021-50581010(总机) </span> <span> 4008115058</span></li>
        <li><span> <div> 市 场 部 </div></span> <span>021-50582919   </span> <span>    手机:15800777959 </span></li>
        <li><span> <div> 商务咨询部</div></span> <span>021-50582307   </span> <span>  手机:15800777961 </span></li>
        <li><span> <div> 钢材事业部</div></span> <span>021-50585263   </span> <span>    手机:15800777960 </span></li>
        <li><span> <div> 特 钢 部 </div></span> <span>0555-2238809   </span> <span>    手机:15800777971 </span></li>
        <li><span> <div> 炉料事业部</div></span> <span>021-50587270   </span> <span>    手机:15800777967 </span></li>
        <li><span> <div> 煤 焦 部 </div></span> <span>0555-2238810   </span> <span>    手机:15800777966 </span></li>
        <li><span> <div> 国 际 部 </div></span> <span>0555-2238932   </span> <span>    手机:18616060095 </span></li>
        <li><span> <div> 发展研究院</div></span> <span>021-50585279   </span> <span>    手机:15800777952 </span></li>
        <!--		<li><span> 服务邮箱</span><a href="mailto:<EMAIL>"><EMAIL></a></li>-->
    </ul>
    <img src="../_v2image/denglu/gzjwx.jpg"/>
</div>

<!---尾   -->

<div id="footer">{footer}</div>
<script src="/js/jquery-1.12.3.min.js?v=v4.2024043001" type="text/javascript"></script>

<script>

    <!-- tab -->
    $(document).ready(function () {
        jQuery.jqtab = function (tabtit, tab_conbox, shijian) {
            $(tab_conbox).find("li").hide();
            $(tabtit).find("li:first").addClass("thistab").show();
            $(tab_conbox).find("li:first").show();

            $(tabtit).find("li").bind(shijian, function () {
                $(this).addClass("thistab").siblings("li").removeClass("thistab");
                var activeindex = $(tabtit).find("li").index(this);
                $(tab_conbox).children().eq(activeindex).show().siblings().hide();
                return false;
            });
        };
        /* 调用方法如下 */
        $.jqtab("#tabs", "#tab_conbox", "click");
        $.jqtab("#tabs0", "#tab_conbox0", "click");
        $.jqtab("#tabs1", "#tab_conbox1", "mouseenter");

    });

</script>


<script src="https://static.gzjimg.com/js/www/layui/layui.js?v=v4.2024032201" type="text/javascript"></script>
<!-- 登录用 -->
<link rel="stylesheet" type="text/css" href="/js/slip_verification/style.css?v=v4.2024032201"/>
<script type="text/javascript" src="/js/slip_verification/tn_code.js?v=v4.2024032201"></script>
<script src="/passport/js/md5.js?v=v4.2024032201" type="text/javascript"></script>
<script src="/js/login.js?v=v4.2024042301" type="text/javascript"></script>
</body>
</html>
