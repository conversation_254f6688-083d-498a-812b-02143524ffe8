<?php
///////////////////////////////////////////////////////
//  create by hzp    2016/05/16
//
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
include_once( LIBS_DIR ."/util/marketdata/MapDataFunc.php" );
require_once ("/etc/steelconf/config/isholiday.php");
include_once( "/usr/local/www/libs/util/marketdata/MapDataFunc.php" );
class produceAction extends AbstractAction{

	public function __construct() {
		parent :: __construct();
	}


	public function checkpower($params=array()){
		/*sthomecs = *"ca:".$_SESSION['gcpower']."|ctg:".$_SESSION['tgpower']."|cbx:".$_SESSION['bxgpower']."|cthj:".$_*SESSION['thjpower']."|cb:".$_SESSION['llpower']."|cc:".$_SESSION['yspower']."|chg:".$_SESSION['hg*power']."|cmhg:".$_SESSION['mhgpower']."|cd:".$_SESSION['isadmin']."|ce:".$_SESSION['memberid']."*|cf:".$_SESSION['id']."|ch:".$_SESSION['maxnum']."|csn:".$_SESSION['snpower'];
		*/
        $sthomecs = $_COOKIE['sthomecs'];
        $sthomecs = explode("|",$sthomecs);

        foreach($sthomecs as &$tmp){
			$tmp = explode(":",$tmp);
			if($tmp[0]=='csj'){
				$csj = $tmp[1];
			}
			if($tmp[0]=='cf'){
				$id = $tmp[1];
			}
			if($tmp[0]=='ce'){
				$memberid = $tmp[1];
			}
		}
		if($memberid=='1'){
			$power1=$this->_dao->getRow("select power,iswwz from adminuser where id ='".$id."' ");
			$powers = explode(",",$power1['power']);
			in_array("BD2",$powers) ? $power2="BD2" : $power2="";
			in_array("BD1",$powers) ? $power3="BD1" : $power3="";
			in_array("A",$powers) ? $isadmin="1" : $isadmin="0";
		}
		if($power3 =='BD1' || $power2 =='BD2' || $power1['iswwz']=='1' || $isadmin=='1'){
			$power = '1';
		}else{
			$power = '0';
		}

		
		return array('csj'=>$csj,'isadmin'=>$isadmin,'id'=>$id,'memberid'=>$memberid,'power'=>$power,'iswwz'=>$power1['iswwz'],'powers'=>$power1);
	}

    public function checkvip(){
        /*sthomecs = *"ca:".$_SESSION['gcpower']."|ctg:".$_SESSION['tgpower']."|cbx:".$_SESSION['bxgpower']."|cthj:".$_*SESSION['thjpower']."|cb:".$_SESSION['llpower']."|cc:".$_SESSION['yspower']."|chg:".$_SESSION['hg*power']."|cmhg:".$_SESSION['mhgpower']."|cd:".$_SESSION['isadmin']."|ce:".$_SESSION['memberid']."*|cf:".$_SESSION['id']."|ch:".$_SESSION['maxnum']."|csn:".$_SESSION['snpower'];
		*/
        $sthomecs = $_COOKIE['sthomecs'];
		$sthomecs = explode("|",$sthomecs);

		foreach($sthomecs as &$tmp){
			$tmp = explode(":",$tmp);
			
			if($tmp[0]!='ce' && $tmp[0]!='cf' && $tmp[1]=='7'){
				return "1";
			}
		}
        return "0";
    }

	function formatquarter($str){
    
		$q_m = date("n", strtotime( $str ) );
		if( $q_m >= '1' && $q_m < '4'){
			$s_quarter = "-Q1";
		}elseif( $q_m >= '4' && $q_m < '7'){
			$s_quarter = "-Q2";
		}elseif( $q_m >= '7' && $q_m < '10'){
			$s_quarter = "-Q3";
		}else{
			$s_quarter = "-Q4";
		}
		
		return date("Y", strtotime( $str ) ).$s_quarter;
	}

    public function newslogin($params){
        $Souso = "/marketdata/produce.php?view=index&id=".$_SESSION['produce_id']."&L2Id=".$_SESSION['produce_L2Id'];
        $Souso1 = "/marketdata/produce.php?view=indexs";
        $this->assign('Souso',$Souso);	
        $this->assign('Souso1',$Souso1);	
    }

    public function indexs($params){
        
        $Souso = "/marketdata/produce.php?view=produce&id=".$_SESSION['produce_id']."&L2Id=".$_SESSION['produce_L2Id'];
        gourl($Souso);
    }


	public function produce($params){
		//print_r($params);
		$power = $this->checkpower();
		//add by zfy started 2018/7/5
		if($power['memberid']=='1'){
			$isgzj = 1;
		}
		$this->assign('isgzj',$isgzj);
        $this->assign("userId",$power['id']);
		//add by zfy started 2018/7/5
		if($params['L2Id']!=""){
			//$power = $this->checkpower();
			//print_r($power);
			if($power['id']==''){
                $_SESSION['produce_L2Id'] = $params['L2Id'];
                $_SESSION['produce_id'] = $params['id'];
                // gourl("/marketdata/produce.php?view=newslogin");
				echo '<script>window.parent.location.href="/MemberLogin.php?urlstr=/marketdata/produce.php?view=index"</script>';
				//https://www.steelhome.com/marketdata/produce.php?view=index
				exit;
				echo '<meta http-equiv="Content-Type" content="textml; charset=utf-8" />';
				alert("您未登录或者无权限查看！");
				//echo "<script>window.top.location.href='http://www.steelhome.cn/MemberLogin.php?urlstr=http://www.steelhome.cn/marketdata/produce.php?view=index'</script>";exit;
				//$url = "http://www.steelhome.cn/MemberLogin.php?urlstr=http://www.steelhome.cn/marketdata/produce.php?view=index";
				//gourl($url);
				$params['id']='';
				$params['L2Id']='';
			}
			$checkip=IsGZJInnerURL();
			//echo $_COOKIE['sthomecs'];
			//print_r($power);
			if($power['memberid']=='1' ){
				/*if($checkip=='0' && $power['power']!='1'){
					alert("受限制的地址！");
					goback();
				}*/
				if($power['power']=='0'){
					//alert("您不是数据频道管理员！");
					
					echo '<meta http-equiv="Content-Type" content="textml; charset=utf-8" />';
					//alert("您不是数据频道管理员！");
					//echo '<script>window.close();</script>'; 
					//exit;
					$params['id']='';
					$params['L2Id']='';

                    echo "<p>您不是数据频道管理员！</p>";
                    exit;
				}
			}else{
                $isvip = $this->checkvip();
				if($power['csj']=='0' && $isvip!='1'){
					//alert("您不是数据频道会员，不能查看！");
					
					echo '<meta http-equiv="Content-Type" content="textml; charset=utf-8" />';
					//alert("您不是数据频道会员，不能查看！");
					//echo '<script>window.close();</script>'; 
					//exit;
					$params['id']='';
					$params['L2Id']='';

                    echo "<p>您不是数据频道会员，不能查看！</p>";
                    exit;
				}
			}
		}
		// echo "1、".date("H:i:s")."<br>";
		
		//=================================
		$where2 = " where 1 ";
		if($power['power'] !='1'  && isset($power['memberid']) ){
			$where2 .=" and ViewPower='".$power['power']."' "; 
		}
		

		$sql="select * from L1Manage $where2 order by OrderNo ASC";
		$L1=$this->_dao->query($sql);

		$sql2="select * from L2Manage  order by OrderNo";
		$L2=$this->_dao->query($sql2);
		
		// 速度优化
		$L2Idarr = array();
		foreach($L2 as $key=>$tmp2){
			$L2Idarr[] = $tmp2['id'];
		}
		$sql = "SELECT L2Id,ViewMid from L2ViewPower where L2Id in ('".implode("','", $L2Idarr)."')";
		$viewmidinfo = $this->_dao->query($sql);
		$viewmidarr = array();
		foreach($viewmidinfo as $key=>$v){
			$viewmidarr[$v['L2Id']] = $v['ViewMid'];
		}
		
	    foreach($L2 as $key=>&$tmp2){
	        //  $tmp2['L2Name']=iconv('gbk','utf-8',$tmp2['L2Name']);
			 $viewmids = $viewmidarr[$tmp2['id']];
			 if(!empty($viewmids)){
				 if($power['memberid']!='1' ){
					if($power['memberid']==$viewmids){
						unset($L2[$key]);
					}
				 }
			 }
		}
	    // foreach($L2 as $key=>&$tmp2){
		// 	 $viewmids=$this->_dao->getOnes("SELECT ViewMid from L2ViewPower where L2Id='".$tmp2['id']."' ");
		// 	 print_r($viewmids);
	    //      $tmp2['L2Name']=iconv('gbk','utf-8',$tmp2['L2Name']);
		// 	 if(!empty($viewmids)){
		// 		 if($power['memberid']!='1' ){
		// 			if(!in_array($power['memberid'],$viewmids)){
		// 				unset($L2[$key]);
		// 			}
		// 		 }
		// 	 }
		// }
		// echo "<pre>";print_r($L1);exit;
		$this->assign('L1',$L1);	
		$this->assign('L2',$L2);
		//=================================
		$str='';
		//print_r($L2);
		if($params['L2Id']==""){
			$params['id'] = $L1[0]['id'];
			$sql2="select id from L2Manage where L1Id='".$params['id']."' order by OrderNo limit 1";
			$params['L2Id']=$this->_dao->getOne($sql2);
			
		}
		
		     $where .="and L2Id = '".$params['L2Id']."' ";
			 $str .= "&L2Id=".$params['L2Id'];
			 $L2Manage=$this->_dao->getRow("select * from L2Manage where id ='".$params['L2Id']."' ");
			 $L1Manage=$this->_dao->getRow("select * from L1Manage where id ='".$params['id']."' ");
			 //print_r($L2Manage);
			 //print_r($L1Manage);
			 $DataId1=$this->_dao->getOne("select DataName from DataAquireType where id ='".$L2Manage['DataId1']."' ");
			 $DataId2=$this->_dao->getOne("select DataName from DataAquireType where id ='".$L2Manage['DataId2']."' ");
			 $DataId3=$this->_dao->getOne("select DataName from DataAquireType where id ='".$L2Manage['DataId3']."' ");
			 $DataId4=$this->_dao->getOne("select DataName from DataAquireType where id ='".$L2Manage['DataId4']."' ");
			 $this->assign('DataId1',$DataId1);
			 $this->assign('DataId2',$DataId2);
			 $this->assign('DataId3',$DataId3);
			 $this->assign('DataId4',$DataId4);
			 $this->assign('L2Manage',$L2Manage);
			 $params['OrderNo'] = $L2Manage['OrderNo'];
			 $params['OrderNo1'] = $L1Manage['OrderNo'];
			 if($L2Manage['DataType'] == '5'){
				//$cg = ",CONCAT( YEAR(  DATE ) ,  '-Q', QUARTER(  DATE ) ) as DATE";
			 }
		
		
		if($params['Start']=="" && $params['End']==""){
			$params['End'] = date("Y-m-d",time());
			//echo $L2Manage['DataType'];
			if($L2Manage['DataType'] == '1' || $L2Manage['DataType'] == '6'){
				$params['Start'] = date("Y-m-d",strtotime("-6 month"));
			}else if($L2Manage['DataType'] == '2' || $L2Manage['DataType'] == '4' ){
				$params['Start'] = date("Y-m-d",strtotime("-1 year"));
			}else if($L2Manage['DataType'] == '3' ||$L2Manage['DataType'] == '5'){
				if($power['memberid']=='1'){
					$params['Start'] = date("Y-m-d",strtotime("-5 year"));
					//echo $params['Start'];
				}else if($power['memberid']==''){
					$params['Start'] = date("Y-m-d",strtotime("-1 year"));
					//echo $params['Start'];
				}else{
					$params['Start'] = date("Y-m-d",strtotime("-3 year"));
					//echo $params['Start'];
				}
			}
		}
		
		if($params['needbg']==''){
			
			$params['needbg'] ='0';
		}
//alert($params['needbg']);
		if($params['Start']!=""){
			
			//added by hezp started 20016/09/01
			if($power['memberid']!='1' && $power['memberid'] > '0' ){
				$sjtrypower=$this->_dao->getOne("select sjtrypower from member where mbid ='".$power['memberid']."' and sjtryendtime>now() ");

				$start_open_date=$this->_dao->getOne("select min(Opendate) from Account where Memberid='".$power['memberid']."' and Channelid = 22");
				//echo $power['memberid']."--".$sjtrypower;
                //added by hezp started 2017/01/16 (1)	没有数据频道权限情况下：任何频道VIP会员可查看（每日/每周/每旬/每季）一年的数据
                $isvip = $this->checkvip();

				if( $isvip=='1' && $power['csj']=='0' ){
					$time1 = strtotime(date("Y-m-d",strtotime("-1 year")));
				}else if( $sjtrypower=='1' && $power['csj']=='0' ){
                    $time1 = strtotime(date("Y-m-d",strtotime("-3 year")));
                }else if( $power['csj']!='0' ){
                	$startime = strtotime($start_open_date." 00:00:00");
					$time1 = strtotime(date("Y-m-d",strtotime("-5 year",$startime)));
				}
				$time2 = strtotime($params['Start']);
				if( $time1> $time2 ){
					$params['Start'] = date("Y-m-d",$time1);
					$this->assign( "error", "您只能查看".$params['Start']."开始的数据" );

				}
			}
			//added by hezp ended 20016/09/01
			//added by hezp started 20016/09/08
			/*3,年 4，周 5季 6旬*/
			if($L2Manage['DataType']=='3'){
				$params['Start'] = date("Y",strtotime($params['Start']))."-1-1";
				//echo $params['End'];
				$params['End'] = date("Y",strtotime("+1 year",strtotime($params['End'])))."-1-1";
				$params['End'] = date("Y-m-d",strtotime("-1 day",strtotime($params['End'])));
				//echo $params['End'];
			}else if($L2Manage['DataType']=='5'){
				/*start = (y, (m/3) * 3 + 1, 1)
				end = (y, (m/3+1) * 3 +1, 1)*/
				$month = date("m",strtotime($params['Start']));
				if( $month<='3' ){
					$month = "1";
				}else if($month>'3' && $month<='6'){
					$month = "4";
				}else if($month>'6' && $month<='9'){
					$month = "7";
				}else{
					$month = "10";
				}
				$month1 = date("m",strtotime($params['End']));
				//echo $month1;
				if( $month1<='3' ){
					$month1 = "4-1";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					$params['End'] = date("Y-m-d",strtotime("-1 day",strtotime($params['End'])));

				}else if($month1>'3' && $month1<='6'){
					$month1 = "7-1";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					$params['End'] = date("Y-m-d",strtotime("-1 day",strtotime($params['End'])));
				}else if($month1>'6' && $month1<='9'){
					$month1 = "10-1";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					
					$params['End'] = date("Y-m-d",strtotime("-1 day",strtotime($params['End'])));
					//echo $params['End'];
				}else{
					
					$month1 = "12-31";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					
				}
				$params['Start'] = date("Y",strtotime($params['Start']))."-".$month."-1";
				
			}else if($L2Manage['DataType']=='6'){
				/*if(d <= 20)
					{
						start = (y, m, (d - 1)/ 10 * 10 + 1)
						end = (y, m, ((d - 1) / 10 + 1) * 10 + 1)
					}
					else
					{
						start = (y, m, 21)
						end = (y, m + 1, 1)
					}*/
				$day = date("d",strtotime($params['Start']));
				if( $day<='10' ){
					$day = "1";
				}else if($day>'10' && $day<='20'){
					$day = "11";
				}else{
					$day = "21";
				}
				$day1 = date("d",strtotime($params['End']));
				if( $day1<='10' ){
					//$day1 = "11";
					$params['End'] = date("Y-m",strtotime($params['End']))."-10";
				}else if($day1>'10' && $day1<='20'){
					//$day1 = "21";
					$params['End'] = date("Y-m",strtotime($params['End']))."-20";
				}else{
					//$day1 = "31";
					$params['End'] = date("Y-m",strtotime("+1 month",strtotime($params['End'])))."-1";
					$params['End'] = date("Y-m-d",strtotime("-1 day",strtotime($params['End'])));
				}
				$params['Start'] = date("Y-m",strtotime($params['Start']))."-".$day;
				
			}
			
			//added by hezp ended 20016/09/08
			// echo date("Y-m-d", strtotime($params['Start']));
			if($L2Manage['DataType']=='3'){
				// $where .="and str_to_date(DATE,'%Y') >= '".date("Y",strtotime($params['Start']))."' ";
				$where .="and DATE >= '".date("Y", strtotime($params['Start']))."' ";
			}else{
				// $where .="and str_to_date(DATE,'%Y-%m-%d') >= '".$params['Start']."' ";
				$where .="and DATE >= '".$params['Start']."' ";
			}
			$str .= "&Start=".$params['Start'];
		}
		if($params['End']!=""){
		    //  $where .="and str_to_date(DATE,'%Y-%m-%d') < '".$params['End']." 23:59:59"."' ";
		     $where .="and DATE < '".$params['End']." 23:59:59"."' ";
			 $str .= "&End=".$params['End'];
		}
		
		if($params['YZM']!= $_SESSION['code'] && $params['submit']){
			
			echo '<meta http-equiv="Content-Type" content="textml; charset=gb2312" />';
			alert("验证码错误");
			goback();
		}
		$total=$this->_dao->getOne("SELECT COUNT(*) from L2Data where 1 $where and is_yuce = 0");
		
		
		
		
		
		
		
		$sql2="select L2Name  from L2Manage where id='".$params['L2Id']."'";
		$infotitle=$this->_dao->getOne($sql2);
		// $info1=$this->_dao->query("select * $cg from L2Data where 1 $where order by str_to_date(DATE,'%Y-%m-%d') desc ");
		$info1=$this->_dao->query("select * $cg from L2Data where 1 $where  and is_yuce = 0 order by DATE desc ");
		
//
		$page = $params['page'] == '' ? 1 : $params['page'];
		
	    $per = 12;
	    $start = ( $page - 1 ) * $per;
		unset ($params['page']);	

        // $info=$this->_dao->query("select * $cg from L2Data where 1 $where order by str_to_date(DATE,'%Y-%m-%d') desc LIMIT $start, $per");
        $info=$this->_dao->query("select * $cg from L2Data where 1 $where and is_yuce = 0 order by DATE desc LIMIT $start, $per");
        
		foreach($info as &$tmp){
			if($L2Manage['DataType'] == '5'){
				$tmp['DATE'] = $this->formatquarter($tmp['DATE']);
			}
			if(ceil($tmp['D1']) != $tmp['D1']){
				$tmp['D1'] = round($tmp['D1'],2);
			}
			if(ceil($tmp['D2']) != $tmp['D2']){
				$tmp['D2'] = round($tmp['D2'],2);
			}
			if(ceil($tmp['D3']) != $tmp['D3']){
				$tmp['D3'] = round($tmp['D3'],2);
			}
			if(ceil($tmp['D4']) != $tmp['D4']){
				$tmp['D4'] = round($tmp['D4'],2);
			}
		}
		if(!empty($info)){
			$data=$this->_dao->getRow("select AVG(D1) as avgd1,AVG(D2) as avgd2,AVG(D3) as avgd3,AVG(D4) as avgd4,MAX(D1) as maxd1,MAX(D2) as maxd2,MAX(D3) as maxd3,MAX(D4) as maxd4,MIN(D1) as mind1,MIN(D2) as mind2,MIN(D3) as mind3,MIN(D4) as mind4 from L2Data where 1 $where and is_yuce = 0 ");
			$data1 = array('DATE'=>"最小值",'D1'=>round($data['mind1'],2),'D2'=>round($data['mind2'],2),'D3'=>round($data['mind3'],2),'D4'=>round($data['mind4'],2));
			$data2 = array('DATE'=>"最大值",'D1'=>round($data['maxd1'],2),'D2'=>round($data['maxd2'],2),'D3'=>round($data['maxd3'],2),'D4'=>round($data['maxd4'],2));
			$data3 = array('DATE'=>"平均值",'D1'=>round($data['avgd1'],2),'D2'=>round($data['avgd2'],2),'D3'=>round($data['avgd3'],2),'D4'=>round($data['avgd4'],2));

			array_unshift($info,$data1); 
			array_unshift($info,$data3); 
			array_unshift($info,$data2); 
			
			$data4 = array('DATE'=>"最小值",'D1'=>round($data['mind1'],2),'D2'=>round($data['mind2'],2),'D3'=>round($data['mind3'],2),'D4'=>round($data['mind4'],2));
			$data5 = array('DATE'=>"最大值",'D1'=>round($data['maxd1'],2),'D2'=>round($data['maxd2'],2),'D3'=>round($data['maxd3'],2),'D4'=>round($data['maxd4'],2));
			$data6 = array('DATE'=>"平均值",'D1'=>round($data['avgd1'],2),'D2'=>round($data['avgd2'],2),'D3'=>round($data['avgd3'],2),'D4'=>round($data['avgd4'],2));
				
			array_unshift($info1,$data4);
			array_unshift($info1,$data6);
			array_unshift($info1,$data5);
		}

		//$pagebar = $this->pagebar($url, $params, $per, $page, $total);
		
		//print_r($params);exit;
		//print_r($_COOKIE);exit;
	
		//判断是不是超级管理员判断有没有后台权限
		//第一步获取权限
		
		$sthomecs = $_COOKIE['sthomecs'];
		$sthomecs = explode("|",$sthomecs);
		$sc_power;
		foreach($sthomecs as &$tmp){
			$tmp = explode(":",$tmp);
			if($tmp[0]=='csj'){
				$csj = $tmp[1];
			}
			if($tmp[0]=='cf'){
				$id = $tmp[1];
			}
			if($tmp[0]=='ce'){
				$memberid = $tmp[1];
			}
		}
		if($memberid=='1'){
			$power1=$this->_dao->getRow("select power,iswwz from adminuser where id ='".$id."' ");
			$powers = explode(",",$power1['power']);
			in_array("BD2",$powers) ? $power2="BD2" : $power2="";
			in_array("BD1",$powers) ? $power3="BD1" : $power3="";
			in_array("A",$powers) ? $isadmin="1" : $isadmin="0";
		}
		if($power3 =='BD1' || $power1['iswwz']=='1' || $isadmin=='1'){
			$sc_power = '1';
		}
		
		
		if( $sc_power=='1'){
			$sjCanExport=1;
			
		}else{
		$qx = "select sjCanExport  from steelhome.member   where 1 and mbid='".$_COOKIE['ce']."' ";
		$sjCanExport=  $this->_dao->getone($qx);
		
		}
		$this->assign( "sjCanExport", $sjCanExport ); 
        $operation = "1";
		if(isset( $params['subtag']))
		{ 
            $operation = "2";
			$this->infoDc($info1,$L2Manage,$DataId1,$DataId2,$DataId3,$DataId4,$infotitle);
			
		}
		$totalall = ceil( ($total +3)/ 15 );
		$this->assign( "total", $totalall );

	    $this->assign( "page", $page );
        $this->assign('info',$info);
        $this->assign('pagebar',$pagebar);
		$this->assign('params',$params);
		$this->assign('str',$str);

        $arr = array(
            "L1No"=>$params['OrderNo1'],    
            "L2No"=>$params['OrderNo'],    
            "L1Name"=>$L2Manage['L1Name'],    
            "L2Name"=>$L2Manage['L2Name'],    
            "StartDate"=>$params['Start'],    
            "EndDate"=>$params['End'],    
            "URL"=>$_SERVER['REQUEST_URI'],    
            "Page"=>$page,    
            "operation"=>$operation
        );
        //日志
        $this->_dao->writelogs($arr);
		
	}
	//----------------------
	public function infoDc($info1,$L2Manage,$DataId1,$DataId2,$DataId3,$DataId4,$infotitle){
		$objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
		$styleArray1 = array(
				'font' => array('size'=>11),
				'alignment' => array('horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER),
		);
		$objActSheet = $objPHPExcel->getActiveSheet();
		$objActSheet->getStyle('A')->applyFromArray($styleArray1);
		$objActSheet->getStyle('B')->applyFromArray($styleArray1);
		$objActSheet->getStyle('C')->applyFromArray($styleArray1);
		$objActSheet->getStyle('D')->applyFromArray($styleArray1);
		$objActSheet->getStyle('E')->applyFromArray($styleArray1);
		$objActSheet->getStyle('F')->applyFromArray($styleArray1);
		$objActSheet->getStyle('G')->applyFromArray($styleArray1);
		$objActSheet->getColumnDimension('A')->setWidth(30);
		$objActSheet->getColumnDimension('B')->setWidth(20);
		$objActSheet->getColumnDimension('C')->setWidth(20);
		$objActSheet->getColumnDimension('D')->setWidth(20);
		$objActSheet->getColumnDimension('E')->setWidth(20);
		$objActSheet->getColumnDimension('F')->setWidth(20);
		$objActSheet->setTitle (  $infotitle  );
		if($L2Manage['DataType']=='5'){
			$objActSheet->setCellValue('A1',  '季度' );				
		}else if($L2Manage['DataType']=='2'){
			$objActSheet->setCellValue('A1',  '年月' );
		}else if($L2Manage['DataType']=='3'){
			$objActSheet->setCellValue('A1',  '年' );
		}else{
			$objActSheet->setCellValue('A1',  '年月日' );
		}
		
		$objActSheet->setCellValue('B1',  $DataId1 );
		
		if($L2Manage['Template']>='2'){
		$objActSheet->setCellValue('C1',  $DataId2 );
		}
		
		if($L2Manage['Template']>='3'){
			$objActSheet->setCellValue('D1',  $DataId3 );
		}
		
		if($L2Manage['Template']=='4'){
			$objActSheet->setCellValue('E1',  $DataId4 );
		}
		
		$n = 2;
		
		foreach($info1 as $key => $value){
			$objActSheet->setCellValue ( 'A' . $n,  $value['DATE']);
			$objActSheet->setCellValue ( 'B' . $n,  $value['D1']);
			if($value['D2']==0){
				if($L2Manage['Template']>='2'){
			      $objActSheet->setCellValue ( 'C' . $n, '0');
				}
			}else{
			$objActSheet->setCellValue ( 'C' . $n,  $value['D2']);
			
			}
			//$objActSheet->setCellValue ( 'C' . $n,  $value['D2']);
			if($value['D3']==0){
				if($L2Manage['Template']>='3'){
				$objActSheet->setCellValue ( 'D' . $n, '0');
				}
			}else{
				$objActSheet->setCellValue ( 'D' . $n, $value['D3']);
				
			}
			if($value['D4']==0){
				if($L2Manage['Template']=='4'){
				$objActSheet->setCellValue ( 'E' . $n,  '0');
				}
			}else{
			$objActSheet->setCellValue ( 'E' . $n,  $value['D4']);			
			}
			$n++;
			
		}
		$day = $infotitle;
		$filename = $day.'.xls';
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
		header("Content-Type:application/force-download");
		header("Content-Type:application/vnd.ms-execl");
		header("Content-Type:application/octet-stream");
		header("Content-Type:application/download");
		header('Content-Disposition:attachment;filename='.$filename);
		header("Content-Transfer-Encoding:binary");
		// $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
		$objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
		$objWriter->save('php://output');
	}
	
	
	public function ajaxgetindexinfo( $params ){

		$power = $this->checkpower();
		if($power['id']==''){
			/*$str=iconv('gbk','utf-8',"您未登陆！");
			alert("您未登陆！");*/exit;
		}

		if($params['L2Id']!=""){
		     $where .="and L2Id = '".$params['L2Id']."' ";
			 $L2Manage=$this->_dao->getRow("select * from L2Manage where id ='".$params['L2Id']."' ");
			 $L1Manage=$this->_dao->getRow("select * from L1Manage where id ='".$L2Manage['L1Id']."' ");
			 if($L2Manage['DataType'] == '5'){
				//$cg = ",CONCAT( YEAR(  DATE ) ,  '-Q', QUARTER(  DATE ) ) as DATE";
			 }
		}
		//added by hezp started 20016/09/08
			/*3,年 4，周 5季 6旬*/
			if($L2Manage['DataType']=='3'){
				$params['Start'] = date("Y",strtotime($params['Start']))."-1-1";
				//$params['End'] = date("Y",strtotime("+1 year",$params['End']))."-1-1";
			}else if($L2Manage['DataType']=='5'){
				/*start = (y, (m/3) * 3 + 1, 1)
				end = (y, (m/3+1) * 3 +1, 1)*/
				$month = date("m",strtotime($params['Start']));
				if( $month<='3' ){
					$month = "1";
				}else if($month>'3' && $month<='6'){
					$month = "4";
				}else if($month>'6' && $month<='9'){
					$month = "7";
				}else{
					$month = "10";
				}
				$month1 = date("m",strtotime($params['End']));
				if( $month1<='3' ){
					$month1 = "4-1";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					$params['End'] = date("Y-m-d",strtotime("-1 day",$params['End']));

				}else if($month1>'3' && $month1<='6'){
					$month1 = "7-1";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					$params['End'] = date("Y-m-d",strtotime("-1 day",$params['End']));
				}else if($month1>'6' && $month1<='9'){
					$month1 = "10-1";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
					$params['End'] = date("Y-m-d",strtotime("-1 day",$params['End']));
				}else{
					$month1 = "12-31 ";
					$params['End'] = date("Y",strtotime($params['End']))."-".$month1;
				}
				$params['Start'] = date("Y",strtotime($params['Start']))."-".$month."-1";

			}else if($L2Manage['DataType']=='6'){
				/*if(d <= 20)
					{
						start = (y, m, (d - 1)/ 10 * 10 + 1)
						end = (y, m, ((d - 1) / 10 + 1) * 10 + 1)
					}
					else
					{
						start = (y, m, 21)
						end = (y, m + 1, 1)
					}*/
				$day = date("d",strtotime($params['Start']));
				if( $day<='10' ){
					$day = "1";
				}else if($day>'10' && $day<='20'){
					$day = "11";
				}else{
					$day = "21";
				}
				$day1 = date("d",strtotime($params['End']));
				if( $day1<='10' ){
					//$day1 = "11";
					$params['End'] = date("Y-m",strtotime($params['End']))."-10 ";
				}else if($day1>'10' && $day1<='20'){
					//$day1 = "21";
					$params['End'] = date("Y-m",strtotime($params['End']))."-20 ";
				}else{
					//$day1 = "31";
					$params['End'] = date("Y-m",strtotime("+1 month",$params['End']))."-1";
					$params['End'] = date("Y-m-d",strtotime("-1 day",$params['End']));
				}
				$params['Start'] = date("Y-m",strtotime($params['Start']))."-".$day;
			}
		//added by hezp ended 20016/09/08
		if($params['Start']!=""){
		    //  $where .="and str_to_date(DATE,'%Y-%m-%d') >= '".$params['Start']."' ";
		     $where .="and DATE >= '".$params['Start']."' ";
		}
		if($params['End']!=""){
		    //  $where .="and str_to_date(DATE,'%Y-%m-%d') < '".$params['End']." 23:59:59"."' ";
		     $where .="and DATE < '".$params['End']." 23:59:59"."' ";
		}

		$page = $params['page'] == '' ? 1 : $params['page'];
	    $per = 15;
		if($page=='1'){
			$per = 12;
			$start = ( $page - 1 ) * $per;
		}else{
			$start = ( $page - 1 ) * $per - 3;
		}

        $info=$this->_dao->query("select * $cg from L2Data where 1 $where and is_yuce = 0 order by DATE desc LIMIT $start, $per");

		if($page=='1'){
			if(!empty($info)){
				$data=$this->_dao->getRow("select AVG(D1) as avgd1,AVG(D2) as avgd2,AVG(D3) as avgd3,AVG(D4) as avgd4,MAX(D1) as maxd1,MAX(D2) as maxd2,MAX(D3) as maxd3,MAX(D4) as maxd4,MIN(D1) as mind1,MIN(D2) as mind2,MIN(D3) as mind3,MIN(D4) as mind4 from L2Data where 1 $where ");
				$data1 = array('DATE'=>"最小值",'D1'=>round($data['mind1'],2),'D2'=>round($data['mind2'],2),'D3'=>round($data['mind3'],2),'D4'=>round($data['mind4'],2));
				$data2 = array('DATE'=>"最大值",'D1'=>round($data['maxd1'],2),'D2'=>round($data['maxd2'],2),'D3'=>round($data['maxd3'],2),'D4'=>round($data['maxd4'],2));
				$data3 = array('DATE'=>"平均值",'D1'=>round($data['avgd1'],2),'D2'=>round($data['avgd2'],2),'D3'=>round($data['avgd3'],2),'D4'=>round($data['avgd4'],2));

				array_unshift($info,$data1); 
				array_unshift($info,$data3); 
				array_unshift($info,$data2); 
			}
		}

		$html_str='';
		foreach($info as $key=>$tmp){
			if($L2Manage['DataType'] == '5' ){
				if($page=='1' && ( $key=='0' || $key=='1' || $key=='2' )){
				}else{
					$tmp['DATE'] = $this->formatquarter($tmp['DATE']);
				}
			}
			if(ceil($tmp['D1']) != $tmp['D1']){
				$tmp['D1'] = round($tmp['D1'],2);
			}
			if(ceil($tmp['D2']) != $tmp['D2']){
				$tmp['D2'] = round($tmp['D2'],2);
			}
			if(ceil($tmp['D3']) != $tmp['D3']){
				$tmp['D3'] = round($tmp['D3'],2);
			}
			if(ceil($tmp['D4']) != $tmp['D4']){
				$tmp['D4'] = round($tmp['D4'],2);
			}

			$html_str .="<tr height=36 align=center><td>".$tmp['DATE']."</td>";
			$html_str .="<td>".$tmp['D1']."</td>";
			if($L2Manage['Template'] >= 2){
				 $html_str .="<td>".$tmp['D2']."</td>";
			}
			if($L2Manage['Template'] >= 3){
				 $html_str .="<td>".$tmp['D3']."</td>";
			}
			if($L2Manage['Template'] >= 4){
				 $html_str .="<td>".$tmp['D4']."</td></tr>";
			}
		}
        if($power['memberid']!='1' && $power['csj']=='0'){
            $isvip = $this->checkvip();
            if($isvip=='1'){
                $power['csj'] = "1";
            }
        }

        $arr = array(
            "L1No"=>$L1Manage['OrderNo'],    
            "L2No"=>$L2Manage['OrderNo'],    
            "L1Name"=>$L2Manage['L1Name'],    
            "L2Name"=>$L2Manage['L2Name'],    
            "StartDate"=>$params['Start'],    
            "EndDate"=>$params['End'],    
            "URL"=>$_SERVER['REQUEST_URI'],    
            "Page"=>$page,    
            "operation"=>"1"
        );
        //日志
        $this->_dao->writelogs($arr);

		$html_str=$html_str."~".$page."~".$power['memberid']."~".$power['csj']."~".$power['power'];
		echo $html_str;
        exit;
	}
    
	public function createimg($params){
		//print_R($params);
		$Start = $params['Start'];
        $End = $params['End'];
        $L2Order = $params['L2Order'];
        $L1Order = $params['L1Order'];
        $title = URLdecode($params['title']);
        $needbg = $params['needbg'];//是否需要背景图片
		$needbg = $needbg=='1' ? $needbg='0' : $needbg='1';
	    //$resurt=CreateDataGraph($L1Order,$L2Order,$Start,$End,$needbg,$title);exit;
		//add by zfy started 2018/6/27 jpgraph改echarts
		$L2Manage =$this->_dao->getRow( "SELECT L2Manage.* FROM  `L2Manage`,`L1Manage` WHERE L2Manage.L1Id = L1Manage.id and L2Manage.OrderNo =  '".$L2Order."' and L1Manage.OrderNo = '".$L1Order."'" );
		$DataType  = $L2Manage['DataType'];

		//二级目录数据类型 1 日 2月 3年 4周 5季  6旬 
		$dateStr = "";
		$dateStr2 = "";
		if($DataType == '1' || $DataType == '4'||$DataType == '6' || $DataType == '5'){
			$dateStr = '\'%Y-%m-%d\'';
			$dateStr2 = 'Y-m-d';
		}else if($DataType == '2' ){
			$dateStr = '\'%Y-%m\'';
			$dateStr2 = 'Y-m';
		}else if($DataType == '3'){
			$dateStr = '\'%Y\'';
			$dateStr2 = 'Y';
		}

        //added by hezpeng started 2019-06-03
        $Y1Min = $params['Y1Min'];
        $Y1Max = $params['Y1Max'];
        if($Y1Min!="" && $Y1Max!=""){
            $L2Manage['Y1Min'] = $Y1Min;
            $L2Manage['Y1Max'] = $Y1Max;
            $L2Manage['Y1Flag'] = "1";
        }
        $Y2Min = $params['Y2Min'];
        $Y2Max = $params['Y2Max'];
        if($Y2Min!="" && $Y2Max!=""){
            $L2Manage['Y2Min'] = $Y2Min;
            $L2Manage['Y2Max'] = $Y2Max;
            $L2Manage['Y2Flag'] = "1";
        }
        //added by hezpeng ended 2019-06-03

		//获取时间sql
		$sql_arr = $this->get_Sql($DataType,$Start,$End,$dateStr2);
		$sql_Start = $sql_arr['sql_Start'];
		$sql_End = $sql_arr['sql_End'];

		//根据开始时间 结束时间获取数据
		// $sql="select * from L2Data where 1 = 1 and DATE_FORMAT( str_to_date(DATE,$dateStr), $dateStr ) >= '${sql_Start}' and DATE_FORMAT( str_to_date(DATE, $dateStr), $dateStr) <= '${sql_End}' and L2Id ='".$L2Manage['id']."' order by str_to_date(DATE, $dateStr) asc";
		$sql="select * from L2Data where 1 = 1 and is_yuce = 0 and DATE >= '${sql_Start}' and DATE <= '${sql_End}' and L2Id ='".$L2Manage['id']."' order by DATE asc";
		$query = $this->_dao->query($sql);
		foreach($query as $key=>$value){
			if($value['D1']!=''){$l1datay[$key] = ($value['D1']);}
			if($value['D2']!=''){$l2datay[$key] = ($value['D2']);}
			if($value['D3']!=''){$l3datay[$key] = ($value['D3']);}
			if($value['D4']!=''){$l4datay[$key] = ($value['D4']);}
			
			
			$datetime[$key] = $value['DATE'];
			$lastdate = $value['DATE'];
		}
		$datax = $datetime;
		if (is_array($datax)) {
			$count = count($datax);
		}

		if( $DataType == '5' ){
			$xLables = array();//X轴数据
			foreach( $datax as $v){
				$xLables[] = $this->formatquarter($v);
			}
		}else{
			$xLables = $datax;
		}
		//echo "<pre>";print_R($xLables);
		//数据为空 显示错误图片
		if( !$l1datay && !$l2datay && !$l3datay && !$l4datay){
			header("Content-type:image/png");
			$image = imagecreatefromgif("../images/gglg01.gif");
			imagepng($image);exit;
		}
		$datasource="数据来源：钢之家数据中心 www.steelhome.com/data";
		$l1Aquire = $this->_dao->getRow( "SELECT * FROM  `DataAquireType` WHERE id =  '".$L2Manage['DataId1']."'" );
		$l2Aquire = $this->_dao->getRow( "SELECT * FROM  `DataAquireType` WHERE id =  '".$L2Manage['DataId2']."'" );
		$l3Aquire = $this->_dao->getRow( "SELECT * FROM  `DataAquireType` WHERE id =  '".$L2Manage['DataId3']."'" );
		$l4Aquire = $this->_dao->getRow( "SELECT * FROM  `DataAquireType` WHERE id =  '".$L2Manage['DataId4']."'" );

		
		 //背景图
		if($needbg == '1'){
			$bg_img="background:url(/marketdata/images/chart_bg.gif) no-repeat center center;";
			//$graph->SetBackgroundImage('/usr/local/www/libs/util/marketdata/chart_bg.jpg',BGIMG_FILLFRAME);
		}

		//标题与副标题
		if( $DataType == '5' ){
			$s_quarter = $this->formatquarter($datax[0]);
			$e_quarter = $this->formatquarter($datax[$count-1]);
			//file_put_contents( '/tmp/data' , $datax[0]." $s_quarter $e_quarter ".$datax[$count-1] ,FILE_APPEND);
			$dateStr = "(".$sq_y."$s_quarter"."至".$eq_y."$e_quarter".")";
		}else{
			$dateStr = "(".$datax[0]."$quarter"."至".$datax[$count-1].")";
		}
		if($title==''){
			$title = $L2Manage['L2Name'].$dateStr;
			if($Title2 != ''){
				$title  = $Title2;
			}
		}


		$groupbar = array();
		$arr_data = array($l1datay,$l2datay,$l3datay,$l4datay);
		$arr_data = array_filter($arr_data);

		$params['L2Manage'] = $L2Manage;
		$params['data'] = $arr_data;
		$params['datasource'] = $datasource;
		$params['xlable'] = $xLables;
		$params['title'] = $title;
		$params['count'] = $count;
		$params['bg_img'] = $bg_img;
		$params['l1Aquire'] = $l1Aquire;
		$params['l2Aquire'] = $l2Aquire;
		$params['l3Aquire'] = $l3Aquire;
		$params['l4Aquire'] = $l4Aquire;


		$this->create_echarts($params);
		//add by zfy ended 2018/6/27 jpgraph改echarts
	}
	//add by zfy started 2018/6/27 jpgraph改echarts
	public function create_echarts($params){
		//echo "<pre>";print_R($params);exit;
		// $params = $this->convert_to_utf8($params);
		$width = 475;//图宽
		$height = 336;//图高
		//echo "<pre>";print_R($params);exit;
		$arr_dataP = $params['data'];
		$L2Manage = $params['L2Manage'];
		$title = $params['title'];
		$datasource = $params['datasource'];
		$count = $params['count'];
		$bg_img = $params['bg_img'];
		$scatter = $params['scatter'];
		$xLables = $params['xlable'];
		$l1Aquire = $params['l1Aquire'];
		$l2Aquire = $params['l2Aquire'];
		$l3Aquire = $params['l3Aquire'];
		$l4Aquire = $params['l4Aquire'];
		$lendtitle = [];
		//added by shizg for 是否需要复制保存 started 2018/08/09
		$nocopy = $params['nocopy'];
		//added by shizg for 是否需要复制保存 ended 2018/08/09

		//arr_aquire.GraphType 1=折线图  2=柱状图
		//arr_aquire.ShowType  1=左轴  2=右轴
		$arr_aquire = array($l1Aquire,$l2Aquire,$l3Aquire,$l4Aquire);
		$arr_aquire = array_filter($arr_aquire);
		for($i =0 ; $i< count($arr_aquire); ++$i) {  //先柱后线
			for($j = 0; $j <  count($arr_aquire)-$i-1; ++$j) {  
				if($arr_aquire[$j]['GraphType'] == 1 && $arr_aquire[$j+1]['GraphType'] == 2 ) {  
					$tmp = $arr_aquire[$j] ; $arr_aquire[$j] = $arr_aquire[$j+1] ;  $arr_aquire[$j+1] = $tmp; 
					$tmp2 = $arr_dataP[$j] ; $arr_dataP[$j] = $arr_dataP[$j+1] ;  $arr_dataP[$j+1] = $tmp2;                  
				}  
			}
		}
		$left_tmp = $this->convert_to_utf8("(左)");
		$right_tmp = $this->convert_to_utf8("(右)");

		for($i =0 ; $i< count($arr_aquire); $i++) {
			//处理图表类型
			if($arr_aquire[$i]['GraphType']==2){
				$tname[$i] = 'bar';
			}else{
				if($scatter==1){
					$tname[$i] = 'scatter';
				}else{
					$tname[$i] = 'line';
				}
			}
			if($arr_aquire[$i]['ShowType']==2){
				$arr_aquire[$i]['DataName'].=$right_tmp;
			}/*else{
				$arr_aquire[$i]['DataName'].=$left_tmp;
			}*/
			//各个数据名称
			$lendtitle[$i] = $arr_aquire[$i]['DataName'];

		}
		//echo "<pre>";print_R($arr_dataP);
		foreach($arr_dataP as $aakk=>$aavv){
			foreach($aavv as $tmp){
				$arr_data[$aakk][] = round($tmp,2);
			}
		}
		//自定义Y轴最大值最小值
		if( $l1Aquire['ShowType'] == '1' || $l2Aquire['ShowType'] == '1' ||$l3Aquire['ShowType'] == '1' ||$l4Aquire['ShowType'] == '1' ){
			if($L2Manage['Y1Flag'] == '1' ){//自定义最大最小值
				$yScaleMin = $L2Manage['Y1Min']; 
				$yScaleMax = $L2Manage['Y1Max']; 

                //左轴
			    $y_arr = $this->solve_data2($yScaleMax,$yScaleMin);
			}else{
				$maxd=array();
				$mind=array();
				foreach($arr_data as $kk=>$vv)
				{
					if($arr_aquire[$kk]['ShowType']==1){
						$maxd[]=max($vv);
						$mind[]=min($vv);
					}
				}
				$yScaleMin = min($mind);
				$yScaleMax = max($maxd);
				//$yScaleMin = null;
				//$yScaleMax = null;

                //左轴
			    $y_arr = $this->solve_data($yScaleMax,$yScaleMin);
			}
			
			$yScaleMax = $y_arr['max'];
			$yScaleMin = $y_arr['min'];
			$splitnum = $y_arr['splitnum'];
			$fix = $y_arr['fix'];
		}
		$isY2 = 0;
		//自定义Y轴最大值最小值
		if( $l1Aquire['ShowType'] == '2'  ||  $l2Aquire['ShowType'] == '2'  || $l3Aquire['ShowType'] == '2'  || $l4Aquire['ShowType'] == '2' ){
			$isY2 = 1;//右轴
			if($L2Manage['Y2Flag'] == '1' ){
				$y2ScaleMin = $L2Manage['Y2Min']; 
				$y2ScaleMax = $L2Manage['Y2Max']; 
                //右轴
			    $y_arr2 = $this->solve_data2($y2ScaleMax,$y2ScaleMin);
			}else{
				$maxd=array();
				$mind=array();
				foreach($arr_data as $kk=>$vv)
				{
					if($arr_aquire[$kk]['ShowType']==2){
						$maxd[]=max($vv);
						$mind[]=min($vv);
					}
				}
				//$y2ScaleMin = min($mind)-5;
				//$y2ScaleMax = max($maxd);
				$y2ScaleMin = min($mind);
				$y2ScaleMax = max($maxd);

                //右轴
			    $y_arr2 = $this->solve_data($y2ScaleMax,$y2ScaleMin);
			}
            //echo $y2ScaleMax."##".$y2ScaleMin;
			
			$y2ScaleMax = $y_arr2['max'];
			$y2ScaleMin = $y_arr2['min'];
			if($y_arr2['splitnum']!=''){
				$splitnum = $y_arr2['splitnum'];
			}
			$rfix = $y_arr2['fix'];
		}
		


		
		//echo "<pre>";print_R($arr_aquire);
		//echo "<pre>";print_R($arr_data);
		//echo "<pre>";print_R($L2Manage);
		
		//exit;

		$linenum = 0;
		$barnum = 0;
		foreach( $arr_aquire as $acc ){
			if($acc['GraphType'] == '1'){ 
				$linenum++;
			}
			if($acc['GraphType'] == '2'){ 
				$barnum++;
			}
		}

		//$linecolor=array("#4E81BD","#C1504C","#9BBB58","#8166A3","418EBD","#1006BD","#FFC101","#A104C1","#FF0000");

		$linecolor=array("#416FA6","#A8423F","#3D96AE","#DA8137");

		$mycolor='color:["'.implode('","',$linecolor).'"],';

		$ldw = $L2Manage['Unit'];//单位
		$rdw = $L2Manage['Unit2'];//单位
		//$count = count($datax);
		if(($sum=$count/5) >1 && ( $DataType != '3' ) ){
			$splitNumbers = 5;
		}else if( ($sum=$count/10)>1 && ( $DataType == '3' )){
			$splitNumbers = 10;
		}
		//echarts生成图片
		$x2 = 40;//右边距
		if($isY2=="1"){
			$other_yaxis=",{name:'".$rdw."',type : 'value',
			splitNumber:".$splitnum.",
			axisLabel: {                   
				formatter: function (value, index) {
					var val=value*1.0;
					val=val.toFixed(".$rfix.");
					return val;
				},
				
			},
			axisLine: {lineStyle: {color:'#000',width:1}}".($y2ScaleMin==null?"":",min:".$y2ScaleMin).($y2ScaleMax==null?"":",max:".$y2ScaleMax)."}";

			$x2 = 57;
		}
		

		$jsarr="var right_tmp=\"".$right_tmp."\";\n
				var left_tmp=\"".$left_tmp."\";\n
				var isY2=".$isY2.";\n
				var youzhou=\"".$arr_aquire[0]['ShowType'].",".$arr_aquire[1]['ShowType'].",".$arr_aquire[2]['ShowType'].",".$arr_aquire[3]['ShowType']."\".split(',');
		";
		$series = [];
		$formatter=',formatter: function(params){
						var len=params.length;
						var showstr=params[0].name+"<br>";
						for(var i=0;i<len;i++){
							sname=params[i].seriesName;
							
							showstr+=sname+" : "+params[i].value+"<br>";
						}
						return showstr
					}';
		$grid=',grid:{
					width:myChart.width,
					height:myChart.height,
					x:57,
					x2:'.$x2.'
				}';
		$xAxis=",xAxis : [
					{
						type : 'category',
						//splitNumber: 7,
						// x轴的字体样式
                        axisLabel: {        
                                show: true,
                                textStyle: {
                                    color: '#000',
                                    fontFamily: 'SimHei',
                                }
                        },
						splitLine:false,
						boundaryGap: true,
						axisLine: {
							onZero:false,
							lineStyle: {
								color:'#000',width:1
							}
						},
						<{xdata}>
					}
				]";
		$yAxis=",yAxis : [
					{
						name:'<{ldw}>',
						type : 'value',
						splitNumber:".$splitnum.",
						// y轴的字体样式
						axisLabel: {                   
							formatter: function (value, index) {
								var val=value*1.0;
								val=val.toFixed(".$fix.");
								return val;
							},
							show: true,
                            textStyle: {
                                color: '#000',
                                fontFamily: 'SimHei',
                            }
						},

						axisLine: {lineStyle: {color:'#000',width:1}}".
						($yScaleMin==null?",min:0":",min:<{yScaleMin}>").($yScaleMax==null?"":",max:<{yScaleMax}>")
					."}<{other_yaxis}>
				]";
		/*axisLabel: {                   
							formatter: function (value, index) {
								return value.toFixed(".$this->getwei2(($yScaleMax-$yScaleMin)/5.0).");
							}
						},*/
			//splitNumber:".$splitNumbers.",	
		
		$legendtitle=implode("','",$lendtitle);
		$rand=rand(0,1000);
		$modeltext=file_get_contents("http://".$_SERVER['SERVER_NAME']."/marketdata/echarts-2.2.7/model.html?v=".$rand);
		for($i=0;$i<count($arr_aquire);$i++){
				//$axisTick="axisTick:{splitNumber: 6},";
				//$splitNumber="splitNumber:".$splitNumbers.",";
				$chartname='"name":"'.$arr_aquire[$i]['DataName'].'"';
				$charttype='"type":"'.$tname[$i].'"';
				$yAxisIndex=",yAxisIndex: ".($arr_aquire[$i]['ShowType']==2?1:0);
				$showSymbol='symbol:"none",';
				if($tname[$i]=="line") $zlevel=",zlevel:1";
				else $zlevel=",zlevel:0";

				if($tname[$i]=='scatter'){
					$j = 0;
					foreach($xLables as $xtmp){
						$xLabless[] = date("Y-m-d",strtotime($xtmp));
						
						$xlabel='data:["'.implode('","',$xLabless).'"]';
					}
					foreach($arr_data[$i] as $tmp){
						$arr_datascatter[$i][] = "[".$xLabless[$j].",".$tmp."]";
						$j++;
					}
					
					$xAxis=",xAxis : [{type : 'value',scale:true,axisLabel : {formatter: '{value}'}}],";
					$yAxis="yAxis : [{type : 'value',scale:true,axisLabel : {formatter: '{value}'}}]";
					$chartdata='"data":['.implode(',',$arr_datascatter[$i]).']';
					$series[]="{".$chartname.",".$charttype.",".$chartdata."}";
				}else{
					$xlabel='data:["'.implode('","',$xLables).'"]';
					$chartdata='"data":['.implode(',',$arr_data[$i]).']';
					$series[]="{".$axisTick.$showSymbol.$splitNumber.$chartname.",".$charttype.$yAxisIndex.$stack.$radius.",".$chartdata.$chart_fg.$zlevel."}";
				}
		}

		$myseries=implode(",",$series);
		$tip_trigger="axis";
		
		$chart_y=40+16*count($lendtitle)+(count($lendtitle)==4?0:15);

		//ie内核浏览器不兼容复制下载功能
		if($nocopy!=1){
			if(!strpos($_SERVER['HTTP_USER_AGENT'],'Trident')){
				$fuzhi='<input type="image" src="./images/copy1.png" onclick="copyimg();" style="float:left;margin-right:2px;">';
				$savejs="onmouseover=\"document.getElementById('save').style.display='block';\"onmouseout=\"document.getElementById('save').style.display='none';\"";
			}
		}

		$modeltext=str_replace('<{savejs}>',$savejs,$modeltext);	//电脑版mouseover显示 
		$modeltext=str_replace('<{fuzhi}>',$fuzhi,$modeltext);	//背景图
		$modeltext=str_replace('<{background_img}>',$bg_img,$modeltext);	//背景图
		$modeltext=str_replace('<{jsarr}>',$jsarr,$modeltext);	//js数组，用于左右轴的
		$modeltext=str_replace('<{title}>',$title,$modeltext);//主标题
		$modeltext=str_replace('<{subtitle}>',$datasource,$modeltext);				//子标题
		$modeltext=str_replace('<{tip_trigger}>',$tip_trigger,$modeltext);
		$modeltext=str_replace('<{formatter}>',$formatter,$modeltext);		//标签样式，自定义
		$modeltext=str_replace('<{legend_data}>',$legendtitle,$modeltext);	//四个标题
		//$modeltext=str_replace('<{legend_x}>',$legend_x,$modeltext);		//标题x轴位置
		$modeltext=str_replace('<{grid}>',$grid,$modeltext);				//坐标系位置
		$modeltext=str_replace('<{chart_y}>',$chart_y,$modeltext);			//用来控制坐标轴位置
		$modeltext=str_replace('<{xAxis}>',$xAxis,$modeltext);				//X轴设定
		$modeltext=str_replace('<{yAxis}>',$yAxis,$modeltext);				//Y轴设定
		$modeltext=str_replace('<{mycolor}>',$mycolor,$modeltext);			//自定义颜色
		$modeltext=str_replace('<{echarts_types}>',$ImageType,$modeltext);	//图形类型设定
		$modeltext=str_replace('<{xdata}>',$xlabel,$modeltext);				//日期
		$modeltext=str_replace('<{ldw}>',$ldw,$modeltext);					//左轴单位
		$modeltext=str_replace('<{yScaleMin}>',$yScaleMin,$modeltext);				//y轴(左)最小值
		$modeltext=str_replace('<{yScaleMax}>',$yScaleMax,$modeltext);				//y轴(左)最大值
		$modeltext=str_replace('<{y2ScaleMin}>',$y2ScaleMin,$modeltext);			//y轴(左)最小值
		$modeltext=str_replace('<{y2ScaleMax}>',$y2ScaleMax,$modeltext);			//y轴(左)最大值
		$modeltext=str_replace('<{other_yaxis}>',$other_yaxis,$modeltext);		//双轴的右轴
		$modeltext=str_replace('<{series}>',$myseries,$modeltext);			//数值
		$modeltext=str_replace('<{data_source}>',$datasource,$modeltext);	//数据来源



		echo "$modeltext";exit;
	}
	//处理y轴最大最小值
	protected function solve_data($Max,$Min){
		//echo "Max=$Max,Min=$Min";
		$PRECION = 0.000001;
		$X = ($Max-$Min)/8;
		if($X < $PRECION)
		{
			$absMax = abs($Max);
			if($absMax > $PRECION)
			{
				$n = log($absMax, 10);
				$n = floor($n);
				$p = pow(10, $n - 1);
				$Max = ceil(($Max + 1)/ $p) * $p;
				$Min = floor(($Min - 1) / $p) * $p;
			}
			else
			{
				$Max = 4;
				$Min = -4;
			}
			$X = ($Max - $Min) / 8;
		}

		if($X <=1)
		{
			$n = log($X, 10);
			$n = -floor($n);
			$p = pow(10, $n + 1);   //2个零时，乘以1000，保证XX.X样式。
			$delta = ceil($X * $p) / $p;
		}
		else
		{
			$n = log($X, 10);
			$n = floor($n);
			$p = pow(10, $n - 1);   //4位整数时，除以100，保证XX.X样式。
			$delta = ceil($X / $p ) *$p;
		}
			
		$n = log($delta, 10);
		$n = floor($n);
		if($n <= 0)   //delta <= 1
		{
			$jn = -$n + 1;    // delta有2个0时，乘以1000
			$p = pow(10, $jn);
			$newMin = floor($Min * $p)/$p;
		}
		else      // delta > 1
		{
			$jn = $n-1;  // delta为3位整数时，除以10
			$p = pow(10, $jn);
			$newMin = floor($Min / $p) * $p;
		}
		//echo "delta=$delta";
		$newMin -= $delta;
		if($Min > 0 && $newMin < 0) $newMin = 0;

		$newMax = $newMin + $delta * 10;
		$fix1 = $this->_getFloatLength($newMin);
		$fix2 = $this->_getFloatLength($newMax);
		$fix = max($fix1,$fix2);
		return array("min"=>$newMin,"max"=>$newMax,"splitnum"=>'10',"fix"=>$fix);
	}

    //处理y轴最大最小值
	protected function solve_data2($Max,$Min){
		//echo "Max=$Max,Min=$Min";
		$PRECION = 0.000001;
		$X = ($Max-$Min)/8;
		if($X < $PRECION)
		{
			$absMax = abs($Max);
			if($absMax > $PRECION)
			{
				$n = log($absMax, 10);
				$n = floor($n);
				$p = pow(10, $n - 1);
				$Max = ceil(($Max +1)/ $p) * $p;
				$Min = floor(($Min -1) / $p) * $p;
			}
			else
			{
				$Max = 4;
				$Min = -4;
			}
			$X = ($Max - $Min) / 8;
		}

		if($X <=1)
		{
			$n = log($X, 10);
			$n = -floor($n);
			$p = pow(10, $n + 1);   //2个零时，乘以1000，保证XX.X样式。
			$delta = ceil($X * $p) / $p;
		}
		else
		{
			$n = log($X, 10);
			$n = floor($n);
			$p = pow(10, $n - 1);   //4位整数时，除以100，保证XX.X样式。
			$delta = ceil($X / $p ) *$p;
		}
			
		$n = log($delta, 10);
		$n = floor($n);
		if($n <= 0)   //delta <= 1
		{
			$jn = -$n + 1;    // delta有2个0时，乘以1000
			$p = pow(10, $jn);
			$newMin = floor($Min * $p)/$p;
		}
		else      // delta > 1
		{
			$jn = $n-1;  // delta为3位整数时，除以10
			$p = pow(10, $jn);
			$newMin = floor($Min / $p) * $p;
		}
		//echo "delta=$delta";
		$newMin = $Min;
		if($Min > 0 && $newMin < 0) $newMin = 0;

		$newMax = $Max;
		$fix1 = $this->_getFloatLength($newMin);
		$fix2 = $this->_getFloatLength($newMax);
		$fix = max($fix1,$fix2);
		return array("min"=>$newMin,"max"=>$newMax,"splitnum"=>'10',"fix"=>$fix);
	}


	private function _getFloatLength($num) {
		$count = 0;
		 
		$temp = explode ( '.', $num );
		 
		if (sizeof ( $temp ) > 1) {
		$decimal = end ( $temp );
		$count = strlen ( $decimal );
		}
		 
		return $count;
	}
	
	//处理y轴最大最小值-旧的
	protected function solve_data1($maxa,$mina){
		//$d = $maxa - $mina;
		if(abs($maxa-$mina)>50000)
			{
				
				$maxa=floor(($maxa+9999)/10000)*10000;
				$mina=floor($mina/10000)*10000;
				$num=ceil(($maxa-$mina)/10000);
				
			}
			else if(abs($maxa-$mina)>10000)
			{
				$maxa=floor(($maxa+4999)/5000)*5000;
				$mina=floor($mina/5000)*5000;
				$num=ceil(($maxa-$mina)/5000);
			}
			else if(abs($maxa-$mina)>5000)
			{
				$maxa=floor(($maxa+999)/1000)*1000;
				$mina=floor($mina/1000)*1000;
				$num=ceil(($maxa-$mina)/1000);
			}
			else if(abs($maxa-$mina)>1000)
			{
				$maxa=floor(($maxa+499)/500)*500;
				$mina=floor($mina/500)*500;
				$num=ceil(($maxa-$mina)/500);
				//echo $maxa."<br/>".$mina."<br/>".$num;
				
			}
			else if(abs($maxa-$mina)>500)
			{
				$maxa=floor(($maxa+99)/100)*100;
				$mina=floor($mina/100)*100;
				$num=ceil(($maxa-$mina)/100);
			}
			else if(abs($maxa-$mina)>100)
			{
				$maxa=floor(($maxa+49)/50)*50;
				$mina=floor($mina/50)*50;
				$num=ceil(($maxa-$mina)/50);
			}
			else if(abs($maxa-$mina)>50)
			{
				$maxa=floor(($maxa+9)/10)*10;
				$mina=floor($mina/10)*10;
				$num=ceil(($maxa-$mina)/10);
			}
			else if(abs($maxa-$mina)>20)
			{
				$maxa=floor(($maxa+4.9)/5)*5;
				$mina=floor($mina/5)*5;
				$num=ceil(($maxa-$mina)/5);
			}
			else if(abs($maxa-$mina)>10)
			{
				$maxa=floor(($maxa+1.99)/2)*2;
				$mina=floor($mina/2)*2;
				$num=ceil(($maxa-$mina)/2);
			}
			else
			{
				$maxa=floor(($maxa+0.99)/1);
				$mina=floor($mina/1)*1;
				$num=ceil(($maxa-$mina)/1);
			}

		return array("min"=>$mina,"max"=>$maxa,"splitnum"=>$num);
	}
	
	protected function convert_to_utf8($params,$array=null){
		if(is_array($array)){
			if(count($array)==0){
				if(is_array($params)&&count($params)>0) {
					foreach($params as $k=>$v){
						$params[$k]=$this->convert_to_utf8($v);
					}
					return $params;
				}else{
					return $params;
				}
			}else{
				foreach($params as $key=>$value){
					if(array_key_exists($array,$key)) $params[$key]=$this->convert_to_utf8($value);
				}
			}
		} else {
			return $params;
		}
	}
	protected function getwei($str) //add by zhangcun 2017/7/19 for 获取小数有效位数
	{
		$wei=0;
		if($str-1.0>0) return $wei;
		for($i=0;$i<10;$i++){
			$str*=10.0;
			$wei++;
			if($str-1.0>0) return $wei;
		}
	}
	protected function getwei2($str)
	{
		$wei=explode(".",$str);
		return strlen($wei[1]);
	}
	protected function get_Sql($DataType,$Start,$End,$dateStr2){
		if($DataType == '5'){
			$start_y = date("Y", strtotime( $Start ) );
			$start_m = ( floor (date("n", strtotime( $Start ) ) / 3) )*3 + 1;
			$sql_Start = formatdate("$start_y-$start_m-01");
			
			$end_y = date("Y", strtotime( $End ) );
			$end_m = ( floor (date("n", strtotime( $End ) ) / 3) )*3 + 1;
			$sql_End = formatdate("$end_y-$end_m-01");
			
			
		}elseif($DataType == '6'){
			$start_y = date("Y", strtotime( $Start ) );
			$start_m = date("m", strtotime( $Start ) );
			
			$end_y = date("Y", strtotime( $End ) );
			$end_m = date("m", strtotime( $End ) );
			
			$s_d = floor (date("j", strtotime( $Start ) ));
			if($s_d <= 20){
				$start_d = ( floor($s_d - 1)/ 10) * 10 + 1;
			}else{
				$start_d = 21;
			}
			
			$e_d = floor (date("j", strtotime( $End ) ));
			if($e_d <= 20){
				$end_d = ( floor($e_d - 1)/ 10) * 10 + 1;
			}else{
				$end_d = 21;
			}

			$sql_Start = formatdate("$start_y-$start_m-$start_d");
			$sql_End = formatdate("$end_y-$end_m-$end_d");
		}else{
			$sql_Start = date($dateStr2,strtotime($Start));
			$sql_End = date($dateStr2,strtotime($End));
		}
		return array("sql_Start"=>$sql_Start,"sql_End"=>$sql_End);
	}
	//add by zfy ended 2018/6/27 jpgraph改echarts

	public function leftmenu($params){
	    //L1Manage
		//L2Manage
		$sql="select * from L1Manage";
		$L1=$this->_dao->query($sql);

		$sql2="select * from L2Manage";
		$L2=$this->_dao->query($sql2);
		foreach($L1 as &$tmp){
	         $tmp['L1Name']=$tmp['L1Name'];
		}
	    foreach($L2 as &$tmp2){
	         $tmp2['L2Name']=$tmp2['L2Name'];
		}

		$file=dirname(__FILE__);
		//echo $file;
        ob_start(); 
        print_r($L1);
        $contents = ob_get_contents();   
        ob_end_clean();
        $fp=fopen($file."/1234567.txt","w");
	    fprintf($fp,$contents);//将数据写入文件
		fprintf($fp,"aaaaa");
        fclose($fp);//关闭文件*/

		$this->assign('L1',$L1);	
		$this->assign('L2',$L2);	
	}

	public function ajaxcheckyzm( $params ){
		$yzm=strtolower($params['yzm']);
		if($yzm!=''){
		  if( $yzm == $_SESSION['code']){
			echo "1";
		  }
		  else{
			echo "0";
		  }
		}
		else{
			echo "2";
		  }
		exit;
   }
   function Pagebar( $url, $param, $limit, $page, $total, $type = 0 ) {
	  if( $total < 0 ) {
		  return false;
	  }
	  if( $url == "" ) {
		return false;
	  }
	  $link = $url . "?";
	  if (is_array($param)) {
		foreach ($param as $str_key => $str_value) {
			$link = $link . "$str_key=" . urlencode($str_value) . "&";
		}
	  }
	  $int_pages = ceil($total / $limit);
	  if ($page < 1) {
		$page = 1;
	  }
	  if ($page > $int_pages) {
		$page = $int_pages;
	  }
	  
	  $start_url = $link . "page=1";
	  $end_url = $link . "page=$int_pages";
	  $pre_url = $link . "page=" . ( $page - 1 );
	  $next_url = $link . "page=" . ( $page + 1 );
	  if( $page < 6 ){
		$start_page = 1;
		$end_page = 7;
	  }else{
		$start_page = $page -5;
		$end_page = $page +1;
	  }
	  if( $end_page > $int_pages ){
		$end_page = $int_pages;
	  }
	  $urls = null;
	  /**  THE URL */
	  for( $i = $start_page, $j = 0; $i <= $end_page; $i++, $j++ ){
		 $temp_url = $link . "page=$i";
		 if( $i == $page ){
			$urls[$j] = "<strong>[" . $i . "]</strong>&nbsp;";
		 }else{
			$urls[$j] = "<a href=\"$temp_url\">[" . $i . "]</a>&nbsp;";
		}
	  }
	  if( is_array( $urls ) ){
		$str_html = "共&nbsp;".$total."&nbsp;条信息&nbsp;&nbsp;共&nbsp;".$int_pages."&nbsp;页&nbsp;&nbsp;". $str_html . "<a href=\"$start_url\" text=\回第1页\>首页</a>&nbsp;&nbsp;";
		if( $page > 1 ){
			$str_html = $str_html . "<a href=\"$pre_url\">上页</a>&nbsp;&nbsp;";
		}else{
			$str_html = $str_html . "上页&nbsp;&nbsp;";
		}
		if( $type == 0 ){
			foreach ($urls as $sub_url) {
			  $str_html = $str_html . $sub_url;
			}
		}
		$str_html = $str_html . "&nbsp;";
		if( $page >= $int_pages ){
			$str_html = $str_html . "下页&nbsp;&nbsp;";
		}else{
			$str_html = $str_html . "<a href=\"$next_url\">下页</a>&nbsp;&nbsp";
		}
		if( $type == 0 ){
			$str_html = $str_html . "<a href=\"$end_url\" text=\"到第$int_pages\">尾页</a> &nbsp;&nbsp;跳到 <input type=text size=4 onBlur=window.location.href='$pre_url&page='+this.value  /> 页 ";  
			return $str_html;
		}
		if( $type == 1 ){
			return $str_html . "<a href=\"$end_url\" text=\"到第$int_pages\">尾页</a>";
		}
	 }
		return false;
	}


    /**
     * 目录搜索ajax
     * Created by zfy.
     * Date:2019/7/23 14:11
     * @param $params
     */
    public function ajax_search($params)
    {
        $power = $this->checkpower();

        if ($power['power'] != '1' && isset($power['memberid'])) {
            $where2 = " and l1.ViewPower='" . $power['power'] . "' ";
        }

        //未登录用户不能搜索有权限设置的目录
        $AllPowerL2Id = $this->_dao->getAllPowerL2Id();
        if ($power) {
            //该用户有权限的目录
            $PowerL2Id = $this->_dao->getL2IdByMid($power['memberid']);
            foreach ($PowerL2Id as $akey => $avalue) {
                if (in_array($avalue, $AllPowerL2Id)) {
                    $AllPowerL2Id = array_merge(array_diff($AllPowerL2Id, array($avalue)));
                }
            }
        }


       if($power['id']=='') {
           $out['status'] = 3;
       }else {
        if (!$params['searchWord']) {
            $out['status'] = 0;
        } else {
            // $arr = $this->array_iconv($this->_dao->getSearchInfo($params['searchWord'], $where2));
            $arr = $this->_dao->getSearchInfo($params['searchWord'], $where2);
			// echo "<pre>";print_r($arr);
            if ($arr) {
                $list = array();
                foreach ($arr as $k => $value) {
                    if ((in_array($value['id'], $AllPowerL2Id))) {
                        continue;
                    }
                    $list[$value['L1Name']][] = $value;
                }
                if ($list) {
                    $out['status'] = 1;
                    $out['result'] = $list;
                } else {
                    $out['status'] = 2;
                }
            } else {
                $out['status'] = 2;
            }
        }
       }
        echo json_encode($out);
    }

    /**
     * 将含有GBK的中文数组转为utf-8
     * Created by zfy.
     * Date:time
     * @param $arr 数组
     * @param string $in_charset 原字符串编码
     * @param string $out_charset 输出的字符串编码
     * @return mixed
     */
    function array_iconv($arr, $in_charset="gbk", $out_charset="utf-8")
    {
        $ret = eval('return '.iconv($in_charset,$out_charset,var_export($arr,true).';'));
        return $ret;
    }

	// update start by gzw 20210317 华东地区钢厂月采购价格指数  狄艳冰需求 
	public function createimgforcaigouzhishu($params){
		$type = $params['type'];
		if ($type == '0') {
			// 硅铁
			$variety = "硅铁";
		} else if ($type == '1') {
			// 硅锰
			$variety = "硅锰";
		}
		//查询近两年的数据
		$start_date = date("Y-m-d",strtotime("-2 year"));
		$sql = "select * from SteelCaiGou_JGZS where variety = '".$variety."' and date >= '".$start_date."' order by date,id asc";
		$dataArray = $this->gcdao->query($sql);
		$guiTie_array_num = array();
		$guiTie_array_date = array();
		if (count($dataArray) > 0) {
			foreach ($dataArray as $key => $value) {
				array_push($guiTie_array_num,$value['cg_zhiShuNum']);
				array_push($guiTie_array_date,$value['date']);
			}
		}
		//背景图
		$bg_img="background:url(./images/chart_bg.gif) no-repeat center center;background-size:95%";
		//数据为空 显示错误图片
		if( !$guiTie_array_num && !$guiTie_array_date){
			header("Content-type:image/png");
			$image = imagecreatefromgif("../images/gglg01.gif");
			imagepng($image);exit;
		}
		$params['data'] = $guiTie_array_num;
		$params['date'] = $guiTie_array_date;
		$params['datasource'] = "来源：钢之家数据中心";
		// $params['title'] = "标题";
		$params['count'] = count($guiTie_array_date);
		$params['bg_img'] = $bg_img;
		$this->create_echartsforcaigouzhishu($params);
	}
	public function create_echartsforcaigouzhishu($params){
		$params = $this->convert_to_utf8($params);
		// $title = $params['title'];
		$datasource = $params['datasource'];
		$count = $params['count'];
		$bg_img = $params['bg_img'];
		$data_num_arr = $params['data'];
		$date_arr = $params['date'];
		$isnew = $params['isnew']??0;
		$width = 300;//图宽
		$height = 240;//图高
		$type = $params['type'];
		if ($type == '0') {
			$typeName = "硅铁月采购价格指数（华东地区）";
			$select0 = "selected";
			$select1 = " ";
		} else if ($type == '1'){
			$typeName = "硅锰月采购价格指数（华东地区）";
			$select0 = " ";
			$select1 = "selected";
		}
		//自定义Y轴最大值最小值
		// $ymax = max($data_num_arr) + 300;
		// $ymin = min($data_num_arr) - 300;
		//  ceil  有余数都向前加一    floor 直接取整   round 四舍五入
		$ymin = floor(min($data_num_arr)/100 - 2)*100;
		$str = ceil(((max($data_num_arr) - $ymin)/4)/100)*100;
		$ymax = $str * 4 + $ymin;
		//echarts生成图片
		$grid='grid:{width:myChart.width,height:myChart.height,x:57,x2:40}';
		$xAxis=",xAxis : [
					{
						type : 'category',
						// splitNumber: 7,
						// x轴的字体样式
                        axisLabel: {        
                                show: true,
                                textStyle: {
                                    color: '#000',
                                }
                        },
						splitLine:false,
						boundaryGap: false,
						axisLine: {
							onZero:false,
							lineStyle: {
								color:'#000',width:1
							}
						},
						<{xdata}>
					}
				]";
		$cgDW = $this->convert_to_utf8("元/吨");
		$yAxis_new = "";
		if($isnew==1) {
			$yAxis_new = "margin: 10,";
			$grid='grid:{width:myChart.width,height:myChart.height,x:45,x2:20}';
		}
		$yAxis=",yAxis : [{
            name:'<{cgDW}>',
            type : 'value',
            max : '<{ymax}>',
            min : '<{ymin}>',
            splitNumber : 4,
            // y轴的字体样式
            axisLabel: {                   
                formatter: function (value, index) {
                    var val=value*1.0;
                    val=val.toFixed(0);
                    return val;
                },
                show: true,".$yAxis_new."textStyle: {
                    color: '#000',
                }
            },
            axisLine: {lineStyle: {color:'#000',width:1}}}
		]";
		$title_bottom = $this->convert_to_utf8($typeName);
		$myseries = ",series: [{
			name:'<{title_bottom}>',
			type: 'line',
			showSymbol: false,
			symbol: 'none',
			itemStyle : {
                normal : {
					color:'#A8423F',
					lineStyle:{
						color:'#A8423F'
					}
                }
              },
			<{xnum}>
		}]";
		$date_m_array = array();
		foreach($date_arr as $value) {
			$v = date("Y-m",strtotime($value));
			array_push($date_m_array,$v);
		}
		$xlabel='data:["'.implode('","',$date_m_array).'"]';
		$xnum='data:["'.implode('","',$data_num_arr).'"]';
		// echo "<pre>";print_R($date_m_array);exit;
		$linecolor=array("#416FA6","#A8423F","#3D96AE","#DA8137");
		$mycolor='color:["'.implode('","',$linecolor).'"],';
		//$legendtitle=implode("','",$lendtitle);
		$rand=rand(0,1000);
		if($isnew==1)
		$modeltext=file_get_contents("http://".$_SERVER['SERVER_NAME']."/marketdata/echarts-2.2.7/modelcaigou_new.html?v=".$rand);
		else
		$modeltext=file_get_contents("http://".$_SERVER['SERVER_NAME']."/marketdata/echarts-2.2.7/modelcaigou.html?v=".$rand);
		$modeltext=str_replace('<{background_img}>',$bg_img,$modeltext);	//背景图
		$modeltext=str_replace('<{subtitle}>',$datasource,$modeltext);		//子标题
		$modeltext=str_replace('<{formatter}>',$formatter,$modeltext);		//标签样式，自定义
		$modeltext=str_replace('<{grid}>',$grid,$modeltext);				//坐标系位置
		$modeltext=str_replace('<{xAxis}>',$xAxis,$modeltext);				//X轴设定
		$modeltext=str_replace('<{yAxis}>',$yAxis,$modeltext);				//Y轴设定
		$modeltext=str_replace('<{mycolor}>',$mycolor,$modeltext);			//自定义颜色
		$modeltext=str_replace('<{xdata}>',$xlabel,$modeltext);				//日期
		$modeltext=str_replace('<{series}>',$myseries,$modeltext);			//数值
		$modeltext=str_replace('<{data_source}>',$datasource,$modeltext);	//数据来源
		$modeltext=str_replace('<{xnum}>',$xnum,$modeltext);				//指数 
		$modeltext=str_replace('<{cgDW}>',$cgDW,$modeltext);
		$modeltext=str_replace('<{ymin}>',$ymin,$modeltext);// Y轴 最小
		$modeltext=str_replace('<{ymax}>',$ymax,$modeltext);
		$modeltext=str_replace('<{title_bottom}>',$title_bottom,$modeltext);
		$modeltext=str_replace('<{select0}>',$select0,$modeltext);
		$modeltext=str_replace('<{select1}>',$select1,$modeltext);
		$modeltext=str_replace('<{isnew}>',$isnew,$modeltext);
		
		echo "$modeltext";exit;
	}
	// update end by gzw 20210317 华东地区钢厂月采购价格指数  狄艳冰需求 
}
?>