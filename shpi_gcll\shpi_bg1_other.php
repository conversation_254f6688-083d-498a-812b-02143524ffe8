<?php
/*
钢材 原料 综合指数图
*/
require_once('../include/configall.php'); 
require_once('../shpi/include/function.php');
require_once('../shpi/include/function_queryaction.php');
require_once('../tksshpi/include/TksShpi.Class.php');
require_once("../shpi/material/tks_weight_fun.php");
// require_once ('/etc/steelconf/config/ess|config.php');
 error_reporting(E_ERROR);
ini_set("display_errors","Off"); 
// error_reporting(E_ERROR);
// ini_set("display_errors","on"); 
// $conn_ess = ADONewConnection('mysql');
// $conn_ess->Connect($hostname_ess,$username_ess,$password_ess,'ess');

function zhangdie1($data_index){
	//$data_index = round($data_y-$data_x,2);
	if(is_numeric($data_index))
	{
		if($data_index>0){
			return "<font color=red><strong>↑".abs($data_index)."</strong></font>";
		}elseif($data_index<0){
			return "<font color=green><strong>↓".abs($data_index)."</strong></font>";
		}else{
			return "<strong>-</strong>";
		}
	}
	else
	{
		return "<strong>-</strong>";
	}
	
}
//$connall->Connect('**********:4307','dbread','sth@50581010','steelhome');
//---------------------------
	$tpl = new Template("../"); 
	$array_tpls = array(
		"gcpd" => "shpi_gcll/tpl/shpi_bg1_other.html",
	);
	$tpl->set_file($array_tpls);
//================================
    $shpi=new shpi($connall);
    $gjshpi=new TksShpi($connall);
	
	$year1 = $_REQUEST['eyear'];
	$mon1 = $_REQUEST['emonth'];
	$day1 = $_REQUEST['eday'];
	
	$year2 = $_REQUEST['syear'];
	$mon2 = $_REQUEST['smonth'];
	$day2 = $_REQUEST['sday'];

	if (isset($year1))
	{
	$qdate=$year1."-".$mon1."-".$day1;
	}
	else
	{
	  $sql="select * from shpi_pi order by dateday desc limit 1";
	  $rs=$connall->Execute($sql);
	  if (!$rs->EOF)
	  {
	    $qdate=$rs->fields("dateday"); 
	  }
	}
	$last_qdate = date("Y",strtotime("-1 year",strtotime($qdate)));
	//================================指数环比
	   $sql="select * from shpi_pi where dateday='".$qdate."' order by dateday desc limit 1";
	  $rs=$connall->Execute($sql);$i=1;
	  if (!$rs->EOF)
	 {
	    $datadayx=$rs->fields("dateday"); 
		$price1=$rs->fields("wpindex");
		$ttdatadayx=$rs->fields("dateday");
		$tpl->set_var("pia",round($price1,2));
		$pia_1 = round($price1,2);
		$tpl->set_var("dateshpi",date("Y年m月d日",strtotime($datadayx)));
	  }

	  $sql="select * from shpi_pp where dateday='".$datadayx."' order by dateday desc";
	  $rsa=$connall->Execute($sql);$i=1;
      if($rsa)
	  {
		while (!$rsa->EOF)
		{
		  $datadayx=$rsa->fields("dateday");
		  $price1=$rsa->fields("wpindex");
		  $bc_id=$rsa->fields("bc_id");
		  $tpl->set_var("pi".$bc_id."x",round($price1,2));
		  $pp[$bc_id] = round($price1,2);
		  $rsa->MoveNext();
		}
	  }
	  
	  // echo $sql;
	 $wdw=$ttdatadayx;

	 $weekd=$shpi->weekday($wdw);
	 
	  $pia1=$shpi->pi_bj($connall,$weekd);
	  $pi1a=$shpi->pi_bpzbj($connall,$weekd,1);
	  $pi2a=$shpi->pi_bpzbj($connall,$weekd,2);
	  $pi3a=$shpi->pi_bpzbj($connall,$weekd,3);
	  $pi4a=$shpi->pi_bpzbj($connall,$weekd,4);
	  $pi5a=$shpi->pi_bpzbj($connall,$weekd,5);
	  $pi6a=$shpi->pi_bpzbj($connall,$weekd,6);
	  $pi7a=$shpi->pi_bpzbj($connall,$weekd,7);
	  
	  $pi1ax=$shpi->pi_bpzbj($connall,$weekd,1);
	  $pi2ax=$shpi->pi_bpzbj($connall,$weekd,2);
	  $pi3ax=$shpi->pi_bpzbj($connall,$weekd,3);
	  $pi4ax=$shpi->pi_bpzbj($connall,$weekd,4);
	  $pi5ax=$shpi->pi_bpzbj($connall,$weekd,5);
	  $pi6ax=$shpi->pi_bpzbj($connall,$weekd,6);
	  $pi7ax=$shpi->pi_bpzbj($connall,$weekd,7);
	  
	  $tpl->set_var("pia1",zhangdie1($pia1));$tpl->set_var("pi1ax",zhangdie1($pi1a)); $tpl->set_var("pi2ax",zhangdie1($pi2a));$tpl->set_var("pi3ax",zhangdie1($pi3a));$tpl->set_var("pi4ax",zhangdie1($pi4a));$tpl->set_var("pi5ax",zhangdie1($pi5a));$tpl->set_var("pi6ax",zhangdie1($pi6a));$tpl->set_var("pi7ax",zhangdie1($pi7a));
      

	  //=====
	 // echo "<br>";
	  $mondayx=$shpi->monday($ttdatadayx);
	 // print_r($mondayx);
	  $pia2=$shpi->pi_bj($connall,$mondayx);
	  $pi1b=$shpi->pi_bpzbj($connall,$mondayx,1);
	  $pi2b=$shpi->pi_bpzbj($connall,$mondayx,2);
	  $pi3b=$shpi->pi_bpzbj($connall,$mondayx,3);
	  $pi4b=$shpi->pi_bpzbj($connall,$mondayx,4);
	  $pi5b=$shpi->pi_bpzbj($connall,$mondayx,5);
	  $pi6b=$shpi->pi_bpzbj($connall,$mondayx,6);
	  $pi7b=$shpi->pi_bpzbj($connall,$mondayx,7);
	  
	  $pi1bx=$shpi->pi_bpzbj($connall,$mondayx,1);
	  $pi2bx=$shpi->pi_bpzbj($connall,$mondayx,2);
	  $pi3bx=$shpi->pi_bpzbj($connall,$mondayx,3);
	  $pi4bx=$shpi->pi_bpzbj($connall,$mondayx,4);
	  $pi5bx=$shpi->pi_bpzbj($connall,$mondayx,5);
	  $pi6bx=$shpi->pi_bpzbj($connall,$mondayx,6);
	  $pi7bx=$shpi->pi_bpzbj($connall,$mondayx,7);
	  
	   $tpl->set_var("pia2",zhangdie1($pia2));$tpl->set_var("pi1bx",zhangdie1($pi1b)); $tpl->set_var("pi2bx",zhangdie1($pi2b));$tpl->set_var("pi3bx",zhangdie1($pi3b));$tpl->set_var("pi4bx",zhangdie1($pi4b));$tpl->set_var("pi5bx",zhangdie1($pi5b));$tpl->set_var("pi6bx",zhangdie1($pi6b));$tpl->set_var("pi7bx",zhangdie1($pi7b));
	  //========
	 // echo "<br>";
	  $seasonx=$shpi->season($ttdatadayx);
	 // print_r($seasonx);
	  $pia3=$shpi->pi_bj($connall,$seasonx);
	  $pi1c=$shpi->pi_bpzbj($connall,$seasonx,1);
	  $pi2c=$shpi->pi_bpzbj($connall,$seasonx,2);
	  $pi3c=$shpi->pi_bpzbj($connall,$seasonx,3);
	  $pi4c=$shpi->pi_bpzbj($connall,$seasonx,4);
	  $pi5c=$shpi->pi_bpzbj($connall,$seasonx,5);
	  $pi6c=$shpi->pi_bpzbj($connall,$seasonx,6);
	  $pi7c=$shpi->pi_bpzbj($connall,$seasonx,7);
	  
	  $pi1cx=$shpi->pi_bpzbj($connall,$seasonx,1);
	  $pi2cx=$shpi->pi_bpzbj($connall,$seasonx,2);
	  $pi3cx=$shpi->pi_bpzbj($connall,$seasonx,3);
	  $pi4cx=$shpi->pi_bpzbj($connall,$seasonx,4);
	  $pi5cx=$shpi->pi_bpzbj($connall,$seasonx,5);
	  $pi6cx=$shpi->pi_bpzbj($connall,$seasonx,6);
	  $pi7cx=$shpi->pi_bpzbj($connall,$seasonx,7);
	  
	  $tpl->set_var("pia3",zhangdie1($pia3));$tpl->set_var("pi1cx",zhangdie1($pi1c)); $tpl->set_var("pi2cx",zhangdie1($pi2c));$tpl->set_var("pi3cx",zhangdie1($pi3c));$tpl->set_var("pi4cx",zhangdie1($pi4c));$tpl->set_var("pi5cx",zhangdie1($pi5c));$tpl->set_var("pi6cx",zhangdie1($pi6c));$tpl->set_var("pi7cx",zhangdie1($pi7c));
	    //========
	  //echo "<br>";
	  $yeardayx=$shpi->yearday($ttdatadayx);
	 // print_r($yeardayx);
	  $pia4=$shpi->pi_bj($connall,$yeardayx);
	  $pi1d=$shpi->pi_bpzbj($connall,$yeardayx,1);
	  $pi2d=$shpi->pi_bpzbj($connall,$yeardayx,2);
	  $pi3d=$shpi->pi_bpzbj($connall,$yeardayx,3);
	  $pi4d=$shpi->pi_bpzbj($connall,$yeardayx,4);
	  $pi5d=$shpi->pi_bpzbj($connall,$yeardayx,5);
	  $pi6d=$shpi->pi_bpzbj($connall,$yeardayx,6);
	  $pi7d=$shpi->pi_bpzbj($connall,$yeardayx,7);
	  
	  $pi1dx=$shpi->pi_bpzbj($connall,$yeardayx,1);
	  $pi2dx=$shpi->pi_bpzbj($connall,$yeardayx,2);
	  $pi3dx=$shpi->pi_bpzbj($connall,$yeardayx,3);
	  $pi4dx=$shpi->pi_bpzbj($connall,$yeardayx,4);
	  $pi5dx=$shpi->pi_bpzbj($connall,$yeardayx,5);
	  $pi6dx=$shpi->pi_bpzbj($connall,$yeardayx,6);
	  $pi7dx=$shpi->pi_bpzbj($connall,$yeardayx,7);
	  
	  $tpl->set_var("pia4",zhangdie1($pia4));$tpl->set_var("pi1dx",zhangdie1($pi1d)); $tpl->set_var("pi2dx",zhangdie1($pi2d));$tpl->set_var("pi3dx",zhangdie1($pi3d));$tpl->set_var("pi4dx",zhangdie1($pi4d));$tpl->set_var("pi5dx",zhangdie1($pi5d));$tpl->set_var("pi6dx",zhangdie1($pi6d));$tpl->set_var("pi7dx",zhangdie1($pi7d));
	  //==========年同比
	  $seyy=(date("Y",strtotime($ttdatadayx))-1)."-".date("m",strtotime($ttdatadayx))."-".date("d",strtotime($ttdatadayx));
	  $seyd=array( "se"=>$seyy,"ss" =>(date("Y",strtotime($ttdatadayx))-1).'-01-01',"be" =>$ttdatadayx,"bs" =>date("Y",strtotime($ttdatadayx)).'-01-01');
	  
	  //$seyd=$shpi->ntb_year($ttdatadayx);
	  $pia5=$shpi->pi_bj($connall,$seyd);
	  $pi1e=$shpi->pi_bpzbj($connall,$seyd,1);
	  $pi2e=$shpi->pi_bpzbj($connall,$seyd,2);
	  $pi3e=$shpi->pi_bpzbj($connall,$seyd,3);
	  $pi4e=$shpi->pi_bpzbj($connall,$seyd,4);
	  $pi5e=$shpi->pi_bpzbj($connall,$seyd,5);
	  $pi6e=$shpi->pi_bpzbj($connall,$seyd,6);
	  $pi7e=$shpi->pi_bpzbj($connall,$seyd,7);
	  
	  $pi1ex=$shpi->pi_bpzbj($connall,$seyd,1);
	  $pi2ex=$shpi->pi_bpzbj($connall,$seyd,2);
	  $pi3ex=$shpi->pi_bpzbj($connall,$seyd,3);
	  $pi4ex=$shpi->pi_bpzbj($connall,$seyd,4);
	  $pi5ex=$shpi->pi_bpzbj($connall,$seyd,5);
	  $pi6ex=$shpi->pi_bpzbj($connall,$seyd,6);
	  $pi7ex=$shpi->pi_bpzbj($connall,$seyd,7);
	  
	  $tpl->set_var("pia5",zhangdie1($pia5));$tpl->set_var("pi1ex",zhangdie1($pi1e)); $tpl->set_var("pi2ex",zhangdie1($pi2e));$tpl->set_var("pi3ex",zhangdie1($pi3e));$tpl->set_var("pi4ex",zhangdie1($pi4e));$tpl->set_var("pi5ex",zhangdie1($pi5e));$tpl->set_var("pi6ex",zhangdie1($pi6e));$tpl->set_var("pi7ex",zhangdie1($pi7e));
	  //==========月同比
	  
	  if ((date("m",strtotime($ttdatadayx))-1)<0){	$yab=date("Y",strtotime($ttdatadayx))-1;$mab=12;}
	  else{	$yab=date("Y",strtotime($ttdatadayx))-1;$mab=date("m",strtotime($ttdatadayx));}
	  $semd=array("se"=>$yab.'-'.$mab.'-31',"ss" =>$yab.'-'.$mab.'-01',
	  "be" =>date("Y",strtotime($ttdatadayx)).'-'.date("m",strtotime($ttdatadayx)).'-'.date("d",strtotime($ttdatadayx)),
	  "bs" =>date("Y",strtotime($ttdatadayx)).'-'.date("m",strtotime($ttdatadayx)).'-01');
	  //print_r($semd);
	  $pia6=$shpi->pi_bj($connall,$semd);
	  $pi1f=$shpi->pi_bpzbj($connall,$semd,1);
	  $pi2f=$shpi->pi_bpzbj($connall,$semd,2);
	  $pi3f=$shpi->pi_bpzbj($connall,$semd,3);   
	  $pi4f=$shpi->pi_bpzbj($connall,$semd,4);
	  $pi5f=$shpi->pi_bpzbj($connall,$semd,5);
	  $pi6f=$shpi->pi_bpzbj($connall,$semd,6);
	  $pi7f=$shpi->pi_bpzbj($connall,$semd,7);
	  
	  $pi1fx=$shpi->pi_bpzbj($connall,$semd,1);
	  $pi2fx=$shpi->pi_bpzbj($connall,$semd,2);
	  $pi3fx=$shpi->pi_bpzbj($connall,$semd,3);   
	  $pi4fx=$shpi->pi_bpzbj($connall,$semd,4);
	  $pi5fx=$shpi->pi_bpzbj($connall,$semd,5);
	  $pi6fx=$shpi->pi_bpzbj($connall,$semd,6);
	  $pi7fx=$shpi->pi_bpzbj($connall,$semd,7);
	  
	  $tpl->set_var("pia6",zhangdie1($pia6));$tpl->set_var("pi1fx",zhangdie1($pi1f)); $tpl->set_var("pi2fx",zhangdie1($pi2f));$tpl->set_var("pi3fx",zhangdie1($pi3f));$tpl->set_var("pi4fx",zhangdie1($pi4f));$tpl->set_var("pi5fx",zhangdie1($pi5f));$tpl->set_var("pi6fx",zhangdie1($pi6f));$tpl->set_var("pi7fx",zhangdie1($pi7f));
	  //=============================== 
        $stime=(date("Y")-1)."-".date("m")."-".date("d"); 
	 	$etime=date("Y-m-d");
 		$arr_name=array("year"=>"year1","mon"=>"mon1","day"=>"day1");
	    $stimes=$shpi->date_options($arr_name,$ttdatadayx);
	    $arr_name1=array("year"=>"year2","mon"=>"mon2","day"=>"day2");
	    $etimes=$shpi->date_options($arr_name1,$etime);
	    $times=$stimes;
		$tpl->set_var("datetime",$times);
		
	  //取数查询当天钢材品种所有指数
	  $sql="select * from shpi_pzp where dateday='".$datadayx."' and bvm_id in ('1','2','3','4','5','6','8','9') order by dateday desc limit 8";
	  $rsa=$connall->Execute($sql);$i=1;
	  if($rsa)
	  {
		while (!$rsa->EOF)
		{
		  $datadayx=$rsa->fields("dateday");
		  $price1=$rsa->fields("weiprice");
		  $price2=$rsa->fields("wpindex");
		  $bvm_id=$rsa->fields("bvm_id");
		  $tpl->set_var("pz".$bvm_id,round($price1));
		  $tpl->set_var("pi".$bvm_id,round($price2,2));
		  $pi[$bvm_id] = round($price2,2);
		  $rsa->MoveNext();
		}
	  }
	  
	  $wdw=$ttdatadayx;
	  $weekd=$shpi->weekday($wdw);
	  $mondayx=$shpi->monday($ttdatadayx);
	  $seasonx=$shpi->season($ttdatadayx);
	  $yeardayx=$shpi->yearday($ttdatadayx);
	  //钢材
	  
	  $array_v=array(1,2,3,4,5,6,8,9);
	  foreach ($array_v as $nk=>$nv)
	  {
	      $wstr="pi".$nv."a";$mstr="pi".$nv."b";$sstr="pi".$nv."c";$ystr="pi".$nv."d";$ytstr="pi".$nv."e";$mtstr="pi".$nv."f";
		  $$wstr=$shpi->pzpi_bpzbj($connall,$weekd,$nv);
		  $$mstr=$shpi->pzpi_bpzbj($connall,$mondayx,$nv);
		  $$sstr=$shpi->pzpi_bpzbj($connall,$seasonx,$nv);
		  $$ystr=$shpi->pzpi_bpzbj($connall,$yeardayx,$nv);
		  $$ytstr=$shpi->pzpi_bpzbj($connall,$seyd,$nv);
		  $$mtstr=$shpi->pzpi_bpzbj($connall,$semd,$nv);
		  $tpl->set_var($wstr,zhangdie1($$wstr)); $tpl->set_var($mstr,zhangdie1($$mstr));$tpl->set_var($sstr,zhangdie1($$sstr));
		  $tpl->set_var($ystr,zhangdie1($$ystr));$tpl->set_var($ytstr,zhangdie1($$ytstr));$tpl->set_var($mtstr,zhangdie1($$mtstr));
	  }
	  //========
	  //煤炭 炼焦煤 动力煤 喷吹煤
	  
	  $week_day = $shpi->weekday($ttdatadayx);
	   $month_day = $shpi->monday($ttdatadayx);
	  $ytb_day = $shpi->ytb_year($ttdatadayx);
	  $season_day = $shpi->season($ttdatadayx);
	  $yearday_day = $shpi->yearday($ttdatadayx);
	  $ntb_day = $shpi->ntb_year($ttdatadayx);
	  
	  // print_r($week_day);exit;
	  $i=0;
	  $s=0;
	  for($i=0;$i<4;$i++){
	  	$zhishu = $shpi->shpi_coal_zs($ttdatadayx,$s,$i);
	  	$zhishu = floor($zhishu*100)/100;
	  	$tpl->set_var("zhishu$i",$zhishu);
	  	$price = $shpi->shpi_coal_price($ttdatadayx,$s,$i);
	  	$price = floor($price*100)/100;
	  	$tpl->set_var("price$i",zhangdie1($price));
	  
	  	$week_zhishu = $shpi->shpi_coal_zhb($week_day,$s,$i);
	  	$tpl->set_var("week$i",zhangdie1($week_zhishu));
	  	$month_zhishu = $shpi->shpi_coal_zhb($month_day,$s,$i);
	  	$tpl->set_var("month$i",zhangdie1($month_zhishu));
	  	$ytb_zhishu = $shpi->shpi_coal_zhb($ytb_day,$s,$i);
	  	$tpl->set_var("ytb$i",zhangdie1($ytb_zhishu));
	  	$season_zhishu = $shpi->shpi_coal_zhb($season_day,$s,$i);
	  	$tpl->set_var("season$i",zhangdie1($season_zhishu));
	  	$yearday_zhishu = $shpi->shpi_coal_zhb($yearday_day,$s,$i);
	  	$tpl->set_var("yearday$i",zhangdie1($yearday_zhishu));
	  	$ntb_zhishu = $shpi->shpi_coal_zhb($ntb_day,$s,$i);
	  	$tpl->set_var("ntb$i",zhangdie1($ntb_zhishu));
	  } 
	  
     //指数替换

$topicture_list = [1,2,3,4,5,6,7,8,9,51];
foreach ($topicture_list as $z) {
//}
//    for($z=1;$z<10;$z++)
//    {
    	
     $msql="select mindex from shpi_material where topicture='".$z."' and dateday='".$ttdatadayx."'";
     
	 //原料当天指数
     $mquery=$connall->Execute($msql);
     $mindex=$mquery?$mquery->fields("mindex"):0;
     $mindex=round($mindex,2);
	 $max[$z] = $mindex;
     $tpl->set_var("ma-".$z,$mindex); 	
    	
     $mw=$shpi->material_week_tb($connall,$weekd,$z);
     $tpl->set_var("mw-".$z,zhangdie1($mw));
	 $mwx[$z] = $mw;
	  
     $mmh=$shpi->material_week_tb($connall,$mondayx,$z);
     $tpl->set_var("mmh-".$z,zhangdie1($mmh));
     $mmhx[$z] = $mmh;
	 
     //月同比
     $mmt=$shpi->material_week_tb($connall,$semd,$z);
     $tpl->set_var("mmt-".$z,zhangdie1($mmt));
	 $mmtx[$z] = $mmt;
     //季环比
     $mjh=$shpi->material_week_tb($connall,$seasonx,$z);
     $tpl->set_var("mjh-".$z,zhangdie1($mjh));
	 $mjhx[$z] = $mjh;
     //年环比
     $myh=$shpi->material_week_tb($connall,$yeardayx,$z);
     $tpl->set_var("myh-".$z,zhangdie1($myh));
	 $myhx[$z] = $myh;
     //年同比
     $myt=$shpi->material_week_tb($connall,$seyd,$z);
     $tpl->set_var("myt-".$z,zhangdie1($myt));
	 $mytx[$z] = $myt;
    } 
    //铁矿石
    
    $todayindex=$gjshpi->TodayIndex_yl($ttdatadayx);
    $todayrate=$gjshpi->TodayRate_yl($ttdatadayx);
    if(!$todayindex){
    	$todayindex['0']=0;
    	$todayindex['1']=0;
    	$todayindex['3']=0;
    }
    if(!$todayrate){
    	$todayrate['0']="―";
    	$todayrate['1']="―";
    	$todayrate['3']="―";
    }
    //$weekrate=$gjshpi->Weekrate($ttdatadayx);
    $weekrate=$gjshpi->Weekrate('',$weekd);
    $monthrate=$gjshpi->MonthRate($ttdatadayx);
    $yearrate=$gjshpi->YearRate($ttdatadayx);
    $monthsame=$gjshpi->MonthSameRate($ttdatadayx);
    $seasonrate=$gjshpi->QuarterRate($ttdatadayx);
    $yearsame=$gjshpi->YearSameRate($ttdatadayx);
	
	
	function zhangdie($data_index){
		if(is_numeric($data_index))
		{
			if($data_index>0){
				$index_zd = "上涨".abs($data_index)."%";
			}elseif($data_index<0){
				$index_zd = "下跌".abs($data_index)."%";
			}else{
				$index_zd = "持平";
			}
		}
		else
		{
			$index_zd = "持平";
		}
		
		return $index_zd;
	}

	function zhangdie_ms($data_index){
		if(is_numeric($data_index))
		{
			$ms = "";
			if(abs($data_index) > 2) {
				$ms = "明显";
			} else if(abs($data_index) < 1 && abs($data_index) > 0) {
				$ms = "稳中有";
			} else {
				$ms = "小幅";
			}
			if($data_index>0){
				$index_zd = "{$ms}上涨";
				if($ms == "稳中有") {
					$index_zd = "{$ms}涨";
				}
			}elseif($data_index<0){
				$index_zd = "{$ms}下跌";
				if($ms == "稳中有") {
					$index_zd = "{$ms}跌";
				}
			}else{
				$index_zd = "{$ms}基本稳定";
			}
		}
		else
		{
			$index_zd = "基本稳定";
		}
		
		return $index_zd;
	}
	
	// $content = "<p>　　钢之家讯：本周国内钢市<font color='red'>【自定义】</font>，其中长材价格<font color='red'>【自定义】</font>。截至本周末，钢之家钢材基准价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI</a>）为".$pia_1."点，较上周".zhangdie($pia1)."，较上月".zhangdie($pia2)."，较去年同月".zhangdie($pia6)."，较去年全年".zhangdie($pia4)."，较去年同期".zhangdie($pia5)."。本周各主要品种钢材价格指数变动如下：<br />　　长材：本周国内长材价格<font color='red'>【自定义】</font>。截至本周末，钢之家长材价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-L</a>）为".$pp['1']."点，较上周".zhangdie($pi1ax)."，较上月".zhangdie($pi1bx)."，较去年同月".zhangdie($pi1fx)."，较去年全年".zhangdie($pi1dx)."，较去年同期".zhangdie($pi1ex)."。其中，钢之家普线材价格指数（SHSPI-WR）为".$pi['1']."点，较上周".zhangdie($pi1a)."，较上月".zhangdie($pi1b)."，较去年同月".zhangdie($pi1f)."，较去年全年".zhangdie($pi1d)."，较去年同期".zhangdie($pi1e)."；钢之家螺纹钢价格指数（SHSPI-RB）为".$pi['2']."点，较上周".zhangdie($pi2a)."，较上月".zhangdie($pi2b)."，较去年同月".zhangdie($pi2f)."，较去年全年".zhangdie($pi2d)."，较去年同期".zhangdie($pi2e)."；型材价格指数（SHSPI-SB）为".$pi['3']."点，较上周".zhangdie($pi3a)."，较上月".zhangdie($pi3b)."，较去年同月".zhangdie($pi3f)."，较去年全年".zhangdie($pi3d)."，较去年同期".zhangdie($pi3e)."。<br /><img src=".APP_URL_WWW."/shpi/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=0||1||2&gj&leixing=1 /><br />　　扁平材：本周国内扁平材市场价格<font color='red'>【自定义】</font>。钢之家扁平材价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-F</a>）为".$pp['2']."点，较上周".zhangdie($pi2ax)."，较上月".zhangdie($pi2bx)."，较去年同月".zhangdie($pi2fx)."，较去年全年".zhangdie($pi2dx)."，较去年同期".zhangdie($pi2ex)."。其中，钢之家中厚板价格指数（SHSPI-MP）为".$pi['4']."点，较上周".zhangdie($pi4a)."，较上月".zhangdie($pi4b)."，较去年同月".zhangdie($pi4f)."，较去年全年".zhangdie($pi4d)."，较去年同期".zhangdie($pi4e)."；热轧板卷价格指数（SHSPI-HR）为".$pi['5']."点，较上周".zhangdie($pi5a)."，较上月".zhangdie($pi5b)."，较去年同月".zhangdie($pi5f)."，较去年全年".zhangdie($pi5d)."，较去年同期".zhangdie($pi5e)."；冷轧板卷价格指数（SHSPI-CR）为".$pi['6']."点，较上周".zhangdie($pi6a)."，较上月".zhangdie($pi6b)."，较去年同月".zhangdie($pi6f)."，较去年全年".zhangdie($pi6d)."，较去年同期".zhangdie($pi6e)."。<br /><img src=".APP_URL_WWW."/shpi/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=0||3&gj&leixing=1 /><br />　　优特钢：本周国内优特钢市场价格<font color='red'>【自定义】</font>。截至本周末，钢之家优特钢价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-S</a>）为".$pp['3']."点，较上周".zhangdie($pi3ax)."，较上月".zhangdie($pi3bx)."，较去年同月".zhangdie($pi3fx)."，较去年全年".zhangdie($pi3dx)."，较去年同期".zhangdie($pi3ex)."。其中，优线价格指数（SHSPI-QW）为".$pi['8']."点，较上周".zhangdie($pi8a)."，较上月".zhangdie($pi8b)."，较去年同月".zhangdie($pi8f)."，较去年全年".zhangdie($pi8d)."，较去年同期".zhangdie($pi8e)."；结构钢价格指数（SHSPI-CA）为".$pi['9']."点，较上周".zhangdie($pi9a)."，较上月".zhangdie($pi9b)."，较去年同月".zhangdie($pi9f)."，较去年全年".zhangdie($pi9d)."，较去年同期".zhangdie($pi9e)."。<br /><img src=".APP_URL_WWW."/shpi/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=0||4&gj&leixing=1 /><br />　　不锈钢：本周国内不锈钢市场价格<font color='red'>【自定义】</font>。截至本周末，钢之家不锈钢价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-SS</a>）为".$pp['4']."点，较上周".zhangdie($pi4ax)."，较上月".zhangdie($pi4bx)."，较去年同月".zhangdie($pi4fx)."，较去年全年".zhangdie($pi4dx)."，较去年同期".zhangdie($pi4ex)."。<br /><img src=".APP_URL_WWW."/shpi-l/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=m1||k3&gj&leixing=1 /><br />　　钢铁原料：本周国内原料市场<font color='red'>【自定义】</font>。截至本周末，钢之家钢铁原料价格指数（SHMPI）为".$max['1']."点，较上周".zhangdie($mwx['1'])."，较上月".zhangdie($mmhx['1'])."，较去年同月".zhangdie($mmtx['1'])."，较去年全年".zhangdie($myhx['1'])."，较去年同期".zhangdie($mytx['1'])."。其中，进口铁矿石价格指数（SHIOPI）为".$todayindex['3']."点，较上周".zhangdie($weekrate['3'])."，较上月".zhangdie($monthrate['3'])."，较去年同月".zhangdie($monthsame['3'])."，较去年全年".zhangdie($yearrate['3'])."，较去年同期".zhangdie($yearsame['3'])."。焦炭（SHCKI）价格指数为".$max['3']."点，较上周".zhangdie($mwx['3'])."，较上月".zhangdie($mmhx['3'])."，较去年同月".zhangdie($mmtx['3'])."，较去年全年".zhangdie($myhx['3'])."，较去年同期".zhangdie($mytx['3'])."；废钢价格指数（SHFSPI）为".$max['4']."点，较上周".zhangdie($mwx['4'])."，较上月".zhangdie($mmhx['4'])."，较去年同月".zhangdie($mmtx['4'])."，较去年全年".zhangdie($myhx['4'])."，较去年同期".zhangdie($mytx['4'])."；铁合金价格指数（SHFPI）为".$max['5']."点，较上周".zhangdie($mwx['5'])."，较上月".zhangdie($mmhx['5'])."，较去年同月".zhangdie($mmtx['5'])."，较去年全年".zhangdie($myhx['5'])."，较去年同期".zhangdie($mytx['5'])."。</p>";
	// 2025-09-08 更改
	$content = "<p>　　钢之家讯：本周国内钢市".zhangdie_ms($pia1)."。截至本周末，钢之家钢材基准价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI</a>）为".$pia_1."点，较上周".zhangdie($pia1)."，较上月".zhangdie($pia2)."，较去年同月".zhangdie($pia6)."，较去年全年".zhangdie($pia4)."，较去年同期".zhangdie($pia5)."。本周各主要品种钢材价格指数变动如下：<br />　　长材：本周国内长材价格".zhangdie_ms($pi1ax)."。截至本周末，钢之家长材价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-L</a>）为".$pp['1']."点，较上周".zhangdie($pi1ax)."，较上月".zhangdie($pi1bx)."，较去年同月".zhangdie($pi1fx)."，较去年全年".zhangdie($pi1dx)."，较去年同期".zhangdie($pi1ex)."。其中，钢之家普线材价格指数（SHSPI-WR）为".$pi['1']."点，较上周".zhangdie($pi1a)."，较上月".zhangdie($pi1b)."，较去年同月".zhangdie($pi1f)."，较去年全年".zhangdie($pi1d)."，较去年同期".zhangdie($pi1e)."；钢之家螺纹钢价格指数（SHSPI-RB）为".$pi['2']."点，较上周".zhangdie($pi2a)."，较上月".zhangdie($pi2b)."，较去年同月".zhangdie($pi2f)."，较去年全年".zhangdie($pi2d)."，较去年同期".zhangdie($pi2e)."；型材价格指数（SHSPI-SB）为".$pi['3']."点，较上周".zhangdie($pi3a)."，较上月".zhangdie($pi3b)."，较去年同月".zhangdie($pi3f)."，较去年全年".zhangdie($pi3d)."，较去年同期".zhangdie($pi3e)."。<br /><img src=".APP_URL_WWW."/shpi/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=0||1||2&gj&leixing=1 /><br />　　扁平材：本周国内扁平材市场价格".zhangdie_ms($pi2ax)."。钢之家扁平材价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-F</a>）为".$pp['2']."点，较上周".zhangdie($pi2ax)."，较上月".zhangdie($pi2bx)."，较去年同月".zhangdie($pi2fx)."，较去年全年".zhangdie($pi2dx)."，较去年同期".zhangdie($pi2ex)."。其中，钢之家中厚板价格指数（SHSPI-MP）为".$pi['4']."点，较上周".zhangdie($pi4a)."，较上月".zhangdie($pi4b)."，较去年同月".zhangdie($pi4f)."，较去年全年".zhangdie($pi4d)."，较去年同期".zhangdie($pi4e)."；热轧板卷价格指数（SHSPI-HR）为".$pi['5']."点，较上周".zhangdie($pi5a)."，较上月".zhangdie($pi5b)."，较去年同月".zhangdie($pi5f)."，较去年全年".zhangdie($pi5d)."，较去年同期".zhangdie($pi5e)."；冷轧板卷价格指数（SHSPI-CR）为".$pi['6']."点，较上周".zhangdie($pi6a)."，较上月".zhangdie($pi6b)."，较去年同月".zhangdie($pi6f)."，较去年全年".zhangdie($pi6d)."，较去年同期".zhangdie($pi6e)."。<br /><img src=".APP_URL_WWW."/shpi/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=0||3&gj&leixing=1 /><br />　　优特钢：本周国内优特钢市场价格".zhangdie_ms($pi3ax)."。截至本周末，钢之家优特钢价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-S</a>）为".$pp['3']."点，较上周".zhangdie($pi3ax)."，较上月".zhangdie($pi3bx)."，较去年同月".zhangdie($pi3fx)."，较去年全年".zhangdie($pi3dx)."，较去年同期".zhangdie($pi3ex)."。其中，优线价格指数（SHSPI-QW）为".$pi['8']."点，较上周".zhangdie($pi8a)."，较上月".zhangdie($pi8b)."，较去年同月".zhangdie($pi8f)."，较去年全年".zhangdie($pi8d)."，较去年同期".zhangdie($pi8e)."；结构钢价格指数（SHSPI-CA）为".$pi['9']."点，较上周".zhangdie($pi9a)."，较上月".zhangdie($pi9b)."，较去年同月".zhangdie($pi9f)."，较去年全年".zhangdie($pi9d)."，较去年同期".zhangdie($pi9e)."。<br /><img src=".APP_URL_WWW."/shpi/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=0||4&gj&leixing=1 /><br />　　不锈钢：本周国内不锈钢市场价格".zhangdie_ms($pi4ax)."。截至本周末，钢之家不锈钢价格指数（<a href=".APP_URL_WWW."/shpi/index.php target=_blank>SHSPI-SS</a>）为".$pp['4']."点，较上周".zhangdie($pi4ax)."，较上月".zhangdie($pi4bx)."，较去年同月".zhangdie($pi4fx)."，较去年全年".zhangdie($pi4dx)."，较去年同期".zhangdie($pi4ex)."。<br /><img src=".APP_URL_WWW."/shpi-l/shpi_qzht.php?stime=".$last_qdate."-01-01&etime=".$qdate."&type=m1||k3&gj&leixing=1 /><br />　　钢铁原料：本周国内原料市场".zhangdie_ms($mwx['1'])."。截至本周末，钢之家钢铁原料价格指数（SHMPI）为".$max['1']."点，较上周".zhangdie($mwx['1'])."，较上月".zhangdie($mmhx['1'])."，较去年同月".zhangdie($mmtx['1'])."，较去年全年".zhangdie($myhx['1'])."，较去年同期".zhangdie($mytx['1'])."。其中，进口铁矿石价格指数（SHIOPI）为".$todayindex['3']."点，较上周".zhangdie($weekrate['3'])."，较上月".zhangdie($monthrate['3'])."，较去年同月".zhangdie($monthsame['3'])."，较去年全年".zhangdie($yearrate['3'])."，较去年同期".zhangdie($yearsame['3'])."。焦炭（SHCKI）价格指数为".$max['3']."点，较上周".zhangdie($mwx['3'])."，较上月".zhangdie($mmhx['3'])."，较去年同月".zhangdie($mmtx['3'])."，较去年全年".zhangdie($myhx['3'])."，较去年同期".zhangdie($mytx['3'])."；废钢价格指数（SHFSPI）为".$max['4']."点，较上周".zhangdie($mwx['4'])."，较上月".zhangdie($mmhx['4'])."，较去年同月".zhangdie($mmtx['4'])."，较去年全年".zhangdie($myhx['4'])."，较去年同期".zhangdie($mytx['4'])."；铁合金价格指数（SHFPI）为".$max['5']."点，较上周".zhangdie($mwx['5'])."，较上月".zhangdie($mmhx['5'])."，较去年同月".zhangdie($mmtx['5'])."，较去年全年".zhangdie($myhx['5'])."，较去年同期".zhangdie($mytx['5'])."。</p>";
   
	echo $content;
	
 if($ttdatadayx>="2012-10-08"){
    	
    $tpl->set_var("ma-2",$todayindex['0']);
    $tpl->set_var("ma1-2",$todayrate['0']);
    $tpl->set_var("mw-2",zhangdie1($weekrate['0']));
    $tpl->set_var("mmh-2",zhangdie1($monthrate['0']));
    $tpl->set_var("mmt-2",zhangdie1($monthsame['0']));
    $tpl->set_var("mjh-2",zhangdie1($seasonrate['0']));
    $tpl->set_var("myh-2",zhangdie1($yearrate['0']));
    $tpl->set_var("myt-2",zhangdie1($yearsame['0']));
    }
    
    $tpl->set_var("tks_0",$todayindex['1']);
    $tpl->set_var("tks_1",zhangdie1($todayrate['1']));
    $tpl->set_var("tks_2",zhangdie1($weekrate['1']));
    $tpl->set_var("tks_3",zhangdie1($monthrate['1']));
    $tpl->set_var("tks_4",zhangdie1($monthsame['1']));
    $tpl->set_var("tks_5",zhangdie1($seasonrate['1']));
    $tpl->set_var("tks_6",zhangdie1($yearrate['1']));
    $tpl->set_var("tks_7",zhangdie1($yearsame['1']));
    
    $tpl->set_var("tks_i_0",$todayindex['3']);
    $tpl->set_var("tks_i_1",zhangdie1($todayrate['3']));
    $tpl->set_var("tks_i_2",zhangdie1($weekrate['3']));
    $tpl->set_var("tks_i_3",zhangdie1($monthrate['3']));
    $tpl->set_var("tks_i_4",zhangdie1($monthsame['3']));
    $tpl->set_var("tks_i_5",zhangdie1($seasonrate['3']));
    $tpl->set_var("tks_i_6",zhangdie1($yearrate['3']));
    $tpl->set_var("tks_i_7",zhangdie1($yearsame['3']));

	$tpl->parse("GCPD","gcpd"); 
	$tpl->p("GCPD");

?>

