       
        .page-title {
            padding: 10px 0;
            border-bottom: 1px solid #1E88E5;
            display: flex;
            align-items: center;
        }
        
        .page-title h1 {
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .user-controls {
            margin-left: auto;
            display: flex;
            gap: 15px;
        }
        
        .user-controls button {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .user-controls button:hover {
            color: #64B5F6;
        }
        
        /* 二级导航 */
        .secondary-nav {
            padding: 8px 0;
        }
        
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 4px 10px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .nav-links a.active {
            background-color: #1E88E5;
            font-weight: bold;
        }
        
        .nav-links a:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        /* 行业分类标签 */
        .industry-tags {
            padding: 8px 0;
            border-top: 1px solid #1E88E5;
            overflow-x: auto;
        }
        
        .tags-container {
            display: flex;
            gap: 8px;
            min-width: max-content;
        }
        
        .tags-container a {
            color: white;
            text-decoration: none;
            padding: 3px 8px;
            border-radius: 3px;
            background-color: rgba(255, 255, 255, 0.1);
            transition: background-color 0.3s;
        }
        
        .tags-container a:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        /* 主内容区 */
        main {
            flex-grow: 1;
            padding: 2px 0;
        }
        
        .content-wrapper {
            display: flex;
            flex-direction: column;
            gap: 5px;
            height:625px;
        }
        
        @media (min-width: 992px) {
            .content-wrapper {
                flex-direction: row;
            }
        }
        
        /* 地图区域 */
        .map-section {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }
        
        .section-header {
            background-color: #316CC4;
            color: white;
            padding: 5px 15px;
            font-weight: bold;
        }
        
        .map-container {
            position: relative;
            background-color: #E8F4FC;
            height: 645px;
        }
        
        .map-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .map-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .map-control-btn {
            width: 32px;
            height: 32px;
            background-color: white;
            border: none;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            color: #0052D9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }
        
        .map-control-btn:hover {
            background-color: #0052D9;
            color: white;
        }
        
        /* 地图标记点 */
        .map-marker {
            position: absolute;
            transform-origin: bottom center;
            animation: pulse 2s infinite;
            cursor: pointer;
        }
        
        .marker-dot {
            width: 20px;
            height: 20px;
            background-color: #0052D9;
            border-radius: 50%;
            border: 2px solid white;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .marker-dot.large {
            width: 24px;
            height: 24px;
            background-color: #0039A6;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        /* 筛选区域 */
        .filter-section {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            overflow: hidden;
			font-size:12px;
        }
        
        .filter-content {
            padding: 2px;
        }
        
        /* 搜索框 */
        .search-box {
            margin-bottom: 5px;
            display: flex;
        }
        
        .search-input {
            flex-grow: 1;
            padding: 5px 5px;
            border: 1px solid #ccc;
            border-right: none;
            border-radius: 4px 0 0 4px;
            outline: none;
        }
        
        .search-input:focus {
            border-color: #0052D9;
            box-shadow: inset 0 0 0 0.5px #0052D9;
        }
        
        .search-btn {
            background: linear-gradient(to right, #1E88E5, #0052D9);
            color: white;
            border: none;
            padding: 0 15px;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .search-btn:hover {
            background: linear-gradient(to right, #0D47A1, #0039A6);
        }
        
        /* 筛选条件 */
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-label {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .filter-label i {
            color: #0052D9;
            margin-right: 8px;
        }
        
        .filter-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            outline: none;
            cursor: pointer;
        }
        
        .filter-input:focus {
            border-color: #0052D9;
            box-shadow: inset 0 0 0 1px #0052D9;
        }
        
        .filter-btn {
            width: 100%;
            margin-top: 5px;
            background: linear-gradient(to right, #1E88E5, #0052D9);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn:hover {
            background: linear-gradient(to right, #0D47A1, #0039A6);
        }
        
        /* 底部按钮组 */
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .reset-btn {
            flex-grow: 1;
            padding: 8px;
            border: 1px solid #ccc;
            background-color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .reset-btn:hover {
            background-color: #f5f5f5;
        }
        
        .confirm-btn {
            flex-grow: 1;
            background: linear-gradient(to right, #1E88E5, #0052D9);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .confirm-btn:hover {
            background: linear-gradient(to right, #0D47A1, #0039A6);
        }
        
        
        .position_title{padding: 0px 5px;}
        
        
        .liebiao {
            width: 100%;
            margin: 0 auto;
        }
        
        /* 标题栏样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 24px;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            font-weight: 600;
        }
        
        
        .enterprise-count {
            background-color: rgba(22, 93, 255, 0.1);
            color: #165DFF;
            padding: 4px 12px;
            border-radius: 20px;
        }
        
        /* 企业列表容器 */
        .enterprise-list {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        /* 企业项样式 */
        .enterprise-item {
            display: flex;
            align-items: center;
            padding: 2px 3px 2px 1px;
            border-bottom: 1px solid #f2f3f5;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .enterprise-item:last-child {
            border-bottom: none;
        }
        
        .enterprise-item:hover {
            background-color: rgba(22, 93, 255, 0.05);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

		.enterprise-page {
            text-align: center;
			padding: 3px 0px;
            cursor: pointer;
        }
        
        .enterprise-letter {
            width: 21px;
            height: 21px;
            border-radius: 50%;
            background-color: #316CC4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 2px;
            flex-shrink: 0;
			font-size:12px;
        }
        
		.enterprise-name a{
			font-size:13px;
		}
        .enterprise-name {
            flex-grow: 1;
            font-weight: 500;
        }
        
        .enterprise-item i {
            color: #86909c;
        }
        
        /* 页脚样式 */
        .footer {
            margin-top: 24px;
            text-align: center;
            color: #86909c;
        }
        
        .footer p {
            margin-bottom: 8px;
        }
		.tdt-container{
			overflow: visible;
		}
		div, ul{overflow: visible;}

        .enterprise-name a{color: red;}
        @media screen and (min-width: 1401px) {
            *{
                font-size: 13px;
            }
            .enterprise-name a{
                font-size:14px;
            }
            .map-container {
                width: 930px;
            }
            .filter-section{
                width: 266px;
            }
            .enterprise-img img{
                width:24px;
            }
            /* .search-input{
                width:120px;
                padding: 5px 2px;
            }
            .search-select{
                width:56px;
                border-radius: 4px;
                margin-right: 3px;
                padding: 3px 1px;
            }
            .search-btn{padding: 0px 8px;} */
        }
        @media screen and (max-width: 1400px) {
            
            .map-container {
                width: 805px;
            }
            .filter-section{
                width: 230px;
            }
            .enterprise-img img{
                width:22px;
            }
            /* .search-input{
                width:120px;
                padding: 5px 2px;
            }
            .search-select{
                width:56px;
                border-radius: 4px;
                margin-right: 3px;
                padding: 3px 1px;
            }
            .search-btn{padding: 8px 5px;} */
        }
        .tdt-infowindow-content p {
            margin: 5px 0;
        }
