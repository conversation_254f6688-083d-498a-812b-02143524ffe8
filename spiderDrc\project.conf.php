<?php
//爬虫爬取时间间隔/分钟
define("INTERVALTIME", 60);
//获取页面数据模式
define("GET_LAST_DAY", 0);
define("GET_CUR_DAY", 1);

define("EVERYDAY", 1);

//BigType
define("RMB_BIGTYPE", 1);
define("SHIBOR_BIGTYPE", 2);
define("CZCE_BIGTYPE", 3);
define("CFFEX_BIGTYPE", 4);
define("SHFE_BIGTYPE", 5);
define("DCE_BIGTYPE", 6);
// xiangbin start
define("SQS_BIGTYPE", 7);
// xiangbin end
define("USD_BIGTYPE", 8);
define("CZCECJ_BIGTYPE", 9);
define("DCEHQ_BIGTYPE", 10);//by hezpeng
define("QUOTE_RATE_BIGTYPE", 11);//by zfy
define("sina_GuShi_BIGTYPE", 12);//by shizg
define("eastmoney_GuShi_BIGTYPE", 13);//by shizg

define("Federalreserve_BIGTYPE", 14);//by xiangbin

define("WHPJ_BIGTYPE", 15);//by lina
define("XYLENE_BIGTYPE", 16);//by lina
define("CANL_BIGTYPE", 17);//by lina
define("SHFEPM_BIGTYPE", 18);//by lina
define("SHFEKC_BIGTYPE", 19);//by lina
define("COAL_JINGJIA_BIGTYPE", 20);//by shizg
define("XNY_BIGTYPE", 21);//by znb
define("COAL_JINGJIA_TAIYUAN_BIGTYPE", 22);//by shizg
define("GZQHJYS_BIGTYPE", 23);//by hezpeng
define("COAL_JINGJIA_NEW_BIGTYPE", 24);//by hezpeng
define("MSE_MN_BIGTYPE", 25);//by hezpeng
define("CZCEKC_BIGTYPE", 26);//郑商所仓单日报

//Type
define("RMB_RATE", 101);				//人民币汇率中间价
define("SHIBOR_DATA", 102);				//Shibor利率数据
define("DLM_QH", 103);					//动力煤
define("JC_QH", 104);					//甲醇
define("PTA_QH", 105);					//PTA期货
define("CFFEX_DATA", 106);				//中金所股指期货
define("SHFE_DATA", 107);				//成交及持仓排名上期所
define("DCE_DATA_TKS", 108);			//铁矿石成交及持仓排名大商所
define("DCE_DATA_JT", 109);				//焦炭成交及持仓排名大商所
define("DCE_DATA_JT_HEYUE", 'j2201');				//合约号
define("DCE_DATA_JM", 110);				//焦煤成交及持仓排名大商所
// xiangbin start
define("SQS_SC", 111);//上期所原油
define("SQS_RU", 112);//上期所天然橡胶
define("SQS_AG", 113);//上期所白银
define("SQS_AU", 114);//上期所黄金
define("SQS_PB", 115);//上期所铅
define("SQS_ZN", 116);//上期所锌
define("SQS_AL", 117);//上期所铝
define("SQS_CU", 118);//上期所铜
define("SQS_SN", 119);//上期所锡
define("SQS_NI", 120);//上期所镍
define("SQS_YS", 121);//上期所有色金属
define("SQS_SS", 139);//上期所不锈钢
define("SQS_RB", 140);//上期所螺纹钢
define("SQS_HC", 141);//上期所热轧卷板
define("SQS_WR", 142);//上期所线材
define("SQS_AD", 242);//上期所铸造铝合金

define("SM_QH", 122);//硅锰
define("SF_QH", 123);//硅铁
define("CFFEX_TF_DATA", 124);//中金所5年国债
define("CFFEX_T_DATA", 125);//中金所10年国债
// xiangbin end
define("AUD_USD_TYPE", 126);	//澳元美元中间价
define("BRL_USD_TYPE", 127);	//雷尔美元中间价
define("EUR_USD_TYPE", 128);	//欧元美元中间价
define("INR_USD_TYPE", 129);	//印度卢比美元中间价
define("USD_JPY_TYPE", 130);	//美元日元中间价
define("RUB_USD_TYPE", 131);	//俄罗斯卢布美元中间价
define("KWR_USD_TYPE", 132);	//韩元美元中间价
define("THB_USD_TYPE", 133);	//泰铢美元中间价
define("USD_KWR_TYPE", 134);	//美元韩元中间价
define("USD_IRR_TYPE", 135);	//美元伊朗里亚尔中间价
define("GBP_USD_TYPE", 136);	//英镑美元中间价
define("GBP_ZAR_TYPE", 137);	//美元南非兰特中间价
define("USD_POINT_TYPE", 138);	//美元指数
define("CZCE_DATA_DLM", 143);	//郑商所动力煤
define("CZCE_DATA_GT", 144);	//郑商所硅铁
define("CZCE_DATA_GM", 145);	//郑商所硅锰
define("DCE_HQ_JT", 151);	//大商所焦炭
define("DCE_HQ_JM", 152);	//大商所焦煤
define("DCE_HQ_ZLY", 153);	//大商所棕榈油
define("DCE_HQ_TKS", 154);	//大商所铁矿石期货
//define("DCE_HQ_JT_Q", 155);	//大商所焦炭
//define("DCE_HQ_JM_Q", 156);	//大商所焦煤
define("DCE_HQ_TKS_Q", 157);	//大商所铁矿石期权
//add by zfy started 2020/01/06
define("QUOTE_RATE", 158);	//商业银行贴现率
//add by zfy ended 2020/01/06

//added by shizg started 2020/2/14
define("YOU_SE_CHENGJIAO", 159);	//有色成交
define("GC_QIHUO_KUCUN", 160);	//上期所钢材期货库存日报
define("DCE_QIHUO_HANGQING", 161);	//大商所期货行情
define("CZCE_QIHUO_HANGQING", 162);	//郑商所期货行情
//added by shizg ended 2020/2/14

//added by shizg for 全球股市 started 2020/05/11
define("eLuoSi_RTS", 163);	//俄罗斯RTS指数
define("huShen_300", 164);	//沪深300
define("shenZhenChengZhi", 165);	//深圳成指
define("shangZhengZhiShu", 166);	//上证指数
define("eLuoSi_MICEX", 167);	//俄罗斯MICEX
define("yinDu_SENSEX30", 168);	//印度SENSEX30
define("baXi_BOVESPA", 169);	//巴西BOVESPA
define("xinJiaPoHaiXiaShiBao", 170);	//新加坡海峡时报
define("taiWangJiaQuan", 171);	//台湾加权
define("hengShengGuoQi", 172);	//恒生国企
define("hengShengZhiShu", 173);	//恒生指数
define("riJingZhiShu", 174);	//日经指数
define("yingGuoFuShi_100", 175);	//英国富时100
define("faGuo_CAC40", 176);	//法国CAC40
define("deGuo_DAX", 177);	//德国DAX
define("naSiDaKeZongZhi", 178);	//纳斯达克综指
define("biaoZhunPuEr_500", 179);	//标准普尔500
define("daoQiongSiGongYe", 180);	//道琼斯工业
//added by shizg for 全球股市 ended 2020/05/11

//xiangbin add 2020713 start
define("MEILIANCHU", 181);	//https://www.federalreserve.gov/data.xml
//xiangbin add 2020713 end

//add changhong 2021-03-24
define("US_10_GUOZHAI", 182);  //美国十年期国债
//end changhong 2021-03-24

define("RMB_WHPJ_TYPE", 183);	//人民币外汇牌价资讯

define("USA_XYLENE_TYPE", 184);	    //美国地区对二甲苯市场收盘价格
define("EUROPE_XYLENE_TYPE", 185);	//欧洲地区对二甲苯市场收盘价格
define("ASIA_XYLENE_TYPE", 186);	//亚洲地区对二甲苯市场收盘价格

define("WUMAO_CANL_TYPE", 187);	//上海物贸有色金属现货行情
define("HUATONG_CANL_TYPE", 188);	//上海华通有色金属现货行情
define("NANHAI_CANL_TYPE", 189);	//南海有色金属现货行情
define("CU_SHFEPM_TYPE", 190);	//上期所铜期货成交及持仓排名
define("AL_SHFEPM_TYPE", 191);	//上期所铝期货成交及持仓排名
define("ZN_SHFEPM_TYPE", 192);	//上期所锌期货成交及持仓排名
define("AU_SHFEPM_TYPE", 193);	//上期所黄金期货成交及持仓排名
define("AG_SHFEPM_TYPE", 194);	//上期所白银期货成交及持仓排名
define("Daily_SHFEKC_TYPE", 195);	//上期所期货库存仓单日报

define("SX_COAL_JINGJIA_TYPE", 196);	//山西焦煤集团煤炭竞价
define("XNY_XLLD_TYPE", 197);	//鑫椤锂电网站爬取锰酸锂统计产量、磷酸铁锂统计产量、三元材料统计产量、钴酸锂统计产量
define("XNY_MYSTEEL_NIE_CL_TYPE", 198);	//爬取我的钢铁镍产量
define("JING_LIU_JINGJIA_TYPE", 199);	//山西晋柳能源有限公司
define("DA_TU_HE_JINGJIA_TYPE", 200);	//山西大土河焦化有限责任公司煤炭竞价
define("QIN_XIN_COAL_JINGJIA_TYPE", 201);	//山西沁新煤炭销售有限公司竞价
define("DONG_HUI_NY_JINGJIA_TYPE", 202);	//山西东辉能源集团有限公司竞价
define("LIU_LIN_HF_JINGJIA_TYPE", 203);	//山西柳林汇丰竞价
define("LIU_AN_HG_JINGJIA_TYPE", 204);	//太原煤炭网站潞安化工竞价
define("US_MGYYZLCB_TYPE", 205);
define("GZQHJYS_SI", 206); //广州期货交易所工业硅
define("TYMQH_JINGJIA_TYPE", 207); //太原煤炭网站太原煤气化化工竞价
define("MTYXJT_JINGJIA_TYPE", 208); //太原煤炭网站山西煤炭运销集团长治有限公司化工竞价
define("GZQHJYS_LC", 209); //广州期货交易所碳酸锂
define("GZQHJYSCD", 210); //广州期货交易所工业硅仓单排名
define("GZQHJYS_SIKC", 211); //广州期货交易所工业硅仓单量
define("GZQHJYS_LCKC", 212); //广州期货交易所碳酸锂仓单量

define("NEW_JINGJIA_TYPE", 213); //太原煤炭网站太原煤气化化工竞价
define("MSE_COAL_TYPE", 214); //蒙煤竞拍
define("MSE_COAL_TYPE2", 215); //蒙煤竞拍

define("GZQHJYS_PS", 216); //广州期货交易所多晶硅
define("GZQHJYS_PSKC", 217); //广州期货交易所多晶硅仓单量
define("GZQHJYS_LCCD", 218); //广州期货交易所碳酸锂仓单排名
define("GZQHJYS_PSCD", 219); //广州期货交易所多晶硅仓单排名

define("CZCE_QIHUO_SFCD", 220);	//郑商所期货硅铁仓单日报
define("CZCE_QIHUO_SMCD", 221);	//郑商所期货硅锰仓单日报



//新版山西焦煤焦炭国际交易中心爬取
define("CHANG_QIN_COAL_JINGJIA_TYPE", 222);	//山西长沁煤焦煤炭销售有限公司
define("HUANG_TU_PO_COAL_JINGJIA_TYPE", 223);	//山西黄土坡集团
define("YONG_TAI_COAL_JINGJIA_TYPE", 224);	//永泰能源
define("JING_HUI_COAL_JINGJIA_TYPE", 225);	//山西金晖万峰煤矿有限公司
define("LAN_XIAN_CH_COAL_JINGJIA_TYPE", 226);	//山西岚县昌恒煤焦有限公司
define("FANG_SHAN_JH_COAL_JINGJIA_TYPE", 227);	//山西方山金晖瑞隆煤业有限公司
define("DONG_TAI_COAL_JINGJIA_TYPE", 228);	//山西东泰控股集团有限公司
define("SHOU_WAN_COAL_JINGJIA_TYPE", 229);	//山西翼城首旺煤业有限责任公司
define("CHANG_SHENG_PING_JINGJIA_TYPE", 230);	//襄汾县昌昇平煤业有限公司
define("JING_GU_JINGJIA_TYPE", 231);	//古县金谷贸易有限公司
define("RUI_NENG_JINGJIA_TYPE", 232);	//介休瑞能煤炭销售有限公司
define("SHAN_MEI_JINGJIA_TYPE", 233);	//山煤国际能源集团股份有限公司

define("SHANXILILIU_JINGJIA_TYPE", 241); //山西离柳鑫瑞能源有限公司

define("RMB_RATE_VND", 243);//人民币汇率 印尼（印尼卢比）、 越南（越南盾）
define("RMB_RATE_IDR", 244);//人民币汇率 印尼（印尼卢比）、 越南（越南盾）
//人民币汇率中间价
$GLOBALS['HangQingSetting'][RMB_RATE] =  array(
    'BigType'=>RMB_BIGTYPE,
    'ClassName'=>'SafeRmbSpider',
    'CaiJiHangQing'=>'人民币汇率中间价',
    'URL'=>'https://www.safe.gov.cn/AppStructured/hlw/RMBQuery.do',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'人民币汇率中间价列表',
    'BlockTag'=>'table#InfoTable',
    'time1'=>'09:45:00',
    'time2'=>'10:20:00',
    'DbTable'=>'rmbrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS ['TAB_TYPE_RMB_RATE'] = array (
		"100美元/人民币",
		"100欧元/人民币",
		"100日元/人民币",
		"100港币/人民币",
		"100英镑/人民币",
		"100人民币兑林吉特",
		"100人民币兑卢布",
		"100人民币兑兰特",
		"100人民币兑韩元",
		"100人民币兑迪拉姆",
		"100人民币兑里亚尔",
		"100人民币兑匈牙利福林",
		"100人民币兑波兰兹罗提",
		"100人民币兑丹麦克朗",
		"100人民币兑瑞典克朗",
		"100人民币兑挪威克朗",
		"100人民币兑土耳其里拉",
		"100人民币兑墨西哥比索",
		"100澳元/人民币",
		"100加元/人民币",
		"100新西兰元/人民币",
		"100新加坡元/人民币",
		"100瑞士法郎/人民币"
);

//Shibor利率数据
$GLOBALS['HangQingSetting'][SHIBOR_DATA] =  array(
    'BigType'=>SHIBOR_BIGTYPE,
    'ClassName'=>'ShiBorSpider',
    'CaiJiHangQing'=>'Shibor利率数据',
    'URL'=>'https://www.chinamoney.com.cn/ags/ms/cm-u-bk-shibor/ShiborHis?lang=cn',
    'Encoding'=>'GBK',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'11:05:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

// //动力煤
// $GLOBALS['HangQingSetting'][DLM_QH] =  array(
//     'BigType'=>CZCE_BIGTYPE,
//     'ClassName'=>'CzceSpider',
//     'CaiJiHangQing'=>'郑商所动力煤期货行情',
//     //'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate='.date("Y-m-d").'&commodity=ZC',
//     //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDailyZC.htm',
//     'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'dlmqh.html',
//     //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511dlmqh.html',
// //    'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate=2019-08-15&commodity=ZC',
//     'Encoding'=>'utf-8',
//     'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
//     'UrlIsList'=>0,
//     'ListBlockKey'=>'',
//     'ListBlockTag'=>'',
//     //'BlockKey'=>'品种月份',
//     'BlockKey'=>'打印',
//     'BlockTag'=>'tbody',
//     'data_type'=>'SHQHDAY_21',
//     'time1'=>'16:15:00',
//     //'time1'=>'09:00:00',
//     'time2'=>'',
//     'DbTable'=>'data_table',
//     'nchannelid'=>'11',
//     'ncolumnid'=>'002,185',
//     'nvarietyid'=>'mj,614,c614',
//     'nkeys'=>'动力煤,期货行情',
// 	'ManagerName'=>'吴运豪',
//     'ManagerNameId'=>'444830'
//  );

//甲醇
$GLOBALS['HangQingSetting'][JC_QH] =  array(
    'BigType'=>CZCE_BIGTYPE,
    'ClassName'=>'CzceSpider',
    'CaiJiHangQing'=>'郑商所甲醇期货收盘行情',
    //'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate='.date("Y-m-d").'&commodity=MA',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDailyMA.htm',
     'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'jcqh.html',
	//'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511jcqh.html',
    //'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate=2019-08-08&commodity=MA',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
	//'BlockKey'=>'品种月份',
	'BlockKey'=>'打印',
    'BlockTag'=>'tbody',
    'data_type'=>'SHQHDAY_14',
    'time1'=>'16:15:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'hg,601,c601',
    'nkeys'=>'甲醇,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//PTA期货
$GLOBALS['HangQingSetting'][PTA_QH] =  array(
    'BigType'=>CZCE_BIGTYPE,
    'ClassName'=>'CzceSpider',
    'CaiJiHangQing'=>'郑商所PTA期货收盘行情',
    //'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate='.date("Y-m-d").'&commodity=TA',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDailyTA.htm',
     'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'ptaqh.html',
     //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511ptaqh.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
	//'BlockKey'=>'品种月份',
	'BlockKey'=>'打印',
    'BlockTag'=>'tbody',
    'data_type'=>'SHQHDAY_13',
    'time1'=>'16:15:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'hg,603,c603',
    'nkeys'=>'PTA,期货行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//xiangbin add 20190814 start

//硅锰
$GLOBALS['HangQingSetting'][SM_QH] =  array(
    'BigType'=>CZCE_BIGTYPE,
    'ClassName'=>'CzceSpider',
    'CaiJiHangQing'=>'郑商所硅锰期货行情',
    //'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate='.date("Y-m-d").'&commodity=SM',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDailySM.htm',
     'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'gmqh.html',
     //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511gmqh.html',
//    'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate=2019-08-15&commodity=SM',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
	//'BlockKey'=>'品种月份',
	'BlockKey'=>'打印',
    'BlockTag'=>'tbody',
    'data_type'=>'SHQHDAY_23',
    'time1'=>'16:15:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'15',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'thj,072,c702',
    'nkeys'=>'硅锰,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//硅铁
$GLOBALS['HangQingSetting'][SF_QH] =  array(
    'BigType'=>CZCE_BIGTYPE,
    'ClassName'=>'CzceSpider',
    'CaiJiHangQing'=>'郑商所硅铁期货行情',
    //'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate='.date("Y-m-d").'&commodity=SF',

    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDailySF.htm',
     'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'gtqh.html',
     //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511gtqh.html',
//    'URL'=>'http://app.czce.com.cn/cms/cmsface/czce/newcms/calendarnewAll.jsp?pubDate=2019-08-15&commodity=SF',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
	//'BlockKey'=>'品种月份',
	'BlockKey'=>'打印',
    'BlockTag'=>'tbody',
    'data_type'=>'SHQHDAY_22',
    'time1'=>'16:15:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'15',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'thj,070,c701',
    'nkeys'=>'硅铁,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );



//上期所原油
$GLOBALS['HangQingSetting'][SQS_SC] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所原油期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_26',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'hg,601,c601,hg,602,c602,hg,605,c605,hg,606,c606,hg,607,c607,hg,608,c608,hg,609,c609,hg,610,c610,hg,611,c611,hg,612,c612,hg,613,c613,hg,603,c603,hg,604,c604,hg,617,c617,hg,618,c618,hg,619,c619,hg,951,c951,hg,955,C955,hg,956,C956,hg,801,c801,hg,802,c802,hg,803,c803,hg,804,c804,hg,805,c805,hg,806,c806,hg,807,c807,hg,808,c808,hg,809,c809,hg,810,c810,hg,811,c811,hg,812,c812',
    'nkeys'=>'原油,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS['HangQingSetting'][SQS_RU] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所天然橡胶期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_7',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'hg,601,c601',
    'nkeys'=>'天然橡胶,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
$GLOBALS['HangQingSetting'][SQS_AG] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所白银期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_11',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,208,c21',
    'nkeys'=>'白银,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
$GLOBALS['HangQingSetting'][SQS_AU] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所黄金期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_2',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,208,c21',
    'nkeys'=>'黄金,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

$GLOBALS['HangQingSetting'][SQS_PB] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所铅期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_8',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,203,c21',
    'nkeys'=>'铅,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
$GLOBALS['HangQingSetting'][SQS_ZN] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所锌期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_6',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid' => '08',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'ys,204,c21',
	'nkeys' => '锌,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
$GLOBALS['HangQingSetting'][SQS_AL] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所铝期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_1',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid' => '08',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'ys,202,c21',
	'nkeys' => '铝,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
$GLOBALS['HangQingSetting'][SQS_CU] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所铜期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_3',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid' => '08',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'ys,201,c21',
	'nkeys' => '铜,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS['HangQingSetting'][SQS_SN] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所锡期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_25',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid' => '08',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'ys,205,c21',
	'nkeys' => '锡,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
  $GLOBALS['HangQingSetting'][SQS_NI] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所镍期货收盘行情',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_24',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid' => '08',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'ys,206,c21',
	'nkeys' => '镍,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
   $GLOBALS['HangQingSetting'][SQS_YS] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所有色金属期货价格指数',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
//	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190815.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_30',
    'time1'=>'17:30:00',
    'time2'=>'',
    'DbTable'=>'YouSePriceBaseIndex',
    'nchannelid' => '08',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'ys,201,c21,ys,202,c21,ys,203,c21,ys,204,c21,ys,205,c21,ys,206,c21',
	'nkeys' => '有色金属,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS['HangQingSetting'][SQS_SS] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所不锈钢期货收盘行情',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
	//'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190926.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'Java-SHQHBXG',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid' => '02,10',
	'ncolumnid' => '002,185',
	'nvarietyid' => 'bx,187,c51,bx,125,c52,bx,127,c54,bx,126,c53,bx,222,c56,bx,221,c58,bx,163,c59,bx,162,c60,bx,130,c57',
	'nkeys' => '不锈钢,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS['HangQingSetting'][SQS_RB] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所螺纹钢期货收盘行情',
	 'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
	//'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190926.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_4',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'02',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'gc,011,c01',
    'nkeys'=>'螺纹钢,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS['HangQingSetting'][SQS_HC] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所热轧卷板期货收盘行情',
	 'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
	//'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190926.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_99',
    'time1'=>'15:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'02',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'gc,013,c03',
    'nkeys'=>'热轧卷板,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 $GLOBALS['HangQingSetting'][SQS_WR] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所线材期货收盘行情',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
	//'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190926.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'SHQHDAY_5',
    'time1'=>'15:10:00',
	//'time1'=>'11:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,208,c21',
    'nkeys'=>'线材,期货行情',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
$GLOBALS['HangQingSetting'][SQS_AD] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所铸造铝合金期货收盘行情',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
    //'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20190926.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'Java-SHQHLHJ',
    'time1'=>'15:10:00',
    //'time1'=>'11:10:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,202,c21',
    'nkeys'=>'铝合金,期货行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);


 //中金所5年国债
$GLOBALS['HangQingSetting'][CFFEX_TF_DATA] =  array(
    'BigType'=>CFFEX_BIGTYPE,
    'ClassName'=>'CffexSpider',
    'CaiJiHangQing'=>'中金所5年国债',
    'URL'=>'http://www.cffex.com.cn/sj/hqsj/rtj/'.date("Ym").'/'.date("d").'/index.xml',
//	'URL'=>'http://www.cffex.com.cn/sj/hqsj/rtj/201908/15/index.xml',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:40:00',
	//'time1'=>'12:40:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
  //中金所10年国债
$GLOBALS['HangQingSetting'][CFFEX_T_DATA] =  array(
    'BigType'=>CFFEX_BIGTYPE,
    'ClassName'=>'CffexSpider',
    'CaiJiHangQing'=>'中金所10年国债',
    'URL'=>'http://www.cffex.com.cn/sj/hqsj/rtj/'.date("Ym").'/'.date("d").'/index.xml',
//	'URL'=>'http://www.cffex.com.cn/sj/hqsj/rtj/201908/15/index.xml',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:40:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//xiangbin add 20190814 end


//中金所股指期货
$GLOBALS['HangQingSetting'][CFFEX_DATA] =  array(
    'BigType'=>CFFEX_BIGTYPE,
    'ClassName'=>'CffexSpider',
    'CaiJiHangQing'=>'中金所股指期货',
    'URL'=>'http://www.cffex.com.cn/sj/hqsj/rtj/'.date("Ym").'/'.date("d").'/index.xml',
//	'URL'=>'http://www.cffex.com.cn/sj/hqsj/rtj/201908/15/index.xml',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:40:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//成交及持仓排名上期所
$GLOBALS['HangQingSetting'][SHFE_DATA] =  array(
    'BigType'=>SHFE_BIGTYPE,
    'ClassName'=>'ShfeSpider',
    'CaiJiHangQing'=>'成交及持仓排名上期所',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/pm'.date("Ymd").'.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'17:00:00',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//铁矿石成交及持仓排名大商所
$GLOBALS['HangQingSetting'][DCE_DATA_TKS] =  array(
    'BigType'=>DCE_BIGTYPE,
    'ClassName'=>'DceSpider',
    'CaiJiHangQing'=>'铁矿石成交及持仓排名大商所',

    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/memberDealPosiQuotes.html?memberDealPosiQuotes.variety=i&memberDealPosiQuotes.trade_type=0&contract.variety_id=i&currDate='.date("Ymd").'&year='.date("Y").'&month='.(intval(date("n"))-1).'&day='.date("d").'&contract.contract_id=',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'pmi0.html',
//    'URL'=>'http://www.dce.com.cn//publicweb/quotesdata/memberDealPosiQuotes.html?memberDealPosiQuotes.variety=i&memberDealPosiQuotes.trade_type=0&contract.variety_id=i&currDate=201908014&year=2019&month=07&day=14&contract.contract_id=',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'乙二醇',
    'ListBlockTag'=>'div.selBox>div>ul.keyWord',
    'BlockKey'=>'导出文本',
    'BlockTag'=>'table',

    'time1'=>'16:10:00',
    'time2'=>'17:00:00',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//焦炭成交及持仓排名大商所
$GLOBALS['HangQingSetting'][DCE_DATA_JT] =  array(
    'BigType'=>DCE_BIGTYPE,
    'ClassName'=>'DceSpider',
    'CaiJiHangQing'=>'焦炭成交及持仓排名大商所',
	//'URL'=>'http://www.dce.com.cn//publicweb/quotesdata/memberDealPosiQuotes.html?memberDealPosiQuotes.variety=j&memberDealPosiQuotes.trade_type=0&contract.variety_id=j&currDate='.date("Ymd").'&year='.date("Y").'&month='.(intval(date("n"))-1).'&day='.date("d").'&contract.contract_id='.DCE_DATA_JT_HEYUE,


	//'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/memberDealPosiQuotes.html?memberDealPosiQuotes.variety=j&memberDealPosiQuotes.trade_type=0&contract.variety_id=j&currDate='.date("Ymd").'&year='.date("Y").'&month='.(intval(date("n"))-1).'&day='.date("d").'&contract.contract_id=',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'pmj0.html',
	//'URL'=>'http://www.dce.com.cn//publicweb/quotesdata/memberDealPosiQuotes.html?memberDealPosiQuotes.variety=j&memberDealPosiQuotes.trade_type=0&contract.variety_id=j&currDate=201908014&year=2019&month=07&day=14&contract.contract_id=',

    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'乙二醇',
    'ListBlockTag'=>'div.selBox>div>ul.keyWord',
    'BlockKey'=>'导出文本',
    'BlockTag'=>'',
    //'BlockTag'=>'table',

	'time1'=>'16:10:00',
	'time2'=>'18:00:00',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//焦煤成交及持仓排名大商所
$GLOBALS['HangQingSetting'][DCE_DATA_JM] =  array(
    'BigType'=>DCE_BIGTYPE,
    'ClassName'=>'DceSpider',
    'CaiJiHangQing'=>'焦煤成交及持仓排名大商所',

    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/memberDealPosiQuotes.html?memberDealPosiQuotes.variety=jm&memberDealPosiQuotes.trade_type=0&contract.variety_id=jm&currDate='.date("Ymd").'&year='.date("Y").'&month='.(intval(date("n"))-1).'&day='.date("d").'&contract.contract_id=',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'pmjm0.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'乙二醇',
    'ListBlockTag'=>'div.selBox>div>ul.keyWord',
    'BlockKey'=>'导出文本',
    'BlockTag'=>'table',

	'time1'=>'16:10:00',
	'time2'=>'18:00:00',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//澳元美元中间价
$GLOBALS['HangQingSetting'][AUD_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'澳元美元中间价',
    'URL'=>'http://forex.wiapi.hexun.com/forex/sortlist?block=303&number=1000&title=15&commodityid=0&direction=0&start=0&column=code,name,price,updown,updownrate,open,high,low,buyPrice,sellPrice,datetime,PriceWeight&callback=quoteforex&time=',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//雷尔美元中间价
//$GLOBALS['HangQingSetting'][BRL_USD_TYPE] =  array(
//    'BigType'=>USD_BIGTYPE,
//    'ClassName'=>'USDSpider',
//    'CaiJiHangQing'=>'雷尔美元中间价',
//    'URL'=>'https://cn.investing.com/currencies/brl-usd-historical-data',
//    'Encoding'=>'utf-8',
//    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
//    'UrlIsList'=>0,
//    'ListBlockKey'=>'',
//    'ListBlockTag'=>'',
//    'BlockKey'=>'下载数据',
//    'BlockTag'=>'div#results_box',
//    'time1'=>'08:30:00',
//    'time2'=>'',
//    'CaptureDayMode'=>EVERYDAY,
 //   'DbTable'=>'usrate',
//    'ManagerName'=>'吴运豪',
//    'ManagerNameId'=>'444830'
// );

//欧元美元中间价
/*$GLOBALS['HangQingSetting'][EUR_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'欧元美元中间价',
    'URL'=>'https://cn.investing.com/currencies/eur-usd-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//印度卢比美元中间价
$GLOBALS['HangQingSetting'][INR_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'印度卢比美元中间价',
    'URL'=>'https://cn.investing.com/currencies/inr-usd-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//美元日元中间价
$GLOBALS['HangQingSetting'][USD_JPY_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'美元日元中间价',
    'URL'=>'https://cn.investing.com/currencies/usd-jpy-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//俄罗斯卢布美元中间价
$GLOBALS['HangQingSetting'][RUB_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'俄罗斯卢布美元中间价',
    'URL'=>'https://cn.investing.com/currencies/rub-usd-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//韩元美元中间价
$GLOBALS['HangQingSetting'][KWR_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'韩元美元中间价',
    'URL'=>'https://cn.investing.com/currencies/krw-usd-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//泰铢美元中间价
$GLOBALS['HangQingSetting'][THB_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'泰铢美元中间价',
    'URL'=>'https://cn.investing.com/currencies/thb-usd-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//美元韩元中间价
$GLOBALS['HangQingSetting'][USD_KWR_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'美元韩元中间价',
    'URL'=>'https://cn.investing.com/currencies/usd-krw-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );


//英镑美元中间价
$GLOBALS['HangQingSetting'][GBP_USD_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'英镑美元中间价',
    'URL'=>'https://cn.investing.com/currencies/gbp-usd-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//美元南非兰特中间价
$GLOBALS['HangQingSetting'][GBP_ZAR_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'美元南非兰特中间价',
    'URL'=>'https://cn.investing.com/currencies/usd-zar-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'usrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//美元指数
$GLOBALS['HangQingSetting'][USD_POINT_TYPE] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'USDSpider',
    'CaiJiHangQing'=>'美元指数',
    'URL'=>'https://cn.investing.com/currencies/us-dollar-index-historical-data',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'下载数据',
    'BlockTag'=>'div#results_box',
    'time1'=>'08:30:00',
    'time2'=>'',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'dolrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

*/

//郑商所动力煤
//$GLOBALS['HangQingSetting'][CZCE_DATA_DLM] =  array(
//    'BigType'=>CZCECJ_BIGTYPE,
//    'ClassName'=>'CzceCJSpider',
//    'CaiJiHangQing'=>'郑商所动力煤',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataHolding.htm',
	//'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDailyZC.htm?yXi2jvqo=wtABkqqbxP3bxP3bxfoAwVDmXzNZou.4jW_xmVlg0FZqqc3',
//     'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'dlmhq.html',
     //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511dlmhq.html',
//    'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/2019/20190926/FutureDataHolding.htm',
 //   'Encoding'=>'utf-8',
 //   'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
 //   'UrlIsList'=>0,
//    'ListBlockKey'=>'',
//    'ListBlockTag'=>'',
//    'BlockKey'=>'打印',
//    'BlockTag'=>'', // 拿合约是ZC的最大成交量的
 //   'time1'=>'16:15:00',
//    'time2'=>'',
//    'CaptureDayMode'=>'',
//    'DbTable'=>'',
//    'ManagerName'=>'吴运豪',
//    'ManagerNameId'=>'444830'
// );

//郑商所硅铁
$GLOBALS['HangQingSetting'][CZCE_DATA_GT] =  array(
    'BigType'=>CZCECJ_BIGTYPE,
    'ClassName'=>'CzceCJSpider',
    'CaiJiHangQing'=>'郑商所硅铁',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataHolding.htm',
    'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'dlmhq.html',
    //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511dlmhq.html',
//    'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/2019/20190926/FutureDataHolding.htm',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'', // 拿合约是SF的最大成交量的
    'time1'=>'16:15:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );


//郑商所硅锰
$GLOBALS['HangQingSetting'][CZCE_DATA_GM] =  array(
    'BigType'=>CZCECJ_BIGTYPE,
    'ClassName'=>'CzceCJSpider',
    'CaiJiHangQing'=>'郑商所硅锰',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataHolding.htm',
    'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'dlmhq.html',
    //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511dlmhq.html',
//    'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/2019/20190926/FutureDataHolding.htm',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'', // 拿合约是SM的最大成交量的
    'time1'=>'16:15:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

 //大商所焦炭
$GLOBALS['HangQingSetting'][DCE_HQ_JT] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所焦炭期货行情',

    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=j&dayQuotes.trade_type=0',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'j0.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',

    'time1'=>'16:10:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'11',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'mj,620,c620',
    'nkeys'=>'焦炭,期货行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 //大商所焦炭期权
/*$GLOBALS['HangQingSetting'][DCE_HQ_JT_Q] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所焦炭期权行情',
    'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=j&dayQuotes.trade_type=1',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',
    'time1'=>'15:20:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'11',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'mj,620,c620',
    'nkeys'=>'焦炭,期权行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );*/


 //大商所焦煤
$GLOBALS['HangQingSetting'][DCE_HQ_JM] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所焦煤期货行情',
    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=jm&dayQuotes.trade_type=0',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'jm0.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',
    'time1'=>'16:10:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'11',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'mj,615,c615',
    'nkeys'=>'焦煤,期货行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 //大商所焦煤期权
/*$GLOBALS['HangQingSetting'][DCE_HQ_JM_Q] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所焦煤期权行情',
    'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=jm&dayQuotes.trade_type=1',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',
    'time1'=>'15:20:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'11',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'mj,615,c615',
    'nkeys'=>'焦煤,期权行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );*/

 //大商所棕榈油
$GLOBALS['HangQingSetting'][DCE_HQ_ZLY] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所棕榈油期货收盘行情',
    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=p&dayQuotes.trade_type=0',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'p0.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',
    'time1'=>'16:10:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'hg,803,c803',
    'nkeys'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
 //大商所铁矿石
$GLOBALS['HangQingSetting'][DCE_HQ_TKS] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所铁矿石期货行情',

    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=i&dayQuotes.trade_type=0',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'i0.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',

    'time1'=>'16:10:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'04',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ll,050,c13',
    'nkeys'=>'铁矿石,期货行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

 //大商所铁矿石期权
$GLOBALS['HangQingSetting'][DCE_HQ_TKS_Q] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所铁矿石期权行情',

    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=i&dayQuotes.trade_type=1',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'i1.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'table',

    'time1'=>'16:10:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'04',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ll,050,c13',
    'nkeys'=>'铁矿石,期权行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
//add by zfy started 2020/01/06
//商业银行贴现率
$GLOBALS['HangQingSetting'][QUOTE_RATE] =  array(
	'BigType'=>QUOTE_RATE_BIGTYPE,
	'ClassName'=>'QuoteRateSpider',
	'CaiJiHangQing'=>'商业银行贴现率',
//	'URL'=>'http://www.shcpe.com.cn/index_132_lcid_1_date_'.date("Y-m-d").'.html',
//    'URL'=>'https://www.maipiaoquan.com/json/wtn/pub/v1/front/org/quote/draft/rate/7day-list',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'利率(%)',
	'BlockTag'=>'table',
	'time1'=>'08:00:00',
	'time2'=>'17:20:00',
//	'time2'=>'18:20:00',
	'CaptureDayMode'=>EVERYDAY,
	'DbTable'=>'busbankrate',
	'nchannelid'=>'',
	'ncolumnid'=>'',
	'nvarietyid'=>'',
	'nkeys'=>'',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);
//add by zfy ended 2020/01/06

//added by shizg started 2020/2/14
//有色成交
$GLOBALS['HangQingSetting'][YOU_SE_CHENGJIAO] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所期货成交情况',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx'.date("Ymd").'.dat?rnd='.rand(),
	//'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/kx20200217.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'',
    'time1'=>'16:00:00',
    'time2'=>'',
    'DbTable'=>'',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,201,c21,ys,202,c21,ys,203,c21,ys,204,c21,ys,208,c21',
    'nkeys'=>'铜,铝,铅,锌,贵金属,期货行情,上期所,期货',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//上期所钢材期货库存日报
$GLOBALS['HangQingSetting'][GC_QIHUO_KUCUN] =  array(
    'BigType'=>SQS_BIGTYPE,
    'ClassName'=>'SqsQhSpider',
    'CaiJiHangQing'=>'上期所钢材期货库存日报',
	'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/'.date("Ymd").'dailystock.dat?rnd='.rand(),
	//'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/20200217dailystock.dat?rnd='.rand(),
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'',
    'time1'=>'16:00:00',
    'time2'=>'',
    'DbTable'=>'',
    'nchannelid'=>'02',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'gc,011,c01',
    'nkeys'=>'建筑钢材,期货行情,期货库存,钢材期货,上期所,期货,库存,热轧卷',
	'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//大商所期货行情
$GLOBALS['HangQingSetting'][DCE_QIHUO_HANGQING] =  array(
    'BigType'=>DCEHQ_BIGTYPE,
    'ClassName'=>'DceHqSpider',
    'CaiJiHangQing'=>'大商所期货行情',

    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=all&dayQuotes.trade_type=0',
    'URL'=>'http://172.16.130.3:8233/dce/zss/dss'.date("Ymd").'all0.html',
    //'URL'=>'http://www.dce.com.cn/publicweb/quotesdata/dayQuotesCh.html?dayQuotes.variety=all&dayQuotes.trade_type=0',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'div#printData>div.dataArea',

    'time1'=>'16:10:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'',
    'nchannelid'=>'01',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'',
    'nkeys'=>'期货行情,期货,大商所,乙烯,铁矿石,收盘,煤,聚氯乙烯',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

//郑商所期货行情
$GLOBALS['HangQingSetting'][CZCE_QIHUO_HANGQING] =  array(
    'BigType'=>CZCE_BIGTYPE,
    'ClassName'=>'CzceSpider',
    'CaiJiHangQing'=>'郑商所期货行情',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/'.date("Y").'/'.date("Ymd").'/FutureDataDaily.htm',
    'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'qhhq.html',

    //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20210511qhhq.html',
    //'URL'=>'http://www.czce.com.cn/cn/DFSStaticFiles/Future/2020/20200218/FutureDataDaily.htm',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'<table border="0" align="center" cellpadding="0" id="tab1">',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'',
    'CaptureDayMode'=>'',
    'DbTable'=>'',
    'nchannelid'=>'01',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'',
    'nkeys'=>'期货行情,郑商所,期货,收盘,交割,持仓,成交量',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );


//added by shizg ended 2020/2/14

//added by shizg for 全球股市 started 2020/05/11

//俄罗斯RTS指数
$GLOBALS['HangQingSetting'][eLuoSi_RTS] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'俄罗斯RTS指数',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.RTS&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery11240667851871287737_1589010258226&type=CT&cmd=RTS_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589010258227',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010127',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//沪深300
$GLOBALS['HangQingSetting'][huShen_300] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'沪深300',
    'URL'=>'http://hq.sinajs.cn/?_=0.9485550424148952&list=sh000300',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010126',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//深圳成指
$GLOBALS['HangQingSetting'][shenZhenChengZhi] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'深圳成指',
    'URL'=>'http://hq.sinajs.cn/?_=0.2903237071764597&list=sz399001',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010125',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//上证指数
$GLOBALS['HangQingSetting'][shangZhengZhiShu] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'上证指数',
    'URL'=>'http://hq.sinajs.cn/?_=0.337477703075616&list=sh000001',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010124',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//俄罗斯MICEX
$GLOBALS['HangQingSetting'][eLuoSi_MICEX] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'俄罗斯MICEX',
    'URL'=>'http://hq.sinajs.cn/?list=znb_INDEXCF',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010123',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//印度SENSEX30
$GLOBALS['HangQingSetting'][yinDu_SENSEX30] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'印度SENSEX30',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.SENSEX&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery112405119210936159058_1589010172710&type=CT&cmd=SENSEX_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589010172711',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010122',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//巴西BOVESPA
$GLOBALS['HangQingSetting'][baXi_BOVESPA] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'巴西BOVESPA',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.BVSP&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery11240916661404744973_1589010096628&type=CT&cmd=BVSP_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589010096629',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010121',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//新加坡海峡时报
$GLOBALS['HangQingSetting'][xinJiaPoHaiXiaShiBao] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'新加坡海峡时报',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.STI&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery112402669998741150623_1589010339277&type=CT&cmd=STI_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589010339278',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010120',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//台湾加权
$GLOBALS['HangQingSetting'][taiWangJiaQuan] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'台湾加权',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.TWII&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery112408179671172289869_1589010394913&type=CT&cmd=TWII_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589010394914',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010119',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//恒生国企
$GLOBALS['HangQingSetting'][hengShengGuoQi] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'恒生国企',
    'URL'=>'http://hq.sinajs.cn/?_=0.049398192632955196&list=rt_hkHSCEI',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010118',
    'CaptureDayMode'=>'',
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//恒生指数
$GLOBALS['HangQingSetting'][hengShengZhiShu] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'恒生指数',
    'URL'=>'http://hq.sinajs.cn/?_=0.22300407060808936&list=rt_hkHSI',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010117',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//日经指数
$GLOBALS['HangQingSetting'][riJingZhiShu] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'日经指数',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.N225&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery1124014838517424806863_1589353871229&type=CT&cmd=N225_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589353871230',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010116',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//英国富时100
$GLOBALS['HangQingSetting'][yingGuoFuShi_100] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'英国富时100',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.FTSE&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery1124007092579925269105_1589009668460&type=CT&cmd=FTSE_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589009668461',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010115',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//法国CAC40
$GLOBALS['HangQingSetting'][faGuo_CAC40] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'法国CAC40',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.FCHI&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery112408305918499921663_1588909927757&type=CT&cmd=FCHI_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1588909927758',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010114',
    'CaptureDayMode'=>'',
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );


//德国DAX
$GLOBALS['HangQingSetting'][deGuo_DAX] =  array(
    'BigType'=>eastmoney_GuShi_BIGTYPE,
    'ClassName'=>'EastMoneySpider',
    'CaiJiHangQing'=>'德国DAX',
    'URL'=>'http://push2.eastmoney.com/api/qt/stock/get?secid=100.GDAXI&ut=bd1d9ddb04089700cf9c27f6f7426281&fields=f43,f44,f45,f46,f47,f59,f60,f152,f169,f86,f170,f171,f118&cb=jQuery112405874533686125856_1589010007764&type=CT&cmd=GDAXI_UI&sty=FDPBPFB&st=z&js=((x))&token=4f1862fc3b5e77c150a2b985b12db0fd&_=1589010007765',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010113',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//纳斯达克综指
$GLOBALS['HangQingSetting'][naSiDaKeZongZhi] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'纳斯达克综指',
    'URL'=>'http://hq.sinajs.cn/?_=0.8852372512347659&list=gb_$ixic',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010112',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//标准普尔500
$GLOBALS['HangQingSetting'][biaoZhunPuEr_500] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'标准普尔500',
    'URL'=>'http://hq.sinajs.cn/?_=0.5646621116810178&list=gb_$inx',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010111',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//道琼斯工业
$GLOBALS['HangQingSetting'][daoQiongSiGongYe] =  array(
    'BigType'=>sina_GuShi_BIGTYPE,
    'ClassName'=>'SinaGuShiSpider',
    'CaiJiHangQing'=>'道琼斯工业',
    'URL'=>'http://hq.sinajs.cn/?_=0.8850287420120055&list=gb_$dji',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'07:30:00',
    'time2'=>'',
    'bianma'=>'10010110',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051'
 );

//added by shizg for 全球股市 ended 2020/05/11


// xiangbin add 20200713 start
 //https://www.federalreserve.gov/data.xml
 $GLOBALS['HangQingSetting'][MEILIANCHU] =  array(
    'BigType'=>Federalreserve_BIGTYPE,
    'ClassName'=>'FederalreserveSpider',
    'CaiJiHangQing'=>'美联储',
    //'URL'=>'https://www.federalreserve.gov/data.xml',
    'URL'=>'https://www.federalreserve.gov/releases/h41/data.xml',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'08:30:00',
	//'time1'=>'12:40:00',
    'time2'=>'20:00:00',
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );

// xiangbin add 20200


//期货持仓量发布设置
$GLOBALS['DCE_DATA_POST_SETING'] = array(
    'rb' => array(
        'title_lb' => '上期所螺纹钢',
        'nchannelid' => '02',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'gc,011,c01',
        'nkeys' => '螺纹期货,持仓排名,成交排名,上期所'
    ),
    'hc' => array(
        'title_lb' => '上期所热卷',
        'nchannelid' => '02',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'gc,013,c03',
        'nkeys' => '热卷期货,持仓排名,成交排名,上期所'
    ),
    'j' => array(
        'title_lb' => '大商所焦炭',
        'nchannelid' => '11',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'mj,620,c620',
        'nkeys' => '焦炭期货,持仓排名,成交排名,大商所'
    ),
    'jm' => array(
        'title_lb' => '大商所焦煤',
        'nchannelid' => '11',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'mj,615,c615',
        'nkeys' => '焦煤期货,持仓排名,成交排名,大商所'
    ),
    'i' => array(
        'title_lb' => '大商所铁矿石',
        'nchannelid' => '04',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'll,614,c614',
        'nkeys' => '铁矿石期货,持仓排名,成交排名,大商所'
    ),
    'zc' => array(
        'title_lb' => '郑商所动力煤',
        'nchannelid' => '11',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'mj,614,c614',
        'nkeys' => '动力煤期货,持仓排名,成交排名,郑商所'
    ),
    'sf' => array(
        'title_lb' => '郑商所硅铁',
        'nchannelid' => '15',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'thj,070,c701',
        'nkeys' => '硅铁期货,持仓排名,成交排名,郑商所'
    ),
    'sm' => array(
        'title_lb' => '郑商所锰硅',
        'nchannelid' => '15',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'thj,072,c702',
        'nkeys' => '锰硅期货,持仓排名,成交排名,郑商所'
    ),
    'cu' => array(
        'title_lb' => '上期所铜',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,201,c21',
        'nkeys' => '铜期货,持仓排名,成交排名,上期所'
    ),
    'al' => array(
        'title_lb' => '上期所铝',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,202,c21',
        'nkeys' => '铝期货,持仓排名,成交排名,上期所'
    ),
    'zn' => array(
        'title_lb' => '上期所锌',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,204,c21',
        'nkeys' => '锌期货,持仓排名,成交排名,上期所'
    ),
    'ni' => array(
        'title_lb' => '上期所镍',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,206,c21',
        'nkeys' => '镍期货,持仓排名,成交排名,上期所'
    ),
    'sn' => array(
        'title_lb' => '上期所锡',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,205,c21',
        'nkeys' => '锡期货,持仓排名,成交排名,上期所'
    ),
    'sc' => array(
        'title_lb' => '上期所原油',
        'nchannelid' => '17',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'hg,601,c601,hg,602,c602,hg,605,c605,hg,606,c606,hg,607,c607,hg,608,c608,hg,609,c609,hg,610,c610,hg,611,c611,hg,612,c612,hg,613,c613,hg,603,c603,hg,604,c604,hg,617,c617,hg,618,c618,hg,619,c619,hg,951,c951,hg,955,C955,hg,956,C956,hg,801,c801,hg,802,c802,hg,803,c803,hg,804,c804,hg,805,c805,hg,806,c806,hg,807,c807,hg,808,c808,hg,809,c809,hg,810,c810,hg,811,c811,hg,812,c812',
        'nkeys' => '原油期货,持仓排名,成交排名,上期所'
    ),
    'ss' => array(
        'title_lb' => '上期所不锈钢',
        'nchannelid' => '12',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'tg,020,c07',
        'nkeys' => '不锈钢期货,持仓排名,成交排名,上期所'
    )
);
$GLOBALS ['TAB_TYPE_QHCJCC'] = array(
    "名次",
    "会员简称",
    "成交量",
    "增减",
    "名次",
    "会员简称",
    "持买单量",
    "增减",
    "名次",
    "会员简称",
    "持卖单量",
    "增减"
);
//xiangbin add 20190814

$GLOBALS['GZ_DATA_POST_SETING'] = array(
	'si' => array(
		'title_lb' => '广期所工业硅',
		'nchannelid'=>'25',
		'ncolumnid'=>'002,185',
		'nvarietyid'=>'ny,325,c325',
		'nkeys' => '工业硅期货,持仓排名,成交排名,广期所'
	),
	'lc' => array(
		'title_lb' => '广期所碳酸锂',
		'nchannelid'=>'25',
		'ncolumnid'=>'002,185',
		'nvarietyid'=>'ny,303,c303',
		'nkeys' => '碳酸锂期货,持仓排名,成交排名,广期所'
	),
    'ps' => array(
        'title_lb' => '广期所多晶硅',
        'nchannelid'=>'25',
        'ncolumnid'=>'002,185',
        'nvarietyid'=>'ny,326,c326',
        'nkeys' => '多晶硅期货,持仓排名,成交排名,广期所'
    )
);

//上海期货交易所期货合约行情日交易快讯
$GLOBALS['DCE_DATA_POST_QH'] = array(
    'sc_f' => array(
        'title' => '上期所原油期货收盘行情',
        'nchannelid' => '17',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'hg,601,c601,hg,602,c602,hg,605,c605,hg,606,c606,hg,607,c607,hg,608,c608,hg,609,c609,hg,610,c610,hg,611,c611,hg,612,c612,hg,613,c613,hg,603,c603,hg,604,c604,hg,617,c617,hg,618,c618,hg,619,c619,hg,951,c951,hg,955,C955,hg,956,C956,hg,801,c801,hg,802,c802,hg,803,c803,hg,804,c804,hg,805,c805,hg,806,c806,hg,807,c807,hg,808,c808,hg,809,c809,hg,810,c810,hg,811,c811,hg,812,c812',
        'nkeys' => '原油期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_26',

     ),
    'ru_f' => array(
        'title' => '上期所天然橡胶期货收盘行情',
        'nchannelid' => '02',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'gc,011,c01',
        'nkeys' => '螺纹期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_7',
    ),
    'ag_f' => array(
        'title' => '上期所白银期货收盘行情',
        'nchannelid' => '02',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'gc,011,c01',
        'nkeys' => '螺纹期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_11',
    ),
	'au_f' => array(
        'title' => '上期所黄金期货收盘行情',
        'nchannelid' => '02',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'gc,011,c01',
        'nkeys' => '螺纹期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_2',
    ),
	'pb_f' => array(
        'title' => '上期所铅期货收盘行情',
        'nchannelid' => '02',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'gc,011,c01',
        'nkeys' => '螺纹期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_8',
    ),
	'zn_f' => array(
        'title' => '上期所锌期货收盘行情',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,204,c21',
        'nkeys' => '锌期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_6',
    ),
	'al_f' => array(
        'title' => '上期所铝期货收盘行情',
        'nchannelid' => '08',
        'ncolumnid' => '002,185',
        'nvarietyid' => 'ys,202,c21',
        'nkeys' => '铝期货,持仓排名,成交排名,上期所',
		'isfb'=>'1',
		'data_type'=>'SHQHDAY_1',
    ),
	'cu_f' => array(
			'title' => '上期所铜期货收盘行情',
			'nchannelid' => '08',
			'ncolumnid' => '002,185',
			'nvarietyid' => 'ys,201,c21',
			'nkeys' => '铜期货,持仓排名,成交排名,上期所',
			'isfb'=>'1',
			'data_type'=>'SHQHDAY_3',
	),
	'sn_f' => array(
			'title' => '上期所锡期货收盘行情',
			'nchannelid' => '08',
			'ncolumnid' => '002,185',
			'nvarietyid' => 'ys,205,c21',
			'nkeys' => '锡期货,持仓排名,成交排名,上期所',
		    'isfb'=>'1',
		    'data_type'=>'SHQHDAY_25',
	),
    'ni_f' => array(
			'title' => '上期所镍期货收盘行情',
			'nchannelid' => '08',
			'ncolumnid' => '002,185',
			'nvarietyid' => 'ys,206,c21',
			'nkeys' => '镍期货,持仓排名,成交排名,上期所',
		    'isfb'=>'1',
		   'data_type'=>'SHQHDAY_24',
    )
);



$GLOBALS ['TAB_TYPE_QH'] = array(
    "交割月份",
    "前结算",
    "今开盘",
    "最高价",
    "最低价",
    "收盘价",
    "结算参考价",
    "涨跌1",
    "涨跌2",
    "成交手",
    "持仓手",
    "变化"
);

$GLOBALS ['TAB_TYPE_QIQUAN'] = array (
	"合约名称",
	"前结算",
	"开盘价",
	"最高价",
	"最低价",
	"收盘价",
	"结算价",
	"涨跌1",
	"涨跌2",
	"Delta",
	"成交量",
	"持仓量",
	"持仓量变化",
	"成交额",
	"行权量"
);

$GLOBALS ['TAB_TYPE_QH_EN'] = array(
    "Delivery Month",
    "Prior settle",
    "Open",
    "High",
    "Low",
    "Closing price",
    "Settlement",
    "Change 1",
    "Change 2",
    "Volume",
    "Open interest",
    "Change"
);
//xiangbin add 20190814

//add changhong 2021-03-24
$GLOBALS['HangQingSetting'][US_10_GUOZHAI] =  array(
    'BigType'=>USD_BIGTYPE,
    'ClassName'=>'US_10_Spider',
    'CaiJiHangQing'=>'美国十年期国债',
    'URL'=>'https://www.federalreserve.gov/releases/h15/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'Yields in percent per annum',
    'BlockTag'=>'div#dailyH15',
    'time1'=>'08:30:00',
    'time2'=>'10:40:00',
    'CaptureDayMode'=>EVERYDAY,
    'DbTable'=>'data_table',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
 );
//end changhong 2021-03-24

$GLOBALS['HangQingSetting'][RMB_WHPJ_TYPE] =  array(
    'BigType'=>WHPJ_BIGTYPE,
    'ClassName'=>'whpjSpider',
    'CaiJiHangQing'=>'人民币外汇牌价',
    'URL'=>'https://www.boc.cn/sourcedb/whpj/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'【关于远离违法违规外汇交易的风险提示】',
    'BlockTag'=>'table:eq(0)',
    'time1'=>'08:30:00',
    'time2'=>'08:30:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'01',
    'ncolumnid'=>'001,013,001,042',
    'nvarietyid'=>'',
    'nkeys'=>'外汇牌价,人民币'
 );

//  美国地区对二甲苯市场收盘价格
$GLOBALS['HangQingSetting'][USA_XYLENE_TYPE] =  array(
    'BigType'=>XYLENE_BIGTYPE,
    'ClassName'=>'xyleneSpider',
    'CaiJiHangQing'=>'美国地区对二甲苯',
    'URL'=>'http://www.100ppi.com/news/list-14--388-1.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'化工最新动态</span>',
    'ListBlockTag'=>'div.list-c',
    'BlockKey'=>'正文</span>',
    'BlockTag'=>'div.news-detail',
    'time1'=>'08:40:00',
    'time2'=>'10:30:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,061',
    'nvarietyid'=>'hg,604,c604,hg,801,c801',
    'nkeys'=>'美国,对二甲苯,外盘',
    'hygs_gj'=>'bm1'
 );

//  欧洲地区对二甲苯市场收盘价格
$GLOBALS['HangQingSetting'][EUROPE_XYLENE_TYPE] =  array(
    'BigType'=>XYLENE_BIGTYPE,
    'ClassName'=>'xyleneSpider',
    'CaiJiHangQing'=>'欧洲地区对二甲苯',
    'URL'=>'http://www.100ppi.com/news/list-14--388-1.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'化工最新动态</span>',
    'ListBlockTag'=>'div.list-c',
    'BlockKey'=>'正文</span>',
    'BlockTag'=>'div.news-detail',
    'time1'=>'08:40:00',
    'time2'=>'10:30:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,061',
    'nvarietyid'=>'hg,604,c604,hg,801,c801',
    'nkeys'=>'欧洲,对二甲苯,外盘',
    'hygs_gj'=>'2001'
 );

 //  亚洲地区对二甲苯市场收盘价格
$GLOBALS['HangQingSetting'][ASIA_XYLENE_TYPE] =  array(
    'BigType'=>XYLENE_BIGTYPE,
    'ClassName'=>'xyleneSpider',
    'CaiJiHangQing'=>'亚洲地区对二甲苯',
    'URL'=>'http://www.100ppi.com/news/list-14--388-1.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'化工最新动态</span>',
    'ListBlockTag'=>'div.list-c',
    'BlockKey'=>'正文</span>',
    'BlockTag'=>'div.news-detail',
    'time1'=>'08:40:00',
    'time2'=>'10:30:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'17',
    'ncolumnid'=>'002,061',
    'nvarietyid'=>'hg,604,c604,hg,801,c801',
    'nkeys'=>'亚洲,对二甲苯,外盘',
    'hygs_gj'=>'3001'
 );

//  上海物贸有色金属现货行情
$GLOBALS['HangQingSetting'][WUMAO_CANL_TYPE] =  array(
    'BigType'=>CANL_BIGTYPE,
    'ClassName'=>'canlSpider',
    'CaiJiHangQing'=>'上海物贸有色金属现货行情',
    'URL'=>'https://market.cnal.com/sme/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'申请免费试用',
    'ListBlockTag'=>'div.grid-market-list',
    'BlockKey'=>'上海有色金属交易中心',
    'BlockTag'=>'table',
    'time1'=>'11:30:00',
    'time2'=>'11:30:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,059',
    'nvarietyid'=>'ys,201,c21,ys,202,c21,ys,203,c21,ys,204,c21,ys,205,c21,ys,206,c21',
    'nkeys'=>'物贸有色,基本金属,现货价格,国内综述',
    'oldCaiJiHangQing'=>'上海现货（SME）基本金属行情'
 );

//  上海华通有色金属现货行情
$GLOBALS['HangQingSetting'][HUATONG_CANL_TYPE] =  array(
    'BigType'=>CANL_BIGTYPE,
    'ClassName'=>'canlSpider',
    'CaiJiHangQing'=>'上海华通有色金属现货行情',
    'URL'=>'https://market.cnal.com/huatong/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'申请免费试用',
    'ListBlockTag'=>'div.grid-market-list',
    'BlockKey'=>'有色宝',
    'BlockTag'=>'table',
    'time1'=>'11:30:00',
    'time2'=>'11:30:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,059',
    'nvarietyid'=>'ys,201,c21,ys,202,c21,ys,203,c21,ys,204,c21,ys,205,c21,ys,206,c21',
    'nkeys'=>'华通有色,基本金属,现货价格,国内综述',
    'oldCaiJiHangQing'=>'上海华通有色金属现货行情'
 );

//  南海有色金属现货行情
$GLOBALS['HangQingSetting'][NANHAI_CANL_TYPE] =  array(
    'BigType'=>CANL_BIGTYPE,
    'ClassName'=>'canlSpider',
    'CaiJiHangQing'=>'南海有色金属现货行情',
    'URL'=>'https://market.cnal.com/nanhai/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'申请免费试用',
    'ListBlockTag'=>'div.grid-market-list',
    'BlockKey'=>'有色宝',
    'BlockTag'=>'table',
    'time1'=>'11:40:00',
    'time2'=>'14:00:00',
    'CaptureDayMode'=>'',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,059',
    'nvarietyid'=>'ys,201,c21,ys,202,c21,ys,203,c21,ys,204,c21,ys,205,c21,ys,206,c21',
    'nkeys'=>'南海有色,基本金属,现货价格,国内综述',
    'oldCaiJiHangQing'=>'南海有色（灵通）金属现货行情'
 );

 $GLOBALS['HangQingSetting'][CU_SHFEPM_TYPE] =  array(
    'BigType'=>SHFEPM_BIGTYPE,
    'ClassName'=>'shfepmSpider',
    'CaiJiHangQing'=>'上期所铜期货成交及持仓排名',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/pm'.date("Ymd").'.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'18:00:00',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,201,c21',
    'nkeys'=>'上期所,铜,期货成交,持仓排名'
 );

 $GLOBALS['HangQingSetting'][AL_SHFEPM_TYPE] =  array(
    'BigType'=>SHFEPM_BIGTYPE,
    'ClassName'=>'shfepmSpider',
    'CaiJiHangQing'=>'上期所铝期货成交及持仓排名',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/pm'.date("Ymd").'.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'18:00:00',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,202,c21',
    'nkeys'=>'上期所,铝,期货成交,持仓排名'
 );

 $GLOBALS['HangQingSetting'][ZN_SHFEPM_TYPE] =  array(
    'BigType'=>SHFEPM_BIGTYPE,
    'ClassName'=>'shfepmSpider',
    'CaiJiHangQing'=>'上期所锌期货成交及持仓排名',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/pm'.date("Ymd").'.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'18:00:00',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,204,c21',
    'nkeys'=>'上期所,锌,期货成交,持仓排名'
 );

 $GLOBALS['HangQingSetting'][AU_SHFEPM_TYPE] =  array(
    'BigType'=>SHFEPM_BIGTYPE,
    'ClassName'=>'shfepmSpider',
    'CaiJiHangQing'=>'上期所黄金期货成交及持仓排名',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/pm'.date("Ymd").'.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'18:00:00',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,208,c21',
    'nkeys'=>'上期所,黄金,期货成交,持仓排名'
 );

 $GLOBALS['HangQingSetting'][AG_SHFEPM_TYPE] =  array(
    'BigType'=>SHFEPM_BIGTYPE,
    'ClassName'=>'shfepmSpider',
    'CaiJiHangQing'=>'上期所白银期货成交及持仓排名',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/pm'.date("Ymd").'.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'18:00:00',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ys,208,c21',
    'nkeys'=>'上期所,白银,期货成交,持仓排名'
 );

 $GLOBALS['HangQingSetting'][Daily_SHFEKC_TYPE] =  array(
    'BigType'=>SHFEKC_BIGTYPE,
    'ClassName'=>'shfekcSpider',
    'CaiJiHangQing'=>'上期所期货库存仓单日报',
    'URL'=>'https://www.shfe.com.cn/data/tradedata/future/dailydata/'.date("Ymd").'dailystock.dat',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'time1'=>'16:00:00',
    'time2'=>'18:00:00',
    'DbTable'=>'news',
    'ManagerName'=>'蒋晨',
    'ManagerNameId'=>'430051',
    'nchannelid'=>'08',
    'ncolumnid'=>'002,063',
    'nvarietyid'=>'ys,201,c21,ys,202,c21,ys,203,c21,ys,204,c21,ys,208,c21',
    'nkeys'=>'上期所,基本金属,贵金属,期货库存'
 );

 $GLOBALS['HangQingSetting'][SX_COAL_JINGJIA_TYPE] =  array(
    'BigType'=>COAL_JINGJIA_BIGTYPE,
    'ClassName'=>'coalJingJiaSpider',
    'CaiJiHangQing'=>'山西焦煤集团竞价信息',
    // 'URL'=>'https://mtex.sxccol.com/expcoal/exp/auction/buy/bout/pmmorelist.do?pageNumber=2&typ=1',
    'URL'=>'https://mtex.sxccol.com/expcoal/exp/auction/buy/bout/pmmorelist.do?typ=1&pageNumber=1&pageSize=50',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'value="清除"',
    'ListBlockTag'=>'ul.list',
    'BlockKey'=>'价格指数</a>',
    'BlockTag'=>'div.xh_scgg_zwnr',
    'time1'=>'14:00:00',
    'time2'=>'16:00:00',
    'KeyWords'=>'煤炭竞卖预告（公路,煤炭竞卖预告(公路,煤炭产品竞卖预告（公路）,煤炭(公路）产品竞卖预告,山西焦煤煤炭产品竞卖预告(公路',
    'DbTable'=>'',
    'ManagerName'=>'侯香',
    'ManagerNameId'=>'444615',
    'scId'=>'1254',
    'ncolumnid'=>'007,067',
    'nvarietyid'=>'mhg,615,c615',
    'nkeys'=>''
 );


 $GLOBALS['HangQingSetting'][CHANG_QIN_COAL_JINGJIA_TYPE] =  array(
    'BigType'=>COAL_JINGJIA_BIGTYPE,
    'ClassName'=>'coalJingJiaShanXiSpider',
    'CaiJiHangQing'=>'山西长沁煤焦煤炭销售有限公司',
    'URL'=>'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%B1%B1%E8%A5%BF%E9%95%BF%E6%B2%81&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'煤炭竞卖公告',
    'BlockTag'=>'div.xh_scgg_zwnr',
    'time1'=>'10:00:00',
    'time2'=>'18:00:00',
    'KeyWords'=>'长沁',
    'DbTable'=>'',
    'ManagerName'=>'侯香',
    'ManagerNameId'=>'444615',
    'scId'=>'5704',
    'ncolumnid'=>'007,067',
    'nvarietyid'=>'mhg,615,c615',
    'nkeys'=>''
 );

 $GLOBALS['HangQingSetting'][HUANG_TU_PO_COAL_JINGJIA_TYPE] =  array(
    'BigType'=>COAL_JINGJIA_BIGTYPE,
    'ClassName'=>'coalJingJiaShanXiSpider',
    'CaiJiHangQing'=>'山西黄土坡集团',
    'URL'=>'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%B1%B1%E8%A5%BF%E9%BB%84%E5%9C%9F%E5%9D%A1&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'煤炭竞卖公告',
    'BlockTag'=>'div.xh_scgg_zwnr',
    'time1'=>'10:00:00',
    'time2'=>'18:00:00',
    'KeyWords'=>'山西黄土坡',
    'DbTable'=>'',
    'ManagerName'=>'侯香',
    'ManagerNameId'=>'444615',
    'scId'=>'5705',
    'ncolumnid'=>'007,067',
    'nvarietyid'=>'mhg,615,c615',
    'nkeys'=>''
 );

 $GLOBALS['HangQingSetting'][DONG_HUI_NY_JINGJIA_TYPE] =  array(
	 'BigType'=>COAL_JINGJIA_BIGTYPE,
	 'ClassName'=>'coalJingJiaSpider',
	 'CaiJiHangQing'=>'山西东辉能源集团',
	 'URL'=>'https://mtex.sxccol.com/expcoal/exp/auction/buy/bout/pmmorelist.do?typ=7&pageNumber=1&pageSize=50',
	 'Encoding'=>'utf-8',
	 'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
	 'UrlIsList'=>1,
	 'ListBlockKey'=>'value="清除"',
	 'ListBlockTag'=>'ul.list',
	 'BlockKey'=>'价格指数</a>',
	 'BlockTag'=>'div.xh_scgg_zwnr',
	 'time1'=>'9:00:00',
	 'time2'=>'16:00:00',
	 'KeyWords'=>'东辉',
	 'DbTable'=>'',
	 'ManagerName'=>'侯香',
	 'ManagerNameId'=>'444615',
	 'scId'=>'4608',
	 'ncolumnid'=>'007,067',
	 'nvarietyid'=>'mhg,615,c615',
	 'nkeys'=>''
 );

$GLOBALS['HangQingSetting'][LIU_LIN_HF_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '柳林汇丰',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E6%B1%87%E4%B8%B0&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '汇丰',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4460',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][YONG_TAI_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '永泰能源',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E6%B0%B8%E6%B3%B0%E8%83%BD%E6%BA%90&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '永泰能源',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4661',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][JING_HUI_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '金晖万峰',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E9%87%91%E6%99%96%E4%B8%87%E5%B3%B0&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '金晖万峰',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4633',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][LAN_XIAN_CH_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西岚县昌恒',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%B1%B1%E8%A5%BF%E5%B2%9A%E5%8E%BF%E6%98%8C%E6%81%92&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '山西岚县昌恒',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4629',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][FANG_SHAN_JH_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西方山金晖瑞隆',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E6%96%B9%E5%B1%B1%E9%87%91%E6%99%96%E7%91%9E%E9%9A%86&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '方山金晖瑞隆',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4589',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][DONG_TAI_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西东泰控股集团有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%B1%B1%E8%A5%BF%E4%B8%9C%E6%B3%B0&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '山西东泰',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4673',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][SHOU_WAN_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西翼城首旺煤业有限责任公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E7%BF%BC%E5%9F%8E%E9%A6%96%E6%97%BA&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '翼城首旺',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4672',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);

$GLOBALS['HangQingSetting'][QIN_XIN_COAL_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西沁新煤炭销售有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%B1%B1%E8%A5%BF%E6%B2%81%E6%96%B0&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '山西沁新',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '5707',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
);


 $GLOBALS['HangQingSetting'][DA_TU_HE_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西大土河国际贸易有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%A4%A7%E5%9C%9F%E6%B2%B3&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '大土河',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '3645',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
 );

 $GLOBALS['HangQingSetting'][JING_LIU_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山西晋柳能源有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E6%99%8B%E6%9F%B3%E8%83%BD%E6%BA%90&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '晋柳能源',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '4462',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
 );

 $GLOBALS['HangQingSetting'][CHANG_SHENG_PING_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '襄汾县昌昇平煤业有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E8%A5%84%E6%B1%BE%E5%8E%BF%E6%98%8C%E6%98%87%E5%B9%B3%E7%85%A4%E4%B8%9A&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '襄汾县昌昇平煤业',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '5709',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
 );


 $GLOBALS['HangQingSetting'][JING_GU_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '古县金谷贸易有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%8F%A4%E5%8E%BF%E9%87%91%E8%B0%B7&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '古县金谷',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '5710',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
 );


 $GLOBALS['HangQingSetting'][RUI_NENG_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '介休瑞能煤炭销售有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E4%BB%8B%E4%BC%91%E7%91%9E%E8%83%BD&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=7',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖公告',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '介休瑞能',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '5712',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
 );


 $GLOBALS['HangQingSetting'][SHAN_MEI_JINGJIA_TYPE] =  array(
    'BigType' => COAL_JINGJIA_BIGTYPE,
    'ClassName' => 'coalJingJiaShanXiSpider',
    'CaiJiHangQing' => '山煤国际能源集团股份有限公司',
    'URL' => 'https://www.sxccol.com/api/epm-sxccol-system/sxccol/api/article/findNoticeListByCategoryId?keywords=%E5%B1%B1%E7%85%A4%E5%9B%BD%E9%99%85&publishDateStart=&publishDateEnd=&pageNo=1&pageSize=20&articleCategory=1',
    'Encoding' => 'utf-8',
    'GetUseAjax' => '1', //0:简单页面截取 1：ajax截取
    'UrlIsList' => 1,
    'ListBlockKey' => '',
    'ListBlockTag' => '',
    'BlockKey' => '煤炭竞卖',
    'BlockTag' => 'div.xh_scgg_zwnr',
    'time1' => '10:00:00',
    'time2' => '18:00:00',
    'KeyWords' => '经坊矿,霍尔辛赫矿',
    'DbTable' => '',
    'ManagerName' => '侯香',
    'ManagerNameId' => '444615',
    'scId' => '5713',
    'ncolumnid' => '007,067',
    'nvarietyid' => 'mhg,615,c615',
    'nkeys' => ''
 );


 $GLOBALS['HangQingSetting'][LIU_AN_HG_JINGJIA_TYPE] =  array(
	 'BigType'=>COAL_JINGJIA_TAIYUAN_BIGTYPE,
	 'ClassName'=>'coalTaiYuanJingJiaSpider',
	 'CaiJiHangQing'=>'潞安',
	 // 'URL'=>'https://mtex.sxccol.com/expcoal/exp/auction/buy/bout/pmmorelist.do?pageNumber=2&typ=1',
	//  'URL'=>'http://e.ctctc.cn/search_list_gg.jspx?page=1',
	 'URL'=>'https://www.ctctc.cn/search_list_gg.jspx?&page=1&limit=100&trademodeid=3&postatus=-1',
	 'Encoding'=>'utf-8',
	 'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	 'UrlIsList'=>0,
	 'ListBlockKey'=>'value="清除"',
	 'ListBlockTag'=>'ul.list',
	 'BlockKey'=>'一、基本情况',
	 'BlockTag'=>'',
	 'time1'=>'10:00:00',
	 'time2'=>'18:00:00',
	 'CaptureDayMode'=>EVERYDAY,
	 'KeyWords'=>'高河,常兴,常村,古城,郭庄,司马,余吾',
	 'DbTable'=>'',
	 'ManagerName'=>'侯香',
	 'ManagerNameId'=>'444615',
	 'scId'=>'1255',
	 'ncolumnid'=>'007,067',
	 'nvarietyid'=>'mhg,615,c615',
	 'nkeys'=>''
 );

$GLOBALS['HangQingSetting'][TYMQH_JINGJIA_TYPE] =  array(
	'BigType'=>COAL_JINGJIA_TAIYUAN_BIGTYPE,
	'ClassName'=>'coalTaiYuanJingJiaSpider',
	'CaiJiHangQing'=>'太原煤气化',
	'URL'=>'https://www.ctctc.cn/search_list_gg.jspx?&page=1&limit=100&trademodeid=3&postatus=-1',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'value="清除"',
	'ListBlockTag'=>'ul.list',
	'BlockKey'=>'一、基本情况',
	'BlockTag'=>'',
	'time1'=>'10:00:00',
	'time2'=>'18:00:00',
	'CaptureDayMode'=>EVERYDAY,
	'KeyWords'=>'华苑矿,东河,华胜,登茂通',
	'DbTable'=>'',
	'ManagerName'=>'侯香',
	'ManagerNameId'=>'444615',
	'scId'=>'3763',
	'ncolumnid'=>'007,067',
	'nvarietyid'=>'mhg,615,c615',
	'nkeys'=>''
);

$GLOBALS['HangQingSetting'][MTYXJT_JINGJIA_TYPE] =  array(
	'BigType'=>COAL_JINGJIA_TAIYUAN_BIGTYPE,
	'ClassName'=>'coalTaiYuanJingJiaSpider',
	'CaiJiHangQing'=>'长治公司',
	'URL'=>'https://www.ctctc.cn/search_list_gg.jspx?&page=1&limit=100&trademodeid=3&postatus=-1',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'value="清除"',
	'ListBlockTag'=>'ul.list',
	'BlockKey'=>'一、基本情况',
	'BlockTag'=>'',
	'time1'=>'10:00:00',
	'time2'=>'18:00:00',
	'CaptureDayMode'=>EVERYDAY,
	'KeyWords'=>'中能,三元,小常',
	'DbTable'=>'',
	'ManagerName'=>'侯香',
	'ManagerNameId'=>'444615',
	'scId'=>'4657',
	'ncolumnid'=>'007,067',
	'nvarietyid'=>'mhg,615,c615',
	'nkeys'=>''
);

$GLOBALS['HangQingSetting'][SHANXILILIU_JINGJIA_TYPE] =  array(
	'BigType'=>COAL_JINGJIA_TAIYUAN_BIGTYPE,
	'ClassName'=>'coalTaiYuanJingJiaSpider',
	'CaiJiHangQing'=>'山西离柳焦煤',
	'URL'=>'https://www.ctctc.cn/search_list_gg.jspx?&page=1&limit=100&trademodeid=3&postatus=-1',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'value="清除"',
	'ListBlockTag'=>'ul.list',
	'BlockKey'=>'一、基本情况',
	'BlockTag'=>'',
	'time1'=>'10:00:00',
	'time2'=>'18:00:00',
	'CaptureDayMode'=>EVERYDAY,
	'KeyWords'=>'兑镇,朱家店,宏岩,鑫瑞,华泰',
	'DbTable'=>'',
	'ManagerName'=>'侯香',
	'ManagerNameId'=>'444615',
	'scId'=>'3602',
	'ncolumnid'=>'007,067',
	'nvarietyid'=>'mhg,615,c615',
	'nkeys'=>''
);

$GLOBALS['HangQingSetting'][XNY_XLLD_TYPE] =  array(
    'BigType'=>XNY_BIGTYPE,
    'ClassName'=>'XnySpider',
    'CaiJiHangQing'=>'统计产量',
    'URL'=>'http://www.icbattery.com/news/list-htm-catid-754-page-1.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>1,
    'ListBlockKey'=>'当前位置:',
    'ListBlockTag'=>'div.left_box>ul',
    'BlockKey'=>'当前位置:',
    'BlockTag'=>'div.introduce',
    'time1'=>'',
    'time2'=>'',
    'KeyWords'=>'',
    'DbTable'=>'',
    'ManagerName'=>'',
    'ManagerNameId'=>'',
    'scId'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>''
);
$GLOBALS['HangQingSetting'][XNY_MYSTEEL_NIE_CL_TYPE] =  array(
    'BigType'=>XNY_BIGTYPE,
    'ClassName'=>'XnyNieSpider',
    'CaiJiHangQing'=>'统计产量',
    'URL'=>'https://nie.mysteel.com/article/p-6078----0222---------1.html?keyWord=%E9%95%8D%E9%93%81%E4%BA%A7%E9%87%8F%E7%BB%9F%E8%AE%A1',
    'Encoding'=>'gb2312',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'AjaxUrl'=>'',
    'UrlIsList'=>1,
    'ListBlockKey'=>'当前位置',
    'ListBlockTag'=>'ul.news-list1',
    'BlockKey'=>'当前位置',
    'BlockTag'=>'div#article-content',
    'time1'=>'',
    'time2'=>'',
    'KeyWords'=>'',
    'DbTable'=>'',
    'LoginURL'=>'https://passport.mysteel.com/login/loginByPwd',
    'LoginData'=>array(
        'username' => '6188288',
        'jumpPage' => 'https://coal.mysteel.com',
        'site' => 'coal.mysteel.com',
        'my_rememberStatus' => '1',
        'vcode' => '',
        'password' => '6188288'
	),
);


$GLOBALS['HangQingSetting'][US_MGYYZLCB_TYPE] =  array(
    'ClassName'=>'MGYYZLCBSpider',
    'CaiJiHangQing'=>'美国原油战略储备',
    'URL'=>'https://www.eia.gov/dnav/pet/pet_stoc_wstk_a_epc0_SAS_mbbl_w.htm',
    'Encoding'=>'GBK',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'class="data1"',
    'ListBlockTag'=>'class="data1"',
    'BlockKey'=>'class="data1"',
    'BlockTag'=>'',
    'time1'=>'9:00:00',
    'time2'=>'11:00:00',
    'CaptureDayMode'=>'',
    'KeyWords'=>'美国原油战略储备',
    'DbTable'=>'',
    'ManagerName'=>'',
    'ManagerNameId'=>'',
    'scId'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>''
);
//广州期货交易所工业硅
$GLOBALS['HangQingSetting'][GZQHJYS_SI] =  array(
	'BigType'=>GZQHJYS_BIGTYPE,
	'ClassName'=>'GZQHJYSSpider',
	'CaiJiHangQing'=>'广州期货交易所工业硅期货收盘行情',
	'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiDayQuotes/loadList?trade_date='.date("Ymd").'&trade_type=0&variety=si',
	//'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiDayQuotes/loadList?trade_date=20230515&trade_type=0',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'',
	'BlockTag'=>'',
	'data_type'=>'Java-GZQHJYSSI',
	'time1'=>'15:40:00',
	'time2'=>'17:10:00',
	'DbTable'=>'data_table',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,325,c325',
	'nkeys'=>'工业硅,期货行情',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYS_LC] =  array(
	'BigType'=>GZQHJYS_BIGTYPE,
	'ClassName'=>'GZQHJYSSpider',
	'CaiJiHangQing'=>'广州期货交易所碳酸锂期货收盘行情',
	'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiDayQuotes/loadList?trade_date='.date("Ymd").'&trade_type=0&variety=lc',
	//'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiDayQuotes/loadList?trade_date=20230515&trade_type=0',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'',
	'BlockTag'=>'',
	'data_type'=>'Java-GZQHJYSLC',
	'time1'=>'15:31:00',
	'time2'=>'16:40:00',
	'DbTable'=>'data_table',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,303,c303',
	'nkeys'=>'碳酸锂,期货行情',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYS_PS] =  array(
    'BigType'=>GZQHJYS_BIGTYPE,
    'ClassName'=>'GZQHJYSSpider',
    'CaiJiHangQing'=>'广州期货交易所多晶硅期货收盘行情',
    'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiDayQuotes/loadList?trade_date='.date("Ymd").'&trade_type=0&variety=ps',
    //'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiDayQuotes/loadList?trade_date=20230515&trade_type=0',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'Java-GZQHJYSPS',
    'time1'=>'15:31:00',
    'time2'=>'16:40:00',
    'DbTable'=>'data_table',
    'nchannelid'=>'25',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ny,326,c326',
    'nkeys'=>'多晶硅,期货行情',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYSCD] =  array(
	'BigType'=>GZQHJYS_BIGTYPE,
	'ClassName'=>'GzfeSpider',
	'CaiJiHangQing'=>'广州期货交易所工业硅持仓排名',
	'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiMemberDealPosiQuotes/',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'',
	'BlockTag'=>'',
	'data_type'=>'',
	'time1'=>'15:40:00',
	'time2'=>'',
	'DbTable'=>'qh_member_paiming',
	'nchannelid'=>'',
	'ncolumnid'=>'',
	'nvarietyid'=>'',
	'nkeys'=>'',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYS_LCCD] =  array(
    'BigType'=>GZQHJYS_BIGTYPE,
    'ClassName'=>'GzfeSpider',
    'CaiJiHangQing'=>'广州期货交易所碳酸锂持仓排名',
    'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiMemberDealPosiQuotes/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'',
    'time1'=>'15:40:00',
    'time2'=>'',
    'DbTable'=>'qh_member_paiming',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYS_PSCD] =  array(
    'BigType'=>GZQHJYS_BIGTYPE,
    'ClassName'=>'GzfeSpider',
    'CaiJiHangQing'=>'广州期货交易所多晶硅持仓排名',
    'URL'=>'http://www.gfex.com.cn/u/interfacesWebTiMemberDealPosiQuotes/',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'',
    'time1'=>'15:40:00',
    'time2'=>'',
    'DbTable'=>'qh_member_paiming',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYS_SIKC] =  array(
	'BigType'=>GZQHJYS_BIGTYPE,
	'ClassName'=>'GzfeKcSpider',
	'CaiJiHangQing'=>'广州期货交易所工业硅仓单日报',
	'URL'=>'http://www.gfex.com.cn/u/interfacesWebTdWbillWeeklyQuotes/loadList?variety=si',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'',
	'BlockTag'=>'',
	'data_type'=>'Java-GZQHJYS_SIKC',
	'time1'=>'16:00:00',
	'time2'=>'',
	'DbTable'=>'data_table',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,325,c325',
	'nkeys'=>'工业硅,期货行情,仓单日报',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][GZQHJYS_LCKC] =  array(
	'BigType'=>GZQHJYS_BIGTYPE,
	'ClassName'=>'GzfeKcSpider',
	'CaiJiHangQing'=>'广州期货交易所碳酸锂仓单日报',
	'URL'=>'http://www.gfex.com.cn/u/interfacesWebTdWbillWeeklyQuotes/loadList?variety=lc',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'',
	'BlockTag'=>'',
	'data_type'=>'Java-GZQHJYS_LCKC',
	'time1'=>'16:00:00',
	'time2'=>'',
	'DbTable'=>'data_table',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,303,c303',
	'nkeys'=>'碳酸锂,期货行情,仓单日报',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

//20250318还无数据，先注释
/*$GLOBALS['HangQingSetting'][GZQHJYS_PSKC] =  array(
    'BigType'=>GZQHJYS_BIGTYPE,
    'ClassName'=>'GzfeKcSpider',
    'CaiJiHangQing'=>'广州期货交易所多晶硅仓单日报',
    'URL'=>'http://www.gfex.com.cn/u/interfacesWebTdWbillWeeklyQuotes/loadList?variety=ps',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'1', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'',
    'data_type'=>'Java-GZQHJYS_PSKC',
    'time1'=>'16:00:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'25',
    'ncolumnid'=>'002,185',
    'nvarietyid'=>'ny,326,c326',
    'nkeys'=>'多晶硅,期货行情,仓单日报',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);*/

$GLOBALS['HangQingSetting'][NEW_JINGJIA_TYPE] =  array(
	'BigType'=>COAL_JINGJIA_NEW_BIGTYPE,
	'ClassName'=>'coalJingJiaNewSpider',
	'CaiJiHangQing'=>'焦煤在线',
	'URL'=>'https://www.sxccol.com/epm-sxccolweb/portal/Notice?categoryId=1',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'div.notice-list',
	'BlockTag'=>'',
	'data_type'=>'',
	'time1'=>'',
	'time2'=>'',
	'DbTable'=>'',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,303,c303',
	'nkeys'=>'',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][MSE_COAL_TYPE] =  array(
	'BigType'=>MSE_MN_BIGTYPE,
	'ClassName'=>'MseSpider',
	'CaiJiHangQing'=>'蒙煤竞拍',
	'URL'=>'https://mse.mn/en/auction_schedules/252',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'Auction schedule',
	'BlockTag'=>'table#auctionTable',
	'data_type'=>'',
	'time1'=>'',
	'time2'=>'',
	'DbTable'=>'',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,303,c303',
	'nkeys'=>'',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][MSE_COAL_TYPE2] =  array(
	'BigType'=>MSE_MN_BIGTYPE,
	'ClassName'=>'MseDealSpider',
	'CaiJiHangQing'=>'蒙煤竞拍',
	'URL'=>'https://mse.mn/en/coal_trading_history/2025',
	'Encoding'=>'utf-8',
	'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
	'UrlIsList'=>0,
	'ListBlockKey'=>'',
	'ListBlockTag'=>'',
	'BlockKey'=>'',
	'BlockTag'=>'table',
	'data_type'=>'',
	'time1'=>'',
	'time2'=>'',
	'DbTable'=>'',
	'nchannelid'=>'25',
	'ncolumnid'=>'002,185',
	'nvarietyid'=>'ny,303,c303',
	'nkeys'=>'',
	'ManagerName'=>'吴运豪',
	'ManagerNameId'=>'444830'
);
$GLOBALS['HangQingSetting'][CZCE_QIHUO_SFCD] =  array(
    'BigType'=>CZCEKC_BIGTYPE,
    'ClassName'=>'CzceKcSpider',
    'CaiJiHangQing'=>'郑州期货交易所硅铁仓单日报',
    'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'sfcd.html',
    //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20250604sfcd.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'',
    'data_type'=>'Java-CZCE_SFCD',
    'time1'=>'16:20:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);
$GLOBALS['HangQingSetting'][CZCE_QIHUO_SMCD] =  array(
    'BigType'=>CZCEKC_BIGTYPE,
    'ClassName'=>'CzceKcSpider',
    'CaiJiHangQing'=>'郑州期货交易所硅锰仓单日报',
    'URL'=>'http://172.16.130.3:8233/dce/zss/zss'.date("Ymd").'smcd.html',
    //'URL'=>'http://172.16.130.3:8233/dce/zss/zss20250604smcd.html',
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'打印',
    'BlockTag'=>'',
    'data_type'=>'Java-CZCE_SMCD',
    'time1'=>'16:20:00',
    'time2'=>'',
    'DbTable'=>'data_table',
    'nchannelid'=>'',
    'ncolumnid'=>'',
    'nvarietyid'=>'',
    'nkeys'=>'',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);

//人民币汇率中间价
$GLOBALS['HangQingSetting'][RMB_RATE_VND] =  array(
    'BigType'=>RMB_BIGTYPE,
    'ClassName'=>'SafeRmb2Spider',
    'CaiJiHangQing'=>'人民币汇率中间价',
    'URL'=>'https://chl.cn/huilv/?vnd',//vnd 越南盾 、idr 印尼卢比
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'div.L1',
    'time1'=>'09:15:00',
    'time2'=>'10:20:00',
    'DbTable'=>'rmbrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);

$GLOBALS['HangQingSetting'][RMB_RATE_IDR] =  array(
    'BigType'=>RMB_BIGTYPE,
    'ClassName'=>'SafeRmb2Spider',
    'CaiJiHangQing'=>'人民币汇率中间价',
    'URL'=>'https://chl.cn/huilv/?idr',//vnd 越南盾 、idr 印尼卢比
    'Encoding'=>'utf-8',
    'GetUseAjax'=>'0', //0:简单页面截取 1：ajax截取
    'UrlIsList'=>0,
    'ListBlockKey'=>'',
    'ListBlockTag'=>'',
    'BlockKey'=>'',
    'BlockTag'=>'div.L1',
    'time1'=>'09:30:00',
    'time2'=>'10:30:00',
    'DbTable'=>'rmbrate',
    'ManagerName'=>'吴运豪',
    'ManagerNameId'=>'444830'
);
?>